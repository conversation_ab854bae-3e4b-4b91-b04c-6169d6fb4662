﻿namespace Racebox.Views.Navigation;

[ContentProperty("CustomContent")]
public class NavBarCustomContent : ContentView
{
    //-------------------------------------------------------------
    // Content
    //-------------------------------------------------------------
    private const string nameContent = "CustomContent";
    public static readonly BindableProperty CustomContentProperty = BindableProperty.Create(nameContent, typeof(View), typeof(NavBarCustomContent), null); //, BindingMode.TwoWay
    public View CustomContent
    {
        get
        {
            return (View)GetValue(CustomContentProperty);
        }
        set
        {
            SetValue(CustomContentProperty, value);
        }
    }
}