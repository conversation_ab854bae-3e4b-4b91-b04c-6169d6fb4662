using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Racebox.Views.Partials;

public partial class ConnectionBar
{

    /// <summary>
    /// We need this in every custom control )with dynamic data
    /// so we redraw graphics when the view returns
    /// to visible onscreen (tabs switching).
    /// SkiaPanel-derived custom control do not need this,
    /// they have built-in mechanics
    /// </summary>
    /// <param name="bounds"></param>
    /// <returns></returns>
    protected override Size ArrangeOverride(Rect bounds)
    {
        Update();

        return base.ArrangeOverride(bounds);
    }

    public ConnectionBar()
    {
        ((Element)this).HandlerChanging += HandlerChanging;
        HandlerChanged += OnHandlerChanged;

        Update();

        InitializeComponent();



    }

    private void OnHandlerChanged(object sender, EventArgs e)
    {
        HandlerChanged -= OnHandlerChanged;

        MainThread.BeginInvokeOnMainThread(() =>
        {
            SetupConnectionIcon();
        });
    }

    protected override void OnBindingContextChanged()
    {
        base.OnBindingContextChanged();

        if (BindingContext is RaceBoxDeviceViewModel vm)
        {
            _vm = vm;
            Subscribe();
        }


    }

    void SetupConnectionIcon()
    {

        //test anim
        //IsBlinking = true;
        //return;

        if (_vm != null)
        {
            if (_vm.IsConnecting)
            {
                IsBlinking = true;
            }
            else
            {
                IsBlinking = false;

                if (_vm.IsConnected)
                {
                    IconBT.TintColor = Color.FromArgb("#36A7CB");
                }
                else
                {
                    IconBT.TintColor = Color.FromArgb("#E47F53");
                }
            }
        }
    }

    //void StopBlinking()
    //{
    //    _blinkAnimator?.Stop();
    //    isBlinking = false;

    //    //if (_animation != null)
    //    //{
    //    //    _animation.Stop();
    //    //    isBlinking = false;
    //    //}
    //}



    private BlinkAnimator _blinkAnimator;



    void SetBlinking(bool state)
    {
        if (state)
        {
            if (_blinkAnimator == null)
            {
                _blinkAnimator = new BlinkAnimator(IconBT)
                {
                    Repeat = -1,
                    Speed = 280,
                    ColorsRatio = 0.66,
                    Color1 = Color.FromArgb("#36A7CB"),
                    Color2 = Color.FromArgb("#1136A7CB"),
                    OnUpdated = (value) =>
                    {
                        IconBT.TintColor = _blinkAnimator.CurrentColor;
                    }
                };
            }

            if (!_blinkAnimator.IsRunning)
            {
                _blinkAnimator.Start();
            }
        }
        else
        {
            if (_blinkAnimator != null && _blinkAnimator.IsRunning)
            {
                _blinkAnimator.Stop();
            }
        }

    }


    public bool IsBlinking
    {
        get
        {
            return _isBlinking;
        }
        set
        {
            if (_isBlinking != value)
            {
                _isBlinking = value;
                OnPropertyChanged();
                SetBlinking(_isBlinking);
            }
        }
    }
    bool _isBlinking;

    void Subscribe()
    {
        if (_vm != null)
        {
            _vm.PropertyChanged += OnModelPropertyChanged;
        }
    }
    void Unsubscribe()
    {
        if (_vm != null)
        {
            _vm.PropertyChanged -= OnModelPropertyChanged;
        }
    }


    private void HandlerChanging(object sender, HandlerChangingEventArgs e)
    {
        if (e.NewHandler == null)
        {
            Unsubscribe();
        }
        else
        {
            Subscribe();
        }
    }



    private void OnModelPropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == "IsConnecting" || e.PropertyName == "IsConnected")
        {
            SetupConnectionIcon();
        }

    }

    protected override void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        base.OnPropertyChanged(propertyName);

        if (propertyName == nameof(Max)
            || propertyName == nameof(Min)
            || propertyName == nameof(Value)
            )
        {
            Update();
        }
    }

    public static FloatingPosition[] GradientPoints =
    {
        //1
        new ()
        {
            IsFixed = true,
            Base = 0.0
        },
        //2
        new ()
        {
            Base = 0.25
        },  
        //3
        new ()
        {
            Base = 0.5
        },  
        //4
        new ()
        {
            Base = 0.95
        },
        //5
		new ()
        {
            Stick = 0.05,
            Base = 1.0
        },  
        //6
        new ()
        {
            IsFixed = true,
            Base = 1.0
        },
    };
    private RaceBoxDeviceViewModel _vm;

    void Update()
    {

        /*
		foreach (var point in GradientPoints)
		{
			if (point.IsFixed)
			{
				point.Value = point.Base;
				continue;
			}

			if (Value >= point.Base)
				point.Value = point.Base;
			else
				point.Value = Value;

			if (point.Stick > 0)
			{
				point.Value += point.Stick;
				if (point.Value > 1)
					point.Value = 1;
			}


		}
		OnPropertyChanged(nameof(Points));
		*/

        SkiaSignal?.Update();
    }

    private double _InverterWidth;
    public double InverterWidth
    {
        get
        {
            return _InverterWidth;
        }
        set
        {
            if (_InverterWidth != value)
            {
                _InverterWidth = value;
                OnPropertyChanged();
            }
        }
    }


    public double[] Points
    {
        get
        {
            return GradientPoints.Select(point => point.Value).ToArray();
        }
    }

    public static readonly BindableProperty MaxProperty =
        BindableProperty.Create(nameof(Max),
            typeof(double),
            typeof(ConnectionBar),
            100.0);
    public double Max
    {
        get { return (double)GetValue(MaxProperty); }
        set { SetValue(MaxProperty, value); }
    }

    public static readonly BindableProperty MinProperty =
        BindableProperty.Create(nameof(Min),
            typeof(double),
            typeof(ConnectionBar),
            0.0);
    public double Min
    {
        get { return (double)GetValue(MinProperty); }
        set { SetValue(MinProperty, value); }
    }

    public static readonly BindableProperty ValueProperty =
        BindableProperty.Create(nameof(Value),
            typeof(double),
            typeof(ConnectionBar),
            0.0);



    public double Value
    {
        get { return (double)GetValue(ValueProperty); }
        set { SetValue(ValueProperty, value); }
    }


}