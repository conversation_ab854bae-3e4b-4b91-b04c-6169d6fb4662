﻿<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="Racebox.Views.PageLog"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:drawn="http://schemas.appomobi.com/drawnUi/2023/drawn"
    xmlns:partials="clr-namespace:Racebox.Views.Partials"
    xmlns:strings="clr-namespace:Racebox.Shared.Strings;assembly=Racebox.Shared"
    xmlns:touch="clr-namespace:AppoMobi.Maui.Gestures;assembly=AppoMobi.Maui.Gestures"
    xmlns:viewModels="clr-namespace:Racebox.ViewModels"
    xmlns:views="clr-namespace:Racebox.Views"
    x:Name="ThisPage"
    Title="PageLog"
    x:DataType="viewModels:ProjectViewModel">

    <!--<?xml version="1.0" encoding="utf-8"?>
<svg width="21px" height="18px" viewBox="0 0 21 18" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg">
  <desc>Created with Lunacy</desc>
  <defs>
    <linearGradient x1="0.8578936" y1="0.1415124" x2="0.1243169" y2="1" id="gradient_1">
      <stop offset="0" stop-color="#1A6FCC" />
      <stop offset="1" stop-color="#299BCB" />
    </linearGradient>
  </defs>
  <path d="M0.01 18L21 9L0.01 0L0 7L15 9L0 11L0.01 18Z" id="Path" fill="url(#gradient_1)" stroke="none" />
</svg>-->

    <Grid
        HorizontalOptions="FillAndExpand"
        VerticalOptions="FillAndExpand">

        <!--<Grid.Background>
            <LinearGradientBrush EndPoint="0,1">
                <GradientStop Offset="0.0" Color="#343C45" />
                <GradientStop Offset="1.0" Color="#11161D" />
            </LinearGradientBrush>
        </Grid.Background>-->

        <partials:GradientToBorderView 
            MissingHeight="{Binding NavbarModel.BottomTabsHeightRequest}"
            VerticalOptions="Fill" HorizontalOptions="Fill"/>

        <Grid.Resources>
            <ResourceDictionary>

                <x:String x:Key="SvgLogo">
                    <![CDATA[ 
                                     

       <?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" id="Слой_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 378 380" enable-background="new 0 0 378 380" xml:space="preserve">
<g>
	
		<radialGradient id="SVGID_1_" cx="52.7571" cy="359.8346" r="0.9118" gradientTransform="matrix(127.1027 142.317 131.4388 -117.3874 -53813.0117 34890.8438)" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#3D9BD7"/>
		<stop  offset="1" style="stop-color:#004A88"/>
	</radialGradient>

	<path fill-rule="evenodd" clip-rule="evenodd" fill="url(#SVGID_1_)" d="M296.4,95.5l-21,1.4l-79.8,6.4l-23.7,2.3
		c0,0-20.5,1.4-23.3,15.5c-1.2,6.4-0.2,10.2,1.4,13.2c1.6,3,4.9,5.9,8.7,9.1l39.7,29.2l112.2,82.5c0,0,13.2,10,5.9,18.7
		s-18.2,18.7-22.8,21.9c-4.6,3.2-7.3,6.4-20.1,1.4l-83.9-31.9l-72-27.8c0,0-17.2-8.2-32.3-24.2c-13.2-17.8-23.8-37.4-25.6-55.6
		s-0.9-44.7,6.4-60.6c2.3-5,4.2-10.3,6.8-15.5c5.6-11.3,13.1-22.7,24.6-33.3c16.9-15.5,34.2-23.3,49.2-26.4c8.3-1.8,20-3.2,33.3-3.2
		c10.9,0,22.4,0.7,33.3,2.7c24.2,4.6,34.2,9.6,46,16.9c7.1,4.3,12.9,8.9,19.1,15.5c4.2,4.4,8.6,9.6,13.7,16.9
		c3.2,4.6,4.9,7.9,6.4,10.9c2,4.1,3.3,6.9,3.2,9.1C301.6,93.6,299.9,94.5,296.4,95.5z"/>
	
		<radialGradient id="SVGID_2_" cx="52.6795" cy="359.9765" r="0.9119" gradientTransform="matrix(155.9997 52.5 65.7752 -195.4458 -31718.0469 67720)" gradientUnits="userSpaceOnUse">
		<stop  offset="0" style="stop-color:#FF6A47"/>
		<stop  offset="1" style="stop-color:#AF3004"/>
	</radialGradient>
	
	<path fill-rule="evenodd" clip-rule="evenodd" fill="url(#SVGID_2_)" d="M192.9,123.3l89.4-7.8c0,0,9-0.9,16.9,1.8
		c4,1.4,5.8,2.6,7.3,4.1c1.5,1.5,2.5,3.1,3.6,5.9c5.9,15,9.6,91.6,9.6,91.6s0.8,6.7-2.7,9.1c-1,0.6-3.7,0.8-6.8-0.5
		c-2.2-0.9-124.5-88-124.5-88s-6.4-4.6-7.3-7.3s-0.9-5.5,3.6-7.3c0,0,1.7-0.5,4.1-0.9C188.4,123.8,191.7,123.5,192.9,123.3z"/>

	<path fill-rule="evenodd" clip-rule="evenodd" fill="#D9DADA" d="M17,343.4c0.4-2.4,1.2-4.5,2.5-6.5c1.3-1.9,2.8-3.6,4.6-5
		c1.8-1.4,3.8-2.5,6.1-3.2c2.2-0.8,4.5-1.1,6.9-1.1h24.6v6.8c-0.4,2.5-1.3,4.7-2.6,6.7c-1.3,2-3,3.7-5,5.1c2,1.3,3.6,2.9,5,4.8
		c1.3,1.9,2.2,4.1,2.6,6.5v6.8h-8.6v-5.1c0-1.4-0.3-2.7-1-3.8c-0.7-1.1-1.5-2-2.6-2.7c-1.1-0.7-2.2-1.3-3.5-1.7
		c-1.3-0.4-2.5-0.7-3.7-0.7H25.6v14.1H17V343.4z M33.5,336c-0.8,0.2-1.6,0.6-2.3,1c-0.8,0.4-1.5,0.9-2.2,1.5
		c-0.7,0.6-1.2,1.2-1.8,1.9c-0.5,0.7-0.9,1.5-1.2,2.3h13.1c1.3,0,2.6-0.1,4.1-0.3c1.4-0.2,2.8-0.6,4.1-1.1c1.3-0.5,2.4-1.3,3.4-2.2
		c1-1,1.6-2.2,2-3.6H39.4c-1,0-2,0-3,0.1C35.5,335.4,34.5,335.6,33.5,336z M95.2,335.9c-1-0.3-2-0.5-3-0.6c-1-0.1-2-0.1-3-0.1H66.8
		v-7.6h24.8c2.3,0,4.6,0.4,6.9,1.1c2.2,0.8,4.3,1.8,6.1,3.2s3.3,3.1,4.6,5c1.3,1.9,2.1,4.1,2.5,6.5v5c-0.4,2.4-1.2,4.5-2.5,6.5
		c-1.3,1.9-2.8,3.6-4.6,5s-3.8,2.5-6.1,3.2c-2.2,0.8-4.5,1.1-6.9,1.1h-4.8c-2.3,0-4.6-0.4-6.9-1.1c-2.2-0.8-4.3-1.8-6.1-3.2
		s-3.3-3.1-4.6-5c-1.3-1.9-2.1-4.1-2.5-6.5v-6.5h35.5c-0.7-1.5-1.7-2.8-3-3.8S96.7,336.3,95.2,335.9z M95.2,355.9
		c1.6-0.4,3-1.2,4.4-2.2c1.4-1.1,2.3-2.4,2.9-4.1H76c0.6,1.6,1.6,3,3,4.1c1.4,1.1,2.8,1.8,4.3,2.2c1,0.3,2,0.5,3,0.6
		c1,0.1,2,0.1,3,0.1c1,0,2,0,3-0.1C93.2,356.4,94.2,356.2,95.2,355.9z M133.4,355.9c-1-0.3-1.9-0.7-2.9-1.3
		c-0.9-0.6-1.8-1.3-2.5-2.1c-0.7-0.8-1.3-1.7-1.8-2.7c-0.4-1-0.7-2.1-0.7-3.2v-1.5c0-1.2,0.2-2.2,0.7-3.2c0.4-1,1-1.9,1.8-2.7
		s1.6-1.4,2.5-2c0.9-0.6,1.9-1,2.9-1.3c1-0.3,2-0.5,3-0.6c1-0.1,2-0.1,3-0.1s2,0,3,0.1c1,0.1,2,0.3,3,0.6c1.8,0.5,3.4,1.5,4.8,2.8
		c1.4,1.3,2.4,2.9,2.8,4.8h8.8c-0.4-2.4-1.2-4.5-2.5-6.5c-1.3-1.9-2.8-3.6-4.6-5c-1.8-1.4-3.8-2.5-6.1-3.3c-2.2-0.8-4.5-1.2-6.9-1.2
		h-4.8c-2.3,0-4.6,0.4-6.9,1.1c-2.2,0.8-4.3,1.8-6.1,3.2s-3.3,3.1-4.6,5c-1.3,1.9-2.1,4.1-2.5,6.5v5c0.4,2.4,1.2,4.5,2.5,6.5
		s2.8,3.6,4.6,5s3.8,2.5,6.1,3.2c2.2,0.8,4.5,1.1,6.9,1.1h24.8v-7.6h-22.4c-1,0-2,0-3-0.1C135.4,356.4,134.4,356.2,133.4,355.9z
		 M183.4,355.9c1,0.3,2,0.5,3,0.6c1,0.1,2,0.1,3,0.1h22.4v7.6H187c-2.3,0-4.6-0.4-6.9-1.1c-2.2-0.8-4.3-1.8-6.1-3.2
		c-1.8-1.4-3.3-3.1-4.6-5c-1.3-1.9-2.1-4.1-2.5-6.5v-5c0.4-2.4,1.2-4.5,2.5-6.5c1.3-1.9,2.8-3.6,4.6-5c1.8-1.4,3.8-2.5,6.1-3.2
		c2.2-0.8,4.5-1.1,6.9-1.1h4.8c2.3,0,4.6,0.4,6.9,1.1c2.2,0.8,4.3,1.8,6.1,3.2c1.8,1.4,3.3,3.1,4.6,5c1.3,1.9,2.1,4.1,2.5,6.5v6.5
		h-35.5c0.7,1.5,1.7,2.8,3,3.8C180.6,354.8,182,355.5,183.4,355.9z M183.4,336c-1.6,0.4-3,1.2-4.4,2.2c-1.4,1.1-2.3,2.4-2.9,4.1
		h26.5c-0.6-1.6-1.6-3-3-4.1c-1.4-1.1-2.8-1.8-4.3-2.2c-1-0.3-2-0.5-3-0.6s-2-0.1-3-0.1c-1,0-2,0-3,0.1S184.4,335.6,183.4,336z
		 M217.1,348.4v-5c0.4-2.4,1.2-4.5,2.5-6.5c1.3-1.9,2.8-3.6,4.6-5s3.8-2.5,6.1-3.2c2.2-0.8,4.5-1.1,6.9-1.1h24.6v6.8
		c-0.4,2.6-1.3,4.8-2.7,6.8c-1.4,1.9-3.2,3.6-5.3,4.8c2.1,1.2,3.9,2.8,5.3,4.8c1.4,1.9,2.3,4.2,2.7,6.8v6.8h-24.6
		c-2.3,0-4.6-0.4-6.9-1.1c-2.2-0.8-4.3-1.8-6.1-3.2s-3.3-3.1-4.6-5C218.3,352.9,217.4,350.8,217.1,348.4z M233.5,355.9
		c1,0.3,2,0.5,3,0.6c1,0.1,2,0.1,3,0.1h13.4c-0.4-1.5-1-2.7-2-3.6c-1-1-2.1-1.7-3.4-2.2c-1.3-0.5-2.7-0.9-4.1-1.1
		c-1.4-0.2-2.8-0.3-4.1-0.3h-13.1c0.3,0.8,0.7,1.6,1.2,2.3c0.5,0.7,1.1,1.3,1.8,1.9c0.7,0.6,1.4,1.1,2.2,1.5
		C232,355.3,232.7,355.6,233.5,355.9z M233.5,336c-0.8,0.2-1.6,0.6-2.3,1s-1.5,0.9-2.2,1.5c-0.7,0.6-1.2,1.2-1.8,1.9
		c-0.5,0.7-0.9,1.5-1.2,2.3h13.1c1.3,0,2.6-0.1,4.1-0.3c1.4-0.2,2.8-0.6,4.1-1.1c1.3-0.5,2.4-1.3,3.4-2.2c1-1,1.6-2.2,2-3.6h-13.4
		c-1,0-2,0-3,0.1S234.5,335.6,233.5,336z M283.3,355.9c1,0.3,2,0.5,3,0.6c1,0.1,2,0.1,3,0.1c1,0,2,0,3-0.1c1,0,2-0.2,3-0.5
		c1-0.3,1.9-0.7,2.9-1.3c0.9-0.6,1.8-1.3,2.5-2.1c0.7-0.8,1.3-1.7,1.8-2.7c0.4-1,0.7-2.1,0.7-3.2v-1.6c0-1.2-0.2-2.2-0.7-3.2
		c-0.4-1-1-1.9-1.8-2.7s-1.6-1.4-2.5-2c-0.9-0.6-1.9-1-2.9-1.3c-1-0.3-2-0.5-3-0.6c-1-0.1-2-0.1-3-0.1c-1,0-2,0-3,0.1
		c-1,0.1-2,0.3-3,0.6c-1,0.3-1.9,0.7-2.9,1.3c-0.9,0.6-1.8,1.2-2.5,2s-1.3,1.7-1.8,2.7c-0.4,1-0.7,2.1-0.7,3.2v1.5
		c0,1.1,0.2,2.2,0.7,3.2c0.4,1,1,1.9,1.8,2.7c0.7,0.8,1.6,1.5,2.5,2.1C281.4,355.2,282.4,355.6,283.3,355.9z M311.7,348.4
		c-0.4,2.4-1.2,4.5-2.5,6.5c-1.3,1.9-2.8,3.6-4.6,5s-3.8,2.5-6.1,3.2c-2.2,0.8-4.5,1.1-6.9,1.1h-4.8c-2.3,0-4.6-0.4-6.9-1.1
		c-2.2-0.8-4.3-1.8-6.1-3.2c-1.8-1.4-3.3-3.1-4.6-5s-2.1-4.1-2.5-6.5v-5c0.4-2.4,1.2-4.5,2.5-6.5c1.3-1.9,2.8-3.6,4.6-5
		c1.8-1.4,3.8-2.5,6.1-3.2c2.2-0.8,4.5-1.1,6.9-1.1h4.8c2.3,0,4.6,0.4,6.9,1.1c2.2,0.8,4.3,1.8,6.1,3.2s3.3,3.1,4.6,5
		c1.3,1.9,2.1,4.1,2.5,6.5V348.4z M325.1,334.3c0.4,1.5,1,2.8,1.9,3.9c0.9,1.1,2,1.9,3.2,2.5c1.2,0.6,2.5,1.1,3.9,1.4
		c1.4,0.3,2.8,0.4,4.1,0.4h0.8c1.3,0,2.7-0.1,4.1-0.4c1.4-0.3,2.7-0.7,3.9-1.4c1.2-0.6,2.3-1.5,3.2-2.5s1.5-2.3,1.9-3.9v-6.7h8.9
		v6.7c-0.5,2.6-1.4,4.8-2.8,6.8c-1.4,1.9-3.1,3.6-5.2,4.9c2,1.3,3.7,2.9,5.2,4.9c1.4,1.9,2.4,4.2,2.8,6.7v6.7h-8.9v-6.7
		c-0.4-1.5-1-2.8-1.9-3.8c-0.9-1-2-1.9-3.2-2.5c-1.2-0.6-2.5-1.1-3.9-1.4c-1.4-0.3-2.8-0.5-4.1-0.5h-0.4c-1.4,0-2.8,0.1-4.2,0.4
		s-2.8,0.7-4.1,1.3c-1.3,0.6-2.4,1.5-3.3,2.5c-0.9,1.1-1.6,2.4-1.9,3.9v6.7h-8.9v-6.7c0.5-2.6,1.4-4.8,2.8-6.7
		c1.4-1.9,3.1-3.5,5.2-4.9c-2-1.3-3.7-3-5.2-4.9c-1.4-1.9-2.4-4.2-2.8-6.8v-6.7h8.9V334.3z"/>
</g>
</svg>

                                
                ]]>

                </x:String>

            </ResourceDictionary>
        </Grid.Resources>

        <partials:EmbossedFrame
            Padding="12,16,12,8"
            VerticalOptions="FillAndExpand">
            <partials:EmbossedFrame.Content>

                <Grid
                    RowDefinitions="65,*"
                    RowSpacing="0">

                    <!--  NEW NAVBAR  -->

                    <draw:Canvas
                        Gestures="Enabled"
                        
                        HeightRequest="65"
                        HorizontalOptions="Fill"
                        VerticalOptions="Start">

                        <partials:SkiaNavBar
                            x:Name="NavBar"
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill">

                            <draw:SkiaSvg
                                Margin="16,0,16,0"
                                HeightRequest="16"
                                HorizontalOptions="Start"
                                SvgString="{StaticResource SvgGoBack}"
                                TintColor="#CB6336"
                                VerticalOptions="Center"
                                WidthRequest="16" />

                            <draw:SkiaHotspot
                                CommandTapped="{Binding NavbarModel.CommandGoBack, Mode=OneTime}"
                                HorizontalOptions="Start"
                                TransformView="{x:Reference NavBar}"
                                WidthRequest="44" />

                            <draw:SkiaLabel
                                Margin="48,0,16,0"
                                FontSize="14"
                                HorizontalOptions="Start"
                                LineBreakMode="TailTruncation"
                                MaxLines="1"
                                Style="{StaticResource SkiaLabelDefaultStyle}"
                                Text="{x:Static strings:ResStrings.VendorTitle}"
                                TextColor="#E8E3D7"
                                TranslationY="1"
                                VerticalOptions="Center" />

                            <!--  LINE HORIZONTAL  -->
                            <draw:SkiaShape
                                Margin="-16,0"
                                BackgroundColor="Black"
                                CornerRadius="0"
                                HeightRequest="1"
                                HorizontalOptions="Fill"
                                StrokeWidth="0"
                                VerticalOptions="End">
                                <draw:SkiaShape.FillGradient>

                                    <draw:SkiaGradient
                                        EndXRatio="1"
                                        EndYRatio="0"
                                        StartXRatio="0"
                                        StartYRatio="0"
                                        Type="Linear">
                                        <draw:SkiaGradient.Colors>
                                            <Color>#00E8E3D7</Color>
                                            <Color>#99E8E3D7</Color>
                                            <Color>#00E8E3D7</Color>
                                        </draw:SkiaGradient.Colors>
                                    </draw:SkiaGradient>

                                </draw:SkiaShape.FillGradient>
                            </draw:SkiaShape>

                        </partials:SkiaNavBar>
                    </draw:Canvas>

                    <ScrollView
                        x:Name="MainScroll"
                        Grid.Row="1"
                        VerticalOptions="FillAndExpand">

                        <VerticalStackLayout>

                            <Editor
                                x:Name="DebugEditor"
                                Margin="16"
                                IsReadOnly="True"
                                MinimumHeightRequest="300"
                                VerticalOptions="FillAndExpand" />

                        </VerticalStackLayout>

                    </ScrollView>



                </Grid>

            </partials:EmbossedFrame.Content>
        </partials:EmbossedFrame>


    </Grid>

</views:BasePage>