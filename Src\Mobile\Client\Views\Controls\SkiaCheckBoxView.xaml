<?xml version="1.0" encoding="utf-8" ?>
<draw:Canvas
    x:Class="Racebox.Views.Partials.SkiaCheckBoxView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:touch="clr-namespace:AppoMobi.Maui.Gestures;assembly=AppoMobi.Maui.Gestures"
    xmlns:racebox="clr-namespace:Racebox"
    x:Name="ThisControl"
    touch:TouchEffect.CommandTapped="{Binding Source={x:Reference ThisControl}, Path=CommandTapped, Mode=OneTime}"
    touch:TouchEffect.HandlerDown="{x:Static racebox:MauiProgram.TouchAnimateRipple}"
    touch:TouchEffect.HandlerTapped="{Binding Source={x:Reference ThisControl}, Path=OnTapped, Mode=OneTime}"
    touch:TouchEffect.PassView="{x:Reference Box}"
    HeightRequest="22"
    HorizontalOptions="Start"
    VerticalOptions="Start"
    WidthRequest="22">

    <draw:SkiaLayout
        UseCache="Operations"
        HorizontalOptions="Fill"
        VerticalOptions="Fill">


        <draw:SkiaSvg
            x:Name="Box"
            SvgString="{StaticResource SvgCheckBoxEmpty}"
            TintColor="#CB6336" />


        <draw:SkiaSvg
            HeightRequest="16"
            HorizontalOptions="Center"
            IsVisible="{Binding Source={x:Reference ThisControl, ThisControl}, Path=IsToggled}"
            SvgString="{StaticResource SvgCheck}"
            TintColor="#CB6336"
            TranslationX="0.2"
            TranslationY="-1"
            VerticalOptions="Center"
            WidthRequest="16" />


    </draw:SkiaLayout>

</draw:Canvas>
