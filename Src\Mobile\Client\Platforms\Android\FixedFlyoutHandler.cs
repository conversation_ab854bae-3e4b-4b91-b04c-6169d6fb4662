﻿using Android.Content;
using Android.Runtime;
using Android.Util;
using AndroidX.DrawerLayout.Widget;
using JetBrains.Annotations;
using Microsoft.Maui.Handlers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using View = Android.Views.View;


namespace Racebox.Platforms.Android
{
    public class FixedFlyoutHandler : FlyoutViewHandler
    {
        protected override void ConnectHandler(View platformView)
        {
            base.ConnectHandler(platformView);

            var drawer = (DrawerLayout)PlatformView;

            var check = VirtualView.FlyoutBehavior;

            drawer.SetDrawerLockMode(DrawerLayout.LockModeLockedClosed);
        }

    }
}
