﻿<?xml version="1.0" encoding="utf-8" ?>
<Grid
    x:Class="Racebox.Views.TabHistory"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:partials="clr-namespace:Racebox.Views.Partials"
    xmlns:racebox="clr-namespace:Racebox"
    xmlns:viewModels1="clr-namespace:Racebox.ViewModels"
    xmlns:drawn="clr-namespace:Racebox.Views.Drawn"
    x:Name="ThisTabHistory"
    x:DataType="viewModels1:HistoryViewModel"
    HorizontalOptions="Fill"
    VerticalOptions="FillAndExpand">

    <!--  page background with gradient and frame  -->
    <draw:Canvas
        HorizontalOptions="Fill"
        Tag="BackgroundWithFrame"
        VerticalOptions="Fill">

        <partials:GradientToBorder
            HorizontalOptions="Fill"
            MissingHeight="{Binding NavbarModel.BottomTabsHeightRequest}"
            VerticalOptions="Fill">

            <drawn:EmbossedFrameDrawn
                x:Name="DesignFrame"
                BackgroundColor="Transparent"
                HorizontalOptions="Fill"
                VerticalOptions="Fill" />

        </partials:GradientToBorder>
    </draw:Canvas>


    <draw:Canvas
        Gestures="Enabled"
        RenderingMode="Accelerated"
        HorizontalOptions="Fill"
        VerticalOptions="FillAndExpand">

        <draw:SkiaLayout
            HorizontalOptions="Fill"
            VerticalOptions="Fill">


            <draw:SkiaLayout
                Margin="12,16,12,8"
                HorizontalOptions="Fill"
                Spacing="0"
                Type="Column"
                VerticalOptions="Fill">

                <!--  NAVBAR  -->
                <partials:SkiaNavBar
                    x:Name="NavBar"
                    Margin="0,0,1,0"
                    HeightRequest="65"
                    HorizontalOptions="Fill">

                    <draw:SkiaLabel
                        Margin="16,0,16,0"
                        FontSize="14"
                        LineBreakMode="TailTruncation"
                        MaxLines="1"
                        Style="{StaticResource SkiaLabelDefaultStyle}"
                        Tag="NavTitle"
                        Text="{Binding Title}"
                        TextColor="#E8E3D7"
                        TranslationY="1"
                        VerticalOptions="Center" />

                    <draw:SkiaSvg
                        Margin="0,0,56,0"
                        HeightRequest="18"
                        HorizontalOptions="End"
                        SvgString="{StaticResource SvgFilter}"
                        TintColor="#CB6336"
                        VerticalOptions="Center"
                        WidthRequest="18" />

                    <draw:SkiaSvg
                        Margin="0,0,16,0"
                        HeightRequest="22"
                        HorizontalOptions="End"
                        SvgString="{StaticResource SvgOrder}"
                        TintColor="#CB6336"
                        VerticalOptions="Center"
                        WidthRequest="22" />

                    <!--  CommandFilter  -->
                    <draw:SkiaHotspot
                        Margin="0,0,44,0"
                        CommandTapped="{Binding CommandFilter, Mode=OneTime}"
                        HorizontalOptions="End"
                        Tag="CommandFilter"
                        TransformView="{x:Reference NavBar}"
                        WidthRequest="44" />

                    <!--  CommandOrder  -->
                    <draw:SkiaHotspot
                        Margin="0,0,0,0"
                        CommandTapped="{Binding CommandOrder, Mode=OneTime}"
                        HorizontalOptions="End"
                        Tag="CommandOrder"
                        TransformView="{x:Reference NavBar}"
                        WidthRequest="44" />

                    <!--  LINE HORIZONTAL  -->
                    <draw:SkiaShape
                        Margin="-16,0"
                        BackgroundColor="Black"
                        CornerRadius="0"
                        HeightRequest="1"
                        HorizontalOptions="Fill"
                        StrokeWidth="0"
                        VerticalOptions="End">
                        <draw:SkiaShape.FillGradient>

                            <draw:SkiaGradient
                                EndXRatio="1"
                                EndYRatio="0"
                                StartXRatio="0"
                                StartYRatio="0"
                                Type="Linear">
                                <draw:SkiaGradient.Colors>
                                    <Color>#00E8E3D7</Color>
                                    <Color>#99E8E3D7</Color>
                                    <Color>#00E8E3D7</Color>
                                </draw:SkiaGradient.Colors>
                            </draw:SkiaGradient>

                        </draw:SkiaShape.FillGradient>
                    </draw:SkiaShape>

                </partials:SkiaNavBar>

                <!--  CONTENT SCROLL  -->
                <draw:SkiaScroll
                    x:Name="MainScroll"
                    HorizontalOptions="Fill"
                    LoadMoreCommand="{Binding CommandLoadMore}"
                    LockChildrenGestures="PassTap"
                    RefreshDistanceLimit="4"
                    VerticalOptions="Fill">

                    <draw:SkiaLayout
                        HorizontalOptions="Fill"
                        IsClippedToBounds="True"
                        ItemsSource="{Binding Loader.Items}"
                        RecyclingTemplate="Enabled"
                        Spacing="0"
                        Tag="StackItems"
                        Type="Column">

                        <draw:SkiaLayout.ItemTemplate>

                            <DataTemplate x:DataType="viewModels1:HistoryCellData">

                                <partials:DrawnHistoryCell
                                    Margin="4,4,4,0"
                                    Padding="6,0"
                                    CommandTapped="{Binding Source={x:Reference ThisTabHistory}, Path=BindingContext.CommandItemTapped}"
                                    HeightRequest="116"
                                    HorizontalOptions="Fill"
                                    UseCache="ImageDoubleBuffered">

                                    <!--  Text="{Binding Id, StringFormat='#{0}'}"  -->
                                    <draw:SkiaLabel
                                        Margin="8,8,30,8"
                                        FontFamily="FontText"
                                        FontSize="20"
                                        HorizontalOptions="End"
                                        Tag="LabelId"
                                        TextColor="#CB6336"
                                        TranslationY="1"
                                        VerticalOptions="Start" />

                                    <draw:SkiaSvg
                                        Margin="8"
                                        HeightRequest="21"
                                        HorizontalOptions="End"
                                        Tag="IconValid"
                                        TranslationY="3"
                                        VerticalOptions="Start"
                                        WidthRequest="21" />

                                    <!--  Text="{Binding DisplayInfo}"  -->
                                    <draw:SkiaLabel
                                        Margin="8,38,8,13"
                                        BackgroundColor="Transparent"
                                        FontFamily="FontText"
                                        FontSize="14.5"
                                        HorizontalOptions="End"
                                        HorizontalTextAlignment="End"
                                        LineSpacing="0.1"
                                        Tag="LabelInfo"
                                        TextColor="#78E8E3D7"
                                        VerticalOptions="Start" />

                                    <!--  TextMeasured="OnResultsMeasured"  -->
                                    <!--  Text="{Binding DisplayResults}"  -->
                                    <draw:SkiaLabel
                                        Margin="8,0,8,0"
                                        FontFamily="FontText"
                                        FontSize="14.0"
                                        HorizontalOptions="Start"
                                        LineSpacing="1.25"
                                        Tag="LabelResults"
                                        TextColor="#E8E3D7"
                                        TranslationY="-2"
                                        VerticalOptions="Center" />

                                    <!--  SEPARATOR LINE  -->
                                    <draw:SkiaShape
                                        BackgroundColor="Black"
                                        CornerRadius="0"
                                        HeightRequest="1"
                                        HorizontalOptions="Fill"
                                        IsClippedToBounds="True"
                                        StrokeWidth="0"
                                        VerticalOptions="End">
                                        <draw:SkiaShape.FillGradient>

                                            <draw:SkiaGradient
                                                EndXRatio="1"
                                                EndYRatio="0"
                                                StartXRatio="0"
                                                StartYRatio="0"
                                                Type="Linear">
                                                <draw:SkiaGradient.Colors>
                                                    <Color>#00E8E3D7</Color>
                                                    <Color>#99E8E3D7</Color>
                                                    <Color>#00E8E3D7</Color>
                                                </draw:SkiaGradient.Colors>
                                            </draw:SkiaGradient>

                                        </draw:SkiaShape.FillGradient>
                                    </draw:SkiaShape>

                                </partials:DrawnHistoryCell>

                            </DataTemplate>
                        </draw:SkiaLayout.ItemTemplate>

                    </draw:SkiaLayout>


                </draw:SkiaScroll>

            </draw:SkiaLayout>

            <draw:SkiaLabelFps
                Margin="32"
                ForceRefresh="False"
                HorizontalOptions="End"
                IsClippedToBounds="True"
                IsVisible="{x:Static racebox:MauiProgram.ShowDebugInfo}"
                Rotation="-45"
                VerticalOptions="End"
                ZIndex="100" />



        </draw:SkiaLayout>
    </draw:Canvas>

    <ActivityIndicator
        HorizontalOptions="Center"
        IsRunning="{Binding IsBusy}"
        IsVisible="{Binding IsBusy}"
        VerticalOptions="Center" />

    <!--<Label
        HorizontalOptions="Center"
        Text="{Binding Items.Count}"
        TextColor="LimeGreen"
        VerticalOptions="Center" />-->

</Grid>
