<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaSlider
    x:Class="Racebox.Views.Drawn.SkiaSliderMetal"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:Racebox.Views.Controls"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    x:Name="ThisSlider"
    HorizontalOptions="Fill"
    RangeMin="1"
    RowDefinitions="40, Auto,32"
    SliderHeight="50"
    RowSpacing="0"
    Type="Grid"
    UseCache="Image">

    <draw:SkiaSlider.Resources>
        <ResourceDictionary>



        </ResourceDictionary>
    </draw:SkiaSlider.Resources>

    <!--  VALUE LABELS  -->
    <draw:SkiaLayout
        HeightRequest="40"
        HorizontalOptions="Fill">

        <!--  START LABEL - RANGED ONLY  -->
        <draw:SliderValueDesc
            x:Name="StartDesc"
            IsVisible="False"
            UseCache="Image"
            VerticalOptions="Fill"
            XCenter="{Binding Source={x:Reference StartThumb}, Path=XCenter}"
            XMaxLimit="{Binding Source={x:Reference EndDesc}, Path=TranslationX}"
            XMinLimit="0">

            <draw:SkiaControl.Triggers>
                <DataTrigger
                    Binding="{Binding Source={x:Reference ThisSlider}, Path=EnableRange}"
                    TargetType="draw:SkiaControl"
                    Value="True">
                    <Setter Property="IsVisible" Value="True" />
                </DataTrigger>
            </draw:SkiaControl.Triggers>

            <!--  cached background buble  -->
            <draw:SkiaLayout
                HorizontalOptions="Fill"
                UseCache="Image"
                VerticalOptions="Fill">

                <draw:SkiaShape
                    Margin="1"
                    BackgroundColor="{StaticResource ColorAccent}"
                    CornerRadius="4"
                    HeightRequest="28"
                    HorizontalOptions="Fill" />

                <draw:SkiaShape
                    BackgroundColor="{StaticResource ColorAccent}"
                    HeightRequest="24"
                    HorizontalOptions="Center"
                    PathData="M 5,5 97.5,50 5,95 Z"
                    Rotation="90"
                    Type="Path"
                    UseCache="Operations"
                    VerticalOptions="End"
                    WidthRequest="16" />

            </draw:SkiaLayout>

            <!--  label overlay  -->
            <draw:SkiaLabel
                Margin="5.5,6,6,0"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                MinimumWidthRequest="38"
                Text="{Binding Source={x:Reference ThisSlider}, Path=StartDesc}"
                TextColor="{StaticResource Gray100}" />

        </draw:SliderValueDesc>

        <!--
            END LABEL              IsVisible="{Binding Source={x:Reference ThisSlider}, Path=EndDesc, Converter={StaticResource StringNotEmptyConverter}}"
        -->
        <draw:SliderValueDesc
            x:Name="EndDesc"
            Spacing="0"
            Tag="LabelEnd"
            UseCache="Image"
            VerticalOptions="Fill"
            XCenter="{Binding Source={x:Reference EndThumb}, Path=XCenter}"
            XMaxLimit="{Binding Source={x:Reference ThisSlider}, Path=Width}"
            XMinLimit="{Binding Source={x:Reference StartDesc}, Path=RightX}">

            <!--  cached background buble  -->
            <draw:SkiaLayout
                HorizontalOptions="Fill"
                Tag="Bubble"
                UseCache="Image"
                VerticalOptions="Fill">

                <draw:SkiaShape
                    Margin="1"
                    BackgroundColor="{StaticResource ColorAccent}"
                    CornerRadius="4"
                    HeightRequest="28"
                    HorizontalOptions="Fill" />

                <draw:SkiaShape
                    BackgroundColor="{StaticResource ColorAccent}"
                    HeightRequest="24"
                    HorizontalOptions="Center"
                    PathData="M 5,5 97.5,50 5,95 Z"
                    Rotation="90"
                    Type="Path"
                    UseCache="Operations"
                    VerticalOptions="End"
                    WidthRequest="16" />

            </draw:SkiaLayout>

            <!--  label overlay  -->
            <draw:SkiaLabel
                Margin="5.5,6,6,0"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                MinimumWidthRequest="38"
                Tag="LabelEndText"
                Text="{Binding Source={x:Reference ThisSlider}, Path=EndDesc}"
                TextColor="{StaticResource Gray100}" />

        </draw:SliderValueDesc>

    </draw:SkiaLayout>

    <!--  MAIN GRID  -->
    <draw:SkiaLayout
        x:Name="ThisSliderGrid"
        Grid.Row="1"
        Margin="0,0,0,0"
        HeightRequest="{Binding Source={x:Reference ThisSlider}, Path=SliderHeight}"
        HorizontalOptions="Fill"
        Tag="Trail"
        VerticalOptions="Start">

        <!--  TRAIL  -->
        <draw:SkiaShape
            BackgroundColor="Gray"
            CornerRadius="4"
            HeightRequest="6"
            HorizontalOptions="Fill"
            UseCache="Image"
            VerticalOptions="Center">
            <draw:SkiaControl.FillGradient>

                <draw:SkiaGradient
                    EndXRatio="1"
                    EndYRatio="1"
                    StartXRatio="1"
                    StartYRatio="0"
                    Type="Linear">
                    <draw:SkiaGradient.Colors>
                        <Color>#A0A0A0</Color>
                        <Color>#E6E6E6</Color>
                    </draw:SkiaGradient.Colors>
                </draw:SkiaGradient>

            </draw:SkiaControl.FillGradient>
        </draw:SkiaShape>

        <!--  SELECTED TRAIL  -->
        <draw:SliderTrail
            BackgroundColor="{StaticResource ColorAccent}"
            CornerRadius="5"
            HeightRequest="8"
            HorizontalOptions="Start"
            SideOffset="0"
            ModifyXPosEnd="10"
            UseCache="Operations"
      
            VerticalOptions="Center"
            XPos="{Binding Source={x:Reference StartThumb}, Path=TranslationX}"
            XPosEnd="{Binding Source={x:Reference EndThumb}, Path=TranslationX}">
            
            <draw:SkiaControl.FillGradient>

                <draw:SkiaGradient
                    EndXRatio="1"
                    EndYRatio="0"
                    StartXRatio="1"
                    StartYRatio="1"
                    Type="Linear">
                    <draw:SkiaGradient.Colors>
                        <Color>#f0CB6336</Color>
                        <Color>#CB6336</Color>
                    </draw:SkiaGradient.Colors>
                </draw:SkiaGradient>

            </draw:SkiaControl.FillGradient>
            
            <draw:SkiaControl.Triggers>
                <DataTrigger
                    Binding="{Binding Source={x:Reference ThisSlider}, Path=EnableRange}"
                    TargetType="draw:SliderTrail"
                    Value="True">
                    <Setter Property="ModifyXPosStart" Value="10" />
                </DataTrigger>
            </draw:SkiaControl.Triggers>

        </draw:SliderTrail>

        <!--  START THUMB - RANGED ONLY  -->
        <draw:SliderThumb
            x:Name="StartThumb"
            HeightRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}"
            IsVisible="{Binding Source={x:Reference ThisSlider}, Path=EnableRange}"
            TranslationX="{Binding Source={x:Reference ThisSlider}, Path=StartThumbX}"
            UseCache="Image"
            VerticalOptions="Center"
            WidthRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}">

            <draw:SkiaShape
                Margin="8"
                HorizontalOptions="Fill"
                LockRatio="1"
                StrokeColor="White"
                StrokeWidth="4"
                Type="Circle"
                VerticalOptions="Fill">

                <draw:SkiaShape.StrokeGradient>

                    <draw:SkiaGradient
                        EndXRatio="1"
                        EndYRatio="1"
                        StartXRatio="0"
                        StartYRatio="0"
                        Type="Linear">
                        <draw:SkiaGradient.Colors>
                            <Color>#FFF9EA</Color>
                            <Color>#7E7A6E</Color>
                        </draw:SkiaGradient.Colors>
                    </draw:SkiaGradient>

                </draw:SkiaShape.StrokeGradient>

                <draw:SkiaShape.FillGradient>

                    <draw:SkiaGradient
                        EndXRatio="1"
                        EndYRatio="1"
                        StartXRatio="0"
                        StartYRatio="0"
                        Type="Linear">
                        <draw:SkiaGradient.Colors>
                            <Color>#7E7A6E</Color>
                            <Color>#FFF9EA</Color>
                        </draw:SkiaGradient.Colors>
                    </draw:SkiaGradient>

                </draw:SkiaShape.FillGradient>

                <draw:SkiaShape.Shadows>

                    <draw:SkiaShadow
                        Blur="5"
                        Opacity="0.64"
                        X="3"
                        Y="3"
                        Color="Black" />

                </draw:SkiaShape.Shadows>

            </draw:SkiaShape>


        </draw:SliderThumb>

        <!--  END THUMB  -->
        <draw:SliderThumb
            x:Name="EndThumb"
            HeightRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}"
            TranslationX="{Binding Source={x:Reference ThisSlider}, Path=EndThumbX}"
            UseCache="Image"
            WidthRequest="{Binding Source={x:Reference ThisSliderGrid}, Path=Height}">

            <draw:SkiaShape
                Margin="8"
                HorizontalOptions="Fill"
                LockRatio="1"
                StrokeColor="White"
                StrokeWidth="4"
                Type="Circle"
                VerticalOptions="Fill">

                <draw:SkiaShape.StrokeGradient>

                    <draw:SkiaGradient
                        EndXRatio="1"
                        EndYRatio="1"
                        StartXRatio="0"
                        StartYRatio="0"
                        Type="Linear">
                        <draw:SkiaGradient.Colors>
                            <Color>#FFF9EA</Color>
                            <Color>#7E7A6E</Color>
                        </draw:SkiaGradient.Colors>
                    </draw:SkiaGradient>

                </draw:SkiaShape.StrokeGradient>

                <draw:SkiaShape.FillGradient>

                    <draw:SkiaGradient
                        EndXRatio="1"
                        EndYRatio="1"
                        StartXRatio="0"
                        StartYRatio="0"
                        Type="Linear">
                        <draw:SkiaGradient.Colors>
                            <Color>#7E7A6E</Color>
                            <Color>#FFF9EA</Color>
                        </draw:SkiaGradient.Colors>
                    </draw:SkiaGradient>

                </draw:SkiaShape.FillGradient>

                <draw:SkiaShape.Shadows>

                    <draw:SkiaShadow
                        Blur="5"
                        Opacity="0.64"
                        X="3"
                        Y="3"
                        Color="Black" />

                </draw:SkiaShape.Shadows>

            </draw:SkiaShape>

        </draw:SliderThumb>

    </draw:SkiaLayout>

    <!--  Ticks  -->
    <controls:ScaleTicks
        Grid.Row="2"
        HeightRequest="24"
        HorizontalOptions="Fill"
        Max="4"
        Min="0"
        Points="4"
        ShowHalves="False"
        SideOffset="18"
        UseCache="Operations"
        VerticalOptions="End" />

</draw:SkiaSlider>
