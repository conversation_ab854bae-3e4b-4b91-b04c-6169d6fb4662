﻿using System.Windows.Input;

// LoginSmsViewModel

namespace Racebox.ViewModels.Navigation
{

	public class TabsViewModel : ProjectViewModel
	{

		private bool _ShowReconnecting;
		public bool ShowReconnecting
		{
			get { return _ShowReconnecting; }
			set
			{
				if (_ShowReconnecting != value)
				{
					_ShowReconnecting = value;
					OnPropertyChanged();
				}
			}
		}


		public TabsViewModel()
		{
			//start with tab number..
			//var maybeTab = LastTab;
			//if (maybeTab != -1)
			//{
			//    SelectedIndex = maybeTab;
			//}
			//else
			//{
			//    switch (App.InRole)
			//    {
			//        case "coach":
			//        case "pal":
			//            SelectedIndex = 2;
			//            break;
			//        case "user":
			//        //SelectedIndex = 4;
			//        //break;
			//        default:
			//            SelectedIndex = 0;
			//            break;
			//    }
			//}



			App.Instance.Messager.Subscribe<string>(this, "Signals", async (sender, arg) =>
			{

				if (arg == "Online")
				{
					ShowReconnecting = false;
				}
				else
				if (arg == "Offline")
				{
					ShowReconnecting = true;
				}
			});

			//ShowReconnecting = !NavbarModel.IsOnline;

			App.Instance.Messager.Subscribe<string>(this, "SelectRootTabExec", async (sender, arg) =>
			{
				// WishTabBinding = arg;
			});

			App.Instance.Messager.Subscribe<string>(this, "Release", async (sender, arg) =>
			{
				App.Instance.Messager.Unsubscribe(this, "Signals");
				App.Instance.Messager.Unsubscribe(this, "SelectRootTabExec");
			});


		}



		private int _SelectedIndex;
		public int SelectedIndex
		{
			get { return _SelectedIndex; }
			set
			{
				if (_SelectedIndex != value)
				{
					_SelectedIndex = value;
					OnPropertyChanged();
					App.Instance.Messager.All("RootTabIndex", SelectedIndex.ToString());

					LastTab = value;
				}
			}
		}

		private int _LastTab;
		public int LastTab
		{
			get { return _LastTab; }
			set
			{
				if (_LastTab != value)
				{
					_LastTab = value;
					OnPropertyChanged();
				}
			}
		}



		public ICommand CommandTabReselected
		{
			get
			{
				return new Command<object>(async (object parameter) =>
				{
					if (IsBusy)
						return;

					await Dispatcher.DispatchAsync(async () =>
					{
						await NavbarModel.Shell.PopTabToRoot();
					});

				});

			}
		}

		public ICommand CommandTabSelected
		{
			get
			{
				return new Command<object>(async (object parameter) =>
				{


				});
			}
		}




	}
}
