// Константы

// Таймаут завершения замера
#define ACC_TIMEOUT 120000UL
// Пороговая скорость старта замера
#define START_SPEED_THRESHOLD     2
// Пороговая скорость готовности к замеру
#define READY_SPEED_THRESHOLD     1
// Пороговое ускорение завершения замера
#define DECELERATION_THRESHOLD    -3
// Пороговое значение валидности замера по уклону дороги
#define INCLINE_THRESHOLD		  20
// Длина дистанции для замера уклона дороги
#define INCLINE_SAMPLE_DIST		  30
// Калибровочная поправка замера времени
#define RACELOGIC_CORR			  75UL
// Калибровочная поправка дистанции
#define DIST_CORR 				  -15L
// Калибровочная поправка дистанции с включенным параметром ролл-аут
#define DIST_CORR_ROLLOUT 		  60UL
// Калибровочная поправка замера времени между парой скоростей
#define RANGE_CORR				  60UL
// Коэффициенты фильтрации ускорения
#define ACCEL_FAST_ALPHA_16HZ 0.6
#define ACCEL_ALPHA_16HZ 0.9
// Переводные коэффициенты
#define KPH_TO_MPH 0.621
#define MPH_TO_KPH 1.609
// Кол-во пар пользовательских скоростей
#define MAX_RANGES 3
// Кол-во граничных скоростей по умолчанию
#define SPD_RESULT_LEN 5
// Кол-во граничных дистанций по умолчанию
#define DIST_RESULT_LEN 5
// Кол-во пользовательских граничных скоростей
#define MAX_EXTRA_SPEEDS 5
// Кол-во пользовательских граничных дистанций
#define MAX_EXTRA_DISTS  5
// Пороговые значения показателя горизонтальной точности HDOP (* 100), с гестерезисом
#define HDOP_UT                   400 // = 4.0
#define HDOP_LT                   250 // = 2.5
// Пороговые значения показателя вертикальной точности VDOP (* 100), с гестерезисом
#define VDOP_UT                   400 // = 4.0
#define VDOP_LT                   250 // = 2.5
// Пороговое значение кол-ва спутников
#define SATS_LT                   5
// размер кольцевых буферов для вычисления средних значений
#define BUFFER_SIZE 10

typedef struct Location {
	int32_t lat;
	int32_t lon;
} Location_t;

// Основная структура для хранения результатов замера
typedef struct {
	// время и дата замера
	uint8_t Hour; // 1
	uint8_t Minute; // 1
	uint8_t Second; // 1
	uint8_t Day; // 1
	uint8_t Month; // 1
	uint8_t Year; // 1 
	// максимальное ускорение
	uint8_t maxAccel; // 1
	// флаг параметра ролл-аут
	bool rollout; // 1
	// флаг валидности замера по уклону дороги
	bool inclineValid; // 1
	// флаг проверки уклона дороги
	bool inclineCheck; // 1
	// максимальный уклон дороги при замере
	int16_t maxIncline; // 2
	// максимальная скорость
	uint16_t maxSpeed; // 2
	// кол-во граничных скоростей в замере (скорости по умолчанию + пользовательские)
	uint8_t speed_num;
	// кол-во граничных дистанций в замере (дистанции по умолчанию + пользовательские)
	uint8_t dist_num;
	// массив граничных скоростей
	uint16_t speed[SPD_RESULT_LEN + MAX_EXTRA_SPEEDS];
	// массив граничных дистанций
	uint16_t dist[DIST_RESULT_LEN + MAX_EXTRA_DISTS];
	// массив результатов замера времени на граничных скоростях
	uint16_t speedMs[SPD_RESULT_LEN + MAX_EXTRA_SPEEDS]; // 2 * 5
	// массив результатов замера времени на граничных дистанциях
	uint16_t distMs[DIST_RESULT_LEN + MAX_EXTRA_DISTS]; // 10
	// массив результатов замера скорости на граничных дистанциях
	uint16_t distSpeed[DIST_RESULT_LEN + MAX_EXTRA_DISTS]; // 10
	// enum единиц измерения (0 - км/ч, 1 - миль/ч)
	uint8_t units;
	// массив начальных скоростей для пользовательских пар скоростей
	uint16_t speed1[MAX_RANGES]; // 6
	// массив конечных скоростей для пользовательских пар скоростей
	uint16_t speed2[MAX_RANGES]; // 6
	// массив результатов замера времени на пользовательских парах скоростей
	uint16_t speed12Ms[MAX_RANGES]; // 6
} DynHistObject;


// Граничные скорости по умолчанию (км/ч)
const uint16_t speedLimit[SPD_RESULT_LEN] = {60, 100, 150, 200, 300}; // Speed Limits km/h

// Граничные скорости по умолчанию (миль/ч)
const uint16_t speedLimit_I[SPD_RESULT_LEN] = {30, 60, 100, 150, 200}; // Speed Limits mph

// Граничные дистанции по умолчанию (метры)
const uint16_t distLimit[DIST_RESULT_LEN] = {18, 201, 402, 1000, 1609}; // Distance Limits m

// Прибор может замерять время разгона между несколькими (3) парами скоростямей, которые задаются в настройках прибора
// Пары хранятся в массивах rangeSpeed1 и rangeSpeed2
// В приложении эти значения должны задаваться в меню на экране Настройки
uint16_t rangeSpeed1[MAX_RANGES] = {0, };
uint16_t rangeSpeed2[MAX_RANGES] = {0, };

// Есть возможность задать дополнительные пользовательские скорости и дистанции через файл-настроек, эти метрики хранятся в массивах extra_speedLimit и extra_distLimit
// В приложении эти значения должны задаваться в меню на экране Настройки
uint16_t extra_speedLimit[MAX_EXTRA_SPEEDS] = {0, };
uint16_t extra_distLimit[MAX_EXTRA_DISTS] = {0, };

#define ACCEL_PATH "/Racebox/Accel"
#define ACCEL_BEST_PATH "/Racebox/Accel/Best"
#define ACCEL_RES_LOG_FILENAME "Racebox/Accel/Accel_Results%s.csv"

// Структуры с настройками прибора
// В приложении эти данные задаются на экране Настройки
extern Config_t config; // Device config
extern Profile_t profile;

// режим работы прибора (тут всегда ACCEL_STATE)
extern uint8_t state;
// подрежим работы прибора (тут всегда ACCEL_FREE_RUN)
extern uint8_t subState;


// Функция сортировки пользовательских скоростей
// Массивы дефолтных и пользовательских граничных скоростей сливаются в один массив и сортируются по возрастанию
void sort_speeds(DynHistObject* dynRecord) {
	if (config.units == EU) {
		memcpy(dynRecord->speed, speedLimit, sizeof speedLimit);
	} else {
		memcpy(dynRecord->speed, speedLimit_I, sizeof speedLimit_I);
	}
	memcpy(dynRecord->speed + SPD_RESULT_LEN, extra_speedLimit, sizeof extra_speedLimit);
	// Цикл сортировки массива
	for (uint8_t i = 0; i < SPD_RESULT_LEN + MAX_EXTRA_SPEEDS; i++) {
		uint16_t spd_min = dynRecord->speed[i];
		if (dynRecord->speed[i] == 0) {
			spd_min = UINT16_MAX;
		}
		for (uint8_t j = i; j < SPD_RESULT_LEN + MAX_EXTRA_SPEEDS; j++) {
			if (dynRecord->speed[j] < spd_min && dynRecord->speed[j] > 0 && j != i) {
				spd_min = dynRecord->speed[j];
				dynRecord->speed[j] = dynRecord->speed[i];
				dynRecord->speed[i] = spd_min;
			}
		}
	}
}

// Функция сортировки пользовательских дистанций
// Массивы дефолтных и пользовательских граничных дистанций сливаются в один массив и сортируются по возрастанию
void sort_dists(DynHistObject* dynRecord) {
	memcpy(dynRecord->dist, distLimit, sizeof distLimit);
	memcpy(dynRecord->dist + DIST_RESULT_LEN, extra_distLimit, sizeof extra_distLimit);
	
	// Цикл сортировки массива
	for (uint8_t i = 0; i < DIST_RESULT_LEN + MAX_EXTRA_DISTS; i++) {
		uint16_t dist_min = dynRecord->dist[i];
		if (dynRecord->dist[i] == 0) {
			dist_min = UINT16_MAX;
		}
		for (uint8_t j = i; j < DIST_RESULT_LEN + MAX_EXTRA_DISTS; j++) {
			if (dynRecord->dist[j] < dist_min && dynRecord->dist[j] > 0 && j != i) {
				dist_min = dynRecord->dist[j];
				dynRecord->dist[j] = dynRecord->dist[i];
				dynRecord->dist[i] = dist_min;
			}
		}
	}
}


// Функция инициализации структуры с результатами замера
void init_dynRecord(DynHistObject* dynRecord) {
	memset(dynRecord, 0, sizeof(*dynRecord));
	dynRecord->rollout = config.rollout;
	dynRecord->units = config.units;
	dynRecord->inclineCheck = true;
	sort_speeds(dynRecord);
	sort_dists(dynRecord);
}


// Основная функция замера разгона
void testDynamic() {
	
	// Основная структуры с результатами замера
	//DynHistObject dynRecord;
    init_dynRecord(&dynRecord);
	
	// Кольцевой буфер значений скоростей и системного времени
	double speedBuffer[BUFFER_SIZE] = {0.0, };
	uint32_t timeBuffer[BUFFER_SIZE] = {0, };
	
	// инициализация переменных для замера разгона между парами скоростей, которые задаются в настройках
	uint8_t configRangeIndex = 0;
	bool configRangeMeasure = false;
	bool finalizedRanges[MAX_RANGES] = {false, };

	if (rangeSpeed1[0] == 0) rangeSpeed1[0] = config.speed1;
	if (rangeSpeed2[0] == 0) rangeSpeed2[0] = config.speed2;

	uint8_t curRange = 0;
	uint16_t nextSpeed2 = 1000;
	uint8_t nextSpeed2index = 0;
	bool log_failed = false;

	// формирование массивов диапазонов скоростей
	setSortedRanges(&dynRecord, &configRangeIndex);

	// флаг качества определения метоположения GPS (в горизонтальной плоскости)
	static bool okHDOP = false;

	// флаг статуса уклона дороги (при превышении уклона дороги определённого значения, замер считается невалидным)
    bool inclineOK = true;
	
	// инициализация переменных для замера
	//время старта замера (системное время в мс)
	uint32_t startTime = 0UL;
	// текущая отметка времени (мс)
	uint32_t curMs = 0UL;
	// предыдущая отметка времени (мс)
	uint32_t prevMs = 0UL;
	// текущее значение уклона дороги
	int16_t curIncline = 0;
	// максимальное значение уклона дороги
	int16_t maxIncline = 0;
	// c
	int32_t avgAlt = 0;
	// предыдущее усреднённое значение высоты над уровнем моря
	int32_t lastAvgAlt = 0;
	// структура местоположения места старта (широта, долгота)
	Location_t startLoc;
	// структура текущего местоположения (широта, долгота)
	Location_t lastLoc;
    startLoc.lat = 0L;
    startLoc.lon = 0L;
	lastLoc.lon = 0L;
	lastLoc.lat = 0L;
	// текущая дистанция от места старта
	double distance = 0.0;
	// дистанция от последнего места замера высоты
	double distAlt = 0.0;
	// текущая скорость
	double curSpeed = 0.0;
	// предыдущая скорость
	double prevSpeed = 0.0;
	// усреднённая скорость
	double avgSpeed = 0.0;
	// параметры фильтров расчёта ускорения
	double alphaAccel = 0;
	double alphaFastAccel = 0;
	// флаг готовности к старту замера
	bool measureReady = false;
	// флаг замера пары скоростей сходу (когда замер начинается не с места, а во время движения, при достижении граничной скорости speed1 пары скоростей)
	bool instantTest = false;
	// флаг завершения замера (при достижении граничного значения замедления)
	bool measureFinished = false;
	// флаг качества определения метоположения GPS (в вертикальной плоскости, для замера уклона дороги) 
	bool vdopOk = false;
	// флаг валидности предыдущей скорости
	bool prevSpeedValid = false;
	// индекс текущей граничной скорости (индекс массива граничных скоростей)
	uint8_t speedIndex = 0;
	// индекс текущей граничной дистанции (индекс массива граничных дистанций)
	uint8_t distIndex = 0;
	// индекс кольцевого буфера скорости и времени
	uint8_t buffIndex = 0;
	// индекс текущего диапазона пар скоростей (rangeSpeed1 и rangeSpeed2)
	uint8_t rangeIndex = 0;
	// текущее ускорение в единицах G * 100 (сильная фильтрация)
	int16_t accelG = 0;
	// текущее ускорение в единицах G * 100 (слабая фильтрация)
	int16_t fastAccelG = 0;
	// текущее ускорение в единицах G * 100 (без фильтрации)
	int16_t rawAccelG = 0;
	// поправка времени старта с включенным параметром ролл-аут
	uint32_t rolloutTime = 0;
	// переводной коэффициент для расчёта ускорения (magic numbers:) (для метрической и дюмовых систем измерений)
	uint16_t ACCEL_COEFF = (config.units == EU) ? 2832 : 4558;
	// константа пороговой скорости начала замера (для км/ч и миль/ч)
	double START_SPEED_THRESHOLD_UNIT = (config.units == EU) ? START_SPEED_THRESHOLD : START_SPEED_THRESHOLD * KPH_TO_MPH;
	// константа пороговой скорости готовности к замеру (для км/ч и миль/ч)
	double READY_SPEED_THRESHOLD_UNIT = (config.units == EU) ? READY_SPEED_THRESHOLD : READY_SPEED_THRESHOLD * KPH_TO_MPH;
	// поправочный коэффициент для поправки времени :)
	double TIME_LAG_UNIT_COEFF = (config.units == EU) ? 1.0 : MPH_TO_KPH;
	// калибровочная поправка для замера дистанции
	int32_t distCorr = (config.rollout) ? DIST_CORR_ROLLOUT : DIST_CORR;
	
	// инициализация при смене режима прибора
	if (stateChanged) { 
		// не применимо для приложения
	}
	
	// обработка длительного нажатия кнопки
	if (longPress) { 
		// не применимо для приложения
	}
	
	// инициализация при смене подрежима прибора
	if (subStateChanged) {
		// не применимо для приложения
	}
	
	// структура параметров для записи в лог-файл
	LogFrame_t frame;
	memset(&frame, 0, sizeof frame);
	// дескриптор лог-файла
	FIL logFile;
// запись заголовка лог-файла
	if (config.sdEnabled && config.logType != LOG_OFF && (subState == ACCEL_FREE_RUN || subState == ACCEL_TREE_RUN)) {
		memset(&logFile, 0, sizeof logFile);
		debug_msg("Write header\n");
		if (!writeLog(&gps, &frame, &logFile, logFields, true)) log_failed = true;
	}	

	// инициализация параметров фильтров расчёта ускорения
	alphaAccel = ACCEL_ALPHA_16HZ;
	alphaFastAccel = ACCEL_FAST_ALPHA_16HZ;
	
	// основной цикл обработки параметров замера
	while (subState <= ACCEL_TREE_RUN && no_irq_button()) {	
		
		// проверка обновления данных GPS (срабатывает с частотой 16Гц, при получении данных от GPS-модуля)
		if (gps.satellites.isUpdated()) 
		{
			gps.satellites.value();
			
			// проверка качества приёма по HDOP и кол-ву спутников
			okHDOP = statusHDOP();
			if (okHDOP || startTime > 0) { // startTime > 0 - если качество приёма ухудшилось во время замера, замер не прерывается
				prevMs = curMs;
				// расчёт текущего времени (время приёма пакета GPS данных)
				curMs = HAL_GetTick() - gps.satellites.age();
				// сохранение предыдущей скорости
				prevSpeed = curSpeed;
				//получение текущей скорости (км/ч или миль/ч)
				if (config.units == EU) {
					curSpeed = (gps.speed.isValid()) ? gps.speed.kmph() : curSpeed; 
				} else {
					curSpeed = (gps.speed.isValid()) ? gps.speed.mph() : curSpeed;
				}
				
				// APP: обновление значения скорости на экранах
				
				// заполнение кольцевого буфера скоростей и времени
				speedBuffer[buffIndex] = gps.speed.kmph(); // запись текущей скорости в буфер
				timeBuffer[buffIndex] = curMs; // запись текущего времени в буфер
				buffIndex = (buffIndex == BUFFER_SIZE - 1) ? 0 : buffIndex + 1;
				
				// расчёт средней скорости в буфере
				avgSpeed = averageSpeed(speedBuffer);
				
				// расчёт текущего ускорения (в G * 100), с проверкой валидности текущей и предыдущей скоростей
				rawAccelG = (gps.speed.isValid() && prevSpeedValid) ? (int16_t)(((curSpeed - prevSpeed) / (curMs - prevMs)) * ACCEL_COEFF) : rawAccelG;
				prevSpeedValid = (gps.speed.isValid()) ? true : false;
				
				// расчёт фильтрованных значений ускорения (сильная и слабая фильтрация)
				accelG = (accelG == 0) ? 1 : (int16_t)(alphaAccel * accelG + (1 - alphaAccel) * rawAccelG); // постоянная времени = 0.3 сек, в G*100
				fastAccelG = (fastAccelG == 0) ? rawAccelG : (int16_t)(alphaFastAccel * fastAccelG + (1 - alphaFastAccel) * rawAccelG);
				
				// сохранение максимального значения ускорения
				dynRecord.maxAccel = (accelG > dynRecord.maxAccel && accelG <= 190) ? accelG : dynRecord.maxAccel; // 1
				
				// сохранение максимального значения скорости
				dynRecord.maxSpeed = ((uint16_t)(curSpeed * 10) > dynRecord.maxSpeed) ? (uint16_t)(curSpeed * 10) : dynRecord.maxSpeed;
				
				// расчёт качества вертикального местоположения
				if (config.incline) vdopOk = statusVDOP();
				
				// замер не начат
				if (startTime == 0) { 
					
					// включение проверки уклона дороги, в зависимости качества вертикального местоположения
					if (HAL_GetTick() > 15000) { // задержка после запуска прибора
						if (inclineOK && !vdopOk) {
							inclineOK = false;
							avgAlt = 0;
							distAlt = 0.0;
							dynRecord.inclineCheck = false;
						} else if (!inclineOK && vdopOk) {
							inclineOK = true;
							dynRecord.inclineCheck = true;
						}
					}
					
					// при готовности к замеру
					if (measureReady) {
						if (subState == ACCEL_FREE_RUN) { // в приложении реализуем только режим ACCEL_FREE_RUN
							
							// проверка остановки автомобиля
							if (curSpeed < START_SPEED_THRESHOLD_UNIT) {
								stringToLED(" -GO-", 3);
								// усреднение точки старта (ACCEL_ALPHA имеет подходящее значение, ускорение ни при чём:)
								startLoc.lat = (int32_t)(startLoc.lat * ACCEL_ALPHA_16HZ + gps.location.intLat() * (1 - ACCEL_ALPHA_16HZ));
								startLoc.lon = (int32_t)(startLoc.lon * ACCEL_ALPHA_16HZ + gps.location.intLon() * (1 - ACCEL_ALPHA_16HZ));
							
							// начало замера с места при превышении скорости START_SPEED_THRESHOLD_UNIT после остановки
							} else {
								// сброс флага готовности к замеру
								measureReady = false;
								// расчёт поправки времени старта
								int32_t timeLag = (int32_t)(ACCEL_COEFF * curSpeed / fastAccelG);
								// расчёт поправки при включенном параметре ролл-аут
								if (config.rollout) rolloutTime = (uint32_t)(2493 / sqrt(fastAccelG)); // учёт ролл-аута	
								// расчёт времени старта
								startTime = curMs + RACELOGIC_CORR - timeLag; // RACELOGIC_CORR - калибровочная поправка
								// сброс флага замера с ходу
								instantTest = false;
							}
						}
						
					// при неготовности к замеру
					} else {
						
						// проверка полной остановки автомобиля для установки статуса готовности к замеру
						if (avgSpeed <= READY_SPEED_THRESHOLD) { // условие готовности к замеру
							// сброс времени начала замера
							startTime = 0;
							// установка точки старта
                            startLoc.lat = gps.location.intLat();
                            startLoc.lon = gps.location.intLon();
							lastLoc = startLoc;
							// установка флага готовности к замеру
							measureReady = true;
							
							// APP: можно вывести на кнопку сообщение "ПОЕХАЛИ!"
						
						}
						
						// старт замера "сходу" между парами пользовательских скоростей
						if (
							(curSpeed >= dynRecord.speed1[curRange] 
								&& prevSpeed < dynRecord.speed1[curRange] 
								&& dynRecord.speed12Ms[curRange] == 0 
								&& prevSpeed > 0) 
							
							|| 
							
							(curSpeed >= dynRecord.speed1[configRangeIndex] 
								&& prevSpeed < dynRecord.speed1[configRangeIndex] 
								&& dynRecord.speed12Ms[configRangeIndex] == 0 
								&& prevSpeed > 0)) 
						
						{
							
							debug_msg("Accel test: RANGE#%d STARTED\n", configRangeIndex);
							
							// проверка, что начался замер между парой скоростей из настроек прибора, а не из конфиг-файла (для приложения не применимо)
							if (curSpeed >= dynRecord.speed1[configRangeIndex] && prevSpeed < dynRecord.speed1[configRangeIndex]) {
								curRange = configRangeIndex;
								configRangeMeasure = true;
								nextSpeed2 = dynRecord.speed2[configRangeIndex];
								nextSpeed2index = configRangeIndex;
							}
							
							// расчёт поправки времени старта (линейная интерполяция)
							uint32_t timeLag = (uint32_t)((curSpeed - dynRecord.speed1[curRange]) * (curMs - prevMs) / (curSpeed - prevSpeed));

							// расчёт времени старта
							startTime = (timeLag < curMs) ? curMs - timeLag : curMs;

							debug_msg("Speed: %d, Timelag: %d, StartTime: %d\n", dynRecord.speed1[curRange], timeLag, startTime);
							// сохранение первой скорости из пары
							dynRecord.speed12Ms[curRange] = startTime;
							// установка флага замера "сходу"
							instantTest = true;
							// установка точки старта
                            startLoc.lat = gps.location.intLat();
                            startLoc.lon = gps.location.intLon();
							// джингл о начале замера
							if (config.toneOn) {
								playJingle(&JINGLE_SUCCESS);
							}
							// выбор скорости окончания замера пары скоростей
							if (!configRangeMeasure) {
								for (uint8_t i = 0; i < MAX_RANGES; i++) {
									if (dynRecord.speed2[i] < nextSpeed2 && dynRecord.speed2[i] > 0 && finalizedRanges[i] == false && curSpeed <= dynRecord.speed2[i]) {
										nextSpeed2 = dynRecord.speed2[i];
										nextSpeed2index = i;
									}
								}
							}
							// переход к следующей паре скоростей
							curRange = (dynRecord.speed1[curRange + 1] > 0 && curRange < MAX_RANGES - 1) ? curRange + 1 : curRange;
						}
					}
				} 
				// замер начался
				if (startTime > 0) { 
					// расчёт дистанции от точки старта в метрах
					distance = gps.location.DistanceKm(&startLoc) * 1000;
					// поправка дистанции при включенном параметре ролл-аут
					if (config.rollout) {
						distance = (distance >= 0.3) ? distance - 0.3 : 0;
					}
					
					// APP: обновление лейбла дистации
                    
					// расчёт уклона дороги
					if (dynRecord.inclineCheck) 
						{
						// фильтрация значения высоты (в сантиметрах)
						avgAlt = (avgAlt == 0) ? gps.altitude.cm() : avgAlt * 0.95 + gps.altitude.cm() * 0.05;
						
						// APP: обновление лейбла высоты на экране
						
						// расчёт дистанции от предыдущей точки замера высоты
						distAlt = (lastLoc.lat == 0) ? INCLINE_SAMPLE_DIST + 0.1 : gps.location.DistanceKm(&lastLoc) * 1000; 
						// расчёт уклона дороги на отрезке INCLINE_SAMPLE_DIST
						if (distAlt >= INCLINE_SAMPLE_DIST) {
							// расчёт уклона дороги (в % * 10)
							curIncline = (lastAvgAlt == 0) ? 0 : int((avgAlt - lastAvgAlt) / (distAlt * 100) * 1000);
							// сохранение максимального значения уклона
							maxIncline = (abs(curIncline) > abs(maxIncline)) ? curIncline : maxIncline;
							// сохранение текущего значения высоты
							lastAvgAlt = avgAlt;
							// начало нового отрезка замера уклона дороги
                            lastLoc.lat = gps.location.intLat();
                            lastLoc.lon = gps.location.intLon();
						}
					}
					
					// заполнение структуры для записи лога
					frame.speed = curSpeed;
					frame.accelG = accelG;
					frame.accelG_raw = rawAccelG;

					// averageSpeed in km/ms => 
					uint16_t distCorrM = (uint16_t)(curSpeed / 360 * distCorr); // (curSpeed * 1000 / 60 / 60 / 1000 * 10 * distCorr) - in m*10 (dm) 
					if ((uint32_t)(distance * 10) >= distCorrM) {
						frame.distance = (config.units == EU) ? (uint32_t)(distance * 10) - distCorrM : (uint32_t)(distance * M_TO_FT * 10) - (distCorrM * M_TO_FT);
					} else {
						frame.distance = (config.units == EU) ? (uint32_t)(distance * 10) : (uint32_t)(distance * M_TO_FT * 10);
					}

					frame.incline = curIncline;

					frame.totalTimeMs = (curMs > startTime) ? curMs - startTime : 0;

					// запись строки с параметрами в лог-файл
					if (config.sdEnabled) {
						if (!writeLog(&gps, &frame, &logFile, logFields, false)) log_failed = true;
					}

					// Проверка на условия прекращения замера:
					// - замедление ниже порога DECELERATION_THRESHOLD
					// - время замера выше ACC_TIMEOUT
					// - замер "сходу" и скорость ниже начальной скорости первой пары и время замера больше 1 сек
					// - замер с места и скорость ниже READY_SPEED_THRESHOLD_UNIT
					if (accelG < DECELERATION_THRESHOLD || curMs > startTime + ACC_TIMEOUT 
						|| (instantTest && curSpeed < dynRecord.speed1[0] && curMs > startTime + 1000) 
						|| (!instantTest && curSpeed < READY_SPEED_THRESHOLD_UNIT)) {
						// обнуление времени разгона для незавершённых диапазонов
						for (uint8_t i = 0; i < MAX_RANGES; i++) {
							if (finalizedRanges[i] == false) dynRecord.speed12Ms[i] = 0;
						}
						debug_msg("Accel test: FINISH FLAG\n");
						// установка флага завершения замера
						measureFinished = true;
					}
					// проверка флага завершения замера
					if (measureFinished && HAL_GetTick() >= delayTime) {
						
						// проверка "неудачности" завершения замера:
						// - замер "сходу" и ненулевой результат замера между парами скоростей
						// - замер с места и ненулевой результат замера до первой граничной скорости
						if ((instantTest && ((!configRangeMeasure && dynRecord.speed12Ms[0] == 0) || (configRangeMeasure && dynRecord.speed12Ms[configRangeIndex] == 0))) || (!instantTest && dynRecord.speedMs[0] == 0)) { // если замер неудачный
							
							// APP: вывод сообщения о неудачности замера
							
							// удаление лог-файла
							if (logFile.flag != 0) {
                                f_close(&logFile);
                                f_unlink(TEMP_LOG_FILENAME);
								if (flash.logStatus != 0xFF) {
									flash.logStatus = 0xFF;
									saveConfigFlash(CFG_NONE_MSG);
								}
							}
							
						// замер удачный
						} else {
							
							if (dynRecord.inclineCheck) {
								// определение валидности замера в зависимости от уклона дороги
								dynRecord.inclineValid = (abs(dynRecord.maxIncline) > INCLINE_THRESHOLD) ? false : true;
								
								// APP: вывод максимального значания уклона на лейбл Уклон на экране, если уклон превышает порог - лейбл становится оранжевым
								
							}
                            
							if (config.sdEnabled) {
								// установка даты и времени замера
								setFrameDateTime(&frame, &gps);

								// проверка корректности записи лога (для приложения не применимо)
								if (log_failed) {
									f_close(&logFile);
									f_unlink(TEMP_LOG_FILENAME);
									delayTime = HAL_GetTick() + 2*MSG_DELAY;
									stringToLED("LOG FAIL", 0);
									while (HAL_GetTick() < delayTime && no_irq_button());
								} else {
									// сохранение лог-файла
									finalizeLog(&frame, &logFile, false);
								}
								
								// APP: сохранение результата замера на экране История
								
							}
						}
						// выход из функции замера (перезагрузка функции)
						return;
					}
					
					// проверка достижения граничной скорости из списка dynRecord.speedLimit[] при замере "с места"
					if (curSpeed >= dynRecord.speed[speedIndex] && dynRecord.speedMs[speedIndex] == 0 && !instantTest) {
						// расчёт поправки времени окончания замера (линейная интерполяция)
						uint32_t timeLag = int((curSpeed - dynRecord.speed[speedIndex]) * TIME_LAG_UNIT_COEFF / averageAccel(speedBuffer, timeBuffer, buffIndex)); 
						// расчёт и сохранение времени разгона (результат)
						dynRecord.speedMs[speedIndex] = curMs - startTime - timeLag;
						// сохранение максимального уклона
						dynRecord.maxIncline = maxIncline;
						// проигрывание джингла
						if (config.toneOn) {
							playJingle(&JINGLE_SUCCESS);
						}
						debug_msg("Accel test: SPEED %d ACHIEVED\n", dynRecord.speed[speedIndex]);
						debug_msg("Speed: %d, Timelag: %d, Result: %d\n", dynRecord.speed[speedIndex], timeLag, dynRecord.speedMs[speedIndex]);
						// переход к следующей граничной скорости
						speedIndex = (speedIndex < dynRecord.speed_num - 1) ? speedIndex + 1 : speedIndex;
						
						// APP: добавление результата в список результатов на экране Разгон
						
					}

					// проверка достижения начальной граничной скорости из пары пользовательских скоростей
					if (!configRangeMeasure && curSpeed >= dynRecord.speed1[curRange] && dynRecord.speed12Ms[curRange] == 0) { 
						debug_msg("Accel test: RANGE#%d STARTED\n", curRange);
						// расчёт поправки времени начала замера пары скоростей (линейная интерполяция)
						uint32_t timeLag = int((curSpeed - dynRecord.speed1[curRange]) * TIME_LAG_UNIT_COEFF / averageAccel(speedBuffer, timeBuffer, buffIndex));
						// расчёт и сохранение времени начала замера между парами пользовательских скоростей
						dynRecord.speed12Ms[curRange] = curMs - timeLag;
						debug_msg("Speed: %d, Timelag: %d, StartTime: %d\n", dynRecord.speed1[curRange], timeLag, dynRecord.speed12Ms[curRange]);
						// определение скорости окончания замера между парами пользовательских скоростей
						for (uint8_t i = 0; i < MAX_RANGES; i++) {
							if (dynRecord.speed2[i] < nextSpeed2 && dynRecord.speed2[i] > 0 && finalizedRanges[i] == false) {
								nextSpeed2 = dynRecord.speed2[i];
								nextSpeed2index = i;
							}
						}
						// переход к следующей паре пользовательских скоростей
						curRange = (dynRecord.speed1[curRange + 1] > 0 && curRange < MAX_RANGES - 1) ? curRange + 1 : curRange;
					}

					// проверка достижения конечной граничной скорости из пары пользовательских скоростей
					if (curSpeed >= nextSpeed2 && finalizedRanges[nextSpeed2index] == false) { 
						debug_msg("Accel test: RANGE#%d FINISHED\n", nextSpeed2index);
						// расчёт поправки времени окончания замера пары скоростей (линейная интерполяция)
						uint32_t timeLag = int((curSpeed - nextSpeed2) * TIME_LAG_UNIT_COEFF / averageAccel(speedBuffer, timeBuffer, buffIndex));
						// расчёт и сохранение времени замера между парами пользовательских скоростей
						dynRecord.speed12Ms[nextSpeed2index] = curMs - timeLag - dynRecord.speed12Ms[nextSpeed2index] + RANGE_CORR;
						// установка флага завершения замера пары скоростей
						finalizedRanges[nextSpeed2index] = true;
						debug_msg("Speed: %d, Timelag: %d, Result: %d\n", nextSpeed2, timeLag, dynRecord.speed12Ms[nextSpeed2index]);
						// проигрывание джингла
						if (config.toneOn) playJingle(&JINGLE_SUCCESS);
						// определение скорости окончания замера между парами пользовательских скоростей
						nextSpeed2 = 1000;
						for (uint8_t i = 0; i < MAX_RANGES; i++) {
							if (dynRecord.speed2[i] < nextSpeed2 && dynRecord.speed2[i] > 0 && finalizedRanges[i] == false && curSpeed <= dynRecord.speed2[i]) {
								nextSpeed2 = dynRecord.speed2[i];
								nextSpeed2index = i;
							}
						}
						// установка флага завершения замера "сходу" при замере всех пар скоростей
						measureFinished = (instantTest && nextSpeed2 == 1000) ? true : measureFinished;
						
						// APP: добавление результата в список результатов на экране Разгон
						
					}

					// проверка достижения граничной дистанции из списка dynRecord.distLimit[] при замере "с места"
					if (distance >= dynRecord.dist[distIndex] && dynRecord.distMs[distIndex] == 0 && !instantTest) {
						debug_msg("Accel test: DISTANCE %d ACHIEVED\n", dynRecord.dist[distIndex]);
						// расчёт поправки времени окончания замера дистанции (линейная интерполяция)
						uint32_t timeLag = int((distance - dynRecord.dist[distIndex]) / avgSpeed / 3.6 * 1000);
						// расчёт и сохранение времени замера дистанции
						dynRecord.distMs[distIndex] = curMs - startTime - timeLag - rolloutTime + distCorr;
						// сохранение скорости на замере дистанции
						dynRecord.distSpeed[distIndex] = int(curSpeed);
						// сохранение максимального уклона дороги
						dynRecord.maxIncline = maxIncline;
						debug_msg("Distance: %d, Speed: %d, Timelag: %d, Result: %d\n", dynRecord.dist[distIndex], dynRecord.distSpeed[distIndex], timeLag, dynRecord.distMs[distIndex]);
						// проигрывание джингла
						if (config.toneOn) playJingle(&JINGLE_SUCCESS);
						// переход к следующей граничной дистанции
						distIndex = (distIndex < dynRecord.dist_num - 1) ? distIndex + 1 : distIndex;
						
						// APP: добавление результата в список результатов на экране Разгон
						
					}
				}
			
			} else {
				// вывод сообщения о низком качестве приёма GPS
				
				// APP: запрещение замера
				
				lowHDOP();
				// return;
			}
		}

		// обновление текущего времени замера
		
		// APP: обновление текущего времени замера на лейбле Время на экране
		
		if (startTime > 0) updateTime((startTime >= curMs) ? 0 : curMs - startTime, &dynRecord, showIndex, rangeIndex);
	}
	
}

// Вспомогательные функции

// Функция расчёта среднего ускорения
double averageAccel (double speedBuffer[], unsigned long timeBuffer[], const uint8_t buffIndex) {
	
	double accelBuffer[BUFFER_SIZE];

	for(uint8_t i = 0; i < BUFFER_SIZE; i++) {
		int prev = (i == 0) ? BUFFER_SIZE - 1 : i - 1;
		accelBuffer[i] = (i == buffIndex || speedBuffer[i] < 0.7) ? 0 : (speedBuffer[i] - speedBuffer[prev]) / (timeBuffer[i] - timeBuffer[prev]);
	}

	uint8_t i = (buffIndex == 0) ? BUFFER_SIZE - 1 : buffIndex - 1; 
	double sumAccel = 0;
	uint8_t countAccel = 0;
	while(accelBuffer[i] > 0.001) {
		sumAccel += accelBuffer[i];
		countAccel++; 
		i = (i == 0) ? BUFFER_SIZE - 1 : i - 1;
	}
	return sumAccel / countAccel;
}

// Функция расчёта средней скорости
double averageSpeed(double speedBuffer[]) {
	double sumSpeed = 0.0;
	uint8_t zeroCount = 0;
	for (uint8_t i = 0; i < BUFFER_SIZE; i++) {
		if (speedBuffer[i] > 0) {
			sumSpeed += speedBuffer[i];
		} else {
			zeroCount++;
		}
	}
	if (zeroCount == BUFFER_SIZE) return 0.0;
	return sumSpeed / (BUFFER_SIZE - zeroCount);
}

// Функция расчёта средней высоты
uint32_t averageAlt(long altitudeBuffer[]) {
	long sumAlt = 0;
	uint8_t zeroCount = 0;
	for (int i = 0; i < BUFFER_SIZE; i++) {
		sumAlt = (altitudeBuffer[i] > 0) ? sumAlt + altitudeBuffer[i] : sumAlt;
		zeroCount = (altitudeBuffer[i] == 0) ? zeroCount + 1: zeroCount;
	}
	return (uint32_t)(sumAlt / (BUFFER_SIZE - zeroCount));
}

// Функция определения статуса горизонтальной точности
bool statusHDOP() {
	static bool isOkHDOP = false;
	if (gps.hdop.isValid() && gps.satellites.isValid()) {
		// фильтрация показателя горизонтальной точности HDOP
		hdop = (hdop == 0) ? gps.hdop.value() : (uint16_t)(0.97 * hdop + 0.03 * gps.hdop.value()); // постоянная времени ~5 сек
		// debug_msg("HDOP: %d\n", gps.hdop.value());		
		if (gps.hdop.value() >= HDOP_UT) {
			isOkHDOP = false;
		} else if (hdop <= HDOP_LT && gps.satellites.value() >= SATS_LT) {
			isOkHDOP = true;
		}
	} else {
		isOkHDOP = false;
	}
	return isOkHDOP;
}

// Функция определения статуса вертикальной точности
bool statusVDOP() {
	static bool isOkVDOP = false;
	if (gps.vdop.isValid()) {
		// фильтрация показателя вертикальной точности HDOP
		vdop = (vdop == 0) ? gps.vdop.value() : (uint16_t)(0.97 * vdop + 0.03 * gps.vdop.value()); // постоянная времени ~5 сек
		//debug_msg("VDOP: %d\n", gps.vdop.value());		
		if (vdop >= VDOP_UT) {
			isOkVDOP = false;
		} else if (vdop <= VDOP_LT) {
			isOkVDOP = true;
		}
	}
	return isOkVDOP;
}

// обработка низкой горизонтальной точности
void lowHDOP() {

	// APP: вывод плашки на экран с результатами "Идёт поиск сигнала GNSS"

}

// Функция расчёта расстояния между текущей позицией и точкой loc
// INT_LOC_COEFF = 10000000UL
double TinyGPSLocation::DistanceKm(Location_t *loc) {
    double delta = radians((double)(intLon() - loc->lon) / INT_LOC_COEFF);
    double sdlong = sin(delta);
    double cdlong = cos(delta);
    double slat1 = sin(radians((double)intLat() / INT_LOC_COEFF));
    double clat1 = cos(radians((double)intLat() / INT_LOC_COEFF));
    double slat2 = sin(radians((double)loc->lat / INT_LOC_COEFF));
    double clat2 = cos(radians((double)loc->lat / INT_LOC_COEFF));
    delta = (clat1 * slat2) - (slat1 * clat2 * cdlong);
    delta = sq(delta);
    delta += sq(clat2 * sdlong);
    delta = sqrt(delta);
    double denom = (slat1 * slat2) + (clat1 * clat2 * cdlong);
    delta = atan2(delta, denom);
    return delta * 6371.0088;
}


