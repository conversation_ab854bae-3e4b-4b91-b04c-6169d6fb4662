﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Racebox.Shared.Interfaces;
using Racebox.Shared.Models;

namespace Racebox.Shared.Services;

// add-migration Change001 -Context Racebox.Shared.Services.LocalDatabase -Verbose

/// <summary>
/// Implemented to be SINGLETON
/// </summary>
public class LocalDatabase : DbContext
{
    //setup to be used as singleton
    private readonly SemaphoreSlim _saveSemaphore = new SemaphoreSlim(1);

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = new CancellationToken())
    {

        await _saveSemaphore.WaitAsync();

        try
        {
            foreach (EntityEntry<IDomainBaseEntity> entry in ChangeTracker.Entries<IDomainBaseEntity>())
            {
                switch (entry.State)
                {
                case EntityState.Added:
                entry.Entity.TimeCreated = DateTime.Now.ToUniversalTime();
                break;
                case EntityState.Modified:
                entry.Entity.TimeEdited = DateTime.Now.ToUniversalTime();
                break;
                case EntityState.Deleted:
                //can implement SafeDelete here..
                break;
                }
            }

            var result = await base.SaveChangesAsync(cancellationToken);

            return result;
        }
        finally
        {
            _saveSemaphore.Release();
        }

    }
    public void Refresh()
    {
        Database.CloseConnection();
        Database.OpenConnection();
    }

    public static bool Initialized { get; protected set; }

    public DbSet<MeasuredDistance> Distances { get; set; }
    public DbSet<MeasuredRange> Ranges { get; set; }
    public DbSet<MeasureResult> Results { get; set; }
    public DbSet<MeasuredLogLine> Logs { get; set; }
    public DbSet<Car> Cars { get; set; }
    public DbSet<AppUser> Users { get; set; }

    void Initialize(bool reset)
    {
        if (!Initialized)
        {
            Initialized = true;

            SQLitePCL.Batteries_V2.Init();

            if (reset)
                this.Database.EnsureDeleted();

            Database.Migrate();
        }
    }

    //parameterless constructor must be above the others,
    //as it seems that migrator just takes the .First() of them
    public LocalDatabase(bool reset = false)
    {
        File = Path.Combine("../", "Data1.db3");
        Initialize(reset);
    }

    public LocalDatabase(string filenameWithPath, bool reset = false)
    {
        File = filenameWithPath;
        Initialize(false);
    }

    public LocalDatabase(DbContextOptions options) : base(options)
    {
        Initialize(false);
    }

    public static string File { get; protected set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        optionsBuilder
            .UseSqlite($"Filename={File}");
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        //not needed, using by convention

        //modelBuilder.Entity<MeasureResult>().HasOne(m => m.CurrentWeather)
        //    .WithOne(o => o.MeasureResult);

        //modelBuilder.Entity<MeasureResult>()
        //    .HasMany(m => m.Ranges)
        //    .WithOne(o => o.MeasureResult);

        //modelBuilder.Entity<MeasureResult>()
        //    .HasMany(m => m.Logs)
        //    .WithOne(o => o.MeasureResult);
    }
}