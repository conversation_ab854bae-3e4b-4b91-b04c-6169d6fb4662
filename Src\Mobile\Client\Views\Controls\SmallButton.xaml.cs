using SkiaSharp;
using System.Diagnostics;

namespace Racebox.Views.Drawn;

public partial class SmallButton : SkiaButton
{
    public SmallButton()
    {
        InitializeComponent();
    }

    SkiaLabel MyLabel;

    public override void FindViews()
    {
        base.FindViews();

        if (MyLabel == null)
        {
            MyLabel = this.FindView<SkiaLabel>("CustomLabel");
        }

        if (MyFrame == null)
        {
            MyFrame = FindView<SkiaShape>("CustomFrame");
        }
    }

    public override void ApplyProperties()
    {
        base.ApplyProperties();

        if (MyLabel != null)
        {
            MyLabel.Text = this.Text;
        }
    }

    protected SkiaShape MyFrame;


    /// <summary>
    /// Clip effects with rounded rect of the frame inside
    /// </summary>
    /// <returns></returns>
    public override SKPath CreateClip(object arguments, bool usePosition, SKPath path = null)
    {
        if (MyFrame != null)
        {
            return MyFrame.CreateClip(arguments, false);
        }

        return base.CreateClip(arguments, usePosition);
    }

    async void AnimatePress(SkiaControl icon)
    {
        await icon.ScaleToAsync(0.9, 0.9, 50, Easing.CubicOut);
        await icon.ScaleToAsync(1.0, 1.0, 50, Easing.SpringOut);
    }


    public override bool OnDown(SkiaGesturesParameters args, GestureEventProcessingInfo apply)
    {

        this.ScaleToAsync(0.98, 0.95, 16, Easing.CubicOut);

        return base.OnDown(args, apply);
    }

    public override void OnUp(SkiaGesturesParameters args, GestureEventProcessingInfo apply)
    {
        this.ScaleToAsync(1.0, 1.0, 32, Easing.SpringOut);

        base.OnUp(args, apply);
    }




}