﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Racebox.Resources
{
    public partial class Styles
    {
        public static Color TintContentColorWithAlpha = Color.FromRgb(0.99215686, 0.5176471, 0).WithAlpha(0.18f);
        public static Color TintContentColor = Color.FromRgb(0.99215686, 0.5176471, 0);
        public static double TintContentColorAlpha = 0.18;
        public static float TintContentContrast = 1.46f;
        public static float TintContentLightness = 0.93f;
        public static float TintContentSaturation = 0.26f;
    }
}
