﻿using Mapster;
using MapsterMapper;
using Microsoft.EntityFrameworkCore;
using Racebox.Shared.Models;

namespace Racebox.Shared.Extensions;

public static class MappingExtensions
{
	public static async Task<List<TDestination>> ProjectToListAsync<TSource, TDestination>(this IQueryable<TSource> queryable, IMapper mapper, CancellationToken cancellationToken = default)
	{
		var originals = await queryable
			.ToListAsync(cancellationToken);

		var dtos = new List<TDestination>();
		foreach (var item in originals)
		{
			dtos.Add(await mapper.MapAsync<TDestination>(item));
		}

		return dtos;
	}

	public static async Task<List<TDestination>> ProjectToListAsync<TSource, TDestination>(this IList<TSource> originals, IMapper mapper, CancellationToken cancellationToken = default)
	{
		var dtos = new List<TDestination>();
		foreach (var item in originals)
		{
			dtos.Add(await mapper.MapAsync<TDestination>(item));
		}

		return dtos;
	}

	/// <summary>
	/// This is PAGE 0-based! Not 1!
	/// </summary>
	/// <typeparam name="TSource"></typeparam>
	/// <typeparam name="TDestination"></typeparam>
	/// <param name="queryable"></param>
	/// <param name="pageNumber"></param>
	/// <param name="pageSize"></param>
	/// <param name="mapper"></param>
	/// <param name="cancellationToken"></param>
	/// <returns></returns>
	public static async Task<FilteredPagedList<TDestination>> ProjectToPagedListAsync<TSource, TDestination>(this IQueryable<TSource> queryable, int pageNumber, int pageSize, IMapper mapper, CancellationToken cancellationToken = default)
	{
		var total = await queryable.CountAsync(cancellationToken);

		var originals = await queryable
			.Skip(pageNumber * pageSize)
			.Take(pageSize)
			.ToListAsync(cancellationToken);

		var dtos = new List<TDestination>();
		foreach (var item in originals)
		{
			dtos.Add(await mapper.MapAsync<TDestination>(item));
		}

		var pagedList = new FilteredPagedList<TDestination>(dtos, total, pageNumber, pageSize);

		return pagedList;
	}

	public static async Task<TOut> MapAsync<TOut>(this MapsterMapper.IMapper mapper, object entity)
	{
		return await mapper.From(entity).AdaptToTypeAsync<TOut>();
	}

}