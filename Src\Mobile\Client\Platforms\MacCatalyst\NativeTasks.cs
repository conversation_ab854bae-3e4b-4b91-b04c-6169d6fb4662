﻿using AVFoundation;
using CoreLocation;
using Foundation;
using UIKit;



namespace Racebox.Helpers
{


	public partial class NativeTasks
	{
        public IEnumerable<string> ListAssets(string subfolder)
        {
            NSBundle mainBundle = NSBundle.MainBundle;
            string resourcesPath = mainBundle.ResourcePath;
            string subfolderPath = Path.Combine(resourcesPath, subfolder);

            if (Directory.Exists(subfolderPath))
            {
                string[] files = Directory.GetFiles(subfolderPath);
                return files.Select(Path.GetFileName).ToList();
            }
            else
            {
                return new List<string>();
            }
        }

        public void OpenLink(string link)
		{
			var url = new NSUrl(link);
			var res = UIApplication.SharedApplication.OpenUrl(url);
		}


		public void OpenTelegram(string channel)
		{
			var tg = new NSUrl($"tg://resolve?domain={channel}");

			if (UIApplication.SharedApplication.CanOpenUrl(tg))
			{
				UIApplication.SharedApplication.OpenUrl(tg);
				return;
			}

			var url = new NSUrl($"https://t.me/{channel}");
			var res = UIApplication.SharedApplication.OpenUrl(url);
		}

		public void ExportLogs(IEnumerable<string> fullFilenames)
		{
			ShareFiles("Логи", fullFilenames);

		}

		public void ShareFiles(string message, IEnumerable<string> fullFilenames)
		{
			MainThread.BeginInvokeOnMainThread(async () =>
			{
				try
				{
					var files = fullFilenames.Select(x => new ShareFile(x)).ToList();
					await Share.Default.RequestAsync(new ShareMultipleFilesRequest
					{
						Title = message,
						Files = files
					});
				}
				catch (Exception e)
				{
					Console.WriteLine(e);
				}

			});
		}

		public bool CheckGpsIsAvailable()
		{
			bool status = CLLocationManager.LocationServicesEnabled;

			return status;
		}

		public void OpenSettings()
		{
			NSUrl url = new NSUrl("x-mac-openurl:x-apple.systempreferences:com.apple.preference.security?Privacy_Bluetooth");

			UIApplication.SharedApplication.OpenUrl(url, new NSDictionary(), (bool success) =>
			{
				if (success)
				{
					Console.WriteLine("Settings opened successfully");
				}
				else
				{
					Console.WriteLine("Failed to open settings");
				}
			});

			//var url = new NSUrl("x-apple.systempreferences:com.apple.preference.security?Privacy_Bluetooth");
			//var res = UIApplication.SharedApplication.OpenUrl(url);
		}


		AVAudioPlayer MediaPlayer;
		public void PlaySoundFromAssets(string filename)
		{
			try
			{
				if (MediaPlayer != null && MediaPlayer.Playing)
				{
					MediaPlayer.Stop();
				}

				if (MediaPlayer != null)
				{
					MediaPlayer.Dispose();
					MediaPlayer = null;
				}

				if (MediaPlayer == null)
				{
					NSError err;
					var ext = Path.GetExtension(filename).Replace(".", string.Empty);
					var file = filename.Replace("." + ext, string.Empty);
					var url = NSBundle.MainBundle.GetUrlForResource(file, ext);

					MediaPlayer = new AVAudioPlayer(url, ext, out err);
				}

				MediaPlayer.PrepareToPlay();
				MediaPlayer.SetVolume(1f, 1f);
				MediaPlayer.NumberOfLoops = 0;
				MediaPlayer.Play();

			}
			catch (Exception e)
			{
				System.Diagnostics.Debug.WriteLine(e);
			}

		}
	}

}
