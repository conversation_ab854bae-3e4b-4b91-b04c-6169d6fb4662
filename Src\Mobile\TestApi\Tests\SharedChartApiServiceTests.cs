using Microsoft.VisualStudio.TestTools.UnitTesting;
using Microsoft.Extensions.DependencyInjection;
using Racebox.Shared.ChartApi;
using Racebox.Shared.Services;
using Racebox.Shared.Models;
using System.Text;

namespace TestApi.Tests;

/// <summary>
/// Tests for the shared ChartApiService from the Racebox.Shared project
/// </summary>
[TestClass]
public class SharedChartApiServiceTests
{
    private ServiceProvider _serviceProvider = null!;
    private ChartApiService _chartService = null!;
    private string _testOutputDirectory = null!;

    [TestInitialize]
    public void Setup()
    {
        // Configure services like in a real mobile app
        var services = new ServiceCollection();
        services.AddChartApi(); // Use the extension method from Shared project
        
        _serviceProvider = services.BuildServiceProvider();
        _chartService = _serviceProvider.GetRequiredService<ChartApiService>();
        
        _testOutputDirectory = Path.Combine(Path.GetTempPath(), "SharedChartApiTests", Guid.NewGuid().ToString());
        Directory.CreateDirectory(_testOutputDirectory);
    }

    [TestCleanup] 
    public void Cleanup()
    {
        _serviceProvider?.Dispose();
        
        if (Directory.Exists(_testOutputDirectory))
        {
            try
            {
                Directory.Delete(_testOutputDirectory, true);
            }
            catch
            {
                // Ignore cleanup failures
            }
        }
    }

    [TestMethod]
    public async Task GenerateChartAsync_NullMeasureResult_ReturnsError()
    {
        // Act
        var result = await _chartService.GenerateChartAsync(null!, _testOutputDirectory);

        // Assert
        Assert.IsFalse(result.IsSuccess);
        Assert.AreEqual("Measurement result is null or has no log data", result.ErrorMessage);
    }

    [TestMethod]
    public async Task GenerateChartAsync_EmptyLogs_ReturnsError()
    {
        // Arrange
        var measureResult = new MeasureResult
        {
            Logs = new List<MeasuredLogLine>()
        };

        // Act
        var result = await _chartService.GenerateChartAsync(measureResult, _testOutputDirectory);

        // Assert
        Assert.IsFalse(result.IsSuccess);
        Assert.AreEqual("Measurement result is null or has no log data", result.ErrorMessage);
    }

    [TestMethod]
    public async Task GenerateChartAsync_NullOutputDirectory_ReturnsError()
    {
        // Arrange
        var measureResult = CreateTestMeasureResult();

        // Act
        var result = await _chartService.GenerateChartAsync(measureResult, null!);

        // Assert
        Assert.IsFalse(result.IsSuccess);
        Assert.AreEqual("Output directory is required", result.ErrorMessage);
    }

    [TestMethod]
    public async Task GenerateChartAsync_ServiceBusy_ReturnsError()
    {
        // This test verifies the IsBusy flag behavior
        // In a real scenario, we'd need to make the service busy by starting a long operation
        // For now, we'll test that the service can handle sequential requests
        
        // Arrange
        var measureResult = CreateTestMeasureResult();

        // Act - Make two rapid calls
        var task1 = _chartService.GenerateChartAsync(measureResult, _testOutputDirectory);
        var task2 = _chartService.GenerateChartAsync(measureResult, _testOutputDirectory);
        
        var results = await Task.WhenAll(task1, task2);

        // Assert - At least one should complete (the other might be busy or both succeed if timing allows)
        Assert.IsTrue(results.Any(r => r.IsSuccess || r.ErrorMessage == "Chart service is busy"));
    }

    [TestMethod]
    public async Task GenerateChartAsync_ValidMeasureResult_CreatesOutputDirectory()
    {
        // Arrange
        var nonExistentDirectory = Path.Combine(Path.GetTempPath(), "NonExistent_" + Guid.NewGuid());
        var measureResult = CreateTestMeasureResult();

        try
        {
            // Act
            var result = await _chartService.GenerateChartAsync(measureResult, nonExistentDirectory);

            // Assert (either success or network error, but directory should be created)
            Assert.IsTrue(Directory.Exists(nonExistentDirectory));
        }
        finally
        {
            // Cleanup
            if (Directory.Exists(nonExistentDirectory))
            {
                try
                {
                    Directory.Delete(nonExistentDirectory, true);
                }
                catch { }
            }
        }
    }

    [TestMethod, Timeout(30000)] // 30 second timeout for integration test
    public async Task GenerateChartAsync_ValidMeasureResult_AttemptsApiCall()
    {
        // This is an integration test that attempts to call the real API
        
        // Arrange
        var measureResult = CreateRealisticMeasureResult();

        // Act
        var result = await _chartService.GenerateChartAsync(measureResult, _testOutputDirectory);

        // Assert
        if (result.IsSuccess)
        {
            // If API call succeeded
            Assert.IsNotNull(result.Data);
            Assert.IsNotNull(result.Data.FilePath);
            Assert.IsNotNull(result.Data.Filename);
            Assert.IsTrue(result.Data.FileSize > 0);
            Assert.IsNotNull(result.ResponseTime);
            Assert.IsTrue(File.Exists(result.Data.FilePath));
            
            Console.WriteLine($"✅ Chart generated successfully!");
            Console.WriteLine($"📁 File: {result.Data.Filename}");
            Console.WriteLine($"🖼️  Size: {result.Data.FileSize:N0} bytes");
            Console.WriteLine($"⏱️  Time: {result.ResponseTime.TotalSeconds:F2}s");
        }
        else
        {
            // If API call failed (network issues, timeout, etc.)
            Assert.IsNotNull(result.ErrorMessage);
            Console.WriteLine($"❌ Chart generation failed (expected for network issues):");
            Console.WriteLine($"🔍 Error: {result.ErrorMessage}");
            
            // Validate that common error types are handled correctly
            Assert.IsTrue(
                result.ErrorMessage.Contains("Network error") ||
                result.ErrorMessage.Contains("Request timed out") ||
                result.ErrorMessage.Contains("API Error") ||
                result.ErrorMessage.Contains("Request was cancelled"));
        }
    }

    [TestMethod]
    public async Task GenerateChartAsync_CancellationRequested_ReturnsCancelledError()
    {
        // Arrange
        var measureResult = CreateTestMeasureResult();
        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel(); // Cancel immediately

        // Act
        var result = await _chartService.GenerateChartAsync(measureResult, _testOutputDirectory, cancellationTokenSource.Token);

        // Assert
        Assert.IsFalse(result.IsSuccess);
        Assert.AreEqual("Request was cancelled", result.ErrorMessage);
    }

    [TestMethod]
    public void ServiceRegistration_AddChartApi_RegistersCorrectly()
    {
        // Arrange
        var services = new ServiceCollection();

        // Act
        services.AddChartApi();
        var serviceProvider = services.BuildServiceProvider();

        // Assert
        var chartService = serviceProvider.GetService<ChartApiService>();
        Assert.IsNotNull(chartService);
        
        // Verify HttpClientFactory is available
        var httpClientFactory = serviceProvider.GetService<IHttpClientFactory>();
        Assert.IsNotNull(httpClientFactory);
        
        // Verify we can create the chartApi client
        var httpClient = httpClientFactory.CreateClient("chartApi");
        Assert.IsNotNull(httpClient);
        Assert.AreEqual("https://chart.racebox.cc:5000/", httpClient.BaseAddress?.ToString());
        
        serviceProvider.Dispose();
    }

    private static MeasureResult CreateTestMeasureResult()
    {
        return new MeasureResult
        {
            Id = 1,
            CreatedTimeUtc = DateTime.UtcNow,
            AppUser = new AppUser { Name = "Test User" },
            CarId = 1,
            Logs = new List<MeasuredLogLine>
            {
                new() { TotalSeconds = 0.18, Lattitude = 56.7916267, Longitude = 60.5735093, Speed = 2.5, Distance = 1.9 },
                new() { TotalSeconds = 3.34, Lattitude = 56.7916267, Longitude = 60.5735093, Speed = 60.5, Distance = 18.5 }
            }
        };
    }

    private static MeasureResult CreateRealisticMeasureResult()
    {
        var logs = new List<MeasuredLogLine>();
        
        // Generate realistic acceleration data
        for (int i = 0; i < 100; i++)
        {
            var time = i * 0.5; // 0.5 second intervals
            var speed = Math.Min(200, Math.Pow(time * 2.5, 1.5)); // Realistic acceleration curve
            var distance = speed * time / 3.6; // Convert to meters
            
            logs.Add(new MeasuredLogLine
            {
                TotalSeconds = time,
                Lattitude = 56.7916267 + (i * 0.0001),
                Longitude = 60.5735093 + (i * 0.0001),
                Speed = speed,
                Distance = distance,
                AccelerationLon = Math.Max(0, 0.8 - (time * 0.02)), // Decreasing acceleration
                Altitude = 268.8 + (i * 0.1),
                Heading = 296 + (i * 0.5)
            });
        }

        return new MeasureResult
        {
            Id = 1,
            CreatedTimeUtc = DateTime.UtcNow.AddMinutes(-10),
            AppUser = new AppUser { Name = "Integration Test User" },
            CarId = 1,
            Logs = logs,
            Distances = new List<MeasuredDistance>
            {
                new() { Length = 201, Time = TimeSpan.FromSeconds(9.78), Speed = 119 },
                new() { Length = 402, Time = TimeSpan.FromSeconds(15.18), Speed = 147 }
            },
            Ranges = new List<MeasuredRange>
            {
                new(0, 60) { Time = TimeSpan.FromSeconds(3.34) },
                new(0, 100) { Time = TimeSpan.FromSeconds(7.25) },
                new(100, 200) { Time = TimeSpan.FromSeconds(31.06) }
            }
        };
    }
}