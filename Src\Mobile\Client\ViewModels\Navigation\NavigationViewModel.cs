﻿using Racebox.Shared.Strings;
using System.Collections.Concurrent;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using AppoMobi.Framework.Maui.Interfaces;
using AppoMobi.Framework.Models;

namespace Racebox.ViewModels.Navigation;

public class NavigationViewModel : BaseViewModel
{


    #region DEBUG

    private string _SignalsInfo;
    public string SignalsInfo
    {
        get { return _SignalsInfo; }
        set
        {
            if (_SignalsInfo != value)
            {
                _SignalsInfo = value;
                OnPropertyChanged();
            }
        }
    }


    #endregion

    private IUIAssistant _ui;

    public NavigationViewModel(IUIAssistant ui, IServiceProvider services)
    {
        _ui = ui;
        _services = services;

        //SetBinding(FocusedElementProperty, new Binding("VisualElement",
        //    BindingMode.TwoWay, null, null, null,
        //    AppoMobi.Framework.Forms.Controls.Input.FocusedElement.Instance));

        SubscribeToEvents();

        //RefreshUserInfo();
    }

    public void Initialize(string startupRoute)
    {
        Shell = new AppFastShell(this, _services);
        SubscribeToEvents();
        Shell.Start(startupRoute);

        CommandGoBack = new Command(async () =>
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                await Shell.Navigation.PopAsync(true);
            });
        });

    }

    protected void UnsubscribeFromEvents()
    {
        if (Shell != null)
        {
            Shell.Navigated -= OnShellNavigated;
            Shell.OnRotation -= OnRotation;
            Shell.TabReselected -= OnTabReselected;
        }

        //App.Instance.Notifications.OnReceived -= OnNotificationsReceived;
    }

    void SubscribeToEvents()
    {

        //App.Instance.Notifications.OnReceived += OnNotificationsReceived;

        if (Shell != null)
        {
            Shell.Navigated += OnShellNavigated;
            Shell.OnRotation += OnRotation;
            Shell.TabReselected += OnTabReselected;
        }

        UpdateControls();

        //give time to update ui
        Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(10), async () =>
        {
            StartupFinished = true;
            return false;
        });

    }

    private void OnShellNavigated(object sender, ShellNavigatedEventArgs e)
    {

        if (e.Current.Location.ToString().SafeContains("/" + AppRoutes.Tabs.Route))
        {
            InitFlyout();

            //todo can do one-time tasks if needed
            //Super.StartTimer(TimeSpan.FromSeconds(1.5), () =>
            //{
            //    InsureSignalsConnected();
            //    return false;
            //});
        }
    }

    /// <summary>
    /// We have no menu atm
    /// </summary>
    public void InitFlyout()
    {
        //todo if needed
    }
    public IAppShell Shell { get; set; }

    #region Track Loaded Data Invalidation


    #region Request

    public void InvalidateRequestsData()
    {
        foreach (var key in TrackRequestsData.Keys.ToArray())
        {
            SetRequestsDataState(key, false);
        }
    }

    public void SetRequestsDataState(string key, bool state)
    {
        TrackRequestsData.AddOrUpdate(
            key,
            state, (k, b) => state);
    }

    public bool GetRequestsDataState(string key)
    {
        var ok = TrackRequestsData.TryGetValue(key, out bool data);
        if (ok)
            return data;
        return false;
    }

    public ConcurrentDictionary<string, bool> TrackRequestsData = new();

    #endregion


    #region Building

    public void InvalidateBuildingData()
    {
        foreach (var key in TrackBuildingData.Keys.ToArray())
        {
            SetBuildingDataState(key, false);
        }
    }

    public void SetBuildingDataState(string key, bool state)
    {
        TrackBuildingData.AddOrUpdate(
            key,
            state, (k, b) => state);
    }

    public bool GetBuildingDataState(string key)
    {
        var ok = TrackBuildingData.TryGetValue(key, out bool data);
        if (ok)
            return data;
        return false;
    }

    public ConcurrentDictionary<string, bool> TrackBuildingData = new();

    #endregion



    #region RequestedDocument

    public void InvalidateRequestedDocumentData()
    {
        foreach (var key in TrackRequestedDocumentData.Keys.ToArray())
        {
            SetRequestedDocumentDataState(key, false);
        }
    }

    public void SetRequestedDocumentDataState(string key, bool state)
    {
        TrackRequestedDocumentData.AddOrUpdate(
            key,
            state, (k, b) => state);
    }

    public bool GetRequestedDocumentDataState(string key)
    {
        var ok = TrackRequestedDocumentData.TryGetValue(key, out bool data);
        if (ok)
            return data;
        return false;
    }

    public ConcurrentDictionary<string, bool> TrackRequestedDocumentData = new();

    #endregion


    #endregion

    #region CACHE to pass models




    public MeasureResult CachedMeasureResult { get; set; }


    #endregion

    public override void OnDisposing()
    {
        UnsubscribeFromEvents();
    }







    private double _Keyboard;
    public double Keyboard
    {
        get { return _Keyboard; }
        set
        {
            if (_Keyboard != value)
            {
                if (value < 0)
                    value = 0.0;
                _Keyboard = value;
                OnPropertyChanged();
                OnPropertyChanged("BottomSafeInsetsOrKeyboardWIthTabs");
                OnPropertyChanged("BottomSafeInsetsOrKeyboard");
                OnPropertyChanged("PaddingBottomTabsAndSafeInsets");
                OnPropertyChanged("KeyboardOffset");
            }
        }
    }

    public double BottomSafeInsetsOrKeyboardWIthTabs
    {
        get
        {
            var value = BottomTabsUnderPadding;// for translucent ios tabbar
                                               //if (Device.RuntimePlatform == Device.Android)
                                               //var value = 0;

            if (Keyboard > 0)
                return value + KeyboardOffset + Super.Screen.BottomInset;

            if (IsFullscreen) return 0;

            return value + PaddingBottom;
        }
    }


    public double BottomSafeInsetsOrKeyboard
    {
        get
        {
            if (Keyboard > 0)
                return KeyboardOffset;

            if (IsFullscreen) return 0;

            return PaddingBottom;
        }
    }



    private Queue<string> queueHideDicoBtn = new Queue<string>();

    public void HideDicoBtn()
    {
        var myId = Guid.NewGuid().ToString();
        queueHideDicoBtn.Enqueue(myId);
        OnPropertyChanged("DicoBtnVisible");
    }

    public void UnhideDicoBtn()
    {
        try
        {
            queueHideDicoBtn.Dequeue();
        }
        catch (Exception e)
        {
        }
        OnPropertyChanged("DicoBtnVisible");
    }

    public bool DicoBtnVisible
    {
        get
        {
            //todo
            return queueHideDicoBtn.Count == 0;
        }
    }

    private double _DicoBtnMoveY;
    public double DicoBtnMoveY
    {
        get { return _DicoBtnMoveY; }
        set
        {
            if (_DicoBtnMoveY != value)
            {
                _DicoBtnMoveY = value;
                OnPropertyChanged();
                OnPropertyChanged("DicoBtnOffsetY");
            }
        }
    }

    public double DicoBtnOffsetY
    {
        get
        {
            return BottomTabsUnderPadding + DicoBtnMoveY + PaddingBottom;
        }
    }





    private async void OnNotificationsReceived(object sender, List<Tree> tree)
    {
        var notification = tree.FirstOrDefault(x => x.Id == "shop");
        if (notification != null)
        {
            NotificationsShop = notification.Total;
        }
        else
            NotificationsShop = 0;

        notification = tree.FirstOrDefault(x => x.Id == "bills");
        if (notification != null)
        {
            NotificationsBills = notification.Total;
        }
        else
            NotificationsBills = 0;

        notification = tree.FirstOrDefault(x => x.Id == "people");
        if (notification != null)
        {
            NotificationsPeople = notification.Total;
        }
        else
            NotificationsPeople = 0;

        notification = tree.FirstOrDefault(x => x.Id == "chat");
        if (notification != null)
        {
            NotificationsChat = notification.Total;
        }
        else
        {
            NotificationsChat = 0;
        }

        notification = tree.FirstOrDefault(x => x.Id == "reqs");
        if (notification != null)
        {
            NotificationsReqs = notification.Total;
        }
        else
        {
            NotificationsReqs = 0;
        }

        notification = tree.FirstOrDefault(x => x.Id == "friends");
        if (notification != null)
        {
            NotificationsPeople += notification.Total;
        }

        notification = tree.FirstOrDefault(x => x.Id == "family");
        if (notification != null)
        {
            NotificationsFamily = notification.Total;
        }
        else
            NotificationsFamily = 0;

        notification = tree.FirstOrDefault(x => x.Id == "system");
        if (notification != null)
        {
            NotificationsSystem = notification.Total;
        }
        else
            NotificationsSystem = 0;

        notification = tree.FirstOrDefault(x => x.Id == "tech");
        if (notification != null)
        {
            NotificationsSupport = notification.Total;
        }
        else
        {
            NotificationsSupport = 0;
        }

        notification = tree.FirstOrDefault(x => x.Id == "wordsets");
        if (notification != null)
        {
            NotificationsWordSets = notification.Total;
        }
        else
        {
            NotificationsWordSets = 0;
        }


        notification = tree.FirstOrDefault(x => x.Id == "profile");
        if (notification != null)
        {
            NotificationsProfile = notification.Total;
        }
        else
        {
            NotificationsProfile = 0;
        }

        //#if DEBUG
        //                NotificationsSupport = 1;
        //#endif

        NotificationsSystem += NotificationsProfile;
        NotificationsSystem += NotificationsSupport;

        Notifications = tree;
    }



    bool StartupFinished { get; set; }

    public void PopToRoot()
    {
        if (Shell.Navigation.NavigationStack.Count > 1 && StartupFinished)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                await Shell.Navigation.PopToRootAsync(false);
            });
        }
    }

    private void OnTabReselected(object sender, IndexArgs e)
    {
        //this can be called at startup (xamarin bug) so need to lock it for some time..
        PopToRoot();
    }



    //-------------------------------------------------------------
    // FocusedElement
    //-------------------------------------------------------------
    private const string nameFocusedElement = "FocusedElement";

    public static readonly BindableProperty FocusedElementProperty = BindableProperty.Create(nameFocusedElement,
        typeof(VisualElement), typeof(NavigationViewModel), null, BindingMode.TwoWay);
    public VisualElement FocusedElement
    {
        get { return (VisualElement)GetValue(FocusedElementProperty); }
        set { SetValue(FocusedElementProperty, value); }
    }



    #region NOTIFICATIONS


    public ICommand CommandNotifications
    {
        get
        {
            return new Command(async (object context) =>
            {
                throw new NotImplementedException();
                //var page = new PageListNotifications();
                //App.OpenPage(page);
            });
        }
    }

    private int _NotificationsWordSets;
    public int NotificationsWordSets
    {
        get { return _NotificationsWordSets; }
        set
        {
            if (_NotificationsWordSets != value)
            {
                _NotificationsWordSets = value;
                OnPropertyChanged();
            }
        }
    }


    private int _NotificationsSupport;
    public int NotificationsSupport
    {
        get { return _NotificationsSupport; }
        set
        {
            if (_NotificationsSupport != value)
            {
                _NotificationsSupport = value;
                OnPropertyChanged();
            }
        }
    }

    private int _NotificationsProfile;
    public int NotificationsProfile
    {
        get { return _NotificationsProfile; }
        set
        {
            if (_NotificationsProfile != value)
            {
                _NotificationsProfile = value;
                OnPropertyChanged();
            }
        }
    }


    private int _NotificationsBills;
    public int NotificationsBills
    {
        get { return _NotificationsBills; }
        set
        {
            if (_NotificationsBills != value)
            {
                _NotificationsBills = value;
                OnPropertyChanged();
            }
        }
    }

    private int _notificationsShop;
    public int NotificationsShop
    {
        get { return _notificationsShop; }
        set
        {
            if (_notificationsShop != value)
            {
                _notificationsShop = value;
                OnPropertyChanged();
            }
        }
    }

    private int _NotificationsFamily;
    public int NotificationsFamily
    {
        get { return _NotificationsFamily; }
        set
        {
            if (_NotificationsFamily != value)
            {
                _NotificationsFamily = value;
                OnPropertyChanged();
            }
        }
    }


    private int _NotificationsChat;
    public int NotificationsChat
    {
        get { return _NotificationsChat; }
        set
        {
            if (_NotificationsChat != value)
            {
                _NotificationsChat = value;
                OnPropertyChanged();
            }
        }
    }

    private int _NotificationsReqs;
    public int NotificationsReqs
    {
        get { return _NotificationsReqs; }
        set
        {
            if (_NotificationsReqs != value)
            {
                _NotificationsReqs = value;
                OnPropertyChanged();
            }
        }
    }


    private int _NotificationsPeople;
    public int NotificationsPeople
    {
        get { return _NotificationsPeople; }
        set
        {
            if (_NotificationsPeople != value)
            {
                _NotificationsPeople = value;
                OnPropertyChanged();
            }
        }
    }

    private int _NotificationsSystem;
    public int NotificationsSystem
    {
        get { return _NotificationsSystem; }
        set
        {
            if (_NotificationsSystem != value)
            {
                _NotificationsSystem = value;
                OnPropertyChanged();
            }
        }
    }

    //-------------------------------------------------------------
    // Notifications
    //-------------------------------------------------------------
    private const string nameNotifications = "Notifications";
    public static readonly BindableProperty NotificationsProperty = BindableProperty.Create(nameNotifications, typeof(List<Tree>), typeof(NavigationViewModel), new List<Tree>()); //, BindingMode.TwoWay
    public List<Tree> Notifications
    {
        get { return (List<Tree>)GetValue(NotificationsProperty); }
        set { SetValue(NotificationsProperty, value); }
    }


    #endregion


    //-------------------------------------------------------------
    // GoBackCheckDenied
    //-------------------------------------------------------------
    private const string nameGoBackCheckDenied = "GoBackCheckDenied";
    public static readonly BindableProperty GoBackCheckDeniedProperty = BindableProperty.Create(nameGoBackCheckDenied, typeof(bool), typeof(NavigationViewModel), false); //, BindingMode.TwoWay
    public bool GoBackCheckDenied
    {
        get { return (bool)GetValue(GoBackCheckDeniedProperty); }
        set { SetValue(GoBackCheckDeniedProperty, value); }
    }

    //-------------------------------------------------------------
    // CommandGoBack
    //-------------------------------------------------------------
    private const string nameCommandGoBack = "CommandGoBack";
    public static readonly BindableProperty CommandGoBackProperty = BindableProperty.Create(nameCommandGoBack, typeof(ICommand), typeof(NavigationViewModel), null); //, BindingMode.TwoWay
    public ICommand CommandGoBack
    {
        get { return (ICommand)GetValue(CommandGoBackProperty); }
        set { SetValue(CommandGoBackProperty, value); }
    }



    public void ShowMenu(bool open = true)
    {

        MainThread.BeginInvokeOnMainThread(async () =>
        {
            // Update the UI
            var mainPage = App.Instance.MainPage;
            if (mainPage is FlyoutPage)
            {
                ((FlyoutPage)mainPage).IsPresented = open;
            }
            else
            {
                await _ui.Alert(ResStrings.Error, "NavigationRoot is not MasterDetail");
            }
        });

    }

    public void GoBack(bool animate = true)
    {

        //App.Instance.Shell.GoToAsync("..");

        //return;

        if (Shell != null)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {

                if (Shell.Navigation.ModalStack.Count > 0)
                {
                    await Shell.Navigation.PopModalAsync(animate);
                }
                else
                {
                    await Shell.Navigation.PopAsync(animate);
                }

                //if (Device.RuntimePlatform == Device.iOS)
                //{
                //    var topPage = NavigationRoot.Navigation.NavigationStack.LastOrDefault();
                //    var modern = topPage as ModernEnhancedPage;
                //    if (modern!=null)
                //    {
                //   //     modern.CallOnAppearing();
                //    }
                //}

            });
        }


    }

    public ICommand CommandNavbarMenu
    {
        get
        {
            return new Command(async (object context) =>
            {
                if (NoMenu)
                    return;
                ShowMenu();
            });
        }
    }



    public ICommand CommandSmartGoBack
    {
        get
        {
            return new Command(async (object context) =>
            {
                GoBack();
            });
        }
    }

    public ICommand CommandNavbarGoBack
    {
        get
        {
            return new Command(async (object context) =>
            {
                if (GoBackCheckDenied)
                    return;

                GoBack();

                //else
                //{
                //    if (!NoMenu)
                //        App.ShowMenu();
                //}
            });
        }
    }

    public ICommand CommandTabGoBack
    {
        get
        {
            return new Command(async (object context) =>
            {
                if (GoBackCheckDenied)
                    return;
                throw new NotImplementedException();
                //App.ViewSwitcher.PopTab();
                //   App.ViewSwitcher.PopTab();
            });
        }
    }

    public ICommand CommandNavbarFilter
    {
        get
        {
            return new Command(async (object context) =>
            {
                if (NoMenu)
                    return;


            });
        }
    }

    #region SEARCH

    //-------------------------------------------------------------
    // CommandOnSearch
    //-------------------------------------------------------------
    private const string nameCommandOnSearch = "CommandOnSearch";
    public new static readonly BindableProperty CommandOnSearchProperty = BindableProperty.Create(nameCommandOnSearch, typeof(ICommand), typeof(NavigationViewModel), null); //, BindingMode.TwoWay
    public new ICommand CommandOnSearch
    {
        get { return (ICommand)GetValue(CommandOnSearchProperty); }
        set { SetValue(CommandOnSearchProperty, value); }
    }


    public ICommand CommandSearch
    {
        get
        {
            return new Command(async (object context) =>
            {


            });
        }
    }



    #endregion

    #region LAYOUT

    private DeviceRotation _orientationRotation;
    public DeviceRotation Orientation
    {
        get { return _orientationRotation; }
        set
        {
            if (_orientationRotation != value)
            {
                _orientationRotation = value;
                OnPropertyChanged();
            }
        }
    }

    private void OnRotation(object sender, RotationEventArgs e)
    {
        Orientation = e.Orientation;

        UpdateControls();
    }


    public double NavBarPadding
    {
        get
        {
            return StatusBarHeightRequest + NavBarHeightRequest;
        }
    }



    /// <summary>
    /// call from page upon orientation changed
    /// </summary>
    public void UpdateControls()
    {

        //if (Display.IsFrameless)
        //{
        //    StatusOpacity = 0;
        //}
        //else
        //{
        //    StatusOpacity = 1;
        //}


        StatusBarHeightRequest = CalculateStatusBar(Orientation);
        NavBarHeightRequest = CalculateNavBar(Orientation);
        ClippedNavBarHeightRequest = CalculateClippedNavBar(Orientation);
        PaddingHeightRequest = NavBarHeightRequest + StatusBarHeightRequest;
        PaddingClippedHeightRequest = ClippedNavBarHeightRequest + StatusBarHeightRequest;

        BottomTabsHeightRequest = Super.BottomTabsHeight;

        BottomTabsUnderPadding = BottomTabsHeightRequest - 2; //for shadow

        NavAndTabsMargin = new Thickness(0, PaddingHeightRequest, 0, 0);

        OnPropertyChanged("DicoBtnOffsetY");

        //if (!Display.Initialized && !lockTimer)
        //{
        //    lockTimer = true;
        //    //todo
        //    Device.StartTimer(TimeSpan.FromMilliseconds(100), () =>
        //    {
        //        //System commands
        //        if (Display.Initialized)
        //        {
        //            UpdateControls();
        //        }
        //        else
        //        {
        //            return true;
        //        }
        //        // false - Don't repeat the timer 

        //        lockTimer = false;
        //        return false;
        //    });
        //}

    }

    private double _StatusOpacity;
    public double StatusOpacity
    {
        get { return _StatusOpacity; }
        set
        {
            if (_StatusOpacity != value)
            {
                _StatusOpacity = value;
                OnPropertyChanged();
            }
        }
    }


    private bool _IsFullscreen;
    public bool IsFullscreen
    {
        get { return _IsFullscreen; }
        set
        {
            if (_IsFullscreen != value)
            {
                _IsFullscreen = value;
                OnPropertyChanged();
                OnPropertyChanged("BottomTabsHeightRequest");
                OnPropertyChanged("BottomTabsUnderPadding");
                OnPropertyChanged("PaddingBottom");
                OnPropertyChanged("PaddingBottomTabsAndSafeInsets");
                OnPropertyChanged("BottomSafeInsetsOrKeyboard");
            }
        }
    }

    private double _BottomTabsHeightRequest;
    public double BottomTabsHeightRequest
    {
        get
        {
            if (IsFullscreen) return 0;

            return _BottomTabsHeightRequest;
        }
        set
        {
            if (_BottomTabsHeightRequest != value)
            {
                _BottomTabsHeightRequest = value;
                OnPropertyChanged();
            }
        }
    }

    private double _BottomTabsUnderPadding;
    public double BottomTabsUnderPadding
    {
        get
        {
            if (IsFullscreen) return 0;
            return _BottomTabsUnderPadding;
        }
        set
        {
            if (_BottomTabsUnderPadding != value)
            {
                _BottomTabsUnderPadding = value;
                OnPropertyChanged();
                OnPropertyChanged("PaddingBottomTabsAndSafeInsets");
                OnPropertyChanged("KeyboardOffset");
            }
        }
    }

    private double _PaddingBottom;
    public double PaddingBottom
    {
        get
        {
            if (IsFullscreen) return 0;
            return _PaddingBottom;
        }
        set
        {
            if (_PaddingBottom != value)
            {
                _PaddingBottom = value;
                OnPropertyChanged();
                OnPropertyChanged("PaddingBottomTabsAndSafeInsets");
                OnPropertyChanged("BottomSafeInsetsOrKeyboard");
                OnPropertyChanged("KeyboardOffset");
            }
        }
    }

    public double KeyboardOffset
    {
        get
        {
            var ret = 0.0;

            if (Keyboard > 0)
            {
                ret = Keyboard;
                //    if (Device.RuntimePlatform == Device.iOS)
                ret -= (PaddingBottom + BottomTabsHeightRequest);
            }

            return ret;
        }
    }

    public double PaddingBottomTabsAndSafeInsets
    {
        get
        {
            if (Keyboard > 0)
                return KeyboardOffset + BottomTabsUnderPadding; ;

            if (IsFullscreen)
                return 0;

            return PaddingBottom + BottomTabsUnderPadding;
        }
    }


    private double _PaddingHeightRequest;
    public double PaddingHeightRequest
    {
        get { return _PaddingHeightRequest; }
        set
        {
            if (_PaddingHeightRequest != value)
            {
                _PaddingHeightRequest = value;
                OnPropertyChanged();
            }
        }
    }

    private double _PaddingClippedHeightRequest;
    public double PaddingClippedHeightRequest
    {
        get { return _PaddingClippedHeightRequest; }
        set
        {
            if (_PaddingClippedHeightRequest != value)
            {
                _PaddingClippedHeightRequest = value;
                OnPropertyChanged();
            }
        }
    }

    private double _StatusBarHeightRequest;
    public double StatusBarHeightRequest
    {
        get { return _StatusBarHeightRequest; }
        set
        {
            if (_StatusBarHeightRequest != value)
            {
                _StatusBarHeightRequest = value;
                OnPropertyChanged();
                OnPropertyChanged("NavBarPadding");
            }
        }
    }





    private double _NavBarHeightRequest;
    public double NavBarHeightRequest
    {
        get { return _NavBarHeightRequest; }
        set
        {
            if (_NavBarHeightRequest != value)
            {
                _NavBarHeightRequest = value;
                OnPropertyChanged();
                OnPropertyChanged("NavBarPadding");
            }
        }
    }


    private double _ClippedNavBarHeightRequest;
    public double ClippedNavBarHeightRequest
    {
        get { return _ClippedNavBarHeightRequest; }
        set
        {
            if (_ClippedNavBarHeightRequest != value)
            {
                _ClippedNavBarHeightRequest = value;
                OnPropertyChanged();
            }
        }
    }


    public double CalculateNavBar(DeviceRotation Orientation)
    {
        if (HideNavigation)
            return 0.0;

        double value = Super.NavBarHeight;
        return value;
    }

    private Thickness _NavAndTabsMargin;
    public Thickness NavAndTabsMargin
    {
        get { return _NavAndTabsMargin; }
        set
        {
            if (_NavAndTabsMargin != value)
            {
                _NavAndTabsMargin = value;
                OnPropertyChanged();
            }
        }
    }




    public double CalculateClippedNavBar(DeviceRotation Orientation)
    {
        if (HideNavigation)
            return 0.0;

        double value = 110;
        return value;
    }

    protected double CalculateStatusBar(DeviceRotation Orientation)
    {


        var value = Super.StatusBarHeight;

        //if (Manager.Screen.TopInset > 0 && Display.IsFrameless)
        //{
        //    if (Orientation == PageInsideShell.Rotation.Landscape)
        //    {
        //        value = 0;
        //    }
        //    else
        //    {
        //        value = Manager.UI.StatusBarHeight + Manager.Screen.TopInset;
        //    }
        //}
        //else
        //{
        //    //use standart height
        //}

        if (Orientation == DeviceRotation.Landscape)
        {

        }
        else
        {

            if (Super.Screen.BottomInset > 0)
                PaddingBottom = Super.Screen.BottomInset + 2;
            else
                PaddingBottom = 0;


        }

        return value;
    }

    private bool lockTimer;

    #endregion




    protected override void OnPropertyChanged([CallerMemberName] string propertyName = "")
    {
        base.OnPropertyChanged(propertyName);

        switch (propertyName)
        {
        //property changed
        case nameHideNavigation:



        break;

        case nameNoMenu:

        NoBell = true;
        NoFilter = true;

        break;

        }

    }



    //-------------------------------------------------------------
    // HasSearch
    //-------------------------------------------------------------
    private const string nameHasSearch = "HasSearch";
    public static readonly BindableProperty HasSearchProperty = BindableProperty.Create(nameHasSearch, typeof(bool), typeof(NavigationViewModel), false); //, BindingMode.TwoWay
    public bool HasSearch
    {
        get { return (bool)GetValue(HasSearchProperty); }
        set { SetValue(HasSearchProperty, value); }
    }

    //-------------------------------------------------------------
    // ShowLogo
    //-------------------------------------------------------------
    private const string nameShowLogo = "ShowLogo";
    public static readonly BindableProperty ShowLogoProperty = BindableProperty.Create(nameShowLogo, typeof(bool), typeof(NavigationViewModel), false); //, BindingMode.TwoWay
    public new bool ShowLogo
    {
        get { return (bool)GetValue(ShowLogoProperty); }
        set { SetValue(ShowLogoProperty, value); }
    }



    //-------------------------------------------------------------
    // HideNavigation
    //-------------------------------------------------------------
    private const string nameHideNavigation = "HideNavigation";
    public static readonly BindableProperty HideNavigationProperty = BindableProperty.Create(nameHideNavigation, typeof(bool), typeof(NavigationViewModel), false); //, BindingMode.TwoWay
    public bool HideNavigation
    {
        get { return (bool)GetValue(HideNavigationProperty); }
        set { SetValue(HideNavigationProperty, value); }
    }

    //-------------------------------------------------------------
    // NoMenu
    //-------------------------------------------------------------
    private const string nameNoMenu = "NoMenu";
    public static readonly BindableProperty NoMenuProperty = BindableProperty.Create(nameNoMenu, typeof(bool), typeof(NavigationViewModel), false); //, BindingMode.TwoWay
    public bool NoMenu
    {
        get { return (bool)GetValue(NoMenuProperty); }
        set { SetValue(NoMenuProperty, value); }
    }


    //-------------------------------------------------------------
    // HasNewNotifications
    //-------------------------------------------------------------
    private const string nameHasNewNotifications = "HasNewNotifications";
    public static readonly BindableProperty HasNewNotificationsProperty = BindableProperty.Create(nameHasNewNotifications, typeof(bool), typeof(NavigationViewModel), false); //, BindingMode.TwoWay
    public bool HasNewNotifications
    {
        get { return (bool)GetValue(HasNewNotificationsProperty); }
        set { SetValue(HasNewNotificationsProperty, value); }
    }



    //-------------------------------------------------------------
    // NewNotificationsCount
    //-------------------------------------------------------------
    private const string nameNewNotificationsCount = "NewNotificationsCount";
    public static readonly BindableProperty NewNotificationsCountProperty = BindableProperty.Create(nameNewNotificationsCount, typeof(int), typeof(NavigationViewModel), 0); //, BindingMode.TwoWay
    public int NewNotificationsCount
    {
        get { return (int)GetValue(NewNotificationsCountProperty); }
        set { SetValue(NewNotificationsCountProperty, value); }
    }



    //-------------------------------------------------------------
    // VisiblePage
    //-------------------------------------------------------------
    private const string nameVisiblePage = "VisiblePage";
    public static readonly BindableProperty VisiblePageProperty = BindableProperty.Create(nameVisiblePage, typeof(string), typeof(NavigationViewModel), null); //, BindingMode.TwoWay
    public string VisiblePage
    {
        get { return (string)GetValue(VisiblePageProperty); }
        set { SetValue(VisiblePageProperty, value); }
    }

    #region LEGACY

    //-------------------------------------------------------------
    // NoBell
    //-------------------------------------------------------------
    private const string nameNoBell = "NoBell";
    public static readonly BindableProperty NoBellProperty = BindableProperty.Create(nameNoBell, typeof(bool), typeof(NavigationViewModel), false); //, BindingMode.TwoWay
    public bool NoBell
    {
        get { return (bool)GetValue(NoBellProperty); }
        set { SetValue(NoBellProperty, value); }
    }


    //-------------------------------------------------------------
    // NoFilter
    //-------------------------------------------------------------
    private const string nameNoFilter = "NoFilter";
    public static readonly BindableProperty NoFilterProperty = BindableProperty.Create(nameNoFilter, typeof(bool), typeof(NavigationViewModel), false); //, BindingMode.TwoWay
    public bool NoFilter
    {
        get { return (bool)GetValue(NoFilterProperty); }
        set { SetValue(NoFilterProperty, value); }
    }


    #endregion


    private bool _ShowGoBack;
    public bool ShowGoBack
    {
        get { return _ShowGoBack; }
        set
        {
            if (_ShowGoBack != value)
            {
                _ShowGoBack = value;
                OnPropertyChanged();
            }
        }
    }

    private bool _CanGoBack = true;
    public bool CanGoBack
    {
        get { return _CanGoBack; }
        set
        {
            if (_CanGoBack != value)
            {
                _CanGoBack = value;
                OnPropertyChanged();
            }
        }
    }




    public void SetDrawerMenuIcon()
    {
        //DrawerIconSource = FaPro.Bars;

        DrawerIconSource = "menu.svg";

    }

    private string _DrawerIconSource;
    private readonly IServiceProvider _services;

    public string DrawerIconSource
    {
        get { return _DrawerIconSource; }
        set
        {
            if (_DrawerIconSource != value)
            {
                _DrawerIconSource = value;
                OnPropertyChanged();
            }
        }
    }

    public string BuildDesc
    {
        get
        {
            return $"Сборка {this.Build} от {this.BuildTime:g}";
        }
    }




}
