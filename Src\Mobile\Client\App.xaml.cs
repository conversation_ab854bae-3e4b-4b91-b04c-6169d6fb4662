﻿using AppoMobi.Specials.Localization;
using DrawnUi.Models;
using NetTopologySuite.Index.HPRtree;
using Racebox.SDK;
using Racebox.Shared.Strings;
using Racebox.ViewModels.Navigation;
using System.Globalization;
using System.Reflection;
using System.Runtime.InteropServices;
using AppoMobi.Framework;
using AppoMobi.Framework.Maui.Interfaces;
using AppoMobi.Framework.Maui.Models;


namespace Racebox;

public partial class App : Application
{


    void Bootstrap()
    {
        SetupCulture(new EnabledLanguage[]
        {
            new EnabledLanguage
            {
                Code = "en", Display = "English"
            },
            new EnabledLanguage
            {
                Code = "ru", Display = "Русский"
            },
        });

        _userManager.LoginOffline();

        //Task.Run(async () => //async in background
        //{
        //    await _userManager.InsureUserIsInDatabase();
        //}).ConfigureAwait(false);

        //MainPage = new NavigationPage(new UI.Dev.PageDev()); //AppShell();

        //App.Instance.PlaySoundFile("welcome.mp3");

        //FAST SHELL
        Presentation.Initialize(AppRoutes.RootDefault);

    }



    //todo move this to some media service
    public void PlaySoundFile(string filename)
    {
        try
        {
            if (_userManager.User.Options.Sound)
            {
                Task.Run(() =>
                {
                    App.Native.PlaySoundFromAssets(filename);
                }).ConfigureAwait(false);
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
        }


    }





    public static void OpenPage(Page page)
    {
        if (App.Instance.MainPage is AppFastShell shell)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                await shell.Navigation.PushAsync(page);
            });
        }
    }

    public static void SelectAction(IEnumerable<SelectableAction> options, string title)
    {
        MainThread.BeginInvokeOnMainThread(async () =>
        {
            var selected = await App.Instance.UI.PresentSelection(options, title) as SelectableAction;
            selected?.Action?.Invoke();
        });
    }

    public static IEnumerable<SelectableAction> GenerateOptionsForEnum<T>(Action<T> callback) where T : Enum
    {
        var enumType = typeof(T);
        var variants = LocalizedEnumConverter.GetValues(enumType);
        List<SelectableAction> options = new();
        foreach (var variant in variants)
        {
            options.Add(
                new SelectableAction
                {
                    Id = variant.Value,
                    Title = LocalizedEnumConverter.ConvertToString(variant.Key),
                    Action = () =>
                    {
                        callback((T)variant.Key);
                    }
                });
        }
        return options;
    }

    #region LOCALIZATION

    public class EnabledLanguage
    {
        public string Code { get; set; }
        public string Display { get; set; }
    }

    public static IEnumerable<EnabledLanguage> EnabledLanguages;

    public void SetLocale(string lang)
    {
        ResStrings.Culture = CultureInfo.CreateSpecificCulture(lang);
        Thread.CurrentThread.CurrentCulture = ResStrings.Culture;
        Thread.CurrentThread.CurrentUICulture = ResStrings.Culture;
        SelectedLang = lang;
        _settings.Set("Lang", lang);
    }



    public void SetupCulture(IEnumerable<EnabledLanguage> allowed)
    {
        EnabledLanguages = allowed;

        var customLang = _settings.Get("Lang", string.Empty);
        if (string.IsNullOrEmpty(customLang))
        {
            //looks like forst run, so set current device culture
            var current = Thread.CurrentThread.CurrentCulture.TwoLetterISOLanguageName.ToLower();
            if (allowed.All(a => a.Code != current))
            {
                current = allowed.First().Code;
            }
            customLang = current;
        }
        SetLocale(customLang);
    }

    public static string SelectedLang { get; protected set; }

    public void ChangeLanguage(string language)
    {
        SetLocale(language);
        Bootstrap();
    }

    /// <summary>
    /// Invoke this on UI thread only
    /// </summary>
    /// <returns></returns>
    public async Task SelectLanguage()
    {
        List<SelectableAction> options = new();
        foreach (var enabledLanguage in EnabledLanguages)
        {
            var culture = CultureInfo.CreateSpecificCulture(enabledLanguage.Code);
            var option = new SelectableAction
            {
                Id = enabledLanguage.Code,
                Title = enabledLanguage.Display.ToTitleCase(),
                Action = () =>
                {
                    ChangeLanguage(enabledLanguage.Code);
                }
            };
            options.Add(option);
        }

        var selected = await _ui.PresentSelection(options, ResStrings.Language) as SelectableAction;
        selected?.Action?.Invoke();
    }

    #endregion

    protected override void OnSleep()
    {
        base.OnSleep();


        MainThread.BeginInvokeOnMainThread(() =>
        {
            DeviceDisplay.Current.KeepScreenOn = false;
        });


        _watchIsSleeping ??= new RestartingTimer<object>(
            3000,
            (context) =>
            {
                DeviceViewModel.TurnOffMeasuring();
            });
        _watchIsSleeping.Kick(null);
    }

    private RestartingTimer<object> _watchIsSleeping;

    protected override void OnStart()
    {
        base.OnStart();

        Tasks.StartDelayed(TimeSpan.FromMilliseconds(3000), () =>
        {
            Dispatcher.Dispatch(() =>
            {
                DeviceDisplay.Current.KeepScreenOn = true;
            });
        });
    }

    protected override void OnResume()
    {
        base.OnResume();

        Dispatcher.Dispatch(() =>
        {
            DeviceDisplay.Current.KeepScreenOn = true;
        });

    }

    public IServiceProvider Services { get; }

    public App(IServiceProvider services, UserManager userManager, IAppStorage prefs)
    {
        _settings = prefs;

        _userManager = userManager;

        Services = services;

        //Super.Services = services;

        InitializeComponent();

        /*
        var source = "Resources/Custom.xaml"; //notice intentionnaly missing "/" before
        var assembly = this.GetType().Assembly; //or other location..
        var fullUri = $"maui:///{source};assembly={assembly.GetName()}"; // ..and we add that missing "/" here
        var uri = new Uri(fullUri, UriKind.Absolute);
        var resource = new ResourceDictionary();
        resource.SetAndLoadSource(uri, source, assembly, null);
        Resources.MergedDictionaries.Add(resource);
        var check = Resources.Get<Color>("XXX");
        */

        //bonus
        // var xamlResources = assembly.GetCustomAttributes<XamlResourceIdAttribute>();
        //var type = xamlResources.FirstOrDefault(x => x.Path == source);
        //var myOwn = (ResourceDictionary)Activator.CreateInstance(type);



        //Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(1000), async () =>
        //{

        //	return false;
        //});

        Bootstrap();
    }

    private void OnScaleChanged(object sender, DisplayDensityChangedEventArgs e)
    {
        Super.Screen.Density = Microsoft.Maui.Devices.DeviceDisplay.Current.MainDisplayInfo.Density;
    }

    private void OnSizeChanged(object sender, EventArgs e)
    {
        var check = DeviceDisplay.Current.MainDisplayInfo.Width;
        var x = (sender as Window).X;
    }


    #region TEXT TO SPEECH

    public List<Locale> Locales;

    private bool _loadingLocales;

    public async Task LoadLocales()
    {
        if (Locales == null)
        {
            var list = await TextToSpeech.GetLocalesAsync();
            Locales = list.ToList();
        }
    }

    /// <summary>
    /// TEXT TO SPEECH
    /// </summary>
    /// <param name="text"></param>
    /// <param name="language"></param>
    /// <returns></returns>
    public async Task SpeakAsync(string text, string language)
    {
        if (Locales == null && !_loadingLocales)
        {
            await LoadLocales();
        }

        while (Locales == null)
        {
            await Task.Delay(30);
        }

        var locale = Locales.FirstOrDefault(x => x.Language.Contains(language));

        var settings = new SpeechOptions()
        {
            Volume = 1.0f,
            Pitch = 1.0f,
            Locale = locale
        };

        await TextToSpeech.SpeakAsync(text, settings);
    }

    #endregion


    protected override Window CreateWindow(IActivationState activationState)
    {
        var window = base.CreateWindow(activationState);

        #region UI TWEAKS

        Super.ColorAccent = Color.FromArgb("FF6A47");
        Super.ColorPrimary = Color.FromArgb("343C45");

        var nav = App.Instance.MainPage as NavigationPage;
        if (App.Instance.RequestedTheme == AppTheme.Dark)
        {
#if ANDROID

            Super.SetNavigationBarColor(Color.Parse("#11161D"), Color.Parse("#11161D"), true);

#endif
            if (nav != null)
            {
                nav.BarBackgroundColor = Colors.Black;
                nav.BarTextColor = Colors.White;
            }
        }
        else
        {

#if ANDROID

            Super.SetNavigationBarColor(Color.Parse("#11161D"), Color.Parse("#11161D"), true);

            //Super.SetNavigationBarColor(Colors.White, Colors.Transparent, false);
            //Super.Native?.SetNavigationBarColor(AppResources.Get<Color>("ColorPrimary"), false);
#endif
            if (nav != null)
            {
                nav.BarBackgroundColor = Colors.Black;
                nav.BarTextColor = Colors.White;
                //nav.BarBackgroundColor = Colors.White;
                //nav.BarTextColor = Colors.Black;
            }
        }

        #endregion

        return window;
    }




    public static DateTime GetLinkerTime(Assembly assembly)
    {
        const string BuildVersionMetadataPrefix = "+build";

        var attribute = assembly.GetCustomAttribute<AssemblyInformationalVersionAttribute>();
        if (attribute?.InformationalVersion != null)
        {
            var value = attribute.InformationalVersion;
            var index = value.IndexOf(BuildVersionMetadataPrefix);
            if (index > 0)
            {
                value = value.Right(value.Length - (index + BuildVersionMetadataPrefix.Length));
                return DateTime.ParseExact(value, "yyyy-MM-ddTHH:mm:ss:fffZ", CultureInfo.InvariantCulture);
            }
        }

        return default;
    }

    private IInAppMessager _messager;
    public IInAppMessager Messager
    {
        get
        {
            if (_messager == null)
                _messager = this.Services.GetService<IInAppMessager>();
            return _messager;
        }
    }

    private NavigationViewModel _navigationVm;
    public NavigationViewModel Presentation
    {
        get
        {
            if (_navigationVm == null)
                _navigationVm = this.Services.GetService<NavigationViewModel>();
            return _navigationVm;
        }
    }

    private IUIAssistant _ui;
    public IUIAssistant UI
    {
        get
        {
            if (_ui == null)
                _ui = this.Services.GetService<IUIAssistant>();
            return _ui;
        }
    }


    public static App Instance => App.Current as App;


    static NativeTasks _native;
    private readonly IAppStorage _settings;
    private readonly UserManager _userManager;


    private RaceBoxDeviceViewModel DeviceViewModel
    {
        get
        {
            return Services.GetService<RaceBoxDeviceViewModel>();
        }
    }

    public static NativeTasks Native
    {
        get
        {
            if (_native == null)
            {
                _native = new NativeTasks();
            }
            return _native;
        }
    }

    public static async Task<IEnumerable<TRet>> RunMultipleTasksAsync<TRet>(params Task<TRet>[] tasks)
    {
        return await Task.WhenAll(tasks);
    }

}

