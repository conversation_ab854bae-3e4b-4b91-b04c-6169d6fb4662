using Mopups.Services;

namespace Racebox.Views.Popups;

public partial class FirmwarePopup
{
    public FirmwarePopup()
    {
        InitializeComponent();
    }

    protected override void OnHandlerChanged()
    {
        base.OnHandlerChanged();

        if (Handler == null)
        {
            MainCanvas?.Dispose();
        }
    }

    private void SkiaMarkdownLabel_OnLinkTapped(object sender, string e)
    {
        if (TouchEffect.CheckLockAndSet())
            return;


        MainThread.BeginInvokeOnMainThread(async () =>
        {
            App.Native.OpenLink(e);
        });


    }

    private void SkiaButton_OnTapped(object sender, ControlTappedEventArgs controlTappedEventArgs)
    {
        MainThread.BeginInvokeOnMainThread(async () =>
        {
            try
            {
                await MopupService.Instance.PopAsync(true);

            }
            catch (Exception exception)
            {
                Console.WriteLine(exception);
            }
        });
    }
}