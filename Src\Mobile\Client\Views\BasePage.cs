﻿using AppoMobi.Maui.Navigation;
using DrawnUi;
using Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;
using System.Diagnostics;

namespace Racebox.Views
{
    public class BasePage : ContentPage
    {


        public BasePage()
        {
            On<Microsoft.Maui.Controls.PlatformConfiguration.iOS>().SetUseSafeArea(false);
        }

        //todo add handler
        public void KeyboardResized(double keyboardSize)
        {
            System.Diagnostics.Debug.WriteLine($"[ModernEnhancedPage] Keyboard {keyboardSize} for {Title}");
            OnKeyboardResized(keyboardSize);
            //OnKeyboardToggled(state);
        }
        public virtual double OnKeyboardResized(double size)
        {
            return size;
        }

        #region STANDART

        public virtual void OnPageWasRotated()
        {

        }

        private double width { get; set; } = 0;
        private double height { get; set; } = 0;
        private bool _resizing { get; set; } = false;

        protected override void OnSizeAllocated(double _width, double _height)
        {
            if (_resizing)
            {
                base.OnSizeAllocated(_width, _height);
                return;
            }
            _resizing = true;

            //InsetsLeft = Display.LeftInsets;
            //InsetsRight = Display.RightInsets;
            //InsetsBottom = Display.BottomInsets;
            //InsetsTop = Display.TopInsets;

            var width = Bounds.Width;
            var height = Bounds.Height;

            if (this.width != width || this.height != height)
            {
                this.width = width;
                this.height = height;
                var layoutDesc = "";
                if (width > 0)
                {
                    layoutDesc = width > height ? "Landscape" : "Portrait";
                    var args = new RotationEventArgs();
                    if (layoutDesc == "Portrait")
                    {
                        args.Orientation = DeviceRotation.Portrait;
                    }
                    else
                    {
                        args.Orientation = DeviceRotation.Landscape;
                    }
                    OrientationDesc = layoutDesc;
                    Orientation = args.Orientation;
                    OnPageWasRotated();
                    OnRotation?.Invoke(this, args);
                }
            }

            base.OnSizeAllocated(_width, _height);

            _resizing = false;
        }

        public String OrientationDesc { get; set; }
        public DeviceRotation Orientation { get; set; }


        public EventHandler<RotationEventArgs> OnRotation;





        private void ListenToNavigationBefore(object sender, ShellNavigatingEventArgs e)
        {
            OnNavigating(e);

            var shell = sender as Shell;


        }

        protected virtual void OnNavigating(ShellNavigatingEventArgs e)
        {

        }

        protected virtual void OnNavigated(ShellNavigatedEventArgs e)
        {

        }

        private void ListenToNavigationAfter(object sender, ShellNavigatedEventArgs e)
        {
            OnNavigated(e);
        }


        public string GetThreadId()
        {
            return Thread.CurrentThread.ManagedThreadId.ToString();
        }

        protected List<string> Threads = new List<string>();

        private bool initialized;

        protected override void OnHandlerChanged()
        {
            base.OnHandlerChanged();

#if IOS

            if (this.Handler != null)
                App.Native.LockOrientationPortrait(this, true);

#endif

        }

        protected override void OnAppearing()
        {

            if (!initialized)
            {
                initialized = true;

#if IOS



#elif ANDROID

                App.Native.LockOrientationPortrait(true);

#endif

                if (App.Instance.Presentation.Shell != null)
                {
                    App.Instance.Presentation.Shell.Navigated += ListenToNavigationAfter;
                    App.Instance.Presentation.Shell.Navigating += ListenToNavigationBefore;
                }
            }

            base.OnAppearing();
        }


        public void Dispose()
        {
            if (IsDisposed)
                return;

            IsDisposed = true;

            Debug.WriteLine($"Page disposed {this.GetType().Name}");

            OnDisposing();

            if (App.Instance.Presentation.Shell != null)
            {
                App.Instance.Presentation.Shell.Navigated -= ListenToNavigationAfter;
                App.Instance.Presentation.Shell.Navigating -= ListenToNavigationBefore;
            }

            this.Parent = null;

            Content?.DisposeControlAndChildren();
            Content = null;

            this.Handler?.DisconnectHandler();
        }

        public bool IsDisposed { get; protected set; }

        public virtual void OnDisposing()
        {
            var disposable = BindingContext as IDisposable;
            disposable?.Dispose();
        }


        #endregion

    }
}
