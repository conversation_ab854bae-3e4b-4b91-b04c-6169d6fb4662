﻿using Racebox.Shared;
using Racebox.ViewModels.Navigation;
using System.Collections.Concurrent;
using System.Runtime.CompilerServices;

namespace Racebox.ViewModels;

public class ProjectViewModel : BaseViewModel
{
    #region TAP LOCKS

    protected bool CheckLocked(string uid)
    {
        if (TapLocks.ContainsKey(uid))
        {
            return TapLocks[uid];
        }
        return false;
    }

    protected bool CheckLockAndSet([CallerMemberName] string uid = null, int ms = 2000)
    {
        if (CheckLocked(uid))
            return true;

        TapLocks[uid] = true;
        Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(ms), async () =>
        {
            TapLocks[uid] = false;
            return false;
        });

        return false;
    }

    public static ConcurrentDictionary<string, bool> TapLocks = new();

    #endregion

    public Command CommandAskQuestion => new Command(async () =>
    {
        if (CheckLockAndSet("CommandAskQuestion"))
            return;

#if ANDROID || IOS || MACCATALYST

        MainThread.BeginInvokeOnMainThread(async () =>
        {
            App.Native.OpenTelegram("racebox_chat");
        });

#endif
    });

    public Command CommandOpenSite => new Command(async () =>
    {
        if (CheckLockAndSet("CommandOpenSite"))
            return;


        MainThread.BeginInvokeOnMainThread(async () =>
        {
            App.Native.OpenLink(SharedSecrets.VendorSite);
        });


    });

    public string DisplayLanguage
    {
        get
        {
            var language = App.EnabledLanguages.First(x => x.Code == App.SelectedLang);
            return language.Display.ToTitleCase();
        }
    }

    public Command CommandSelectLanguage => new Command(async () =>
    {
        MainThread.BeginInvokeOnMainThread(async () =>
        {
            await App.Instance.SelectLanguage();
        });
    });


    public ProjectViewModel()
    {
        Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(1000), async () =>
        {
            OnSubscribing(true);
            return false;
        });
    }

    private NavigationViewModel _navigationVm;
    public NavigationViewModel NavbarModel
    {
        get
        {
            if (_navigationVm == null)
                _navigationVm = App.Instance.Services.GetService<NavigationViewModel>();
            return _navigationVm;
        }
    }


}