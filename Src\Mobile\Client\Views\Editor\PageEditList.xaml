﻿<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="Racebox.Views.PageEditList"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:interfaces="clr-namespace:Racebox.Interfaces"
    xmlns:partials="clr-namespace:Racebox.Views.Partials"
    xmlns:views="clr-namespace:Racebox.Views"
    xmlns:models="clr-namespace:AppoMobi.Framework.Maui.Models;assembly=AppoMobi.Framework.Maui"
    xmlns:racebox="clr-namespace:Racebox"
    xmlns:drawn="clr-namespace:Racebox.Views.Drawn"
    x:Name="ThisPage"
    Title="PageEditList"
    x:DataType="interfaces:ISupportsListEditor">

    <Grid
        HorizontalOptions="FillAndExpand"
        VerticalOptions="FillAndExpand">

        <!--  page background with gradient and frame  -->
        <draw:Canvas
            HorizontalOptions="Fill"
            Tag="BackgroundWithFrame"
            VerticalOptions="Fill">

            <partials:GradientToBorder
                HorizontalOptions="Fill"
                MissingHeight="{Binding NavbarModel.BottomTabsHeightRequest}"
                VerticalOptions="Fill">

                <drawn:EmbossedFrameDrawn
                    x:Name="DesignFrame"
                    BackgroundColor="Transparent"
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill" />

            </partials:GradientToBorder>
        </draw:Canvas>

        <draw:Canvas
            x:Name="MainCanvas"
            Gestures="Enabled"
            RenderingMode="Accelerated"
            HorizontalOptions="Fill"
            VerticalOptions="FillAndExpand">

            <draw:SkiaLayout
                HorizontalOptions="Fill"
                VerticalOptions="Fill">

                <draw:SkiaLayout
                    Margin="12,16,12,8"
                    HorizontalOptions="Fill"
                    Spacing="0"
                    Type="Column"
                    VerticalOptions="Fill">

                    <!--  NAVBAR  -->
                    <partials:SkiaNavBar
                        x:Name="NavBar"
                        Margin="0,0,1,0"
                        HeightRequest="65"
                        HorizontalOptions="Fill">

                        <draw:SkiaSvg
                            Margin="16,0,16,0"
                            HeightRequest="16"
                            HorizontalOptions="Start"
                            SvgString="{StaticResource SvgGoBack}"
                            TintColor="#CB6336"
                            VerticalOptions="Center"
                            WidthRequest="16" />

                        <draw:SkiaHotspot
                            CommandTapped="{Binding NavbarModel.CommandGoBack, Mode=OneTime}"
                            HorizontalOptions="Start"
                            TransformView="{x:Reference NavBar}"
                            WidthRequest="44" />

                        <draw:SkiaLabel
                            Margin="48,0,16,0"
                            FontSize="14"
                            LineBreakMode="TailTruncation"
                            MaxLines="1"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Tag="NavTitle"
                            Text="{Binding Title}"
                            TextColor="#E8E3D7"
                            TranslationY="1"
                            VerticalOptions="Center" />

                        <draw:SkiaSvg
                            Margin="0,0,16,0"
                            HeightRequest="16"
                            HorizontalOptions="End"
                            SvgString="{StaticResource SvgAdd}"
                            TintColor="#CB6336"
                            VerticalOptions="Center"
                            WidthRequest="16" />

                        <draw:SkiaHotspot
                            CommandTapped="{Binding CommandAddItem, Mode=OneTime}"
                            HorizontalOptions="End"
                            TransformView="{x:Reference NavBar}"
                            WidthRequest="44" />


                        <!--  LINE HORIZONTAL  -->
                        <draw:SkiaShape
                            Margin="-16,0"
                            BackgroundColor="Black"
                            CornerRadius="0"
                            HeightRequest="1"
                            HorizontalOptions="Fill"
                            StrokeWidth="0"
                            VerticalOptions="End">
                            <draw:SkiaShape.FillGradient>

                                <draw:SkiaGradient
                                    EndXRatio="1"
                                    EndYRatio="0"
                                    StartXRatio="0"
                                    StartYRatio="0"
                                    Type="Linear">
                                    <draw:SkiaGradient.Colors>
                                        <Color>#00E8E3D7</Color>
                                        <Color>#99E8E3D7</Color>
                                        <Color>#00E8E3D7</Color>
                                    </draw:SkiaGradient.Colors>
                                </draw:SkiaGradient>

                            </draw:SkiaShape.FillGradient>
                        </draw:SkiaShape>

                    </partials:SkiaNavBar>

                    <!--  CONTENT SCROLL  -->
                    <draw:SkiaScroll
                        x:Name="MainScroll"
                        AutoScrollingSpeedMs="600"
                        FrictionScrolled="0.3"
                        HorizontalOptions="Fill"
                        LockChildrenGestures="PassTapAndLongPress"
                        RefreshCommand="{Binding CommandRefreshData}"
                        RefreshDistanceLimit="4"
                        RefreshEnabled="False"
                        VerticalOptions="Fill">

                        <!--<draw:SkiaScroll.RefreshIndicator>

                                            IsRefreshing="{Binding IsLoading, Mode=TwoWay}"

                                 <partials:RefreshIndicatorForScroll>

                                     <draw:SkiaLabelFps
                                         VerticalOptions="End"
                                         Margin="10"
                                         HorizontalOptions="Center"/>

                                     <draw:SkiaLottie
                                         AutoPlay="True"
                                         File="Lottie/Loader.json"
                                         HorizontalOptions="Center"
                                         LockRatio="1"
                                         Repeat="-1"
                                         Tag="Loader"
                                         TranslationY="8"
                                         VerticalOptions="Center"
                                         WidthRequest="32" />

                                 </partials:RefreshIndicatorForScroll>

                             </draw:SkiaScroll.RefreshIndicator>-->

                        <draw:SkiaLayout
                            HorizontalOptions="Fill"
                            ItemsSource="{Binding Items}"
                            Spacing="0"
                            Tag="StackItems"
                            Type="Column">

                            <draw:SkiaLayout.ItemTemplate>
                                <DataTemplate>

                                    <partials:DrawnOptionItemCell
                                        Padding="4,0"
                                        x:DataType="models:OptionItem"
                                        CommandLongPressing="{Binding Source={x:Reference MainCanvas}, Path=BindingContext.CommandManageItem}"
                                        CommandTapped="{Binding Source={x:Reference MainCanvas}, Path=BindingContext.CommandSelectItem}"
                                        DebugRendering="False"
                                        HeightRequest="60"
                                        HorizontalOptions="Fill"
                                        Tag="CellTpl"
                                        UseCache="Image">

                                        <draw:SkiaLabel
                                            Margin="16,0,44,0"
                                            FontSize="18"
                                            LineBreakMode="TailTruncation"
                                            MaxLines="2"
                                            Style="{x:StaticResource SkiaLabelDefaultStyle}"
                                            Tag="LabelTitle"
                                            VerticalOptions="Center">
                                            <!--<draw:SkiaControl.Styles>
                                                     <draw:ConditionalStyle
                                                         State="Normal"
                                                         Style="{x:StaticResource SkiaLabelDefaultStyle}" />
                                                     <draw:ConditionalStyle
                                                         CheckCondition="{Binding IsReadOnly}"
                                                         Style="{x:StaticResource SkiaLabelDisabledStyle}" />
                                                 </draw:SkiaControl.Styles>-->
                                        </draw:SkiaLabel>

                                        <draw:SkiaSvg
                                            Margin="16,0"
                                            HeightRequest="16"
                                            HorizontalOptions="End"
                                            SvgString="{StaticResource SvgCheck}"
                                            Tag="SvgCheck"
                                            TintColor="#CB6336"
                                            VerticalOptions="Center"
                                            WidthRequest="16" />

                                    </partials:DrawnOptionItemCell>

                                </DataTemplate>
                            </draw:SkiaLayout.ItemTemplate>

                        </draw:SkiaLayout>


                    </draw:SkiaScroll>



                </draw:SkiaLayout>

                <draw:SkiaLabelFps
                    Margin="32"
                    ForceRefresh="False"
                    HorizontalOptions="End"
                    IsVisible="{x:Static racebox:MauiProgram.ShowDebugInfo}"
                    Rotation="-45"
                    VerticalOptions="End"
                    ZIndex="100" />



            </draw:SkiaLayout>
        </draw:Canvas>


    </Grid>

</views:BasePage>