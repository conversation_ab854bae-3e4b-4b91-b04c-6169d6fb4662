﻿using MapsterMapper;
using Racebox.Shared.Enums;
using Racebox.Shared.Services;
using Racebox.Shared.Strings;
using Racebox.Views.Maps;
using Racebox.Views.Reports;
using SkiaSharp;
using System.Text;
using AppoMobi.Framework.Maui.Interfaces;
using AppoMobi.Framework.Maui.Models;

namespace Racebox.ViewModels;

public class ContentViewModel : ProjectViewModel
{
    protected readonly UserManager _userManager;
    protected readonly IUIAssistant UI;
    protected readonly IMapper _mapper;
    private bool _lockLogs;

    protected MeasureResult SelectedResult { get; set; }

    public LocalDatabase GetDatabase()
    {
        return App.Instance.Services.GetService<LocalDatabase>();
    }

    #region EXPORT

    public RaceBoxMap Map;

    public Command CommandExportLogs => new Command(async () =>
    {
        if (SelectedResult == null || _lockLogs || SelectedResult.Id == 0 || IsBusy)
        {
            return;
        }



        List<SelectableAction> options = new()
        {
            new SelectableAction
            {
                Id = OptionsLogFormat.VBO.ToString(),
                Title = ResStrings.Report,
                Action = () =>
                {
                    UseLogFormat(OptionsLogFormat.Report);
                }
            },
            new SelectableAction
            {
                Id = OptionsLogFormat.CSV.ToString(),
                Title = OptionsLogFormat.CSV.ToString(),
                Action = () =>
                {
                    UseLogFormat(OptionsLogFormat.CSV);
                }
            },
            new SelectableAction
            {
                Id = OptionsLogFormat.VBO.ToString(),
                Title = OptionsLogFormat.VBO.ToString(),
                Action = () =>
                {
                    UseLogFormat(OptionsLogFormat.VBO);
                }
            },
        };

        App.SelectAction(options, ResStrings.ExportFormat);


        void UseLogFormat(OptionsLogFormat type)
        {
            _userManager.User.Options.LogFormat = type;

            Files.CheckPermissionsAsync(async () =>
            {

                try
                {
                    _lockLogs = true;
                    string fullFilename = null;
                    string filename = null;
                    var subfolder = "Logs";

                    var data = SelectedResult;

                    async Task CreateRenderingReport()
                    {
                        var scale = 1;

                        var layout = new ReportDefault()
                        {
                            BindingContext = this
                        };

                        if (layout != null)
                        {

                            SkiaImage mapImage = null;

                            if (this.Map != null)
                            {
                                var imageMap = this.Map.GetSnapshot();
                                if (imageMap != null)
                                {

                                    mapImage = new()
                                    {
                                        HorizontalOptions = LayoutOptions.Fill,
                                        HeightRequest = 100,
                                        ImageBitmap = new LoadedImageSource(imageMap)
                                    };
                                }
                            }

                            if (mapImage != null)
                                layout.Children.Add(mapImage);

                            var measured = layout.Measure(620, float.PositiveInfinity, scale);

                            var reportSize = new SKSize(measured.Units.Width, measured.Units.Height);

                            fullFilename = Files.GetFullFilename(filename, StorageType.Cache, subfolder);

                            if (File.Exists(fullFilename))
                            {
                                File.Delete(fullFilename);
                            }

                            using (var ms = new MemoryStream())
                            using (var stream = new SKManagedWStream(ms))
                            {
                                using (var document = SKDocument.CreatePdf(stream, new SKDocumentPdfMetadata
                                {
                                    Author = ResStrings.VendorTitle,
                                    Producer = ResStrings.VendorTitle,
                                    Subject = this.Title
                                }))
                                {
                                    using (var canvas = document.BeginPage(reportSize.Width, reportSize.Height))
                                    {
                                        var ctx = new SkiaDrawingContext()
                                        {
                                            Canvas = canvas,
                                            Width = reportSize.Width,
                                            Height = reportSize.Height
                                        };

                                        //layout.Render();
                                        //TODO layout.Render(ctx, new SKRect(0, 0, reportSize.Width, reportSize.Height), scale);

                                    }
                                    document.EndPage();
                                    document.Close();
                                }

                                ms.Position = 0;
                                var content = ms.ToArray();

                                var file = Files.OpenFile(fullFilename, StorageType.Cache, subfolder);

                                // Write the bytes to the FileStream of the FileDescriptor
                                await file.Handler.WriteAsync(content, 0, content.Length);

                                // Ensure all bytes are written to the underlying device
                                await file.Handler.FlushAsync();

                                Files.CloseFile(file, true);
                                await Task.Delay(500);
                            }

                        }
                    }

                    async Task WriteFile(Action<StreamWriter> action)
                    {
                        fullFilename = Files.GetFullFilename(filename, StorageType.Cache, subfolder);
                        if (!File.Exists(fullFilename))
                        {
                            var file = Files.OpenFile(filename, StorageType.Cache, subfolder);
                            using (StreamWriter s = new StreamWriter(file.Handler, Encoding.UTF8))
                            {
                                action(s);
                            }
                            Files.CloseFile(file, true);
                            await Task.Delay(500);
                        }
                    }

                    switch (_userManager.User.Options.LogFormat)
                    {
                    case OptionsLogFormat.Report:
                    filename = Exporter.GenerateLogFileName(data.CreatedTimeUtc.ToLocalTime(), "pdf");
                    await CreateRenderingReport();
                    break;

                    case OptionsLogFormat.VBO:
                    filename = Exporter.GenerateLogFileName(data.CreatedTimeUtc.ToLocalTime(), "vbo");
                    await WriteFile((s) => Exporter.WriteVbo(s, data));
                    break;

                    case OptionsLogFormat.CSV:
                    default:
                    filename = Exporter.GenerateLogFileName(data.CreatedTimeUtc.ToLocalTime(), "csv");
                    await WriteFile((s) => Exporter.WriteCsv(s, data));
                    break;
                    }

                    //we use fullFilename that was filled by actions above.. maybe todo refactor
                    App.Native.ExportLogs(new string[] { fullFilename });

                }
                catch (Exception e)
                {
                    Console.WriteLine(e);

                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        await UI.Alert(ResStrings.VendorTitle, e.ToString());
                    });

                }
                finally
                {
                    _lockLogs = false;
                }
            });

        }

    });


    #endregion

    public ContentViewModel(UserManager manager, IUIAssistant ui, IMapper mapper)
    {
        _userManager = manager;
        UI = ui;
        _mapper = mapper;
    }


    protected List<OptionItem> LoadedCars;

    protected async Task InsureCarsLoaded(bool reload = false)
    {
        await _userManager.InsureUserIsInDatabase();

        LoadedCars = _userManager.User.Cars
            .Select(x =>
                new OptionItem(x.Id.ToString(), x.Title)
                {
                    Selected = _userManager.User.SelectedCar.Id == x.Id
                }).ToList();
    }



}