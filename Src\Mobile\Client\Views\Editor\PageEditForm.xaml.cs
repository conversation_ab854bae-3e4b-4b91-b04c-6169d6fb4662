
using Racebox.Views.Partials;

namespace Racebox.Views;

public partial class PageEditForm : ILazyPage
{
    private readonly IEditorForm _vm;

    public PageEditForm(IEditorForm vm)
    {
        try
        {
            BindingContext = _vm = vm;

            InitializeComponent();
        }
        catch (Exception e)
        {

            Designer.DisplayException(this, e);
        }
    }

    private bool formCreated;

    protected override void OnAppearing()
    {
        base.OnAppearing();

        //Settings.UpdateStatusBarUponTheme();

        var canUpdate = BindingContext as IUpdateUIState;
        canUpdate?.UpdateState(true);

        if (!formCreated)
        {
            formCreated = true;
            _vm.Bind();
            Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(200), async () =>
            {
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    Form.Content = _vm.CreateForm();
                    await Task.Delay(10); //update ui
                    await Form.FadeTo(1.0, 150, Easing.CubicIn);
                });
                return false;
            });
        }
    }

    public void OnViewAppearing()
    {
        OnAppearing();
    }

    public void OnViewDisappearing()
    {
        OnDisappearing();
    }

    public void UpdateControls(DeviceRotation orientation)
    {

    }




}