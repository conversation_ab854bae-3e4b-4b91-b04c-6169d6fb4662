﻿
using Racebox.Shared.Enums;
using Racebox.Shared.Strings;
using Racebox.Views.Partials;
using System.Windows.Input;

namespace Racebox.ViewModels;

public class EditValueDoubleWIthEntryViewModel : ProjectViewModel, IEditorFormDouble
{
    public SkiaControl CreateForm()
    {
        return new FormEditDouble();
    }

    public static List<string> _distances;
    private readonly Func<double, Task<bool>> _callback;

    public EditValueDoubleWIthEntryViewModel(
        double value,
        double min, double max,
        Func<double, Task<bool>> callback)
    {
        Value = value;
        _min = min;
        _max = max;
        _callback = callback;
    }

    private string _Hint;
    public string Hint
    {
        get
        {
            return _Hint;
        }
        set
        {
            if (_Hint != value)
            {
                _Hint = value;
                OnPropertyChanged();
            }
        }
    }


    private bool _IsReady;
    public bool IsReady
    {
        get
        {
            return _IsReady;
        }
        set
        {
            if (_IsReady != value)
            {
                _IsReady = value;
                OnPropertyChanged();
            }
        }
    }

    public void Bind()
    {


        Validate();

        IsReady = true;
    }

    public ICommand CommandSubmitForm => new Command(async (object context) =>
    {
        UpdateValidator();
        if (!CanSubmit)
        {
            return;
        }

        try
        {
            IsBusy = true;
            await Task.Delay(10); //update UI

            var success = await _callback(Value);

            if (!success)
            {
                throw new ApplicationException(ResStrings.ErrorFailedToSaveRecord);
            }

            this.NavbarModel.CommandGoBack.Execute(null);

        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            App.Instance.UI.ShowToast($"{ResStrings.Error}: {e.Message}");
        }
        finally
        {
            IsBusy = false;
        }
    });

    public Command CommandSelectMetricsUnit => new Command(() =>
    {

        MainThread.BeginInvokeOnMainThread(async () =>
        {

            List<ISelectableOption> options = new()
            {
                new SelectableAction
                {
                    Id = OptionsUnits.EU.ToString(),
                    Title = ResStrings.Meters,
                    Action = () =>
                    {
                        Units = OptionsUnits.EU;
                        OnPropertyChanged(nameof(DisplayUnits));
                    }
                },
                new SelectableAction
                {
                    Id = OptionsUnits.US.ToString(),
                    Title = ResStrings.Feet,
                    Action = () =>
                    {
                        Units = OptionsUnits.US;
                        OnPropertyChanged(nameof(DisplayUnits));
                    }
                },
            };

            var selected = await App.Instance.UI.PresentSelection(options, ResStrings.Select) as SelectableAction;
            selected?.Action?.Invoke();
        });


    });

    public string DisplayUnits
    {
        get
        {
            if (Units == OptionsUnits.US)
                return ResStrings.Feet;
            return ResStrings.Meters;
        }
    }

    private OptionsUnits _units;
    public OptionsUnits Units
    {
        get
        {
            return _units;
        }
        set
        {
            if (_units != value)
            {
                _units = value;
                OnPropertyChanged();
            }
        }
    }

    private double _Value;
    public double Value
    {
        get
        {
            return _Value;
        }
        set
        {
            if (_Value != value)
            {
                _Value = value;
                OnPropertyChanged();
                ValueString = $"{Value}";
            }
        }
    }

    private string _ValueString;
    public string ValueString
    {
        get
        {
            return _ValueString;
        }
        set
        {
            if (_ValueString != value)
            {
                _ValueString = value;
                OnPropertyChanged();
                UpdateValidator();
            }
        }
    }


    #region VALIDATION

    protected virtual void UpdateValidator()
    {
        Validate();
        OnPropertyChanged("CanSubmit");
    }

    private bool _IsBusy;
    private readonly double _max;
    private readonly double _min;

    public new bool IsBusy
    {
        get { return _IsBusy; }
        set
        {
            if (_IsBusy != value)
            {
                _IsBusy = value;
                OnPropertyChanged();
                OnPropertyChanged("CanSubmit");
            }
        }
    }

    public bool CanSubmit
    {
        get
        {
            var validated = Validate();
            return validated && !IsBusy;
        }
    }

    protected virtual bool Validate()
    {
        var value = ValueString.ToDouble();
        if (value <= _max && value >= _min)
        {
            Value = value;
            return true;
        }

        return false;
    }

    #endregion
}