﻿namespace Racebox.ApiClient;

public static class MimeTypes
{
    private static readonly Dictionary<string, string> Mappings = new Dictionary<string, string>(StringComparer.InvariantCultureIgnoreCase)
    {
        // Text types
        {".txt", "text/plain"},
        {".css", "text/css"},
        {".csv", "text/csv"},
        {".html", "text/html"},
        {".xml", "text/xml"},
        {".json", "application/json"},
        
        // Image types
        {".jpg", "image/jpeg"},
        {".jpeg", "image/jpeg"},
        {".png", "image/png"},
        {".gif", "image/gif"},
        {".bmp", "image/bmp"},
        {".ico", "image/x-icon"},
        {".svg", "image/svg+xml"},
        {".tiff", "image/tiff"},
        {".webp", "image/webp"},

        // Audio types
        {".mp3", "audio/mpeg"},
        {".wav", "audio/wav"},
        {".ogg", "audio/ogg"},
        {".aac", "audio/aac"},
        
        // Video types
        {".mp4", "video/mp4"},
        {".mpeg", "video/mpeg"},
        {".webm", "video/webm"},
        {".mov", "video/quicktime"},
        {".avi", "video/x-msvideo"},
        {".wmv", "video/x-ms-wmv"},
        
        // Application types
        {".pdf", "application/pdf"},
        {".doc", "application/msword"},
        {".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"},
        {".xls", "application/vnd.ms-excel"},
        {".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},
        {".ppt", "application/vnd.ms-powerpoint"},
        {".pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation"},
        {".odt", "application/vnd.oasis.opendocument.text"},
        {".ods", "application/vnd.oasis.opendocument.spreadsheet"},
        
        // Archive types
        {".zip", "application/zip"},
        {".rar", "application/vnd.rar"},
        {".7z", "application/x-7z-compressed"},
        {".tar", "application/x-tar"},
        {".gz", "application/gzip"},

        //... you can continue adding more as required
    };

    public static string GetMimeType(string fileName)
    {
        string extension = Path.GetExtension(fileName);
        if (extension == null) return "application/octet-stream"; // Default if no extension provided
        if (Mappings.TryGetValue(extension, out var mimeType))
        {
            return mimeType;
        }
        return "application/octet-stream"; // default mime type for unknown extensions
    }
}