﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net9.0;net9.0-android;net9.0-ios;net9.0-maccatalyst</TargetFrameworks>
		<TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<SkipValidateMauiImplicitPackageReferences>true</SkipValidateMauiImplicitPackageReferences>
		<NoWarn>$(NoWarn);NU1608</NoWarn>

    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.2</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">23.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</SupportedOSPlatformVersion>
    <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</TargetPlatformMinVersion>

  </PropertyGroup>

	<ItemGroup>
	  <Compile Remove="Dto\Weather\**" />
	  <EmbeddedResource Remove="Dto\Weather\**" />
	  <MauiCss Remove="Dto\Weather\**" />
	  <MauiXaml Remove="Dto\Weather\**" />
	  <None Remove="Dto\Weather\**" />
	</ItemGroup>

  <!--for development-->
  <!--<ItemGroup Condition="'$(UseNuget)' != 'true'">
    <ProjectReference Include="..\..\..\..\AppoMobi.Maui.Infrastructure\src\Lib\AppoMobi.Maui.Infrastructure.csproj" />
    <ProjectReference Include="..\..\..\..\DrawnUi.Maui\src\Addons\DrawnUi.Maui.MapsUi\src\DrawnUi.Maui.MapsUi.csproj" />
    <ProjectReference Include="..\..\..\..\DrawnUi.Maui\src\Engine\Maui\DrawnUi.Maui.csproj" />
  </ItemGroup>-->

	<ItemGroup>
    <PackageReference Include="AppoMobi.Framework.Maui" Version="9.1.2.51" />
    <PackageReference Include="AppoMobi.Maui.Navigation" Version="1.9.3-pre" />
    <!--<PackageReference Include="DrawnUi.Maui.MapsUi" Version="1.6.2.26" />-->
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.0" />
		<PackageReference Include="SQLitePCLRaw.bundle_e_sqlite3" Version="2.1.10" />
    <PackageReference Include="Microsoft.Maui.Controls" Version="9.0.70" />
	</ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Racebox.ApiClient\Racebox.ApiClient.csproj" />
  </ItemGroup>

	<ItemGroup>
	  <None Remove="SQLitePCLRaw.bundle_e_sqlite3" />
	</ItemGroup>

		<!--IOS RELEASE-->
	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net9.0-ios|AnyCPU'">
		<!--to be able to use EF core dynamic stuff-->
		<MtouchExtraArgs>--interpreter</MtouchExtraArgs>
		<UseInterpreter>True</UseInterpreter>
		<CreatePackage>false</CreatePackage>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net9.0-ios|AnyCPU'">
	  <CreatePackage>false</CreatePackage>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Update="Strings\ResStrings.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>ResStrings.resx</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Update="Strings\ResStrings.resx">
	    <Generator>PublicResXFileCodeGenerator</Generator>
	    <LastGenOutput>ResStrings.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Storage\" />
	</ItemGroup>


</Project>
