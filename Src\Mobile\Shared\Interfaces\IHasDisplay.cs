﻿using Racebox.Shared.Enums;

namespace Racebox.Shared.Interfaces;

public interface IHasDisplayFromDb : IHasDisplay
{
    public int Id { get; set; }
}

public interface IHasDetailedDisplay : IHasDisplayFromDb
{
    public string Display1 { get; }
    public string Display2 { get; }


    /// <summary>
    /// for ui
    /// </summary>
    public bool WasShown { get; set; }
}

public interface IHasDisplay
{
    public string Display { get; }

    public OptionsUnits Units { get; set; }
}

public interface IHasSpeech : IHasDisplay
{
    public string Say { get; }
}