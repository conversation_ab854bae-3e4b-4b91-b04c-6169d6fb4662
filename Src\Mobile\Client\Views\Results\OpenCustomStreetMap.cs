﻿using BruTile;
using BruTile.Cache;
using BruTile.Predefined;
using BruTile.Web;
using DrawnUi.MapsUi;
using Mapsui;
using Mapsui.Tiling.Fetcher;
using Mapsui.Tiling.Layers;
using Mapsui.Tiling.Rendering;
using Racebox.Resources;
using SkiaSharp;

namespace Racebox.Views.Maps;

#pragma warning disable CS8670
public static class OpenCustomStreetMap
{
    public class CustomizedLayer : TileLayer
    {
        public CustomizedLayer(ITileSource tileSource, int minTiles = 200, int maxTiles = 300, IDataFetchStrategy dataFetchStrategy = null, IRenderFetchStrategy renderFetchStrategy = null, int minExtraTiles = -1, int maxExtraTiles = -1, Func<TileInfo, Task<IFeature>> fetchTileAsFeature = null) : base(tileSource, minTiles, maxTiles, dataFetchStrategy, renderFetchStrategy, minExtraTiles, maxExtraTiles, fetchTileAsFeature)
        {
        }


    }

    public static IPersistentCache<byte[]> DefaultCache = null;

    private static readonly BruTile.Attribution OpenStreetMapAttribution = new(
        "OpenStreetMap", "https://www.openstreetmap.org/copyright");

    public static TileLayer CreateTileLayer(string userAgent = null)
    {
        userAgent ??= $"user-agent-of-{Path.GetFileNameWithoutExtension(System.AppDomain.CurrentDomain.FriendlyName)}";

        return new CustomizedLayer(CreateTileSource(userAgent)) { Name = "OpenStreetMap" };
    }

    //private static HttpTileSource CreateTileSource(string userAgent)
    //{

    //    return new HttpTileSource(new GlobalSphericalMercator(),
    //        "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
    //        new[] { "a", "b", "c" }, name: "OpenStreetMap",
    //        attribution: OpenStreetMapAttribution, userAgent: userAgent, persistentCache: DefaultCache);
    //}

    private static HttpTileSource CreateTileSource(string userAgent)
    {
        var source = new CustomizeDownloadedTiles(new GlobalSphericalMercator(),
            "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
            new[] { "a", "b", "c" }, name: "OpenStreetMap",
            attribution: OpenStreetMapAttribution, persistentCache: DefaultCache);


        source.RenderBitmap = (renderAction, canvas) =>
        {
            using SKPaint paintTint = new SKPaint()
            {
                ColorFilter = SkiaImageEffects.Tint(Styles.TintContentColorWithAlpha, SKBlendMode.SrcATop)
            };

            using SKPaint paintContrast = new SKPaint()
            {
                ColorFilter = ChainAdjustContrastEffect.CreateContrastFilter(Styles.TintContentContrast)
            };

            using SKPaint paintBrightness = new SKPaint()
            {
                ColorFilter = ChainAdjustLightnessEffect.CreateLightnessFilter(Styles.TintContentLightness)
            };

            using SKPaint paintDesaturate = new SKPaint()
            {
                ColorFilter = ChainSaturationEffect.CreateSaturationFilter(Styles.TintContentSaturation)
            };

            //apply main filter
            var restore = canvas.SaveLayer(paintTint);

            canvas.SaveLayer(paintContrast);
            canvas.SaveLayer(paintBrightness);
            canvas.SaveLayer(paintDesaturate);

            renderAction(null);

            canvas.RestoreToCount(restore);
        };

        return source;
    }
}

