<html xmlns:o="urn:schemas-microsoft-com:office:office"
xmlns:x="urn:schemas-microsoft-com:office:excel"
xmlns="http://www.w3.org/TR/REC-html40">

<head>
<meta http-equiv=Content-Type content="text/html; charset=windows-1251">
<meta name=ProgId content=Excel.Sheet>
<meta name=Generator content="Microsoft Excel 11">
<link id=Main-File rel=Main-File href="../Racebox_BLE_Protocol_v1.3-V2.htm">
<link rel=File-List href=filelist.xml>
<link rel=Edit-Time-Data href=editdata.mso>
<link rel=Stylesheet href=stylesheet.css>
<style>
<!--table
	{mso-displayed-decimal-separator:"\,";
	mso-displayed-thousand-separator:" ";}
@page
	{margin:.75in .7in .75in .7in;
	mso-header-margin:.3in;
	mso-footer-margin:.3in;}
-->
</style>
<![if !supportTabStrip]><script language="JavaScript">
<!--
function fnUpdateTabs()
 {
  if (parent.window.g_iIEVer>=4) {
   if (parent.document.readyState=="complete"
    && parent.frames['frTabs'].document.readyState=="complete")
   parent.fnSetActiveSheet(1);
  else
   window.setTimeout("fnUpdateTabs();",150);
 }
}

if (window.name!="frSheet")
 window.location.replace("../Racebox_BLE_Protocol_v1.3-V2.htm");
else
 fnUpdateTabs();
//-->
</script>
<![endif]><!--[if gte mso 9]><xml>
 <x:WorksheetOptions>
  <x:DefaultRowHeight>312</x:DefaultRowHeight>
  <x:Print>
   <x:ValidPrinterInfo/>
   <x:PaperSizeIndex>9</x:PaperSizeIndex>
   <x:HorizontalResolution>600</x:HorizontalResolution>
   <x:VerticalResolution>600</x:VerticalResolution>
  </x:Print>
  <x:Zoom>70</x:Zoom>
  <x:Selected/>
  <x:LeftColumnVisible>2</x:LeftColumnVisible>
  <x:Panes>
   <x:Pane>
    <x:Number>3</x:Number>
    <x:ActiveRow>7</x:ActiveRow>
    <x:ActiveCol>4</x:ActiveCol>
    <x:RangeSelection>$E$8:$E$10</x:RangeSelection>
   </x:Pane>
  </x:Panes>
  <x:ProtectContents>False</x:ProtectContents>
  <x:ProtectObjects>False</x:ProtectObjects>
  <x:ProtectScenarios>False</x:ProtectScenarios>
 </x:WorksheetOptions>
</xml><![endif]-->
</head>

<body link=blue vlink=purple>

<table x:str border=0 cellpadding=0 cellspacing=0 width=1603 style='border-collapse:
 collapse;table-layout:fixed;width:1202pt'>
 <col width=127 style='mso-width-source:userset;mso-width-alt:4070;width:95pt'>
 <col class=xl66 width=137 style='mso-width-source:userset;mso-width-alt:4377;
 width:103pt'>
 <col class=xl66 width=112 style='mso-width-source:userset;mso-width-alt:3584;
 width:84pt'>
 <col class=xl83 width=144 style='mso-width-source:userset;mso-width-alt:4608;
 width:108pt'>
 <col class=xl66 width=131 style='mso-width-source:userset;mso-width-alt:4198;
 width:98pt'>
 <col class=xl66 width=82 style='mso-width-source:userset;mso-width-alt:2611;
 width:61pt'>
 <col class=xl66 width=114 style='mso-width-source:userset;mso-width-alt:3635;
 width:85pt'>
 <col class=xl66 width=86 style='mso-width-source:userset;mso-width-alt:2739;
 width:64pt'>
 <col class=xl66 width=122 style='mso-width-source:userset;mso-width-alt:3891;
 width:91pt'>
 <col class=xl66 width=153 style='mso-width-source:userset;mso-width-alt:4889;
 width:115pt'>
 <col width=185 style='mso-width-source:userset;mso-width-alt:5913;width:139pt'>
 <col width=70 span=3 style='width:53pt'>
 <tr height=21 style='height:15.6pt'>
  <td height=21 width=127 style='height:15.6pt;width:95pt'></td>
  <td class=xl66 width=137 style='width:103pt'></td>
  <td class=xl66 width=112 style='width:84pt'></td>
  <td class=xl83 width=144 style='width:108pt'></td>
  <td class=xl66 width=131 style='width:98pt'></td>
  <td class=xl66 width=82 style='width:61pt'></td>
  <td class=xl66 width=114 style='width:85pt'></td>
  <td class=xl66 width=86 style='width:64pt'></td>
  <td class=xl66 width=122 style='width:91pt'></td>
  <td class=xl66 width=153 style='width:115pt'></td>
  <td width=185 style='width:139pt'></td>
  <td width=70 style='width:53pt'></td>
  <td width=70 style='width:53pt'></td>
  <td width=70 style='width:53pt'></td>
 </tr>
 <tr height=42 style='height:31.2pt'>
  <td height=42 class=xl78 style='height:31.2pt'>Byte #</td>
  <td class=xl78 style='border-left:none'>Bit #</td>
  <td class=xl78 style='border-left:none'>Size (bits)</td>
  <td class=xl79 width=144 style='border-left:none;width:108pt'>Parameter</td>
  <td class=xl78 style='border-left:none'>Units</td>
  <td class=xl79 width=82 style='border-left:none;width:61pt'>Formula / Value</td>
  <td class=xl78 style='border-left:none'>Data Type</td>
  <td class=xl78 style='border-left:none'>Range Min</td>
  <td class=xl79 width=122 style='border-left:none;width:91pt'>Range Max</td>
  <td class=xl79 width=153 style='border-left:none;width:115pt'>Invalid Value</td>
  <td class=xl79 width=185 style='border-left:none;width:139pt'>Comments</td>
  <td colspan=3 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='mso-height-source:userset;height:15.75pt'>
  <td rowspan=8 height=168 class=xl67 style='height:125.1pt;border-top:none'
  x:num>0</td>
  <td class=xl67 style='border-top:none;border-left:none' x:num>0</td>
  <td rowspan=5 class=xl67 style='border-top:none' x:num>5</td>
  <td rowspan=5 class=xl127 width=144 style='border-bottom:.5pt solid black;
  border-top:none;width:108pt'>Extended frame marker</td>
  <td rowspan=5 class=xl67 style='border-top:none'>-</td>
  <td rowspan=5 class=xl67 style='border-top:none' x:num>0</td>
  <td rowspan=5 class=xl67 style='border-top:none'>UINT</td>
  <td rowspan=5 class=xl81 width=86 style='border-top:none;width:64pt'>-</td>
  <td rowspan=5 class=xl81 width=122 style='border-top:none;width:91pt'>-</td>
  <td rowspan=5 class=xl81 width=153 style='border-top:none;width:115pt'>-</td>
  <td rowspan=8 class=xl102 width=185 style='border-bottom:.5pt solid black;
  border-top:none;width:139pt'>First byte:<br>
    0x00</td>
  <td colspan=3 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>1</td>
  <td colspan=3 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>2</td>
  <td colspan=3 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>3</td>
  <td colspan=3 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>4</td>
  <td colspan=3 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='mso-height-source:userset;height:15.75pt'>
  <td height=21 class=xl67 style='height:15.75pt;border-top:none;border-left:
  none' x:num>5</td>
  <td rowspan=3 class=xl113 style='border-bottom:.5pt solid black;border-top:
  none' x:num>3</td>
  <td rowspan=3 class=xl127 width=144 style='border-bottom:.5pt solid black;
  border-top:none;width:108pt'>Extended data marker</td>
  <td rowspan=3 class=xl113 style='border-bottom:.5pt solid black;border-top:
  none'>-</td>
  <td rowspan=3 class=xl113 style='border-bottom:.5pt solid black;border-top:
  none' x:num>0</td>
  <td rowspan=3 class=xl113 style='border-bottom:.5pt solid black;border-top:
  none'>UINT</td>
  <td rowspan=3 class=xl113 style='border-bottom:.5pt solid black;border-top:
  none'>-</td>
  <td rowspan=3 class=xl113 style='border-bottom:.5pt solid black;border-top:
  none'>-</td>
  <td rowspan=3 class=xl113 style='border-bottom:.5pt solid black;border-top:
  none'>-</td>
  <td colspan=3 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>6</td>
  <td colspan=3 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>7</td>
  <td colspan=3 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=45 style='mso-height-source:userset;height:33.75pt'>
  <td rowspan=8 height=243 class=xl67 style='height:181.5pt;border-top:none'
  x:num>1</td>
  <td class=xl67 style='border-top:none;border-left:none' x:num>0</td>
  <td rowspan=2 class=xl67 style='border-top:none' x:num>2</td>
  <td rowspan=2 class=xl81 width=144 style='border-top:none;width:108pt'>Battery
  charging state</td>
  <td rowspan=2 class=xl67 style='border-top:none'>-</td>
  <td rowspan=2 class=xl67 style='border-top:none'>-</td>
  <td rowspan=2 class=xl67 style='border-top:none'>ENUM</td>
  <td rowspan=2 class=xl67 style='border-top:none'>-</td>
  <td rowspan=2 class=xl67 style='border-top:none'>-</td>
  <td rowspan=2 class=xl67 style='border-top:none'>-</td>
  <td rowspan=2 class=xl105 width=185 style='border-top:none;width:139pt'>00 -
  not charging,<br>
    01 - precharge,<br>
    10 - fast,<br>
    11 - charge terminated</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>0</td>
 </tr>
 <tr height=45 style='mso-height-source:userset;height:33.75pt'>
  <td height=45 class=xl67 style='height:33.75pt;border-top:none;border-left:
  none' x:num>1</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>1</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>2</td>
  <td rowspan=5 class=xl67 style='border-top:none' x:num>5</td>
  <td rowspan=5 class=xl81 width=144 style='border-top:none;width:108pt'>Battery
  Level</td>
  <td rowspan=5 class=xl67 style='border-top:none'>%</td>
  <td rowspan=5 class=xl67 style='border-top:none'>val * 5</td>
  <td rowspan=5 class=xl67 style='border-top:none'>UINT</td>
  <td rowspan=5 class=xl67 style='border-top:none' x:num>0</td>
  <td rowspan=5 class=xl67 style='border-top:none' x:num>20</td>
  <td rowspan=5 class=xl67 style='border-top:none'>0x1F</td>
  <td rowspan=5 class=xl105 width=185 style='border-top:none;width:139pt'>0x1F
  - device without battery</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>0</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>3</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>0</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>4</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>1</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>5</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>0</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>6</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>1</td>
 </tr>
 <tr height=48 style='mso-height-source:userset;height:36.0pt'>
  <td height=48 class=xl67 style='height:36.0pt;border-top:none;border-left:
  none' x:num>7</td>
  <td rowspan=2 class=xl67 style='border-top:none' x:num>2</td>
  <td rowspan=2 class=xl81 width=144 style='border-top:none;width:108pt'>Device
  type</td>
  <td rowspan=2 class=xl67 style='border-top:none'>-</td>
  <td rowspan=2 class=xl67 style='border-top:none'>-</td>
  <td rowspan=2 class=xl67 style='border-top:none'>ENUM</td>
  <td rowspan=2 class=xl67 style='border-top:none'>-</td>
  <td rowspan=2 class=xl67 style='border-top:none'>-</td>
  <td rowspan=2 class=xl67 style='border-top:none'>-</td>
  <td rowspan=2 class=xl105 width=185 style='border-top:none;width:139pt'>00 -
  Racebox Pro,<br>
    01 - Racebox Pro+, <br>
    10 - Racebox DataTrack<br>
    11 - Reserved</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>1</td>
 </tr>
 <tr height=50 style='mso-height-source:userset;height:37.5pt'>
  <td rowspan=8 height=197 class=xl67 style='height:146.7pt;border-top:none'
  x:num>2</td>
  <td class=xl67 style='border-top:none;border-left:none' x:num>0</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>0</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>1</td>
  <td rowspan=4 class=xl67 style='border-top:none' x:num>4</td>
  <td rowspan=4 class=xl67 style='border-top:none'>SW Version [0]</td>
  <td rowspan=4 class=xl67 style='border-top:none'>-</td>
  <td rowspan=4 class=xl67 style='border-top:none'>val</td>
  <td rowspan=4 class=xl67 style='border-top:none'>UINT</td>
  <td rowspan=4 class=xl67 style='border-top:none' x:num>0</td>
  <td rowspan=4 class=xl67 style='border-top:none' x:num>10</td>
  <td rowspan=4 class=xl67 style='border-top:none'>0xF</td>
  <td rowspan=12 class=xl105 width=185 style='border-top:none;width:139pt'>0x4,
  0x0, 0x7 - version 4.07</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>0</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>2</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>1</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>3</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>1</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>4</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>0</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>5</td>
  <td rowspan=4 class=xl67 style='border-top:none' x:num>4</td>
  <td rowspan=4 class=xl67 style='border-top:none'>SW Version [1]</td>
  <td rowspan=4 class=xl67 style='border-top:none'>-</td>
  <td rowspan=4 class=xl67 style='border-top:none'>val</td>
  <td rowspan=4 class=xl67 style='border-top:none'>UINT</td>
  <td rowspan=4 class=xl67 style='border-top:none' x:num>0</td>
  <td rowspan=4 class=xl67 style='border-top:none' x:num>10</td>
  <td rowspan=4 class=xl67 style='border-top:none'>0xF</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>0</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>6</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>1</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>7</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>0</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td rowspan=8 height=168 class=xl67 style='height:124.8pt;border-top:none'
  x:num>3</td>
  <td class=xl67 style='border-top:none;border-left:none' x:num>0</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>0</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>1</td>
  <td rowspan=4 class=xl67 style='border-top:none' x:num>4</td>
  <td rowspan=4 class=xl67 style='border-top:none'>SW Version [2]</td>
  <td rowspan=4 class=xl67 style='border-top:none'>-</td>
  <td rowspan=4 class=xl67 style='border-top:none'>val</td>
  <td rowspan=4 class=xl67 style='border-top:none'>UINT</td>
  <td rowspan=4 class=xl67 style='border-top:none' x:num>0</td>
  <td rowspan=4 class=xl67 style='border-top:none' x:num>10</td>
  <td rowspan=4 class=xl67 style='border-top:none'>0xF</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>1</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>2</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>0</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>3</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>0</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>4</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>1</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>5</td>
  <td class=xl84 style='border-top:none;border-left:none'>&nbsp;</td>
  <td rowspan=3 class=xl81 width=144 style='border-top:none;width:108pt'>N/A</td>
  <td class=xl84 style='border-top:none;border-left:none'>&nbsp;</td>
  <td class=xl84 style='border-top:none;border-left:none' x:num>1</td>
  <td class=xl84 style='border-top:none;border-left:none'>&nbsp;</td>
  <td class=xl84 style='border-top:none;border-left:none'>&nbsp;</td>
  <td class=xl84 style='border-top:none;border-left:none'>&nbsp;</td>
  <td class=xl84 style='border-top:none;border-left:none'>&nbsp;</td>
  <td rowspan=3 class=xl81 width=185 style='border-top:none;width:139pt'>not
  used</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>1</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>6</td>
  <td class=xl84 style='border-top:none;border-left:none'>&nbsp;</td>
  <td class=xl84 style='border-top:none;border-left:none'>&nbsp;</td>
  <td class=xl84 style='border-top:none;border-left:none' x:num>1</td>
  <td class=xl84 style='border-top:none;border-left:none'>&nbsp;</td>
  <td class=xl84 style='border-top:none;border-left:none'>&nbsp;</td>
  <td class=xl84 style='border-top:none;border-left:none'>&nbsp;</td>
  <td class=xl84 style='border-top:none;border-left:none'>&nbsp;</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>1</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 class=xl67 style='height:15.6pt;border-top:none;border-left:
  none' x:num>7</td>
  <td class=xl84 style='border-top:none;border-left:none'>&nbsp;</td>
  <td class=xl84 style='border-top:none;border-left:none'>&nbsp;</td>
  <td class=xl84 style='border-top:none;border-left:none' x:num>1</td>
  <td class=xl84 style='border-top:none;border-left:none'>&nbsp;</td>
  <td class=xl84 style='border-top:none;border-left:none'>&nbsp;</td>
  <td class=xl84 style='border-top:none;border-left:none'>&nbsp;</td>
  <td class=xl84 style='border-top:none;border-left:none'>&nbsp;</td>
  <td colspan=2 style='mso-ignore:colspan'></td>
  <td align=right x:num>1</td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td colspan=2 class=xl66 style='mso-ignore:colspan'></td>
  <td class=xl83></td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl66></td>
  <td class=xl74>EXAMPLE:</td>
  <td class=xl83></td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl89>Example message:</td>
  <td class=xl90>00 D2 08 EE</td>
  <td class=xl83></td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=22 style='height:16.2pt'>
  <td height=22 style='height:16.2pt'></td>
  <td colspan=2 class=xl66 style='mso-ignore:colspan'></td>
  <td class=xl83></td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=22 style='mso-height-source:userset;height:16.5pt'>
  <td height=22 style='height:16.5pt'></td>
  <td rowspan=8 class=xl93 style='border-bottom:1.0pt solid black' x:str="'00">00</td>
  <td class=xl98 width=112 style='width:84pt' x:num>0</td>
  <td rowspan=5 class=xl131 width=144 style='border-bottom:1.0pt solid black;
  width:108pt'>Extended frame marker</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl99 width=112 style='border-top:none;width:84pt' x:num>0</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl99 width=112 style='border-top:none;width:84pt' x:num>0</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl99 width=112 style='border-top:none;width:84pt' x:num>0</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=22 style='height:16.2pt'>
  <td height=22 style='height:16.2pt'></td>
  <td class=xl100 width=112 style='border-top:none;width:84pt' x:num>0</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl98 width=112 style='border-top:none;width:84pt' x:num>0</td>
  <td rowspan=3 class=xl131 width=144 style='border-bottom:1.0pt solid black;
  border-top:none;width:108pt'>Extended data marker</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl99 width=112 style='border-top:none;width:84pt' x:num>0</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=22 style='height:16.2pt'>
  <td height=22 style='height:16.2pt'></td>
  <td class=xl100 width=112 style='border-top:none;width:84pt' x:num>0</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td rowspan=8 class=xl134 style='border-bottom:1.0pt solid black;border-top:
  none'>D2</td>
  <td class=xl97 style='border-left:none' x:num>0</td>
  <td rowspan=2 class=xl140 width=144 style='border-bottom:1.0pt solid black;
  width:108pt'>0b10 - fast charge</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=22 style='height:16.2pt'>
  <td height=22 style='height:16.2pt'></td>
  <td class=xl92 style='border-top:none;border-left:none' x:num>1</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl91 style='border-top:none;border-left:none' x:num>0</td>
  <td rowspan=5 class=xl137 width=144 style='border-bottom:1.0pt solid black;
  border-top:none;width:108pt'>0b10100 = 20 = 100%</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl96 style='border-top:none;border-left:none' x:num>0</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl96 style='border-top:none;border-left:none' x:num>1</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl96 style='border-top:none;border-left:none' x:num>0</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=22 style='height:16.2pt'>
  <td height=22 style='height:16.2pt'></td>
  <td class=xl92 style='border-top:none;border-left:none' x:num>1</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=22 style='height:16.2pt'>
  <td height=22 style='height:16.2pt'></td>
  <td class=xl91 style='border-top:none;border-left:none' x:num>1</td>
  <td rowspan=2 class=xl137 width=144 style='border-bottom:1.0pt solid black;
  border-top:none;width:108pt'>0b01 - Racebox Bat</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=22 style='height:16.2pt'>
  <td height=22 style='height:16.2pt'></td>
  <td rowspan=8 class=xl134 style='border-bottom:1.0pt solid black;border-top:
  none' x:str="'08">08</td>
  <td class=xl92 style='border-top:none;border-left:none' x:num>0</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl91 style='border-top:none;border-left:none' x:num>0</td>
  <td rowspan=4 class=xl137 width=144 style='border-bottom:1.0pt solid black;
  border-top:none;width:108pt'>0b0100 = 4</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl96 style='border-top:none;border-left:none' x:num>0</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl96 style='border-top:none;border-left:none' x:num>1</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=22 style='height:16.2pt'>
  <td height=22 style='height:16.2pt'></td>
  <td class=xl92 style='border-top:none;border-left:none' x:num>0</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl91 style='border-top:none;border-left:none' x:num>0</td>
  <td rowspan=4 class=xl137 width=144 style='border-bottom:1.0pt solid black;
  border-top:none;width:108pt'>0b0000 = 0</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl96 style='border-top:none;border-left:none' x:num>0</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=22 style='height:16.2pt'>
  <td height=22 style='height:16.2pt'></td>
  <td class=xl96 style='border-top:none;border-left:none' x:num>0</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=22 style='height:16.2pt'>
  <td height=22 style='height:16.2pt'></td>
  <td rowspan=8 class=xl134 style='border-bottom:1.0pt solid black;border-top:
  none'>EE</td>
  <td class=xl92 style='border-top:none;border-left:none' x:num>0</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl91 style='border-top:none;border-left:none' x:num>1</td>
  <td rowspan=4 class=xl137 width=144 style='border-bottom:1.0pt solid black;
  border-top:none;width:108pt'>0b0111 = 7</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl96 style='border-top:none;border-left:none' x:num>1</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl96 style='border-top:none;border-left:none' x:num>1</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=22 style='height:16.2pt'>
  <td height=22 style='height:16.2pt'></td>
  <td class=xl92 style='border-top:none;border-left:none' x:num>0</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl91 style='border-top:none;border-left:none' x:num>1</td>
  <td rowspan=3 class=xl137 width=144 style='border-bottom:1.0pt solid black;
  border-top:none;width:108pt'>-</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl96 style='border-top:none;border-left:none' x:num>1</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=22 style='height:16.2pt'>
  <td height=22 style='height:16.2pt'></td>
  <td class=xl92 style='border-top:none;border-left:none' x:num>1</td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl85></td>
  <td class=xl66></td>
  <td class=xl83></td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl85></td>
  <td class=xl66></td>
  <td class=xl83></td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl85></td>
  <td class=xl66></td>
  <td class=xl83></td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl85></td>
  <td class=xl66></td>
  <td class=xl83></td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <tr height=21 style='height:15.6pt'>
  <td height=21 style='height:15.6pt'></td>
  <td class=xl85></td>
  <td class=xl66></td>
  <td class=xl83></td>
  <td colspan=6 class=xl66 style='mso-ignore:colspan'></td>
  <td colspan=4 style='mso-ignore:colspan'></td>
 </tr>
 <![if supportMisalignedColumns]>
 <tr height=0 style='display:none'>
  <td width=127 style='width:95pt'></td>
  <td width=137 style='width:103pt'></td>
  <td width=112 style='width:84pt'></td>
  <td width=144 style='width:108pt'></td>
  <td width=131 style='width:98pt'></td>
  <td width=82 style='width:61pt'></td>
  <td width=114 style='width:85pt'></td>
  <td width=86 style='width:64pt'></td>
  <td width=122 style='width:91pt'></td>
  <td width=153 style='width:115pt'></td>
  <td width=185 style='width:139pt'></td>
  <td width=70 style='width:53pt'></td>
  <td width=70 style='width:53pt'></td>
  <td width=70 style='width:53pt'></td>
 </tr>
 <![endif]>
</table>

</body>

</html>
