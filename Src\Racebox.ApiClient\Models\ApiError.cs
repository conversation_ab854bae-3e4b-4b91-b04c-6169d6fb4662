﻿using Racebox.ApiClient.Interfaces;

namespace Racebox.ApiClient.Models;

public class ApiError : IWithErrorDto
{
    public ApiError(int code, string message)
    {
        ErrorCode = new int?(code);
        ErrorMessage = message;
    }

    public ApiError()
    {
    }

    public int? ErrorCode { get; set; }

    public string ErrorMessage { get; set; }

    public string ErrorDetails { get; set; }
}