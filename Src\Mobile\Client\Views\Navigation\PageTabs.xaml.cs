﻿using Racebox.Shared.Strings;
using Racebox.ViewModels.Navigation;
using System.Runtime.CompilerServices;
using AppoMobi.Framework.Maui.Controls.Navigation.Tabs;
using AppoMobi.Framework.Maui.Touch;


namespace Racebox.Views.Navigation.FastShell
{
    public partial class PageTabs : BasePage, ISupportsLazyViews
    {

        private bool maybe_exit { get; set; } = false;
        protected override bool OnBackButtonPressed()
        {
            if (Navigation.NavigationStack.Count > 1)
            {
                return false; //do nothing, lets the system pop us
            }

            if (maybe_exit)
            {
                return false; //exit
            }

            var myIndex = Switcher.GetCurrentTabNavigationIndex();
            if (myIndex > 0)
            {
                Switcher.PopTab();
                return true;
            }

            App.Instance.UI.ShowToast(ResStrings.PressBACKOnceAgain);
            maybe_exit = true;

            Tasks.StartTimerAsync(TimeSpan.FromSeconds(2), async () =>
            {
                maybe_exit = false;
                return false;
            });

            return true; //true - dont process BACK by system
        }

        //public override void OnRendered()
        //{
        //    base.OnRendered();

        //    App.Instance.Messager.All("UIRendered", "Tabs");
        //}

        public override double OnKeyboardResized(double size)
        {
            if (Model.NavbarModel.Keyboard != size)
            {
                Model.NavbarModel.Keyboard = size;
                Switcher.InvokeOnKeyboardResized(size);
            }

            return base.OnKeyboardResized(size);
        }


        public override void OnPageWasRotated()
        {
            base.OnPageWasRotated();

            Switcher?.UpdateControls(this.Orientation);
        }



        public override void OnDisposing()
        {
            Switcher.Dispose();
            TabHost.Dispose();

            base.OnDisposing();
        }


        public TabsViewModel Model;

        #region STYLES 



        //protected override void OnSizeAllocated(double _width, double _height)
        //{
        //    App.Current.UpdateDynamicStylesUponWidth(_width, () =>
        //        {
        //            Resources["BottomTabStyle"] = Resources["BottomTabDefault"];
        //        },
        //        () =>
        //        {
        //            Resources["BottomTabStyle"] = Resources["BottomTabSmall"];
        //        },
        //        this.Id.ToString());

        //    base.OnSizeAllocated(_width, _height);
        //}


        #endregion


        public IViewSwitcher ViewSwitcher
        {
            get
            {
                return this.Switcher;
            }
        }

        public IViewsContainer ViewsContainer
        {
            get
            {
                return this.Container as IViewsContainer;
            }
        }

        public PageTabs()
        {
            var check = Id;

            try
            {
                BindingContext = Model = new TabsViewModel();

                InitializeComponent();

                OnLoaded();
            }
            catch (Exception e)
            {
                Designer.DisplayException(this, e);
            }


            App.Instance.Messager.Subscribe<string>(this, "SelectRootTabExec", async (sender, arg) =>
            {
                WishTabBinding = arg;
            });

            App.Instance.Messager.Subscribe<string>(this, "Release", async (sender, arg) =>
            {
                App.Instance.Messager.Unsubscribe(this, "Release");
                App.Instance.Messager.Unsubscribe(this, "SelectRootTabExec");
            });

        }

        public void OnLoaded()
        {


        }



        private bool once;

        protected override void OnAppearing()
        {
            base.OnAppearing();

            Switcher?.UpdateControls(Orientation);

            try
            {
                Switcher?.SendOnAppearing(Switcher.ActiveView);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            if (!once)
            {
                once = true;

                App.Instance.UI.HideAllPopups();

                SelectWishTab();

            }

        }


        //-------------------------------------------------------------
        // WishTabBinding
        //-------------------------------------------------------------
        private const string nameWishTabBinding = "WishTabBinding";
        public static readonly BindableProperty WishTabBindingProperty = BindableProperty.Create(nameWishTabBinding, typeof(string), typeof(PageTabs), string.Empty); //, BindingMode.TwoWay
        public string WishTabBinding
        {
            get { return (string)GetValue(WishTabBindingProperty); }
            set { SetValue(WishTabBindingProperty, value); }
        }

        protected override void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            base.OnPropertyChanged(propertyName);

            if (propertyName == nameWishTabBinding)
            {
                SelectWishTab();
            }

        }


        public void SelectWishTab()
        {
            try
            {
                if (!string.IsNullOrEmpty(WishTabBinding))
                {
                    // Update the UI
                    if (WishTabBinding.SafeContainsInLower("last"))
                    {
                        var i = Switcher.Children.Count;
                        if (i > 0)
                            Model.SelectedIndex = i - 1;
                    }
                    else
                    if (WishTabBinding.SafeContainsInLower("first"))
                    {
                        //todo!!! - select first visible child
                        Model.SelectedIndex = 0;
                    }
                    else
                    {
                        var index = WishTabBinding.ToInteger();
                        var at = 0;
                        bool found = false;
                        foreach (var tab in TabHost.Tabs)
                        {
                            if (tab is TabItem tabItem)
                            {
                                if (tabItem.TabIndex == index)
                                {
                                    Model.SelectedIndex = at;
                                    found = true;
                                    break;
                                }
                            }
                            at++;
                        }
                        if (!found)
                            Model.SelectedIndex = WishTabBinding.ToInteger();
                    }


                    WishTabBinding = null;
                }

            }
            catch (Exception e)
            {
                Super.Log(e);
            }
        }

        private void PannedLeft(object sender, TouchActionEventArgs e)
        {
            if (sender is PanAwareHotspot pan && pan.DistanceX < -40)
            {
                Switcher.NavigateNext();
            }
        }

        private async void PannedRight(object sender, TouchActionEventArgs e)
        {
            if (sender is PanAwareHotspot pan && pan.DistanceX > 40)
            {
                await Switcher.NavigatePrevious();
            }
        }

        private void SwipedLeft(object sender, TouchActionEventArgs e)
        {
            //Switcher.NavigateNext();
        }

        private void SwipedRight(object sender, TouchActionEventArgs e)
        {
            //Switcher.NavigatePrevious();
        }
    }
}