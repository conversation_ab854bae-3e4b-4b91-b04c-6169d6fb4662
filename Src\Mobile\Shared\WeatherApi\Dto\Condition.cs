﻿using Newtonsoft.Json;

namespace Racebox.ApiClient.Dto;




public class Condition
{
    /// <summary>
    /// Weather condition text
    /// </summary>
    [JsonProperty("text")]
    public string Text { get; set; }

    /// <summary>
    /// Weather icon url
    /// </summary>
    [JsonProperty("icon")]
    public string Icon { get; set; }

    /// <summary>
    /// Weather condition unique code
    /// </summary>
    [JsonProperty("code")]
    public int Code { get; set; }
}