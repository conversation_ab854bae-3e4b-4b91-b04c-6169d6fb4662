﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Racebox.Shared.Services;

#nullable disable

namespace Racebox.Shared.Migrations
{
    [DbContext(typeof(LocalDatabase))]
    [Migration("20221127153211_Initial")]
    partial class Initial
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "6.0.10");

            modelBuilder.Entity("Racebox.Shared.Models.AppUser", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Key")
                        .HasColumnType("TEXT");

                    b.Property<string>("Meta")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .HasColumnType("TEXT");

                    b.<PERSON>("Id");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("Racebox.Shared.Models.Car", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("AppUserId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Brand")
                        .HasColumnType("TEXT");

                    b.Property<string>("Model")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("AppUserId");

                    b.ToTable("Cars");
                });

            modelBuilder.Entity("Racebox.Shared.Models.MeasuredDistance", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<double>("Length")
                        .HasColumnType("REAL");

                    b.Property<double>("MaxIncline")
                        .HasColumnType("REAL");

                    b.Property<int>("MeasureResultId")
                        .HasColumnType("INTEGER");

                    b.Property<double>("Speed")
                        .HasColumnType("REAL");

                    b.Property<TimeSpan?>("Time")
                        .HasColumnType("TEXT");

                    b.Property<int>("Units")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("MeasureResultId");

                    b.ToTable("Distances");
                });

            modelBuilder.Entity("Racebox.Shared.Models.MeasuredLogLine", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<double>("AccelerationLat")
                        .HasColumnType("REAL");

                    b.Property<double>("AccelerationLon")
                        .HasColumnType("REAL");

                    b.Property<double>("AccelerationLonRaw")
                        .HasColumnType("REAL");

                    b.Property<double>("Altitude")
                        .HasColumnType("REAL");

                    b.Property<double>("Distance")
                        .HasColumnType("REAL");

                    b.Property<double>("HDOP")
                        .HasColumnType("REAL");

                    b.Property<double>("Heading")
                        .HasColumnType("REAL");

                    b.Property<double>("Incline")
                        .HasColumnType("REAL");

                    b.Property<int>("Index")
                        .HasColumnType("INTEGER");

                    b.Property<double>("Lattitude")
                        .HasColumnType("REAL");

                    b.Property<double>("Longitude")
                        .HasColumnType("REAL");

                    b.Property<int>("MeasureResultId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("SatellitesCount")
                        .HasColumnType("INTEGER");

                    b.Property<double>("Speed")
                        .HasColumnType("REAL");

                    b.Property<double>("TotalSeconds")
                        .HasColumnType("REAL");

                    b.HasKey("Id");

                    b.HasIndex("MeasureResultId");

                    b.ToTable("Logs");
                });

            modelBuilder.Entity("Racebox.Shared.Models.MeasuredRange", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<double>("End")
                        .HasColumnType("REAL");

                    b.Property<bool>("IsUserDefined")
                        .HasColumnType("INTEGER");

                    b.Property<int>("MeasureResultId")
                        .HasColumnType("INTEGER");

                    b.Property<double>("Start")
                        .HasColumnType("REAL");

                    b.Property<double>("StartedAtMs")
                        .HasColumnType("REAL");

                    b.Property<TimeSpan?>("Time")
                        .HasColumnType("TEXT");

                    b.Property<int>("Units")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("MeasureResultId");

                    b.ToTable("Ranges");
                });

            modelBuilder.Entity("Racebox.Shared.Models.MeasureResult", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("AppUserId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("CarId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Crash")
                        .HasColumnType("TEXT");

                    b.Property<TimeSpan>("Duration")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsValid")
                        .HasColumnType("INTEGER");

                    b.Property<double>("Lattitude")
                        .HasColumnType("REAL");

                    b.Property<double>("Longitude")
                        .HasColumnType("REAL");

                    b.Property<double>("MaxAcceleration")
                        .HasColumnType("REAL");

                    b.Property<double>("MaxIncline")
                        .HasColumnType("REAL");

                    b.Property<double>("MaxSpeed")
                        .HasColumnType("REAL");

                    b.Property<bool>("RollOut")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("TEXT");

                    b.Property<int>("Units")
                        .HasColumnType("INTEGER");

                    b.HasKey("Id");

                    b.HasIndex("AppUserId");

                    b.ToTable("Results");
                });

            modelBuilder.Entity("Racebox.Shared.Models.Car", b =>
                {
                    b.HasOne("Racebox.Shared.Models.AppUser", "AppUser")
                        .WithMany("Cars")
                        .HasForeignKey("AppUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AppUser");
                });

            modelBuilder.Entity("Racebox.Shared.Models.MeasuredDistance", b =>
                {
                    b.HasOne("Racebox.Shared.Models.MeasureResult", "MeasureResult")
                        .WithMany("Distances")
                        .HasForeignKey("MeasureResultId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MeasureResult");
                });

            modelBuilder.Entity("Racebox.Shared.Models.MeasuredLogLine", b =>
                {
                    b.HasOne("Racebox.Shared.Models.MeasureResult", "MeasureResult")
                        .WithMany("Logs")
                        .HasForeignKey("MeasureResultId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MeasureResult");
                });

            modelBuilder.Entity("Racebox.Shared.Models.MeasuredRange", b =>
                {
                    b.HasOne("Racebox.Shared.Models.MeasureResult", "MeasureResult")
                        .WithMany("Ranges")
                        .HasForeignKey("MeasureResultId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MeasureResult");
                });

            modelBuilder.Entity("Racebox.Shared.Models.MeasureResult", b =>
                {
                    b.HasOne("Racebox.Shared.Models.AppUser", "AppUser")
                        .WithMany("MeasureResults")
                        .HasForeignKey("AppUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AppUser");
                });

            modelBuilder.Entity("Racebox.Shared.Models.AppUser", b =>
                {
                    b.Navigation("Cars");

                    b.Navigation("MeasureResults");
                });

            modelBuilder.Entity("Racebox.Shared.Models.MeasureResult", b =>
                {
                    b.Navigation("Distances");

                    b.Navigation("Logs");

                    b.Navigation("Ranges");
                });
#pragma warning restore 612, 618
        }
    }
}
