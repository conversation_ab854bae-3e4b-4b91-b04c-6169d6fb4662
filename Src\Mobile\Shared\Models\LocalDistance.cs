﻿
using Racebox.Shared.Enums;
using Racebox.Shared.Strings;

namespace Racebox.Shared.Models;

public class LocalDistance : OptionItem
{
    public double Start { get; set; }
    public double End { get; set; }
    public OptionsUnits Units { get; set; }

    public LocalDistance()
    {
        Init();
    }

    public LocalDistance(double start, double end, OptionsUnits units, string id = null, bool argIsReadOnly = false)
    {
        Start = start;
        End = end;
        Units = units;
        IsReadOnly = argIsReadOnly;
        Id = id;
        Init();
    }

    public void Init()
    {
        Id = string.IsNullOrEmpty(Id) ? Guid.NewGuid().ToString() : Id;
        Title = $"{End:0} {LocalDistance.GetUnitsDisplay(Units)}"; //{Start:0.0}-
    }
    public static string GetUnitsDisplay(OptionsUnits units)
    {
        if (units == OptionsUnits.US)
            return ResStrings.FeetShort;
        return ResStrings.MetersShort;
    }
}