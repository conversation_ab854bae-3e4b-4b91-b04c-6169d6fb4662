﻿using AppoMobi.Maui.Navigation;
using Racebox.UI.Dev;
using Racebox.ViewModels.Navigation;
using Racebox.Views.Navigation;
using Racebox.Views.Navigation.FastShell;
using System.Windows.Input;

namespace Racebox
{
    public class AppFastShell : FastShell
    {

        private readonly NavigationViewModel _vm;

        public AppFastShell(NavigationViewModel vm, IServiceProvider services) : base(services)
        {
            _vm = vm;

            Flyout.Title = "...";
            if (Flyout is ContentPage drawer)
            {
                drawer.Content = new DrawerMenuView();
            }

            FlyoutEnabled = false;
            this.IsGestureEnabled = false;
            FlyoutLayoutBehavior = FlyoutLayoutBehavior.Popover;

            //ROUTES
            foreach (var appRoute in AppRoutes.GetRoutes())
            {
                RegisterRoute(appRoute.Item1, appRoute.Item2);
            }

            RegisterActionRoute("home", () =>
            {
                App.Instance.Messager.All("SelectRootTabExec", "first");
            });
            //RegisterActionRoute("reqs", () =>
            //{
            //    App.Instance.Messager.All("SelectRootTabExec", "1");
            //});
            //RegisterActionRoute("chats", () =>
            //{
            //    App.Instance.Messager.All("SelectRootTabExec", "2");
            //});
            //RegisterActionRoute("bills", () =>
            //{
            //    App.Instance.Messager.All("SelectRootTabExec", "3");
            //});
            RegisterActionRoute("settings", () =>
            {
                App.Instance.Messager.All("SelectRootTabExec", "last");
            });

        }

        protected override void OnStarted()
        {
            base.OnStarted();

            App.Instance.MainPage = this;
        }

        public override void OnNavBarInvalidated()
        {
            base.OnNavBarInvalidated();

            _vm.UpdateControls();
        }

        public ICommand CommandMenuItemTapped
        {
            get
            {
                return new Command<object>(async (context) =>
                {
                    var item = context as MenuPageItem;
                    if (item != null)
                    {
                        _vm.ShowMenu(false);

                        if (item.OnSelected != null)
                        {
                            item.OnSelected.Invoke();
                        }
                    }
                });
            }
        }

    }
}
