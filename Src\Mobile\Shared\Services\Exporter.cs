﻿using Racebox.ApiClient.Dto;
using Racebox.Shared.Models;
using Racebox.Shared.Strings;

namespace Racebox.Shared.Services;

public static partial class Exporter
{
  

    public static string GenerateLogFileName(DateTime timeStamp, string extension)
    {
        // AccelLog_YYYY-MM-DD_hhmmss

        var filename = $"AccelLog_{timeStamp:yyyy-MM-dd_HHmmss}.{extension}";

        return filename;
    }

    #region CSV

    public static void WriteCsv(StreamWriter writer, MeasureResult data)
    {
        writer.WriteLine(CreateCsvHeader());

        foreach (var line in data.Logs.ToList())
        {
            var write = line.ToCsvRow();
            writer.WriteLine(write);
        }
    }

    public static string ToCsvRow(this MeasuredLogLine line, string separator = null)
    {
        var row =
            $"{line.TotalSeconds:0.00};" +
            $"{line.Lattitude:0.0000000};" +
            $"{line.Longitude:0.0000000};" +
            $"{line.Speed:0.0};" +
            $"{line.Distance:0.0};" +
            $"{line.AccelerationLat:0.00};" +
            $"{line.AccelerationLon:0.00};" +
            $"{line.Altitude:0.0};" +
            $"{line.Incline:0.0};" +
            $"{line.Heading:0.0};" +
            $"{line.HDOP:0.0};" +
            $"{line.SatellitesCount:0}".Replace(",", ".");

        if (!string.IsNullOrEmpty(separator))
        {
            return row.Replace(";", separator);
        }
        return row;
    }

    public static string CreateCsvHeader()
    {
        return
            "Total Time (s);" +
            "Lat;" +
            "Lon;" +
            "Speed (km/h);" +
            "Distance (m);" +
            "LatAccel (G);" +
            "LonAccel (G);" +
            "Alt (m);" +
            "Incline (%);" +
            "Course (deg);" +
            "HDOP;" +
            "Sats";
    }


    #endregion

    #region VBO

    // https://en.racelogic.support/VBOX_Automotive/01General_Information/Knowledge_Base/VBO_File_Format

    public static string CreateVboHeader(DateTime time)
    {
        var header =
            $"File created on {ExplainDate(time)} at {ExplainTime(time)}\r\n\r\n" +
            $"[header]\r\n" +
            $"satellites\r\ntime\r\nlatitude\r\nlongitude\r\nvelocity kmh\r\nheading\r\nheight\r\nlong accel g\r\n\r\n" +
            $"[comments]\r\n" +
            $"Generated by Racebox\r\n" +
            $"http://www.racebox.cc\r\n\r\n" +
            $"[column names]\r\n" +
            $"sats time lat long velocity heading height longacc" +
            $"\r\n\r\n" +
            $"[data]";

        return header;
    }

    public static string ExplainTime(DateTime time)
    {
        var ret = $"{time.ToString($"{LocalizedDisplayProvider.HoursFormat}:mm:ss", ResStrings.Culture)}";
        return ret;
    }
    public static string ExplainDate(DateTime time)
    {
        var ret = $"{time.Date.ToString("d", ResStrings.Culture)}";
        return ret;
    }


    public static void WriteVbo(StreamWriter writer, MeasureResult data)
    {
        writer.WriteLine(CreateVboHeader(data.CreatedTimeUtc.ToLocalTime()));

        foreach (var line in data.Logs.ToList())
        {
            var write = line.ToVboRow(data.StartTime);
            writer.WriteLine(write);
        }

    }

    public static string ExplainCoordinate(double value)
    {
        var sign = value < 0 ? "-" : "+";
        var intValue = (int)(value * 10000000);
        var p1 = Math.Abs(intValue) / 166667U;
        var p2 = Math.Abs(intValue) % 166667U * 6;

        return $"{sign}{p1}.{p2}";
    }

    public static string ToVboRow(this MeasuredLogLine line, DateTime startTime)
    {
        //Формирование строки в VBO-логе
        // sats time lat long velocity heading height longacc

        var time = startTime + TimeSpan.FromSeconds(line.TotalSeconds);

        var row =

            //Sats: This is the number of satellites in use in decimal format.
            //64 is added to this number if the brake trigger input is activated.
            //128 is added to this number if the VBOX is using a DGPS correction.
            $"{line.SatellitesCount:000} " +

            //Time: This is UTC time since midnight in the form HH:MM:SS.SS.
            $"{time:HHmmss}.{time.Millisecond:000} " +

            //Latitude: Latitude in minutes MMMMM.MMMMM +ve = North
            //e.g. 03119.09973M = 51D, 59M, 5.9838S.
            $"{ExplainCoordinate(line.Lattitude)} " +

            //Longitude in minutes MMMMM.MMM +ve = West
            //e.g. 00058.49277M = 00D, 58M, 29.562S.
            $"{ExplainCoordinate(-line.Longitude)} " +

            //Velocity: Velocity in km/h.
            $"{line.Speed:000.000} " +

            //Heading: Heading in degrees with respect to North.
            $"{line.Heading:000.00} " +

            //Height: Height above sea level in meters based on the WGS84
            //model of the earth used by VBOX GPS engines.
            $"{line.Altitude:+0000.00;-0000.00} " +

            //Vert-velocity: Vertical velocity in km/h. +ve velocity uphill, -ve velocity downhill.
            $"{line.AccelerationLon:+0000.00;-0000.00}";

        return row.Replace(",", ".");

        /*
	var str = string.Format("{0:D3} {1:D2}{2:D2}{3:D2}.{4:D2} {5}{6:D5}.{7:D6} {8}{9:D5}.{10:D6} {11:D3}.{12:D2} {13:D3}.{14:D2} {15}{16:D4}.{17:D2} {18}{19,1:D}.{20:D2} {21}{22,1:D}.{23:D2} {24}{25:D}.{26,1:D} {27}{28:D}.{29,1:D} {30:D5} {31:D3} {32}{33:D3} {34}{35:D3}\n",
		(byte)gps.satellites.value(),
		getFixHour(gps.time.hour()),
		gps.time.minute(),
		gps.time.second(),
		gps.time.centisecond(),

		(gps.location.intLat() < 0) ? "-" : "+",
		Math.Abs(gps.location.intLat()) / 166667U,
		Math.Abs(gps.location.intLat()) % 166667U * 6,

		(gps.location.intLon() > 0) ? "-" : "+",
		Math.Abs(gps.location.intLon()) / 166667U,
		Math.Abs(gps.location.intLon()) % 166667U * 6,

		(ushort)(spd_kph), 
		(ushort)(spd_kph * 100) % 100,

		validHeading / 100, 
		validHeading % 100 / 10,

		(gps.altitude.cm() < 0) ? "-" : "+",
		Math.Abs(gps.altitude.cm()) / 100,
		(byte)(Math.Abs(gps.altitude.cm()) % 100),

		(frame.accelG < 0) ? "-" : "+",
		(byte)(Math.Abs(frame.accelG) / 100),
		Math.Abs(frame.accelG) % 100,

		(frame.latAccelG < 0) ? "-" : "+",
		(ushort)(Math.Abs(frame.latAccelG) / 100),
		Math.Abs(frame.latAccelG) % 100,

		(frame.yawGyro < 0) ? "-" : "+",
		Math.Abs(frame.yawGyro) / 10,
		Math.Abs(frame.yawGyro) % 10,
		(frame.driftAngle < 0) ? "-" : "+",
		Math.Abs(frame.driftAngle) / 10,
		Math.Abs(frame.driftAngle) % 10,
		frame.engRPM, 
		frame.thrPos,
		(frame.coolTemp < 0) ? "-" : "",
		Math.Abs(frame.coolTemp),
		(frame.intakeTemp < 0) ? "-" : "",
		Math.Abs(frame.intakeTemp));
*/
    }


    #endregion

    public static DateTime CrateTimeFromMilliseconds(long ms)
    {
        TimeSpan time = TimeSpan.FromMilliseconds(ms);
        var ret = new DateTime(time.Ticks);
        return ret;
    }

    public static DateTime CrateTimeFromSeconds(long ms)
    {
        TimeSpan time = TimeSpan.FromSeconds(ms);
        var ret = new DateTime(time.Ticks);
        return ret;
    }


}

//[Flags]
//public enum FileMode
//{
//    Read,
//    Write,
//}