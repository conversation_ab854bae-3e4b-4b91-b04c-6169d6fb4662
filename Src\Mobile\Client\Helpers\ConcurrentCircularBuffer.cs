﻿using Racebox.SDK;
using System.Collections.Immutable;

namespace Racebox.Helpers;


public class RaceboxDataBuffer : ConcurrentCircularBuffer<RaceBoxState>
{
	public RaceboxDataBuffer(int capacity) : base(capacity)
	{
	}

	public double[] GetBufferFrequency()
	{
		var arr = this.Select(s => s.FrequencyUpdatedHz).ToArray();

		//если есть нули заполнить соседними не-нулями
		for (int i = 0; i < arr.Length; i++)
		{
			if (arr[i] == 0.0)
			{
				int leftIndex = i - 1;
				int rightIndex = i + 1;
				while (leftIndex >= 0 || rightIndex < arr.Length)
				{
					if (leftIndex >= 0 && arr[leftIndex] != 0.0)
					{
						arr[i] = arr[leftIndex];
						break;
					}
					else if (rightIndex < arr.Length && arr[rightIndex] != 0.0)
					{
						arr[i] = arr[rightIndex];
						break;
					}
					leftIndex--;
					rightIndex++;
				}
			}
		}

		return arr;
	}

	static DateTime UtcBase = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);

}
public class ConcurrentCircularBuffer<T> : IEnumerable<T> //where T : IQueueItem
{
	private readonly object _locker = new();
	private readonly int _capacity;
	private ImmutableQueue<T> _queue = ImmutableQueue<T>.Empty;
	private int _count = 0;


	public ConcurrentCircularBuffer(int capacity) => _capacity = capacity;

	public void Enqueue(T item)
	{
		lock (_locker)
		{
			_queue = _queue.Enqueue(item);
			if (_count < _capacity)
				_count++;
			else
				_queue = _queue.Dequeue();
		}
	}

	public IEnumerator<T> GetEnumerator()
	{
		var enumerator = Volatile.Read(ref _queue).GetEnumerator();
		while (enumerator.MoveNext())
			yield return enumerator.Current;
	}

	System.Collections.IEnumerator System.Collections.IEnumerable.GetEnumerator()
	{
		return GetEnumerator();
	}
}
