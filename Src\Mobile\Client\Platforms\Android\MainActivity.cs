﻿using Android.App;
using Android.Content.PM;
using Android.Graphics.Drawables;
using Android.OS;
using Android.Runtime;
using Android.Views;
using AppoMobi.Framework.Maui.Controls;
using Microsoft.Maui.Controls.PlatformConfiguration;
using ColorExtensions = Microsoft.Maui.Controls.Compatibility.Platform.Android.ColorExtensions;

namespace Racebox;

//removed from manifest: android:icon="@mipmap/appicon" android:roundIcon="@mipmap/appicon_round"

[Activity(Theme = "@style/MainSplashTheme",
//[Activity(Theme = "@style/Maui.SplashTheme",
    MainLauncher = true,
    Icon = "@mipmap/ic_launcher",
    RoundIcon = "@mipmap/ic_launcher_round",
    LaunchMode = LaunchMode.SingleTask,
    ConfigurationChanges = ConfigChanges.ScreenSize
                           | ConfigChanges.Orientation
                           | ConfigChanges.UiMode
                           | ConfigChanges.ScreenLayout
                           | ConfigChanges.SmallestScreenSize
                           | ConfigChanges.Density)]
public class MainActivity : MauiAppCompatActivity
{

    #region LOCK FOCUS inside entry when needed (ex: chat page)

    private bool _lieAboutCurrentFocus;
    public override bool DispatchTouchEvent(MotionEvent ev)
    {
        var focused = CurrentFocus;
        bool locked = false;
        if (focused != null)
        {
            var maybeEntry = focused.Parent as AMEntry;
            if (maybeEntry != null)
            {
                if (maybeEntry.UnfocusLocked)
                {
                    locked = true;
                }
            }

            //var maybeEditor = focused.Parent as AMEntry;
            //if (maybeEditor != null)
            //{
            //    if (((AMEditor)maybeEditor.Element).UnfocusLocked)
            //    {
            //        locked = true;
            //    }
            //}

        }

        _lieAboutCurrentFocus = locked;
        var result = base.DispatchTouchEvent(ev);
        _lieAboutCurrentFocus = false;

        return result;
    }

    public override Android.Views.View CurrentFocus
    {
        get
        {
            if (_lieAboutCurrentFocus)
            {
                return null;
            }

            var focused = base.CurrentFocus;

            //can be null
            //Console.WriteLine($"[FOCUS] {focused.Id} {focused.ToString()}");

            return focused;
        }
    }

    #endregion

    public override void OnRequestPermissionsResult(int requestCode, string[] permissions, [GeneratedEnum] Android.Content.PM.Permission[] grantResults)
    {
        Microsoft.Maui.ApplicationModel.Platform.OnRequestPermissionsResult(requestCode, permissions, grantResults);

        base.OnRequestPermissionsResult(requestCode, permissions, grantResults);
    }



    protected override void OnCreate(Bundle savedInstanceState)
    {
        base.OnCreate(savedInstanceState);

        //color behind keyboard etc..
        Window.SetBackgroundDrawable(new ColorDrawable(ColorExtensions.ToAndroid(Color.FromArgb("#FF343C45"))));
    }
}

