﻿
using Racebox.Shared.Enums;
using Racebox.Shared.Strings;
using Racebox.Views.Partials;
using System.Windows.Input;

namespace Racebox.ViewModels;

public class EditValueWithWheelViewModel : ProjectViewModel, IEditWithWheel
{
    public SkiaControl CreateForm()
    {
        return new FormEditWithWheelDrawn();
    }

    public static List<string> _distances;
    private readonly Func<string, Task<bool>> _callback;

    public EditValueWithWheelViewModel(
        string value,
        List<string> values,
        Func<string, Task<bool>> callback)
    {
        Value = value;
        _callback = callback;
        _distances = values;
    }

    private int _selectedIndex = -1;
    public int SelectedIndex
    {
        get
        {
            return _selectedIndex;
        }
        set
        {
            if (_selectedIndex != value)
            {
                _selectedIndex = value;
                OnPropertyChanged();

                if (value >= 0)
                    Value = _distances[value];
                else
                {
                    Value = _distances[0];
                }
            }
        }
    }

    private List<string> _distancesList;
    public List<string> ItemsList
    {
        get
        {
            return _distancesList;
        }
        set
        {
            if (_distancesList != value)
            {
                _distancesList = value;
                OnPropertyChanged();
            }
        }
    }

    private bool _IsReady;
    public bool IsReady
    {
        get
        {
            return _IsReady;
        }
        set
        {
            if (_IsReady != value)
            {
                _IsReady = value;
                OnPropertyChanged();
            }
        }
    }

    public void Bind()
    {
        ItemsList = _distances;

        var hasIndex = _distances.IndexOf(Value);
        if (hasIndex >= 0)
        {
            SelectedIndex = hasIndex;
        }
        else
        {
            SelectedIndex = 0;
        }

        Validate();

        IsReady = true;
    }

    public ICommand CommandSubmitForm => new Command(async (object context) =>
    {
        UpdateValidator();
        if (!CanSubmit)
        {
            return;
        }

        try
        {
            IsBusy = true;
            await Task.Delay(10); //update UI

            var success = await _callback(Value);

            if (!success)
            {
                throw new ApplicationException(ResStrings.ErrorFailedToSaveRecord);
            }

            this.NavbarModel.CommandGoBack.Execute(null);

        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            App.Instance.UI.ShowToast($"{ResStrings.Error}: {e.Message}");
        }
        finally
        {
            IsBusy = false;
        }
    });

    public Command CommandSelectMetricsUnit => new Command(() =>
    {

        MainThread.BeginInvokeOnMainThread(async () =>
        {

            List<ISelectableOption> options = new()
            {
                new SelectableAction
                {
                    Id = OptionsUnits.EU.ToString(),
                    Title = ResStrings.Meters,
                    Action = () =>
                    {
                        Units = OptionsUnits.EU;
                        OnPropertyChanged(nameof(DisplayUnits));
                    }
                },
                new SelectableAction
                {
                    Id = OptionsUnits.US.ToString(),
                    Title = ResStrings.Feet,
                    Action = () =>
                    {
                        Units = OptionsUnits.US;
                        OnPropertyChanged(nameof(DisplayUnits));
                    }
                },
            };

            var selected = await App.Instance.UI.PresentSelection(options, ResStrings.Select) as SelectableAction;
            selected?.Action?.Invoke();
        });


    });

    public string DisplayUnits
    {
        get
        {
            if (Units == OptionsUnits.US)
                return ResStrings.Feet;
            return ResStrings.Meters;
        }
    }

    private OptionsUnits _units;
    public OptionsUnits Units
    {
        get
        {
            return _units;
        }
        set
        {
            if (_units != value)
            {
                _units = value;
                OnPropertyChanged();
            }
        }
    }

    private string _Value;
    public string Value
    {
        get
        {
            return _Value;
        }
        set
        {
            if (_Value != value)
            {
                _Value = value;
                OnPropertyChanged();
                UpdateValidator();
            }
        }
    }

    #region VALIDATION

    protected virtual void UpdateValidator()
    {
        Validate();
        OnPropertyChanged("CanSubmit");
    }

    private bool _IsBusy;

    public new bool IsBusy
    {
        get { return _IsBusy; }
        set
        {
            if (_IsBusy != value)
            {
                _IsBusy = value;
                OnPropertyChanged();
                OnPropertyChanged("CanSubmit");
            }
        }
    }

    public bool CanSubmit
    {
        get
        {
            var validated = Validate();
            return validated && !IsBusy;
        }
    }

    protected virtual bool Validate()
    {
        return true;
    }

    #endregion
}