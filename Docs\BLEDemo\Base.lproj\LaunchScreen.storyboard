<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="14868" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14824"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="FordAntennaWGL-Medium.otf">
            <string>FordAntenna-Medium</string>
        </array>
    </customFonts>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="Llm-lL-Icb"/>
                        <viewControllerLayoutGuide type="bottom" id="xb3-aO-Qok"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Dev Build 0.0.1" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0yU-6X-iUd">
                                <rect key="frame" x="20" y="390" width="335" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="2vv-hl-ILc"/>
                                    <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="40" id="Xux-bb-IS5"/>
                                </constraints>
                                <fontDescription key="fontDescription" name="FordAntenna-Medium" family="Ford Antenna" pointSize="25"/>
                                <color key="textColor" cocoaTouchSystemColor="viewFlipsideBackgroundColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="Logo_small_transp-1" translatesAutoresizingMaskIntoConstraints="NO" id="XwV-5J-60f">
                                <rect key="frame" x="40" y="100" width="295" height="280"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="280" id="r1j-7O-glt"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="XwV-5J-60f" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" constant="40" id="AWW-CJ-tL5"/>
                            <constraint firstAttribute="trailing" secondItem="0yU-6X-iUd" secondAttribute="trailing" constant="20" id="U1x-3F-eiG"/>
                            <constraint firstItem="0yU-6X-iUd" firstAttribute="top" secondItem="XwV-5J-60f" secondAttribute="bottom" constant="10" id="aP3-wz-hLa"/>
                            <constraint firstItem="XwV-5J-60f" firstAttribute="top" secondItem="Llm-lL-Icb" secondAttribute="bottom" constant="100" id="jVP-u8-sUQ"/>
                            <constraint firstItem="0yU-6X-iUd" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" constant="20" id="jtZ-IS-waW"/>
                            <constraint firstAttribute="trailing" secondItem="XwV-5J-60f" secondAttribute="trailing" constant="40" id="vel-OV-mU9"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="52" y="374.66266866566718"/>
        </scene>
    </scenes>
    <resources>
        <image name="Logo_small_transp-1" width="800" height="800"/>
    </resources>
</document>
