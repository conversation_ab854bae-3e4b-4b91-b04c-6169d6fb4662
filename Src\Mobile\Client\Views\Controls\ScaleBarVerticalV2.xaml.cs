namespace Racebox.Views.Partials;

public partial class ScaleBarVerticalV2
{
	public ScaleBarVerticalV2()
	{
		UpdateControl();
		InitializeComponent();
	}

	private double minValue;
	private double maxValue;
	private double leftValue;
	private double rightValue;
	private double _point;

	public void ResetMinMax()
	{
		minValue = 0;
		maxValue = 0;
		UpdateMinMax();
	}
	protected override void OnUpdating()
	{
		base.OnUpdating();


		if (Bar != null)
		{
			UpdateMinMax();
			UpdateBars();
		}
	}


	public override void UpdateControl()
	{
		//just clamp here

		if (Bar != null)
		{
			_point = (Bar.Height / 2.0 - ValueSideOffset) / Limit;

			var value = Value;

			if (value < 0)
			{
				if (value < -Limit)
				{
					value = -Limit;
				}

				leftValue = value;
				rightValue = 0;

				if (minValue > value)
				{
					minValue = value;
				}
			}
			else
			if (value > 0)
			{
				if (value > Limit)
				{
					value = Limit;
				}

				rightValue = value;
				leftValue = 0;

				if (maxValue < value)
				{
					maxValue = value;
				}
			}
			else
			{
				leftValue = 0;
				rightValue = 0;
			}
		}

	}

	public void UpdateBars()
	{

		if (leftValue < 0)
		{
			IndicatorLeft.IsVisible = true;
			var offset = _point * leftValue;
			IndicatorLeft.TranslationY = offset / 2.0;
			IndicatorLeft.HeightRequest = -offset;
		}
		else
		{
			IndicatorLeft.IsVisible = false;
		}

		if (rightValue > 0)
		{
			IndicatorRight.IsVisible = true;
			var offset = _point * rightValue;
			IndicatorRight.TranslationY = offset / 2.0;
			IndicatorRight.HeightRequest = offset;
		}
		else
		{
			IndicatorRight.IsVisible = false;
		}
	}

	public void UpdateMinMax()
	{
		if (minValue < 0)
		{
			IndicatorMin.IsVisible = true;
			var offset = _point * minValue;
			IndicatorMin.TranslationY = offset;
		}
		else
		{
			IndicatorMin.IsVisible = false;
		}

		if (maxValue > 0)
		{
			IndicatorMax.IsVisible = true;
			var offset = _point * maxValue;
			IndicatorMax.TranslationY = offset;
		}
		else
		{
			IndicatorMax.IsVisible = false;
		}
	}


}