﻿using Windows.Storage;

namespace Racebox.Helpers
{
    public partial class NativeTasks
    {
        //public IEnumerable<string> ListAssets(string subfolder)
        //{
        //    StorageFolder appFolder = ApplicationData.Current.LocalFolder;
        //    StorageFolder subfolderStorage;

        //    try
        //    {
        //        subfolderStorage = appFolder.GetFolderAsync(subfolder).GetResults();
        //    }
        //    catch (FileNotFoundException)
        //    {
        //        return new List<string>();
        //    }

        //    IReadOnlyList<StorageFile> files = subfolderStorage.GetFilesAsync().GetResults();
        //    return files.Select(file => file.Name).ToList();
        //}

        public IEnumerable<string> ListAssets(string sub)
        {

            //MSIX
            try
            {
                StorageFolder installFolder = Windows.ApplicationModel.Package.Current.InstalledLocation;
                StorageFolder subfolder = installFolder.GetFolderAsync(sub).GetAwaiter().GetResult();
                IReadOnlyList<StorageFile> files = subfolder.GetFilesAsync().GetAwaiter().GetResult();

                return files.Select(f => f.Name);
            }
            catch (Exception e)
            {
                //UNPACKAGED
                string appDirectory = AppDomain.CurrentDomain.BaseDirectory;
                string subfolderPath = Path.Combine(appDirectory, sub);

                if (!Directory.Exists(subfolderPath))
                {
                    return new List<string>();
                }

                return Directory.GetFiles(subfolderPath)
                    .Select(Path.GetFileName)
                    .ToList();
            }

        }


        public void ExportLogs(IEnumerable<string> fullFilenames)
        {
            ShareFiles("Логи", fullFilenames);
        }
        public void ShareFiles(string message, IEnumerable<string> fullFilenames)
        {

        }

        public bool CheckGpsIsAvailable()
        {
            throw new NotImplementedException();
        }
        public void OpenSettings()
        {
            //throw new NotImplementedException();
        }

        public void PlaySoundFromAssets(string filename)
        {
            //odo
        }

        public void OpenLink(string link)
        {
            try
            {
                Windows.System.Launcher.LaunchUriAsync(new Uri(link));
            }
            catch (Exception e)
            {
                Super.Log(e);
            }
        }
    }
}
