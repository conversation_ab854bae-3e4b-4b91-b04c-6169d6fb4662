﻿using Mapster;
using MapsterMapper;
using Microsoft.EntityFrameworkCore;
using Racebox.Shared.Models;
using Racebox.Shared.Services;

namespace Racebox.Shared.Extensions
{
	public static class DatabaseExtensions
	{



		public static async Task<AppUser> AddUser(this LocalDatabase db, LocalUser item, IMapper mapper)
		{
			var dbUser = await mapper.MapAsync<AppUser>(item);

			db.Users.Add(dbUser);
			await db.SaveChangesAsync();

			item.Id = dbUser.Id;

			return dbUser;
		}



		public static async Task<bool> RestoreUser(this LocalDatabase db, LocalUser item, IMapper mapper)
		{
			var dbUser = await db.GetUser(item.Id);

			if (dbUser == null)
			{
				return false;
			}

			await mapper.From(dbUser).AdaptToAsync(item);

			return true;
		}

		public static async Task<bool> UpdateUser(this LocalDatabase db, LocalUser item, IMapper mapper)
		{
			var dbUser = await db.GetUser(item.Id);

			if (dbUser == null)
			{
				return false;
			}

			await mapper.From(item).AdaptToAsync(dbUser);

			db.Users.Update(dbUser);
			await db.SaveChangesAsync();

			item.Id = dbUser.Id;

			return true;
		}


		public static async Task<FilteredPagedList<HistoryCellData>> GetMeasuredResultsPagedList(this LocalDatabase db,
			FilteredListRequestDto args,
			IMapper mapper,
			CancellationToken cancellationToken = default)
		{
			var query = db.Results
				//                .Where(x => x.AppUserId == userId)
				.Include(i => i.Ranges)
				.Include(i => i.Distances);
			//.Include(i => i.Logs);

			var pagedList = await query.ApplyFilteredListOptions(args)
				.ProjectToPagedListAsync<MeasureResult, HistoryCellData>(args.PageNumber, args.PageSize, mapper, cancellationToken);

			pagedList.Filter = args.Filter;
			pagedList.Order = args.Order;
			pagedList.Search = args.Search;

			return pagedList;

		}

		public static async Task<List<LocalUser>> GetUsersList(this LocalDatabase db,
			IMapper mapper,
			CancellationToken cancellationToken = default)
		{
			var query = db.Users
				.Include(i => i.Cars);

			var ret = await query.ProjectToListAsync<AppUser, LocalUser>(mapper, cancellationToken);

			return ret;
		}



	}
}
