using System.Globalization;
using TestApi.Models;

namespace TestApi.Services;

/// <summary>
/// Cross-platform service for generating chart metadata from CSV data
/// Compatible with .NET, Android, and iOS platforms
/// </summary>
public class CsvMetadataGenerator : ICsvMetadataGenerator
{
    public ChartMetadata GenerateFromCsv(string csvText, long fileSize, string? fileName = null)
    {
        if (string.IsNullOrWhiteSpace(csvText))
            throw new ArgumentException("CSV text cannot be null or empty", nameof(csvText));

        var lines = csvText.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
        if (lines.Length < 2)
        {
            throw new InvalidOperationException("CSV file must have at least header and one data row");
        }

        // Parse CSV data to extract performance metrics
        var dataLines = lines.Skip(1).Where(line => !string.IsNullOrWhiteSpace(line));
        var performanceMetrics = CalculatePerformanceMetrics(dataLines);
        
        // Generate filename based on current timestamp if not provided
        var generatedFileName = fileName ?? $"AccelLog_{DateTime.Now:yyyy-MM-dd_HHmmss}.csv";
        
        return new ChartMetadata
        {
            Name = generatedFileName,
            Size = fileSize,
            Type = "accel",
            DataSource = "app", // Mobile app identifier
            Meta = new MetaInfo
            {
                Lang = "en", // Can be made configurable
                User = "Mobile App User", // Can be made configurable
                Vehicle = "Test Vehicle", // Can be made configurable
                Descr = "Generated from mobile app",
                Share = 0,
                Date = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")
            },
            Spd = performanceMetrics.SpeedMetrics,
            Dst = performanceMetrics.DistanceMetrics,
            Rng = performanceMetrics.RangeMetrics,
            Shift = new { }
        };
    }
    
    public PerformanceMetrics CalculatePerformanceMetrics(IEnumerable<string> dataLines)
    {
        var speedMetrics = new Dictionary<string, string>();
        var distanceMetrics = new Dictionary<string, string[]>();
        var rangeMetrics = new Dictionary<string, string>();
        
        try
        {
            // Parse data points for performance calculations
            var dataPoints = new List<(double time, double speed, double distance)>();
            
            foreach (var line in dataLines)
            {
                if (string.IsNullOrWhiteSpace(line)) continue;
                
                var fields = line.Split(';');
                if (fields.Length >= 6)
                {
                    // Use invariant culture for cross-platform number parsing
                    if (double.TryParse(fields[0], NumberStyles.Float, CultureInfo.InvariantCulture, out var time) &&
                        double.TryParse(fields[3], NumberStyles.Float, CultureInfo.InvariantCulture, out var speed) &&
                        double.TryParse(fields[5], NumberStyles.Float, CultureInfo.InvariantCulture, out var distance))
                    {
                        dataPoints.Add((time, speed, distance));
                    }
                }
            }
            
            if (dataPoints.Count > 0)
            {
                // Calculate speed achievements (0-60, 0-100, etc.)
                var speedTargets = new[] { 60, 100, 150, 200 };
                foreach (var target in speedTargets)
                {
                    var achievement = dataPoints.FirstOrDefault(p => p.speed >= target);
                    if (achievement != default)
                    {
                        speedMetrics[$"0-{target}"] = achievement.time.ToString("F2", CultureInfo.InvariantCulture);
                    }
                }
                
                // Calculate distance achievements
                var distanceTargets = new[] { 
                    ("60 ft", 18.29), // 60 feet in meters (cross-platform constant)
                    ("201 m", 201),
                    ("402 m", 402)
                };
                
                foreach (var (label, targetDistance) in distanceTargets)
                {
                    var achievement = dataPoints.FirstOrDefault(p => p.distance >= targetDistance);
                    if (achievement != default)
                    {
                        distanceMetrics[label] = new[] { 
                            achievement.time.ToString("F2", CultureInfo.InvariantCulture),
                            achievement.speed.ToString("F0", CultureInfo.InvariantCulture)
                        };
                    }
                }
                
                // Calculate speed range metrics (e.g., 100-200 km/h)
                var maxSpeed = dataPoints.Max(p => p.speed);
                if (maxSpeed >= 200)
                {
                    var start100 = dataPoints.FirstOrDefault(p => p.speed >= 100);
                    var reach200 = dataPoints.FirstOrDefault(p => p.speed >= 200);
                    if (start100 != default && reach200 != default)
                    {
                        var rangeTime = reach200.time - start100.time;
                        rangeMetrics["100-200"] = rangeTime.ToString("F2", CultureInfo.InvariantCulture);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            // Log warning but don't fail the operation
            System.Diagnostics.Debug.WriteLine($"Warning: Error calculating performance metrics: {ex.Message}");
        }
        
        return new PerformanceMetrics
        {
            SpeedMetrics = speedMetrics,
            DistanceMetrics = distanceMetrics,
            RangeMetrics = rangeMetrics
        };
    }
}