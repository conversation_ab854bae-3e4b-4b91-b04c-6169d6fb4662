using DrawnUi;
using SkiaSharp;
using System.ComponentModel;
using System.Windows.Input;

namespace Racebox.Views.Partials;

public class DrawnHistoryCell : SkiaLayout, ISkiaGestureListener, ISkiaCell
{
    public DrawnHistoryCell()
    {
        if (SvgStatusOk == null)
            SvgStatusOk = App.Current.Resources.Get<string>("SvgStatusOk");

        if (SvgStatusFail == null)
            SvgStatusFail = App.Current.Resources.Get<string>("SvgStatusFail");
    }


    private INotifyPropertyChanged _lastContext;

    public override void OnDisposing()
    {
        base.OnDisposing();

        FreeContext();
    }

    void FreeContext()
    {
        if (_lastContext != null)
        {
            _lastContext = null;
        }
    }

    void AttachContext()
    {
        if (BindingContext != null)
        {
            _lastContext = BindingContext as INotifyPropertyChanged;
            //if (_lastContext != null)
            //	_lastContext.PropertyChanged += Item_PropertyChanged;
        }
    }

    public void ApplyBindingContext()
    {
        if (BindingContext != _lastContext && !_isAttaching)
        {
            _isAttaching = true;

            FreeContext();

            if (_lastContext == null) //todo _isAttaching lock with cancel token !
            {

                SetContent();

                AttachContext();

            }


            _isAttaching = false;
        }
    }

    protected override void OnBindingContextChanged()
    {
        base.OnBindingContextChanged();

        ApplyBindingContext();
    }

    private static IEnumerable<Setter> _styles;

    protected SkiaLabel LabelId;
    protected SkiaLabel LabelInfo;
    protected SkiaLabel LabelResults;
    protected SkiaSvg IconValid;

    private static string SvgStatusOk;
    private static string SvgStatusFail;

    public void SetContent()
    {


        if (BindingContext is HistoryCellData model)
        {
            LockUpdate(true);

            if (LabelId == null)
            {
                LabelId = this.FindView<SkiaLabel>("LabelId");
            }
            if (LabelId != null)
            {
                LabelId.Text = $"#{model.Id}";
            }

            if (LabelInfo == null)
            {
                LabelInfo = this.FindView<SkiaLabel>("LabelInfo");
            }
            if (LabelInfo != null)
            {
                LabelInfo.Text = model.DisplayInfo;
            }

            if (LabelResults == null)
            {
                LabelResults = this.FindView<SkiaLabel>("LabelResults");
            }
            if (LabelResults != null)
            {
                LabelResults.Text = model.DisplayResults;
            }

            if (IconValid == null)
            {
                IconValid = FindView<SkiaSvg>("IconValid");
            }

            if (IconValid != null)
            {
                if (model.IsValid)
                {
                    IconValid.SvgString = SvgStatusOk;
                    IconValid.TintColor = Colors.Green;
                }
                else
                {
                    IconValid.SvgString = SvgStatusFail;
                    IconValid.TintColor = Colors.Red;
                }
            }

            LockUpdate(false);
            Update();

        }
    }

    public override ISkiaGestureListener ProcessGestures(SkiaGesturesParameters args, GestureEventProcessingInfo apply)
    {
        if (args.Type == TouchActionResult.Tapped)
        {
            PlayRippleAnimation(Colors.White, args.Event.Location.X / Super.Screen.Density, args.Event.Location.Y / Super.Screen.Density);

            if (CommandTapped != null)
            {
                Tasks.StartDelayedAsync(TimeSpan.FromMilliseconds(250), async () =>
                {
                    await Task.Run(() => { CommandTapped?.Execute(this.BindingContext); }).ConfigureAwait(false);
                });
            }

            return this;
        }

        return base.ProcessGestures(args, apply);
    }

    //-------------------------------------------------------------
    // CommandTapped
    //-------------------------------------------------------------
    public static readonly BindableProperty CommandTappedProperty = BindableProperty.Create(nameof(CommandTapped), typeof(ICommand), typeof(DrawnHistoryCell),
        null);
    public ICommand CommandTapped
    {
        get { return (ICommand)GetValue(CommandTappedProperty); }
        set { SetValue(CommandTappedProperty, value); }
    }

    //-------------------------------------------------------------
    // CommandLongPressing
    //-------------------------------------------------------------
    public static readonly BindableProperty CommandLongPressingProperty = BindableProperty.Create(nameof(CommandLongPressing), typeof(ICommand), typeof(DrawnHistoryCell),
        null);


    private bool _isAttaching;

    public ICommand CommandLongPressing
    {
        get { return (ICommand)GetValue(CommandLongPressingProperty); }
        set { SetValue(CommandLongPressingProperty, value); }
    }

    CancellationTokenSource cancellationTokenSource;



    public void OnScrolled()
    { }
}