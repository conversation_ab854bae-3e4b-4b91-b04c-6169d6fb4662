﻿using SkiaSharp;

namespace Racebox.Views.Controls
{
    public class GfxScale : SkiaLayout
    {
        private static void NeedUpdate(BindableObject bindable, object oldvalue, object newvalue)
        {
            if (bindable is GfxScale control)
            {
                control.Render();
            }
        }


        public void Render()
        {
            _dirty = true;

            Update();
        }

        SkiaLabel CreatePointLabel(double value, double translationX, double translationY)
        {
            var text = $"{value:0.0}".Replace("-", string.Empty).Trim();

            if (ShowSign)
            {
                if (value > 0)
                {
                    text = "+" + text;
                }
                else
                if (value < 0)
                {
                    text = "-" + text;
                }
            }

            return new SkiaLabel()
            {
                FontFamily = "FontText",
                FontSize = 10,
                Text = text,
                TextColor = Color.FromArgb("#82E8E3D7"),
                TranslationX = Orientation == ScrollOrientation.Horizontal ? translationX : translationY - 12.0,
                TranslationY = Orientation == ScrollOrientation.Horizontal ? translationY : translationX,
                HorizontalOptions = Orientation == ScrollOrientation.Horizontal ? LayoutOptions.Center : LayoutOptions.End,
                VerticalOptions = Orientation == ScrollOrientation.Horizontal ? LayoutOptions.Start : LayoutOptions.Center
            };
        }

        SkiaShape CreateScalePointLine(double translationX,
            double translationY,
            double height)
        {
            return new SkiaShape()
            {
                StrokeWidth = 0.0,
                WidthRequest = Orientation == ScrollOrientation.Horizontal ? 1.0 : height,
                HeightRequest = Orientation == ScrollOrientation.Horizontal ? height : 1.0,
                Type = ShapeType.Rectangle,
                BackgroundColor = ScaleColor,
                TranslationX = Orientation == ScrollOrientation.Horizontal ? translationX : translationY,
                TranslationY = Orientation == ScrollOrientation.Horizontal ? translationY : translationX,
                HorizontalOptions = Orientation == ScrollOrientation.Horizontal ? LayoutOptions.Center : LayoutOptions.End,
                VerticalOptions = Orientation == ScrollOrientation.Horizontal ? LayoutOptions.End : LayoutOptions.Center
            };
        }

        void Build(SKRect destination, double scale)
        {
            var lineLength = 8.0;

            var scaledWidth = Orientation == ScrollOrientation.Horizontal ? destination.Width / scale : destination.Height / scale;

            var totalNumbers = Points * 2 + 1; // *per side + center

            var step = Limit / (Points * 1.0);

            var stepDistance = scaledWidth / (totalNumbers - 1) - SideOffset;

            var children = new List<SkiaControl>();

            var y = 0.0;

            //base
            children.Add(CreatePointLabel(0, 0, y));
            children.Add(CreateScalePointLine(0, 0, lineLength));

            for (int i = 1; i <= Points; i++)
            {
                var value = step * i;

                if (Invert)
                {
                    value = -value;
                }

                children.Add(CreatePointLabel(-value, stepDistance * i, y));
                children.Add(CreatePointLabel(value, -stepDistance * i, y));

                var offset = stepDistance * i;

                children.Add(CreateScalePointLine(offset - stepDistance / 2.0, 0, lineLength / 2.0));
                children.Add(CreateScalePointLine(offset, 0, lineLength));

                children.Add(CreateScalePointLine(-offset + stepDistance / 2.0, 0, lineLength / 2.0));
                children.Add(CreateScalePointLine(-offset, 0, lineLength));
            }

            SetChildren(children);
        }

        private SKRect lastDestination = SKRect.Empty;

        public override void Arrange(SKRect destination, float widthRequest, float heightRequest, float scale)
        {
            base.Arrange(destination, widthRequest, heightRequest, scale);

            if (lastDestination != Destination)
            {
                lastDestination = Destination;
                _dirty = true;
            }

            if (_dirty)
            {
                _dirty = false;
                Build(Destination, scale);
            }
        }

        private bool _dirty = true;

        public static readonly BindableProperty InvertProperty = BindableProperty.Create(nameof(Invert),
            typeof(bool),
            typeof(GfxScale),
            false,
            propertyChanged: NeedUpdate);
        public bool Invert
        {
            get { return (bool)GetValue(InvertProperty); }
            set { SetValue(InvertProperty, value); }
        }

        public static readonly BindableProperty OrientationProperty = BindableProperty.Create(nameof(Orientation),
        typeof(ScrollOrientation),
        typeof(GfxScale),
        ScrollOrientation.Horizontal,
        propertyChanged: NeedUpdate);
        public ScrollOrientation Orientation
        {
            get { return (ScrollOrientation)GetValue(OrientationProperty); }
            set { SetValue(OrientationProperty, value); }
        }


        public static readonly BindableProperty LimitProperty = BindableProperty.Create(nameof(Limit),
        typeof(double),
        typeof(GfxScale),
        1.0, propertyChanged: NeedUpdate);
        public double Limit
        {
            get { return (double)GetValue(LimitProperty); }
            set { SetValue(LimitProperty, value); }
        }

        public static readonly BindableProperty PointsProperty = BindableProperty.Create(nameof(Points),
            typeof(int),
            typeof(GfxScale),
            2, propertyChanged: NeedUpdate);
        public int Points
        {
            get { return (int)GetValue(PointsProperty); }
            set { SetValue(PointsProperty, value); }
        }


        public static readonly BindableProperty SideOffsetProperty = BindableProperty.Create(nameof(SideOffset),
            typeof(double),
            typeof(GfxScale),
            8.0, propertyChanged: NeedUpdate);
        public double SideOffset
        {
            get { return (double)GetValue(SideOffsetProperty); }
            set { SetValue(SideOffsetProperty, value); }
        }

        public static readonly BindableProperty TextColorProperty = BindableProperty.Create(nameof(TextColor), typeof(Color), typeof(GfxScale),
            Colors.Gray,
            propertyChanged: NeedUpdate);
        public Color TextColor
        {
            get { return (Color)GetValue(TextColorProperty); }
            set { SetValue(TextColorProperty, value); }
        }

        public static readonly BindableProperty ScaleColorProperty = BindableProperty.Create(nameof(ScaleColor), typeof(Color), typeof(GfxScale),
            Colors.Gray,
            propertyChanged: NeedUpdate);
        public Color ScaleColor
        {
            get { return (Color)GetValue(ScaleColorProperty); }
            set { SetValue(ScaleColorProperty, value); }
        }

        public static readonly BindableProperty AccentColorProperty = BindableProperty.Create(nameof(AccentColor),
            typeof(Color), typeof(GfxScale),
            Colors.Red,
            propertyChanged: NeedUpdate);
        public Color AccentColor
        {
            get { return (Color)GetValue(AccentColorProperty); }
            set { SetValue(AccentColorProperty, value); }
        }

        public static readonly BindableProperty ShowSignProperty =
            BindableProperty.Create(nameof(ShowSign),
                typeof(bool),
                typeof(GfxScale),
                false,
                propertyChanged: NeedUpdate);
        public bool ShowSign
        {
            get { return (bool)GetValue(ShowSignProperty); }
            set { SetValue(ShowSignProperty, value); }
        }


    }
}
