﻿<?xml version="1.0" encoding="utf-8" ?>
<Grid
    x:Class="Racebox.Views.TabMonitor"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:drawn="clr-namespace:Racebox.Views.Drawn"
    xmlns:partials="clr-namespace:Racebox.Views.Partials"
    xmlns:strings="clr-namespace:Racebox.Shared.Strings;assembly=Racebox.Shared"
    xmlns:viewModels="clr-namespace:Racebox.ViewModels"
    x:Name="ThisTabMonitor"
    Padding="0,0,0,8"
    x:DataType="viewModels:RaceBoxDeviceViewModel"
    RowDefinitions="52, 210, *"
    VerticalOptions="FillAndExpand">

    <Grid.Resources>
        <ResourceDictionary />
    </Grid.Resources>

    <!--<Grid.Background>
            <LinearGradientBrush EndPoint="0,1">
                <GradientStop Offset="0.0" Color="#343C45" />
                <GradientStop Offset="1.0" Color="#11161D" />
            </LinearGradientBrush>
        </Grid.Background>-->

    <partials:GradientToBorderView
        Grid.RowSpan="3"
        HorizontalOptions="Fill"
        MissingHeight="{Binding NavbarModel.BottomTabsHeightRequest}"
        VerticalOptions="Fill" />

    <!--  CONNECTION BAR  -->
    <partials:ConnectionBar
        Grid.Row="0"
        Value="{Binding SignalStrength}" />

    <!--  GAUGE AREA  -->
    <draw:Canvas
        Grid.Row="1"
        RenderingMode="Accelerated"
        HorizontalOptions="Fill"
        Tag="MonitorGauge"
        VerticalOptions="Fill">

        <draw:SkiaLayout
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <!--  Static layer  -->
            <draw:SkiaLayout
                Margin="0,0,38,36"
                Padding="14"
                HorizontalOptions="Fill"
                Opacity="0.41"
                UseCache="Image"
                VerticalOptions="Fill">

                <!--  GAUGE BACKGROUND  -->
                <draw:SkiaShape
                    BackgroundColor="#42464B"
                    HorizontalOptions="Center"
                    LockRatio="-1"
                    StrokeColor="#15191E"
                    StrokeWidth="1.85"
                    Type="Circle"
                    VerticalOptions="Fill"
                    WidthRequest="-1">

                    <draw:SkiaShape.StrokeGradient>

                        <draw:SkiaGradient
                            EndXRatio="0.8"
                            EndYRatio="0.8"
                            StartXRatio="0.2"
                            StartYRatio="0.2"
                            Type="Linear">
                            <draw:SkiaGradient.Colors>
                                <Color>#15191E</Color>
                                <Color>#42464B</Color>
                            </draw:SkiaGradient.Colors>
                        </draw:SkiaGradient>

                    </draw:SkiaShape.StrokeGradient>
                    <draw:SkiaShape.FillGradient>

                        <draw:SkiaGradient
                            EndXRatio="1"
                            EndYRatio="1"
                            StartXRatio="0"
                            StartYRatio="0"
                            Type="Linear">
                            <draw:SkiaGradient.Colors>
                                <Color>#232323</Color>
                                <Color>#000000</Color>
                            </draw:SkiaGradient.Colors>
                        </draw:SkiaGradient>

                    </draw:SkiaShape.FillGradient>
                    <draw:SkiaShape.Shadows>

                        <draw:SkiaShadow
                            Blur="15"
                            Opacity="0.64"
                            X="4"
                            Y="4"
                            Color="Black" />

                        <draw:SkiaShadow
                            Blur="15"
                            Opacity="0.32"
                            X="-4"
                            Y="-4"
                            Color="White" />

                    </draw:SkiaShape.Shadows>

                </draw:SkiaShape>

            </draw:SkiaLayout>

            <!--  Dynamic foreground  -->
            <draw:SkiaLayout
                Margin="0,0,38,36"
                BackgroundColor="Transparent"
                HorizontalOptions="Fill"
                Tag="GaugeStack"
                UseCache="Operations"
                VerticalOptions="Fill">

                <draw:SkiaLabel
                    Padding="4"
                    BackgroundColor="#11924321"
                    CharacterSpacing="2.8"
                    FontFamily="FontText"
                    FontSize="30"
                    HorizontalOptions="Center"
                    IsVisible="{Binding UseMock}"
                    Rotation="-25"
                    Text="DEMO"
                    TextColor="#10ffffff"
                    TranslationX="0"
                    TranslationY="0"
                    UseCache="Operations"
                    VerticalOptions="Center"
                    ZIndex="100" />

                <!--#region GAUGE-->

                <!--  ARC  -->
                <!--  Value2="{Binding DisplaySpeedValue}"  -->
                <draw:SkiaShape
                    x:Name="SpeedArc"
                    Margin="22"
                    HorizontalOptions="Center"
                    IsVisible="True"
                    LockRatio="-1"
                    StrokeCap="Butt"
                    StrokeColor="Red"
                    StrokeWidth="6"
                    Type="Arc"
                    UseCache="Operations"
                    Value1="125"
                    Value2="{Binding DisplaySpeedValue}"
                    VerticalOptions="Fill">

                    <draw:SkiaShape.StrokeGradient>

                        <draw:SkiaGradient Type="Sweep">
                            <draw:SkiaGradient.Colors>
                                <Color>#CB6235</Color>
                                <Color>#CB6235</Color>
                                <Color>#00CB6235</Color>
                            </draw:SkiaGradient.Colors>
                            <draw:SkiaGradient.ColorPositions>
                                <x:Double>0.0</x:Double>
                                <x:Double>0.933</x:Double>
                                <x:Double>1.0</x:Double>
                            </draw:SkiaGradient.ColorPositions>
                        </draw:SkiaGradient>

                    </draw:SkiaShape.StrokeGradient>

                </draw:SkiaShape>

                <!--  SPEED  -->
                <draw:SkiaLabel
                    x:Name="LabelSpeed"
                    FontFamily="FontTextBold"
                    FontSize="32"
                    HorizontalOptions="Center"
                    MaxLines="1"
                    MonoForDigits="8"
                    Text="{Binding DisplaySpeedMonitor}"
                    TranslationY="0"
                    UseCache="Operations"
                    VerticalOptions="Center">
                    <!--<draw:SkiaControl.Styles>
                        <draw:ConditionalStyle
                            State="Normal"
                            Style="{x:StaticResource SkiaEnabledLabel}" />
                        <draw:ConditionalStyle
                            State="Disabled"
                            Style="{x:StaticResource SkiaDisabledLabel}" />
                    </draw:SkiaControl.Styles>-->
                    <draw:SkiaControl.Triggers>
                        <DataTrigger
                            Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Normal'}"
                            TargetType="draw:SkiaLabel"
                            Value="True">
                            <Setter Property="Style" Value="{x:StaticResource SkiaEnabledLabel}" />
                        </DataTrigger>
                        <DataTrigger
                            Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Disabled'}"
                            TargetType="draw:SkiaLabel"
                            Value="True">
                            <Setter Property="Style" Value="{x:StaticResource SkiaDisabledLabel}" />
                        </DataTrigger>
                    </draw:SkiaControl.Triggers>

                </draw:SkiaLabel>

                <!--  speed units  -->
                <draw:SkiaLabel
                    FontFamily="FontText"
                    FontSize="14"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    MaxLines="1"
                    Text="{Binding SpeedUnitsDisplay}"
                    TranslationY="34"
                    UseCache="Operations"
                    VerticalOptions="Center"
                    WidthRequest="100">
                    <draw:SkiaControl.Triggers>
                        <DataTrigger
                            Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Normal'}"
                            TargetType="draw:SkiaLabel"
                            Value="True">
                            <Setter Property="Style" Value="{x:StaticResource SkiaEnabledLabel}" />
                        </DataTrigger>
                        <DataTrigger
                            Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Disabled'}"
                            TargetType="draw:SkiaLabel"
                            Value="True">
                            <Setter Property="Style" Value="{x:StaticResource SkiaDisabledLabel}" />
                        </DataTrigger>
                    </draw:SkiaControl.Triggers>

                    <!--<draw:SkiaControl.Styles>
                        <draw:ConditionalStyle
                            State="Normal"
                            Style="{x:StaticResource SkiaEnabledLabel}" />
                        <draw:ConditionalStyle
                            State="Disabled"
                            Style="{x:StaticResource SkiaDisabledLabel}" />
                    </draw:SkiaControl.Styles>-->
                </draw:SkiaLabel>

                <!--#endregion-->

            </draw:SkiaLayout>

        </draw:SkiaLayout>

    </draw:Canvas>

    <partials:ScaleBarVerticalV2
        x:Name="MinMaxBarGVertical"
        Grid.Row="1"
        Grid.RowSpan="1"
        Margin="170,16,0,46"
        HorizontalOptions="Center"
        Invert="True"
        Limit="0.8"
        ValueSideOffset="14"
        VerticalOptions="Fill"
        Value="{Binding DisplayAccelerationValue}" />

    <partials:ScaleBarV2
        x:Name="MinMaxBarG"
        Grid.Row="1"
        Margin="0,0,38,0"
        HorizontalOptions="Center"
        Limit="1.2"
        ValueSideOffset="14"
        VerticalOptions="End"
        WidthRequest="150"
        Value="{Binding DisplayAccelerationSideValue}" />

    <!--  G  -->
    <Label
        x:Name="LetterG"
        Grid.Row="1"
        Margin="176,0,0,6"
        FontSize="26"
        HorizontalOptions="Center"
        Opacity="0.15"
        Text="G"
        VerticalOptions="End">
        <Label.GestureRecognizers>
            <TapGestureRecognizer
                NumberOfTapsRequired="1"
                Tapped="OnTappedG" />
        </Label.GestureRecognizers>
    </Label>

    <!--  FRAME  -->

    <draw:Canvas
        x:Name="MonitorTable"
        Grid.Row="2"
        Gestures="Enabled"
        RenderingMode="Accelerated"
        HorizontalOptions="Fill"
        MinimumHeightRequest="50"
        Tag="MonitorTable"
        VerticalOptions="Fill">

        <draw:SkiaLayout
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <drawn:DrawnFrame />

            <!--
                We are setting IgnoreChildrenInvalidations here because otherwise when
                labels inside update their text they will force grid invalidation
            -->
            <draw:SkiaScroll
                x:Name="MainScroll"
                Margin="10,10,10,4"
                AutoScrollingSpeedMs="600"
                ChangeVelocityScrolled="0.75"
                HorizontalOptions="Fill"
                RefreshDistanceLimit="4">

                <draw:SkiaDecoratedGrid
                    Margin="0,0"
                    Padding="4,4"
                    ColumnDefinitions="*,*"
                    ColumnSpacing="1.0"
                    DefaultRowDefinition="68"
                    IgnoreChildrenInvalidations="True"
                    RowSpacing="1.0"
                    Tag="Deco"
                    Type="Grid"
                    UseCache="ImageComposite">

                    <!--  row 0  -->

                    <draw:SkiaLayout
                        Grid.Column="0"
                        HorizontalOptions="Fill"
                        UseCache="Operations"
                        VerticalOptions="Fill">

                        <draw:SkiaLabel
                            MonoForDigits="8"
                            Style="{StaticResource OutputSkiaLabelStyle}"
                            Text="{Binding DisplayAltitudeMonitor}" />

                        <draw:SkiaLabel
                            HorizontalOptions="Center"
                            Style="{StaticResource SkiaLabelStyle}"
                            Text="{x:Static strings:ResStrings.Altitude}"
                            TranslationY="-12"
                            UseCache="Operations"
                            VerticalOptions="End" />

                    </draw:SkiaLayout>

                    <draw:SkiaLayout
                        Grid.Column="1"
                        HorizontalOptions="FillAndExpand"
                        UseCache="Operations"
                        VerticalOptions="FillAndExpand">

                        <draw:SkiaLabel
                            MonoForDigits="8"
                            Style="{StaticResource OutputSkiaLabelStyle}"
                            Text="{Binding DisplayHeading}" />

                        <draw:SkiaLabel
                            HorizontalOptions="Center"
                            Style="{StaticResource SkiaLabelStyle}"
                            Text="{x:Static strings:ResStrings.Heading}"
                            TranslationY="-12"
                            UseCache="Operations"
                            VerticalOptions="End" />



                    </draw:SkiaLayout>

                    <!--  row 1  -->

                    <!--  MaxSpeed  -->
                    <draw:SkiaLayout
                        Grid.Row="1"
                        Grid.Column="0"
                        HorizontalOptions="FillAndExpand"
                        UseCache="Operations"
                        VerticalOptions="FillAndExpand">


                        <draw:SkiaLabel
                            AutoSize="FitHorizontal"
                            FontSize="17"
                            MonoForDigits="8"
                            Style="{StaticResource OutputSkiaLabelStyle}"
                            Text="{Binding DisplayMaxSpeed}" />

                        <draw:SkiaLabel
                            HorizontalOptions="Center"
                            Style="{StaticResource SkiaLabelStyle}"
                            Text="{x:Static strings:ResStrings.MaxSpeed}"
                            TranslationY="-12"
                            UseCache="Operations"
                            VerticalOptions="End" />


                    </draw:SkiaLayout>

                    <!--  УКЛОН  -->
                    <draw:SkiaLayout
                        Grid.Row="1"
                        Grid.Column="1"
                        HorizontalOptions="FillAndExpand"
                        UseCache="Operations"
                        VerticalOptions="FillAndExpand">


                        <draw:SkiaLabel
                            MonoForDigits="8"
                            Style="{StaticResource OutputSkiaLabelStyle}"
                            Text="{Binding DisplayIncline}" />

                        <draw:SkiaLabel
                            Style="{StaticResource SkiaLabelStyle}"
                            Text="{x:Static strings:ResStrings.Incline}"
                            TranslationY="-12"
                            UseCache="Operations"
                            VerticalOptions="End" />


                    </draw:SkiaLayout>

                    <!--  row 2  -->

                    <!--  DisplayAcceleration  -->
                    <draw:SkiaLayout
                        Grid.Row="2"
                        Grid.Column="0"
                        HorizontalOptions="FillAndExpand"
                        UseCache="Operations"
                        VerticalOptions="FillAndExpand">


                        <draw:SkiaLabel
                            AutoSize="FitHorizontal"
                            MonoForDigits="8"
                            Style="{StaticResource OutputSkiaLabelStyle}"
                            Text="{Binding DisplayAcceleration}" />

                        <draw:SkiaLabel
                            HorizontalOptions="Center"
                            Style="{StaticResource SkiaLabelStyle}"
                            Text="{x:Static strings:ResStrings.Acceleration}"
                            TranslationY="-12"
                            UseCache="Operations"
                            VerticalOptions="End" />


                    </draw:SkiaLayout>

                    <!--  DisplaySideAcceleration  -->
                    <draw:SkiaLayout
                        Grid.Row="2"
                        Grid.Column="1"
                        HorizontalOptions="FillAndExpand"
                        UseCache="Operations"
                        VerticalOptions="FillAndExpand">


                        <draw:SkiaLabel
                            MonoForDigits="8"
                            Style="{StaticResource OutputSkiaLabelStyle}"
                            Text="{Binding DisplaySideAcceleration}" />

                        <draw:SkiaLabel
                            Style="{StaticResource SkiaLabelStyle}"
                            Text="{x:Static strings:ResStrings.AccelerationSide}"
                            TranslationY="-12"
                            UseCache="Operations"
                            VerticalOptions="End" />


                    </draw:SkiaLayout>

                    <!--  row 3  -->

                    <!--  lat  -->
                    <draw:SkiaLayout
                        Grid.Row="3"
                        Grid.Column="0"
                        HorizontalOptions="FillAndExpand"
                        UseCache="Operations"
                        VerticalOptions="FillAndExpand">

                        <draw:SkiaLabel
                            AutoSize="FitHorizontal"
                            FontSize="17"
                            MonoForDigits="8"
                            Style="{StaticResource OutputSkiaLabelStyle}"
                            Text="{Binding DisplayLat}" />

                        <draw:SkiaLabel
                            HorizontalOptions="Center"
                            Style="{StaticResource SkiaLabelStyle}"
                            Text="{x:Static strings:ResStrings.Latitude}"
                            TranslationY="-12"
                            UseCache="Operations"
                            VerticalOptions="End" />

                    </draw:SkiaLayout>
                    <!--  lon  -->
                    <draw:SkiaLayout
                        Grid.Row="3"
                        Grid.Column="1"
                        HorizontalOptions="FillAndExpand"
                        UseCache="Operations"
                        VerticalOptions="FillAndExpand">

                        <draw:SkiaLabel
                            AutoSize="FitHorizontal"
                            FontSize="17"
                            MonoForDigits="8"
                            Style="{StaticResource OutputSkiaLabelStyle}"
                            Text="{Binding DisplayLon}" />

                        <draw:SkiaLabel
                            HorizontalOptions="Center"
                            Style="{StaticResource SkiaLabelStyle}"
                            Text="{x:Static strings:ResStrings.Longitude}"
                            TranslationY="-12"
                            UseCache="Operations"
                            VerticalOptions="End" />

                    </draw:SkiaLayout>

                    <!--  row 4  -->

                    <!--  batt  -->
                    <draw:SkiaLayout
                        Grid.Row="4"
                        Grid.Column="0"
                        HorizontalOptions="FillAndExpand"
                        UseCache="Operations"
                        VerticalOptions="FillAndExpand">

                        <draw:SkiaLabel
                            AutoSize="FitHorizontal"
                            FontSize="17"
                            MonoForDigits="8"
                            Style="{StaticResource OutputSkiaLabelStyle}"
                            Text="{Binding DisplayPower}" />

                        <draw:SkiaLabel
                            HorizontalOptions="Center"
                            Style="{StaticResource SkiaLabelStyle}"
                            Text="{x:Static strings:ResStrings.X_Power}"
                            TranslationY="-12"
                            UseCache="Operations"
                            VerticalOptions="End" />


                    </draw:SkiaLayout>

                    <!--  weather  -->
                    <draw:SkiaLayout
                        Grid.Row="4"
                        Grid.Column="1"
                        draw:AddGestures.CommandTapped="{Binding CommandUpdateWeather}"
                        HorizontalOptions="FillAndExpand"
                        UseCache="Operations"
                        VerticalOptions="FillAndExpand">

                        <draw:SkiaLayout
                            Margin="0,0,0,0"
                            HorizontalOptions="Center"
                            TranslationY="15"
                            Type="Row">

                            <draw:SkiaLabel
                                FontFamily="Fa"
                                FontSize="16"
                                Text="{Binding WeatherIcon}"
                                TranslationY="4" />

                            <draw:SkiaLabel
                                Margin="0"
                                HorizontalOptions="Start"
                                HorizontalTextAlignment="Start"
                                MonoForDigits="8"
                                Style="{StaticResource OutputSkiaLabelStyle}"
                                Text="{Binding DisplayTemp}"
                                TranslationY="0"
                                VerticalOptions="Start" />

                        </draw:SkiaLayout>

                        <draw:SkiaLabel
                            HorizontalOptions="Center"
                            Style="{StaticResource SkiaLabelStyle}"
                            Text="{x:Static strings:ResStrings.Weather}"
                            TranslationY="-12"
                            UseCache="Operations"
                            VerticalOptions="End" />

                    </draw:SkiaLayout>

                    <!--  row 5  -->
                    <draw:SkiaLayout
                        Grid.Row="5"
                        Grid.Column="0"
                        HorizontalOptions="FillAndExpand"
                        UseCache="Operations"
                        VerticalOptions="FillAndExpand">

                        <draw:SkiaLabel
                            AutoSize="FitHorizontal"
                            FontSize="17"
                            Style="{StaticResource OutputSkiaLabelStyle}"
                            Text="{Binding DisplayDevice}" />

                        <draw:SkiaLabel
                            MaxLines="1"
                            Style="{StaticResource SkiaLabelStyle}"
                            Text="{x:Static strings:ResStrings.Device}"
                            TranslationY="-12"
                            UseCache="Operations"
                            VerticalOptions="End" />

                    </draw:SkiaLayout>

                    <!--  HZ  -->
                    <draw:SkiaLayout
                        Grid.Row="5"
                        Grid.Column="1"
                        HorizontalOptions="FillAndExpand"
                        UseCache="Operations"
                        VerticalOptions="FillAndExpand">



                        <draw:SkiaLabel
                            MonoForDigits="8"
                            Style="{StaticResource OutputSkiaLabelStyle}"
                            Text="{Binding DisplayFrequency}" />

                        <draw:SkiaLabel
                            Style="{StaticResource SkiaLabelStyle}"
                            Text="{x:Static strings:ResStrings.Frequency}"
                            TranslationY="-12"
                            UseCache="Operations"
                            VerticalOptions="End" />



                    </draw:SkiaLayout>


                </draw:SkiaDecoratedGrid>

            </draw:SkiaScroll>
        </draw:SkiaLayout>
    </draw:Canvas>




</Grid>
