﻿
using System.Runtime.CompilerServices;
using AppoMobi.Framework.Maui.Controls;

namespace Racebox.Views.Controls
{
    public class EntryDouble : AMEntry
    {
        public EntryDouble()
        {
            Keyboard = Keyboard.Numeric;
            this.TextChanged += Entry_OnTextChanged;
        }

        //-------------------------------------------------------------
        // Source
        //-------------------------------------------------------------
        private const string nameSource = "Source";

        public static readonly BindableProperty SourceProperty =
            BindableProperty.Create(nameSource, typeof(double), typeof(EntryDouble), 0.0, BindingMode.TwoWay);
        public double Source
        {
            get { return (double)GetValue(SourceProperty); }
            set { SetValue(SourceProperty, value); }
        }

        //-------------------------------------------------------------
        // Min
        //-------------------------------------------------------------
        private const string nameMin = "Min";

        public static readonly BindableProperty MinProperty =
            BindableProperty.Create(nameMin, typeof(double), typeof(EntryDouble), Double.NegativeInfinity, BindingMode.TwoWay);
        public double Min
        {
            get { return (double)GetValue(MinProperty); }
            set { SetValue(MinProperty, value); }
        }

        //-------------------------------------------------------------
        // Max
        //-------------------------------------------------------------
        private const string nameMax = "Max";

        public static readonly BindableProperty MaxProperty =
            BindableProperty.Create(nameMax, typeof(double), typeof(EntryDouble), Double.PositiveInfinity, BindingMode.TwoWay);
        public double Max
        {
            get { return (double)GetValue(MaxProperty); }
            set { SetValue(MaxProperty, value); }
        }


        protected override void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            if (propertyName == nameSource)
            {
                Text = Source.ToString();
            }
            //else
            //if (propertyName == "Text")
            //{
            //    Source = Text.ToInteger();
            //}

            base.OnPropertyChanged(propertyName);
        }

        private string _prevousText;

        protected override void OnBindingContextChanged()
        {
            _prevousText = Text;
            if (_prevousText == null)
            {
                Text = "0";
            }

            base.OnBindingContextChanged();
        }

        private bool _HasMinus;
        public bool HasMinus
        {
            get { return _HasMinus; }
            set
            {
                if (_HasMinus != value)
                {
                    _HasMinus = value;
                    OnPropertyChanged();
                }
            }
        }

        private void Entry_OnTextChanged(object sender, TextChangedEventArgs e)
        {
            bool validated = false;
            var input = Text;
            var value = 0.0;
            var clean = Text;

            if (input != null)
            {
                //check numeric
                clean = input.Replace(".", "").Replace(",", "");
                validated = clean.All(a => a == '-' || Char.IsNumber(a));
                if (clean.Contains("-") && Source == 0)
                {
                    HasMinus = true;
                }
                else
                {
                    HasMinus = false;
                }

                if (validated)
                {
                    value = clean.ToDouble();

                    if (value > Max || value < Min)
                        validated = false; //thou shell not pass
                }
            }

            if (validated && !HasMinus)
            {
                var result = value;
                if (true)//result.HasValue())
                {
                    _prevousText = Text;
                    Source = result;
                }
            }
            else
            {
                if (HasMinus)
                {
                    Text = clean;
                }
                else
                    Text = _prevousText;
            }

        }

    }
}
