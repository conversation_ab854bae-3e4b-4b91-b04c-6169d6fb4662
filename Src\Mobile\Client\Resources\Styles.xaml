﻿<?xml version="1.0" encoding="UTF-8" ?>
<?xaml-comp compile="true" ?>
<ResourceDictionary
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:drawn="http://schemas.appomobi.com/drawnUi/2023/drawn">

    <!--  default drawn label  -->

    <Style TargetType="draw:SkiaLabel" ApplyToDerivedTypes="True">
        <Setter Property="TextColor" Value="#E8E3D7" />
        <Setter Property="FontFamily" Value="FontText" />
        <Setter Property="FontSize" Value="14" />
    </Style>


    <Style
        x:Key="SkiaLabelDefaultStyle"
        Class="SkiaLabelDefaultStyle"
        TargetType="draw:SkiaLabel">
        <Setter Property="TextColor" Value="#E8E3D7" />
        <Setter Property="FontFamily" Value="FontText" />
        <Setter Property="FontSize" Value="15" />
    </Style>

    <Style
        x:Key="SkiaLabelDisabledStyle"
        BasedOn="{StaticResource SkiaLabelDefaultStyle}"
        Class="SkiaLabelDisabledStyle"
        TargetType="draw:SkiaLabel">
        <Setter Property="TextColor" Value="{StaticResource Accent}" />
    </Style>

    <Style
        x:Key="SkiaLabelStyle"
        Class="SkiaLabelStyle"
        TargetType="draw:SkiaLabel">
        <Setter Property="TextColor" Value="#78E8E3D7" />
        <Setter Property="FontFamily" Value="FontText" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="MaxLines" Value="1" />
        <Setter Property="HorizontalOptions" Value="Center" />
    </Style>

    <Style
        x:Key="SkiaLabelStyleReport"
        BasedOn="{StaticResource SkiaLabelStyle}"
        Class="SkiaLabelStyleReport"
        TargetType="draw:SkiaLabel">
        <Setter Property="TextColor" Value="Black" />
    </Style>


    <Style
        x:Key="OutputSkiaLabelStyle"
        BasedOn="{StaticResource SkiaLabelStyle}"
        TargetType="draw:SkiaLabel">
        <Setter Property="Margin" Value="16,0,16,0" />
        <Setter Property="FontSize" Value="20" />
        <Setter Property="TextColor" Value="#E8E3D7" />
        <Setter Property="TranslationY" Value="15" />
    </Style>

    <Style
        x:Key="OutputSkiaLabelStyleReport"
        BasedOn="{StaticResource OutputSkiaLabelStyle}"
        Class="OutputSkiaLabelStyleReport"
        TargetType="draw:SkiaLabel">
        <Setter Property="TextColor" Value="Black" />
    </Style>

    <Style
        x:Key="SkiaEditorFieldTitle"
        BasedOn="{StaticResource SkiaLabelDefaultStyle}"
        Class="SkiaEditorFieldTitle"
        TargetType="draw:SkiaLabel">
        <Setter Property="FontSize" Value="12" />
        <Setter Property="TextColor" Value="Gray" />
    </Style>


    <!--  BTN START  -->

    <Style
        x:Key="SkiaStartBtnStopStyle"
        Class="SkiaStartBtnStopStyle"
        TargetType="draw:SkiaShape">
        <Setter Property="StrokeGradient">
            <draw:SkiaGradient
                EndXRatio="0.9"
                EndYRatio="0.9"
                StartXRatio="0.1"
                StartYRatio="0.1"
                Type="Linear">
                <draw:SkiaGradient.Colors>
                    <Color>#E47F53</Color>
                    <Color>#924321</Color>
                </draw:SkiaGradient.Colors>
            </draw:SkiaGradient>
        </Setter>
        <Setter Property="FillGradient">
            <draw:SkiaGradient
                EndXRatio="1"
                EndYRatio="1"
                StartXRatio="0"
                StartYRatio="0"
                Type="Linear">
                <draw:SkiaGradient.Colors>
                    <Color>#924321</Color>
                    <Color>#E47F53</Color>
                </draw:SkiaGradient.Colors>
            </draw:SkiaGradient>
        </Setter>
    </Style>

    <Style
        x:Key="SkiaStartBtnStartStyle"
        Class="SkiaStartBtnStartStyle"
        TargetType="draw:SkiaShape">
        <Setter Property="StrokeGradient">
            <draw:SkiaGradient
                EndXRatio="0.9"
                EndYRatio="0.9"
                StartXRatio="0.1"
                StartYRatio="0.1"
                Type="Linear">
                <draw:SkiaGradient.Colors>
                    <Color>#5DC10F</Color>
                    <Color>#3E830A</Color>
                </draw:SkiaGradient.Colors>
            </draw:SkiaGradient>
        </Setter>
        <Setter Property="FillGradient">
            <draw:SkiaGradient
                EndXRatio="1"
                EndYRatio="1"
                StartXRatio="0"
                StartYRatio="0"
                Type="Linear">
                <draw:SkiaGradient.Colors>
                    <Color>#3E830A</Color>
                    <Color>#5DC10F</Color>
                </draw:SkiaGradient.Colors>
            </draw:SkiaGradient>
        </Setter>
    </Style>



    <Style TargetType="ActivityIndicator">
        <Setter Property="Color" Value="{StaticResource Accent}" />
        <Setter Property="Scale">
            <Setter.Value>
                <OnPlatform x:TypeArguments="x:Double">
                    <On
                        Platform="Android"
                        Value="0.5" />
                </OnPlatform>
            </Setter.Value>
        </Setter>
    </Style>


    <Style TargetType="IndicatorView">
        <Setter Property="IndicatorColor" Value="{AppThemeBinding Light={StaticResource Gray200}, Dark={StaticResource Gray500}}" />
        <Setter Property="SelectedIndicatorColor" Value="{AppThemeBinding Light={StaticResource Gray950}, Dark={StaticResource Gray100}}" />
    </Style>

    <Style TargetType="Border">
        <Setter Property="Stroke" Value="{AppThemeBinding Light={StaticResource Gray200}, Dark={StaticResource Gray500}}" />
        <Setter Property="StrokeShape" Value="Rectangle" />
        <Setter Property="StrokeThickness" Value="1" />
    </Style>

    <Style TargetType="BoxView">
        <Setter Property="Color" Value="{AppThemeBinding Light={StaticResource Gray950}, Dark={StaticResource Gray200}}" />
    </Style>

    <Style TargetType="Button">
        <Setter Property="HorizontalOptions" Value="Center" />
        <Setter Property="WidthRequest" Value="150" />
        <Setter Property="TextColor" Value="White" />
        <Setter Property="BackgroundColor" Value="Gray" />
        <Setter Property="FontFamily" Value="FontText" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="CornerRadius" Value="18" />
        <Setter Property="Padding" Value="14,10" />
        <Setter Property="VisualStateManager.VisualStateGroups">
            <VisualStateGroupList>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="Disabled">
                        <VisualState.Setters>
                            <Setter Property="TextColor" Value="{StaticResource Gray200}" />
                            <Setter Property="BackgroundColor" Value="{StaticResource Gray600}" />
                        </VisualState.Setters>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateGroupList>
        </Setter>
    </Style>

    <Style TargetType="CheckBox">
        <Setter Property="Color" Value="{StaticResource White}" />
        <Setter Property="VisualStateManager.VisualStateGroups">
            <VisualStateGroupList>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="Disabled">
                        <VisualState.Setters>
                            <Setter Property="Color" Value="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}" />
                        </VisualState.Setters>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateGroupList>
        </Setter>
    </Style>

    <Style TargetType="DatePicker">
        <Setter Property="TextColor" Value="{AppThemeBinding Light={StaticResource Gray900}, Dark={StaticResource White}}" />
        <Setter Property="BackgroundColor" Value="Transparent" />
        <Setter Property="FontFamily" Value="FontText" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="VisualStateManager.VisualStateGroups">
            <VisualStateGroupList>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="Disabled">
                        <VisualState.Setters>
                            <Setter Property="TextColor" Value="{AppThemeBinding Light={StaticResource Gray200}, Dark={StaticResource Gray500}}" />
                        </VisualState.Setters>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateGroupList>
        </Setter>
    </Style>

    <Style
        ApplyToDerivedTypes="True"
        TargetType="Editor">
        <Setter Property="TextColor" Value="#E8E3D7" />
        <Setter Property="BackgroundColor" Value="#33000000" />
        <Setter Property="FontFamily" Value="FontText" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="PlaceholderColor" Value="#78E8E3D7" />
        <!--<Setter Property="VisualStateManager.VisualStateGroups">
            <VisualStateGroupList>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="Disabled">
                        <VisualState.Setters>
                            <Setter Property="TextColor" Value="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}" />
                        </VisualState.Setters>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateGroupList>
        </Setter>-->
    </Style>

    <Style
        ApplyToDerivedTypes="True"
        TargetType="Entry">
        <Setter Property="BackgroundColor" Value="#33000000" />
        <Setter Property="TextColor" Value="#E8E3D7" />
        <Setter Property="FontFamily" Value="FontText" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="PlaceholderColor" Value="#78E8E3D7" />
        <!--<Setter Property="VisualStateManager.VisualStateGroups">
            <VisualStateGroupList>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="Disabled">
                        <VisualState.Setters>
                            <Setter Property="TextColor" Value="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}" />
                        </VisualState.Setters>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateGroupList>
        </Setter>-->
    </Style>

    <Style TargetType="Frame">
        <Setter Property="HasShadow" Value="False" />
        <Setter Property="BorderColor" Value="{StaticResource Gray950}" />
        <Setter Property="CornerRadius" Value="8" />
    </Style>

    <Style TargetType="ImageButton">
        <Setter Property="Opacity" Value="1" />
        <Setter Property="BorderColor" Value="Transparent" />
        <Setter Property="BorderWidth" Value="0" />
        <Setter Property="CornerRadius" Value="0" />
        <Setter Property="VisualStateManager.VisualStateGroups">
            <VisualStateGroupList>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="Disabled">
                        <VisualState.Setters>
                            <Setter Property="Opacity" Value="0.5" />
                        </VisualState.Setters>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateGroupList>
        </Setter>
    </Style>


    <Style TargetType="Label">
        <Setter Property="TextColor" Value="#E8E3D7" />
        <Setter Property="FontFamily" Value="FontText" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="VisualStateManager.VisualStateGroups">
            <VisualStateGroupList>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="Disabled">
                        <VisualState.Setters>
                            <Setter Property="TextColor" Value="{StaticResource Gray600}" />
                        </VisualState.Setters>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateGroupList>
        </Setter>
    </Style>



    <Style TargetType="ListView">
        <Setter Property="SeparatorColor" Value="{AppThemeBinding Light={StaticResource Gray200}, Dark={StaticResource Gray500}}" />
        <Setter Property="RefreshControlColor" Value="{AppThemeBinding Light={StaticResource Gray900}, Dark={StaticResource Gray200}}" />
    </Style>

    <Style TargetType="Picker">
        <Setter Property="TextColor" Value="{AppThemeBinding Light={StaticResource Gray900}, Dark={StaticResource White}}" />
        <Setter Property="TitleColor" Value="{AppThemeBinding Light={StaticResource Gray900}, Dark={StaticResource Gray200}}" />
        <Setter Property="BackgroundColor" Value="Transparent" />
        <Setter Property="FontFamily" Value="FontText" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="VisualStateManager.VisualStateGroups">
            <VisualStateGroupList>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="Disabled">
                        <VisualState.Setters>
                            <Setter Property="TextColor" Value="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}" />
                            <Setter Property="TitleColor" Value="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}" />
                        </VisualState.Setters>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateGroupList>
        </Setter>
    </Style>

    <Style TargetType="ProgressBar">
        <Setter Property="ProgressColor" Value="{StaticResource White}" />
        <Setter Property="VisualStateManager.VisualStateGroups">
            <VisualStateGroupList>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="Disabled">
                        <VisualState.Setters>
                            <Setter Property="ProgressColor" Value="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}" />
                        </VisualState.Setters>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateGroupList>
        </Setter>
    </Style>

    <Style TargetType="RadioButton">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="TextColor" Value="{AppThemeBinding Light={StaticResource Black}, Dark={StaticResource White}}" />
        <Setter Property="FontFamily" Value="FontText" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="VisualStateManager.VisualStateGroups">
            <VisualStateGroupList>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="Disabled">
                        <VisualState.Setters>
                            <Setter Property="TextColor" Value="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}" />
                        </VisualState.Setters>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateGroupList>
        </Setter>
    </Style>

    <Style TargetType="RefreshView">
        <Setter Property="RefreshColor" Value="{AppThemeBinding Light={StaticResource Gray900}, Dark={StaticResource Gray200}}" />
    </Style>

    <Style TargetType="SearchBar">
        <Setter Property="TextColor" Value="{AppThemeBinding Light={StaticResource Gray900}, Dark={StaticResource White}}" />
        <Setter Property="PlaceholderColor" Value="{StaticResource Gray500}" />
        <Setter Property="CancelButtonColor" Value="{StaticResource Gray500}" />
        <Setter Property="BackgroundColor" Value="Transparent" />
        <Setter Property="FontFamily" Value="FontText" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="VisualStateManager.VisualStateGroups">
            <VisualStateGroupList>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="Disabled">
                        <VisualState.Setters>
                            <Setter Property="TextColor" Value="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}" />
                            <Setter Property="PlaceholderColor" Value="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}" />
                        </VisualState.Setters>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateGroupList>
        </Setter>
    </Style>

    <Style TargetType="SearchHandler">
        <Setter Property="TextColor" Value="{AppThemeBinding Light={StaticResource Gray900}, Dark={StaticResource White}}" />
        <Setter Property="PlaceholderColor" Value="{StaticResource Gray500}" />
        <Setter Property="BackgroundColor" Value="Transparent" />
        <Setter Property="FontFamily" Value="FontText" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="VisualStateManager.VisualStateGroups">
            <VisualStateGroupList>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="Disabled">
                        <VisualState.Setters>
                            <Setter Property="TextColor" Value="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}" />
                            <Setter Property="PlaceholderColor" Value="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}" />
                        </VisualState.Setters>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateGroupList>
        </Setter>
    </Style>

    <Style TargetType="Shadow">
        <Setter Property="Radius" Value="15" />
        <Setter Property="Opacity" Value="0.5" />
        <Setter Property="Brush" Value="{AppThemeBinding Light={StaticResource White}, Dark={StaticResource White}}" />
        <Setter Property="Offset" Value="10,10" />
    </Style>

    <Style TargetType="Slider">
        <Setter Property="MinimumTrackColor" Value="{StaticResource White}" />
        <Setter Property="MaximumTrackColor" Value="{AppThemeBinding Light={StaticResource Gray200}, Dark={StaticResource Gray600}}" />
        <Setter Property="ThumbColor" Value="{StaticResource White}" />
        <Setter Property="VisualStateManager.VisualStateGroups">
            <VisualStateGroupList>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="Disabled">
                        <VisualState.Setters>
                            <Setter Property="MinimumTrackColor" Value="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}" />
                            <Setter Property="MaximumTrackColor" Value="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}" />
                            <Setter Property="ThumbColor" Value="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}" />
                        </VisualState.Setters>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateGroupList>
        </Setter>
    </Style>

    <Style TargetType="SwipeItem">
        <Setter Property="BackgroundColor" Value="{StaticResource Black}" />
    </Style>

    <Style TargetType="Switch">
        <Setter Property="OnColor" Value="{StaticResource White}" />
        <Setter Property="ThumbColor" Value="{StaticResource White}" />
        <Setter Property="VisualStateManager.VisualStateGroups">
            <VisualStateGroupList>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="Disabled">
                        <VisualState.Setters>
                            <Setter Property="OnColor" Value="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}" />
                            <Setter Property="ThumbColor" Value="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}" />
                        </VisualState.Setters>
                    </VisualState>
                    <VisualState x:Name="On">
                        <VisualState.Setters>
                            <Setter Property="OnColor" Value="{AppThemeBinding Light={StaticResource Secondary}, Dark={StaticResource Gray200}}" />
                            <Setter Property="ThumbColor" Value="{StaticResource White}" />
                        </VisualState.Setters>
                    </VisualState>
                    <VisualState x:Name="Off">
                        <VisualState.Setters>
                            <Setter Property="ThumbColor" Value="{AppThemeBinding Light={StaticResource Gray400}, Dark={StaticResource Gray500}}" />
                        </VisualState.Setters>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateGroupList>
        </Setter>
    </Style>

    <Style TargetType="TimePicker">
        <Setter Property="TextColor" Value="{AppThemeBinding Light={StaticResource Gray900}, Dark={StaticResource White}}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="FontFamily" Value="FontText" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="VisualStateManager.VisualStateGroups">
            <VisualStateGroupList>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="Disabled">
                        <VisualState.Setters>
                            <Setter Property="TextColor" Value="{AppThemeBinding Light={StaticResource Gray300}, Dark={StaticResource Gray600}}" />
                        </VisualState.Setters>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateGroupList>
        </Setter>
    </Style>

    <Style
        ApplyToDerivedTypes="True"
        TargetType="Page">
        <Setter Property="Padding" Value="0" />
        <Setter Property="BackgroundColor" Value="Black" />
    </Style>

    <Style
        ApplyToDerivedTypes="True"
        TargetType="Shell">
        <Setter Property="Shell.BackgroundColor" Value="{StaticResource Gray950}" />
        <Setter Property="Shell.ForegroundColor" Value="{OnPlatform WinUI={StaticResource Primary}, Default={StaticResource White}}" />
        <Setter Property="Shell.TitleColor" Value="{StaticResource White}" />
        <Setter Property="Shell.DisabledColor" Value="{StaticResource Gray950}" />
        <Setter Property="Shell.UnselectedColor" Value="{AppThemeBinding Light={StaticResource Gray200}, Dark={StaticResource Gray200}}" />
        <Setter Property="Shell.NavBarHasShadow" Value="False" />
        <Setter Property="Shell.TabBarBackgroundColor" Value="{StaticResource Black}" />
        <Setter Property="Shell.TabBarForegroundColor" Value="{StaticResource White}" />
        <Setter Property="Shell.TabBarTitleColor" Value="{StaticResource White}" />
        <Setter Property="Shell.TabBarUnselectedColor" Value="{AppThemeBinding Light={StaticResource Gray900}, Dark={StaticResource Gray200}}" />
    </Style>

    <Style
        ApplyToDerivedTypes="True"
        TargetType="NavigationPage">
        <Setter Property="BackgroundColor" Value="{StaticResource Gray950}" />
        <Setter Property="BarBackgroundColor" Value="{StaticResource Gray950}" />
        <Setter Property="BarTextColor" Value="{StaticResource White}" />
        <Setter Property="IconColor" Value="{StaticResource White}" />
    </Style>

    <Style
        ApplyToDerivedTypes="True"
        TargetType="TabbedPage">
        <Setter Property="BackgroundColor" Value="{StaticResource Gray950}" />
        <Setter Property="BarBackgroundColor" Value="{StaticResource Gray950}" />
        <Setter Property="BarTextColor" Value="{StaticResource White}" />
        <Setter Property="UnselectedTabColor" Value="{StaticResource Gray950}" />
        <Setter Property="SelectedTabColor" Value="{StaticResource Gray200}" />
    </Style>


    <Style
        x:Key="SkiaValidationErrorLabel"
        TargetType="draw:SkiaLabel">
        <Setter Property="FontSize" Value="10" />
        <Setter Property="TextColor" Value="#AAFF4444" />
        <!--<Setter Property="Margin" Value="0,-8,0,0" />-->
    </Style>

    <Style
        x:Key="SkiaValidationErrorLabelHover"
        TargetType="draw:SkiaLabel">
        <Setter Property="FontSize" Value="14" />
        <Setter Property="HorizontalOptions" Value="Center" />
        <Setter Property="HorizontalTextAlignment" Value="Center" />
        <Setter Property="TextColor" Value="#AAFF4444" />
    </Style>

    <Style
        x:Key="ValidationErrorLabel"
        TargetType="Label">
        <Setter Property="FontSize" Value="10" />
        <Setter Property="TextColor" Value="#AAFF4444" />
        <Setter Property="Margin" Value="0,-8,0,0" />
    </Style>

    <Style
        x:Key="EditorFieldTitle"
        TargetType="Label">
        <Setter Property="FontSize" Value="12" />
        <Setter Property="TextColor" Value="Gray" />
    </Style>

    <Style
        x:Key="SkiaEnabledLabel"
        ApplyToDerivedTypes="True"
        TargetType="draw:SkiaLabel">
        <Setter Property="TextColor" Value="#E8E3D7" />
    </Style>

    <Style
        x:Key="SkiaAccentedLabel"
        ApplyToDerivedTypes="True"
        TargetType="draw:SkiaLabel">
        <Setter Property="TextColor" Value="{StaticResource Accent}" />
    </Style>

    <Style
        x:Key="SkiaErrorLabel"
        ApplyToDerivedTypes="True"
        TargetType="draw:SkiaLabel">
        <Setter Property="TextColor" Value="{StaticResource ColorFail}" />
    </Style>

    <Style
        x:Key="SkiaSuccessLabel"
        ApplyToDerivedTypes="True"
        TargetType="draw:SkiaLabel">
        <Setter Property="TextColor" Value="#CB6336" />
    </Style>

    <Style
        x:Key="SkiaDisabledLabel"
        ApplyToDerivedTypes="True"
        TargetType="draw:SkiaLabel">
        <Setter Property="TextColor" Value="#78E8E3D7" />
    </Style>


    <Style
        x:Key="SkiaEnabledIcon"
        Class="SkiaEnabledIcon"
        TargetType="draw:SkiaSvg">
        <Setter Property="TintColor" Value="#E8E3D7" />
    </Style>

    <Style
        x:Key="SkiaDisabledIcon"
        Class="SkiaDisabledIcon"
        TargetType="draw:SkiaSvg">
        <Setter Property="TintColor" Value="#78E8E3D7" />
    </Style>

    <Style
        x:Key="SkiaErrorIcon"
        Class="SkiaErrorIcon"
        TargetType="draw:SkiaSvg">
        <Setter Property="TintColor" Value="{StaticResource ColorFail}" />
    </Style>

    <Style
        x:Key="SkiaSuccessIcon"
        Class="SkiaSuccessIcon"
        TargetType="draw:SkiaSvg">
        <Setter Property="TintColor" Value="#CB6336" />
    </Style>

</ResourceDictionary>

