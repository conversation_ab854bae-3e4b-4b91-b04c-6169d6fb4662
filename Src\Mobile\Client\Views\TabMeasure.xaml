﻿<?xml version="1.0" encoding="utf-8" ?>
<Grid
    x:Class="Racebox.Views.TabMeasure"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:drawn="clr-namespace:Racebox.Views.Drawn"
    xmlns:interfaces="clr-namespace:Racebox.Shared.Interfaces;assembly=Racebox.Shared"
    xmlns:partials="clr-namespace:Racebox.Views.Partials"
    xmlns:shared="clr-namespace:Racebox.Shared.Strings;assembly=Racebox.Shared"
    xmlns:strings="clr-namespace:Racebox.SDK.Strings;assembly=Racebox.SDK"
    xmlns:viewModels="clr-namespace:Racebox.ViewModels"
    x:Name="ThisTabMeasure"
    Padding="0,0,0,8"
    x:DataType="viewModels:RaceBoxDeviceViewModel"
    HorizontalOptions="Fill"
    RowDefinitions="52, 232,64,*"
    VerticalOptions="FillAndExpand">

    <Grid.Resources>
        <ResourceDictionary>


            <Style
                x:Key="StyleSkiaIconedContainer"
                Class="StyleSkiaIconedContainer"
                TargetType="draw:SkiaLayout">
                <Setter Property="WidthRequest" Value="80" />
                <Setter Property="HeightRequest" Value="46" />
                <Setter Property="Margin" Value="10" />
            </Style>

            <Style
                x:Key="StyleIconedLabel"
                TargetType="Grid">
                <Setter Property="WidthRequest" Value="80" />
                <Setter Property="HeightRequest" Value="48" />
                <Setter Property="Margin" Value="10" />
            </Style>

            <Style
                x:Key="StyleIconedLabelText"
                TargetType="Label">
                <Setter Property="FontSize" Value="17" />
                <Setter Property="HorizontalOptions" Value="Center" />
                <Setter Property="MaxLines" Value="1" />
                <Setter Property="LineBreakMode" Value="NoWrap" />
            </Style>

            <Style
                x:Key="StyleSkiaIconedLabelText"
                BasedOn="{StaticResource SkiaLabelDefaultStyle}"
                Class="StyleSkiaIconedLabelText"
                TargetType="draw:SkiaLabel">
                <Setter Property="FontSize" Value="17" />
                <Setter Property="HorizontalOptions" Value="Center" />
                <Setter Property="MaxLines" Value="1" />
                <Setter Property="LineBreakMode" Value="NoWrap" />
            </Style>



            <x:String x:Key="SvgIncline">
                <![CDATA[                                     
 
            <svg width="40" height="24" viewBox="0 0 40 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M2.1 24L0 21.9L10.95 10.95C12.0167 9.88333 13.3167 9.35 14.85 9.35C16.3833 9.35 17.6833 9.88333 18.75 10.95L21.05 13.25C21.55 13.75 22.1417 14 22.825 14C23.5083 14 24.1 13.75 24.6 13.25L34.85 3H29V0H40V11H37V5.15L26.7 15.4C25.6333 16.4667 24.3333 17 22.8 17C21.2667 17 19.9667 16.4667 18.9 15.4L16.55 13.05C16.0833 12.5833 15.5 12.35 14.8 12.35C14.1 12.35 13.5167 12.5833 13.05 13.05L2.1 24Z" fill="#E8E3D7"/>
            </svg>
                
                ]]>
            </x:String>

            <x:String x:Key="SvgAltitude">
                <![CDATA[                                     
 
            <svg width="44" height="24" viewBox="0 0 44 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0 24L12 8L21.75 21H38L26 5.05L19.75 13.35L17.85 10.85L26 0L44 24H0ZM6 21H18L12 13L6 21ZM6 21H18H6Z" fill="#E8E3D7"/>
            </svg>
                
                ]]>
            </x:String>

            <x:String x:Key="SvgTime">
                <![CDATA[                                     
 
             <svg width="32" height="37" viewBox="0 0 32 37" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.5 2.625V0H21V2.625H10.5ZM14.4375 22.1812H17.0625V12.1187H14.4375V22.1812ZM15.75 36.7062C13.5917 36.7062 11.5573 36.2906 9.64687 35.4594C7.73646 34.6281 6.06667 33.4979 4.6375 32.0687C3.20833 30.6396 2.07813 28.9698 1.24688 27.0594C0.415625 25.149 0 23.1146 0 20.9562C0 18.7979 0.415625 16.7635 1.24688 14.8531C2.07813 12.9427 3.20833 11.2729 4.6375 9.84375C6.06667 8.41458 7.73646 7.28437 9.64687 6.45312C11.5573 5.62187 13.5917 5.20625 15.75 5.20625C17.7042 5.20625 19.5417 5.53437 21.2625 6.19062C22.9833 6.84687 24.5146 7.75833 25.8562 8.925L28.0875 6.69375L29.925 8.53125L27.6938 10.7625C28.7438 11.9292 29.6406 13.3437 30.3844 15.0062C31.1281 16.6687 31.5 18.6521 31.5 20.9562C31.5 23.1146 31.0844 25.149 30.2531 27.0594C29.4219 28.9698 28.2917 30.6396 26.8625 32.0687C25.4333 33.4979 23.7635 34.6281 21.8531 35.4594C19.9427 36.2906 17.9083 36.7062 15.75 36.7062ZM15.75 34.0812C19.3958 34.0812 22.4948 32.8052 25.0469 30.2531C27.599 27.701 28.875 24.6021 28.875 20.9562C28.875 17.3104 27.599 14.2115 25.0469 11.6594C22.4948 9.10729 19.3958 7.83125 15.75 7.83125C12.1042 7.83125 9.00521 9.10729 6.45312 11.6594C3.90104 14.2115 2.625 17.3104 2.625 20.9562C2.625 24.6021 3.90104 27.701 6.45312 30.2531C9.00521 32.8052 12.1042 34.0812 15.75 34.0812Z" fill="#E8E3D7"/>
            </svg>
               
                ]]>
            </x:String>

            <x:String x:Key="SvgDistance">
                <![CDATA[                                     
 
            <svg width="40" height="24" viewBox="0 0 40 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3 24C2.2 24 1.5 23.7 0.9 23.1C0.3 22.5 0 21.8 0 21V3C0 2.23333 0.3 1.54167 0.9 0.925C1.5 0.308334 2.2 0 3 0H37C37.8 0 38.5 0.308334 39.1 0.925C39.7 1.54167 40 2.23333 40 3V21C40 21.8 39.7 22.5 39.1 23.1C38.5 23.7 37.8 24 37 24H3ZM3 21H37V3H30.5V12H27.5V3H21.5V12H18.5V3H12.5V12H9.5V3H3V21ZM9.5 12H12.5H9.5ZM18.5 12H21.5H18.5ZM27.5 12H30.5H27.5Z" fill="#E8E3D7"/>
            </svg>
                
                ]]>
            </x:String>


        </ResourceDictionary>
    </Grid.Resources>

    <!--<Grid.Background>
        <LinearGradientBrush EndPoint="0,1">
            <GradientStop Offset="0.0" Color="#343C45" />
            <GradientStop Offset="1.0" Color="#11161D" />
        </LinearGradientBrush>
    </Grid.Background>-->

    <partials:GradientToBorderView
        Grid.RowSpan="4"
        HorizontalOptions="Fill"
        MissingHeight="{Binding NavbarModel.BottomTabsHeightRequest}"
        VerticalOptions="Fill" />


    <!--  CONNECTION BAR  -->
    <partials:ConnectionBar
        Grid.Row="0"
        Value="{Binding SignalStrength}" />

    <!--  GAUGE AREA  -->
    <draw:Canvas
        Grid.Row="1"
        Gestures="Enabled"
        RenderingMode="Accelerated"
        HorizontalOptions="Fill"
        Tag="MainGauge"
        VerticalOptions="Fill">

        <draw:SkiaLayout
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <!--  Static layer  -->
            <draw:SkiaLayout
                Padding="18"
                HorizontalOptions="Fill"
                Opacity="0.4"
                UseCache="Image"
                VerticalOptions="Fill">

                <!--  GAUGE BACKGROUND  -->
                <draw:SkiaShape
                    BackgroundColor="#42464B"
                    HorizontalOptions="Center"
                    LockRatio="-1"
                    StrokeColor="#15191E"
                    StrokeWidth="1.85"
                    Type="Circle"
                    VerticalOptions="Fill"
                    WidthRequest="-1">

                    <draw:SkiaShape.StrokeGradient>

                        <draw:SkiaGradient
                            EndXRatio="0.8"
                            EndYRatio="0.8"
                            StartXRatio="0.2"
                            StartYRatio="0.2"
                            Type="Linear">
                            <draw:SkiaGradient.Colors>
                                <Color>#15191E</Color>
                                <Color>#42464B</Color>
                            </draw:SkiaGradient.Colors>
                        </draw:SkiaGradient>

                    </draw:SkiaShape.StrokeGradient>
                    <draw:SkiaShape.FillGradient>

                        <draw:SkiaGradient
                            EndXRatio="1"
                            EndYRatio="1"
                            StartXRatio="0"
                            StartYRatio="0"
                            Type="Linear">
                            <draw:SkiaGradient.Colors>
                                <Color>#232323</Color>
                                <Color>#000000</Color>
                            </draw:SkiaGradient.Colors>
                        </draw:SkiaGradient>

                    </draw:SkiaShape.FillGradient>
                    <draw:SkiaShape.Shadows>

                        <draw:SkiaShadow
                            Blur="15"
                            Opacity="0.64"
                            X="4"
                            Y="4"
                            Color="Black" />

                        <draw:SkiaShadow
                            Blur="15"
                            Opacity="0.32"
                            X="-4"
                            Y="-4"
                            Color="White" />

                    </draw:SkiaShape.Shadows>

                </draw:SkiaShape>

            </draw:SkiaLayout>

            <!--  Dynamic foreground  -->
            <draw:SkiaLayout
                BackgroundColor="Transparent"
                HorizontalOptions="Fill"
                Tag="GaugeStack"
                UseCache="Operations"
                VerticalOptions="Fill">

                <!--#region GAUGE-->

                <!--  ARC  -->
                <!--  Value2="{Binding DisplaySpeedValue}"  -->
                <draw:SkiaShape
                    x:Name="SpeedArc"
                    Margin="26"
                    HorizontalOptions="Center"
                    IsVisible="True"
                    LockRatio="-1"
                    StrokeCap="Butt"
                    StrokeColor="Red"
                    StrokeWidth="8"
                    Type="Arc"
                    UseCache="Operations"
                    Value1="125"
                    Value2="{Binding DisplaySpeedValue}"
                    VerticalOptions="Fill">

                    <draw:SkiaShape.StrokeGradient>

                        <draw:SkiaGradient Type="Sweep">
                            <draw:SkiaGradient.Colors>
                                <Color>#CB6235</Color>
                                <Color>#CB6235</Color>
                                <Color>#00CB6235</Color>
                            </draw:SkiaGradient.Colors>
                            <draw:SkiaGradient.ColorPositions>
                                <x:Double>0.0</x:Double>
                                <x:Double>0.933</x:Double>
                                <x:Double>1.0</x:Double>
                            </draw:SkiaGradient.ColorPositions>
                        </draw:SkiaGradient>

                    </draw:SkiaShape.StrokeGradient>

                </draw:SkiaShape>

                <!--  SelectedCarTitle     Text="{Binding SelectedCarTitle}"  -->
                <draw:SkiaLabel
                    FontFamily="FontText"
                    FontSize="12"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    MaxLines="1"
                    Tag="CarName"
                    Text="{Binding SelectedCarTitle}"
                    TextColor="{x:StaticResource Accent}"
                    TranslationY="-48"
                    UpdateWhenReturnedFromBackground="True"
                    UseCache="Operations"
                    VerticalOptions="Center"
                    WidthRequest="100" />

                <!--  SPEED  Text="{Binding DisplaySpeed}"  -->
                <draw:SkiaLabel
                    x:Name="LabelSpeed"
                    FontFamily="FontTextBold"
                    FontSize="58"
                    HorizontalOptions="Center"
                    MonoForDigits="8"
                    Text="{Binding DisplaySpeed}"
                    TranslationY="0"
                    UseCache="Operations"
                    VerticalOptions="Center">
                    <!--<draw:SkiaControl.Styles>
                        <draw:ConditionalStyle
                            State="Normal"
                            Style="{x:StaticResource SkiaEnabledLabel}" />
                        <draw:ConditionalStyle
                            State="Disabled"
                            Style="{x:StaticResource SkiaDisabledLabel}" />
                    </draw:SkiaControl.Styles>-->
                    <draw:SkiaControl.Triggers>
                        <DataTrigger
                            Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Normal'}"
                            TargetType="draw:SkiaLabel"
                            Value="True">
                            <Setter Property="Style" Value="{x:StaticResource SkiaEnabledLabel}" />
                        </DataTrigger>
                        <DataTrigger
                            Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Disabled'}"
                            TargetType="draw:SkiaLabel"
                            Value="True">
                            <Setter Property="Style" Value="{x:StaticResource SkiaDisabledLabel}" />
                        </DataTrigger>
                    </draw:SkiaControl.Triggers>


                </draw:SkiaLabel>

                <!--  speed units  -->
                <draw:SkiaLabel
                    FontFamily="FontText"
                    FontSize="18"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    MaxLines="1"
                    Text="{Binding SpeedUnitsDisplay}"
                    TranslationY="50"
                    UpdateWhenReturnedFromBackground="True"
                    UseCache="Operations"
                    VerticalOptions="Center"
                    WidthRequest="100">
                    <draw:SkiaControl.Triggers>
                        <DataTrigger
                            Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Normal'}"
                            TargetType="draw:SkiaLabel"
                            Value="True">
                            <Setter Property="Style" Value="{x:StaticResource SkiaEnabledLabel}" />
                        </DataTrigger>
                        <DataTrigger
                            Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Disabled'}"
                            TargetType="draw:SkiaLabel"
                            Value="True">
                            <Setter Property="Style" Value="{x:StaticResource SkiaDisabledLabel}" />
                        </DataTrigger>
                    </draw:SkiaControl.Triggers>
                    <!--<draw:SkiaControl.Styles>
                        <draw:ConditionalStyle
                            State="Normal"
                            Style="{x:StaticResource SkiaEnabledLabel}" />
                        <draw:ConditionalStyle
                            State="Disabled"
                            Style="{x:StaticResource SkiaDisabledLabel}" />
                    </draw:SkiaControl.Styles>-->
                </draw:SkiaLabel>

                <!--#endregion-->

                <!--<partials:OneTimeEffect
                    AutoPlay="True"
                    BackgroundColor="#00ffffff"
                    BindableTrigger="{Binding Source={x:Reference TapHotspot}, Path=TotalDown}"
                    File="Lottie/car.json"
                    HeightRequest="50"
                    HorizontalOptions="Center"
                    LockRatio="1"
                    Repeat="-1"
                    Tag="TabEffect"
                    TranslationY="-16"
                    VerticalOptions="End"
                    ZIndex="2" />-->


                <!--#region INDICATORS-->

                <!--  Incline  -->
                <draw:SkiaLayout
                    AddMarginTop="0"
                    Style="{x:StaticResource StyleSkiaIconedContainer}"
                    Tag="InclineStack">

                    <draw:SkiaSvg
                        HeightRequest="30"
                        HorizontalOptions="Center"
                        SvgString="{StaticResource SvgIncline}"
                        LockRatio="1">
                        <!--<draw:SkiaControl.Styles>
                            <draw:ConditionalStyle
                                State="Normal"
                                Style="{x:StaticResource SkiaEnabledIcon}" />
                            <draw:ConditionalStyle
                                State="Disabled"
                                Style="{x:StaticResource SkiaDisabledIcon}" />
                            <draw:ConditionalStyle
                                State="BadIncline"
                                Style="{x:StaticResource SkiaErrorIcon}" />
                        </draw:SkiaControl.Styles>-->
                        <draw:SkiaControl.Triggers>
                            <DataTrigger
                                Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Normal'}"
                                TargetType="draw:SkiaSvg"
                                Value="True">
                                <Setter Property="Style" Value="{x:StaticResource SkiaEnabledIcon}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Disabled'}"
                                TargetType="draw:SkiaSvg"
                                Value="True">
                                <Setter Property="Style" Value="{x:StaticResource SkiaDisabledIcon}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='BadIncline'}"
                                TargetType="draw:SkiaSvg"
                                Value="True">
                                <Setter Property="Style" Value="{x:StaticResource SkiaErrorIcon}" />
                            </DataTrigger>
                        </draw:SkiaControl.Triggers>
                    </draw:SkiaSvg>

                    <draw:SkiaLabel
                        MonoForDigits="8"
                        Style="{StaticResource StyleSkiaIconedLabelText}"
                        Text="{Binding DisplayIncline}"
                        VerticalOptions="End">
                        <draw:SkiaControl.Triggers>
                            <DataTrigger
                                Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Normal'}"
                                TargetType="draw:SkiaLabel"
                                Value="True">
                                <Setter Property="TextColor" Value="{StaticResource ColorEnabled}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Disabled'}"
                                TargetType="draw:SkiaLabel"
                                Value="True">
                                <Setter Property="TextColor" Value="{StaticResource ColorDisabled}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='BadIncline'}"
                                TargetType="draw:SkiaLabel"
                                Value="True">
                                <Setter Property="TextColor" Value="{StaticResource ColorFail}" />
                            </DataTrigger>
                        </draw:SkiaControl.Triggers>
                        <!--<draw:SkiaControl.Styles>
                            <draw:ConditionalStyle
                                State="Normal"
                                Style="{x:StaticResource SkiaEnabledLabel}" />
                            <draw:ConditionalStyle
                                State="Disabled"
                                Style="{x:StaticResource SkiaDisabledLabel}" />
                            <draw:ConditionalStyle
                                State="BadIncline"
                                Style="{x:StaticResource SkiaErrorLabel}" />
                        </draw:SkiaControl.Styles>-->
                    </draw:SkiaLabel>

                </draw:SkiaLayout>

                <!--  Distance  -->
                <draw:SkiaLayout
                    Style="{x:StaticResource StyleSkiaIconedContainer}"
                    Tag="DistanceStack"
                    VerticalOptions="End">

                    <draw:SkiaSvg
                        HeightRequest="28"
                        HorizontalOptions="Center"
                        SvgString="{StaticResource SvgDistance}"
                        WidthRequest="28">
                        <!--<draw:SkiaControl.Styles>
                            <draw:ConditionalStyle
                                State="Normal"
                                Style="{x:StaticResource SkiaEnabledIcon}" />
                            <draw:ConditionalStyle
                                State="Disabled"
                                Style="{x:StaticResource SkiaDisabledIcon}" />
                        </draw:SkiaControl.Styles>-->
                        <draw:SkiaControl.Triggers>
                            <DataTrigger
                                Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Normal'}"
                                TargetType="draw:SkiaSvg"
                                Value="True">
                                <Setter Property="Style" Value="{x:StaticResource SkiaEnabledIcon}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Disabled'}"
                                TargetType="draw:SkiaSvg"
                                Value="True">
                                <Setter Property="Style" Value="{x:StaticResource SkiaDisabledIcon}" />
                            </DataTrigger>
                        </draw:SkiaControl.Triggers>

                    </draw:SkiaSvg>

                    <draw:SkiaLabel
                        MonoForDigits="8"
                        Style="{StaticResource StyleSkiaIconedLabelText}"
                        Text="{Binding DistanceMeasure}"
                        VerticalOptions="End">
                        <draw:SkiaControl.Triggers>
                            <DataTrigger
                                Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Normal'}"
                                TargetType="draw:SkiaLabel"
                                Value="True">
                                <Setter Property="TextColor" Value="{StaticResource ColorEnabled}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Disabled'}"
                                TargetType="draw:SkiaLabel"
                                Value="True">
                                <Setter Property="TextColor" Value="{StaticResource ColorDisabled}" />
                            </DataTrigger>
                        </draw:SkiaControl.Triggers>
                        <!--<draw:SkiaControl.Styles>
                            <draw:ConditionalStyle
                                State="Normal"
                                Style="{x:StaticResource SkiaEnabledLabel}" />
                            <draw:ConditionalStyle
                                State="Disabled"
                                Style="{x:StaticResource SkiaDisabledLabel}" />
                        </draw:SkiaControl.Styles>-->
                    </draw:SkiaLabel>

                </draw:SkiaLayout>

                <!--  Altitude  -->
                <draw:SkiaLayout
                    HorizontalOptions="End"
                    Style="{x:StaticResource StyleSkiaIconedContainer}"
                    Tag="AttitudeStack">

                    <draw:SkiaSvg
                        HeightRequest="31"
                        HorizontalOptions="Center"
                        SvgString="{StaticResource SvgAltitude}"
                        LockRatio="1">
                        <draw:SkiaControl.Triggers>
                            <DataTrigger
                                Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Normal'}"
                                TargetType="draw:SkiaSvg"
                                Value="True">
                                <Setter Property="Style" Value="{x:StaticResource SkiaEnabledIcon}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Disabled'}"
                                TargetType="draw:SkiaSvg"
                                Value="True">
                                <Setter Property="Style" Value="{x:StaticResource SkiaDisabledIcon}" />
                            </DataTrigger>
                        </draw:SkiaControl.Triggers>
                        <!--<draw:SkiaControl.Styles>
                            <draw:ConditionalStyle
                                State="Normal"
                                Style="{x:StaticResource SkiaEnabledIcon}" />
                            <draw:ConditionalStyle
                                State="Disabled"
                                Style="{x:StaticResource SkiaDisabledIcon}" />
                        </draw:SkiaControl.Styles>-->
                    </draw:SkiaSvg>

                    <draw:SkiaLabel
                        MonoForDigits="8"
                        Style="{StaticResource StyleSkiaIconedLabelText}"
                        Text="{Binding DisplayAltitude}"
                        VerticalOptions="End">
                        <draw:SkiaControl.Triggers>
                            <DataTrigger
                                Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Normal'}"
                                TargetType="draw:SkiaLabel"
                                Value="True">
                                <Setter Property="TextColor" Value="{StaticResource ColorEnabled}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Disabled'}"
                                TargetType="draw:SkiaLabel"
                                Value="True">
                                <Setter Property="TextColor" Value="{StaticResource ColorDisabled}" />
                            </DataTrigger>
                        </draw:SkiaControl.Triggers>
                        <!--<draw:SkiaControl.Styles>
                            <draw:ConditionalStyle
                                State="Normal"
                                Style="{x:StaticResource SkiaEnabledLabel}" />
                            <draw:ConditionalStyle
                                State="Disabled"
                                Style="{x:StaticResource SkiaDisabledLabel}" />
                        </draw:SkiaControl.Styles>-->
                    </draw:SkiaLabel>

                </draw:SkiaLayout>

                <!--  Time  -->
                <draw:SkiaLayout
                    HorizontalOptions="End"
                    Style="{x:StaticResource StyleSkiaIconedContainer}"
                    Tag="TimerStack"
                    VerticalOptions="End">

                    <draw:SkiaSvg
                        HeightRequest="27"
                        HorizontalOptions="Center"
                        SvgString="{StaticResource SvgTime}"
                        TranslationY="-2"
                        WidthRequest="27">
                        <draw:SkiaControl.Triggers>
                            <DataTrigger
                                Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Normal'}"
                                TargetType="draw:SkiaSvg"
                                Value="True">
                                <Setter Property="Style" Value="{x:StaticResource SkiaEnabledIcon}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Disabled'}"
                                TargetType="draw:SkiaSvg"
                                Value="True">
                                <Setter Property="Style" Value="{x:StaticResource SkiaDisabledIcon}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding IsCalibrating}"
                                TargetType="draw:SkiaSvg"
                                Value="True">
                                <Setter Property="Style" Value="{x:StaticResource SkiaSuccessIcon}" />
                            </DataTrigger>
                        </draw:SkiaControl.Triggers>

                        <!--<draw:SkiaControl.Styles>
                            <draw:ConditionalStyle
                                State="Normal"
                                Style="{x:StaticResource SkiaEnabledIcon}" />
                            <draw:ConditionalStyle
                                State="Disabled"
                                Style="{x:StaticResource SkiaDisabledIcon}" />
                            <draw:ConditionalStyle
                                Condition="{Binding IsCalibrating}"
                                Style="{x:StaticResource SkiaSuccessIcon}" />
                        </draw:SkiaControl.Styles>-->
                    </draw:SkiaSvg>

                    <draw:SkiaLabel
                        MonoForDigits="8"
                        Style="{StaticResource StyleSkiaIconedLabelText}"
                        Text="{Binding TimeMeasure}"
                        TranslationY="0"
                        VerticalOptions="End">
                        <draw:SkiaControl.Triggers>
                            <DataTrigger
                                Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Normal'}"
                                TargetType="draw:SkiaLabel"
                                Value="True">
                                <Setter Property="TextColor" Value="{StaticResource ColorEnabled}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Disabled'}"
                                TargetType="draw:SkiaLabel"
                                Value="True">
                                <Setter Property="TextColor" Value="{StaticResource ColorDisabled}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding IsCalibrating}"
                                TargetType="draw:SkiaLabel"
                                Value="True">
                                <Setter Property="TextColor" Value="{StaticResource ColorCalibrating}" />
                            </DataTrigger>
                        </draw:SkiaControl.Triggers>
                        <!--<draw:SkiaControl.Styles>
                            <draw:ConditionalStyle
                                State="Normal"
                                Style="{x:StaticResource SkiaEnabledLabel}" />
                            <draw:ConditionalStyle
                                State="Disabled"
                                Style="{x:StaticResource SkiaDisabledLabel}" />
                            <draw:ConditionalStyle
                                Condition="{Binding IsCalibrating}"
                                Style="{x:StaticResource SkiaSuccessLabel}" />
                        </draw:SkiaControl.Styles>-->
                    </draw:SkiaLabel>

                </draw:SkiaLayout>

                <!--#endregion-->

                <draw:SkiaLabel
                    Padding="4"
                    BackgroundColor="#10924321"
                    CharacterSpacing="2.8"
                    FontFamily="FontText"
                    FontSize="48"
                    HorizontalOptions="Center"
                    IsVisible="{Binding UseMock}"
                    Rotation="-25"
                    Text="DEMO"
                    TextColor="#11ffffff"
                    TranslationX="0"
                    TranslationY="0"
                    VerticalOptions="Center"
                    ZIndex="100" />

                <!--  CROPPER for shimmer effect etc  -->
                <partials:OneTimeShimmerContainer
                    x:Name="Cropper"
                    Margin="19"
                    BindableTrigger="{Binding CanToggleMeasure}"
                    HorizontalOptions="Center"
                    IsGhost="True"
                    LockRatio="-1"
                    Type="Circle"
                    VerticalOptions="Fill"
                    ZIndex="1" />

                <draw:SkiaHotspot
                    x:Name="TapHotspot"
                    Margin="20"
                    AnimationTapped="Shimmer"
                    CommandTapped="{Binding CommandStartSimulation, Mode=OneTime}"
                    HorizontalOptions="Fill"
                    ShimmerEffectAngle="25"
                    ShimmerEffectSpeed="500"
                    ShimmerEffectWidth="250"
                    TransformView="{x:Reference Cropper}"
                    VerticalOptions="Fill"
                    ZIndex="2" />



            </draw:SkiaLayout>

        </draw:SkiaLayout>

    </draw:Canvas>

    <!--  MainStatus  -->
    <Label
        Grid.Row="2"
        BackgroundColor="Transparent"
        FontFamily="FontTextBold"
        FontSize="16"
        HorizontalOptions="Center"
        IsVisible="{Binding CanToggleMeasure, Converter={StaticResource NotConverter}}"
        Opacity="0.41"
        Text="{Binding MainStatus}"
        TranslationY="0"
        VerticalOptions="Center" />

    <!--  START BTN  -->
    <draw:Canvas
        Grid.Row="2"
        Margin="-16"
        Gestures="Enabled"
        RenderingMode="Accelerated"
        HorizontalOptions="Center"
        IsVisible="{Binding CanToggleMeasure}"
        TranslationY="5"
        VerticalOptions="Fill"
        WidthRequest="156">

        <draw:SkiaLayout
            HeightRequest="73"
            HorizontalOptions="Fill"
            UseCache="Image"
            VerticalOptions="Start">

            <draw:SkiaShape
                x:Name="BtnBackground"
                Margin="16"
                BackgroundColor="#42464B"
                CornerRadius="24"
                HorizontalOptions="Fill"
                StrokeColor="#15191E"
                StrokeWidth="1.85"
                VerticalOptions="Fill">

                <draw:SkiaControl.Triggers>
                    <DataTrigger
                        Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Normal'}"
                        TargetType="draw:SkiaShape"
                        Value="True">
                        <Setter Property="Style" Value="{StaticResource SkiaStartBtnStartStyle}" />
                    </DataTrigger>
                    <DataTrigger
                        Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='IsMeasuring'}"
                        TargetType="draw:SkiaShape"
                        Value="True">
                        <Setter Property="Style" Value="{StaticResource SkiaStartBtnStopStyle}" />
                    </DataTrigger>
                </draw:SkiaControl.Triggers>
                <!--<draw:SkiaControl.Styles>
                    <draw:ConditionalStyle
                        State="Normal"
                        Style="{x:StaticResource SkiaStartBtnStartStyle}" />
                    <draw:ConditionalStyle
                        State="IsMeasuring"
                        Style="{x:StaticResource SkiaStartBtnStopStyle}" />
                </draw:SkiaControl.Styles>-->

                <draw:SkiaShape.Shadows>

                    <draw:SkiaShadow
                        Blur="8"
                        Opacity="0.33"
                        X="3"
                        Y="3"
                        Color="Black" />

                    <!--<draw:SkiaShadow
                        Blur="8"
                        Opacity="0.10"
                        X="-3"
                        Y="-1"
                        Color="White" />-->

                    <draw:SkiaShadow
                        Blur="5"
                        Opacity="0.245"
                        X="-2"
                        Y="-1"
                        Color="White" />

                </draw:SkiaShape.Shadows>
            </draw:SkiaShape>

            <draw:SkiaLabel
                FontFamily="FontText"
                FontSize="20"
                HorizontalOptions="Center"
                Text="{Binding MeasureActionTitle}"
                TextColor="#E8E3D7"
                TranslationY="-1"
                UseCache="Operations"
                VerticalOptions="Center" />

            <draw:SkiaHotspot
                CommandTapped="{Binding CommandSwitchMeasuring, Mode=OneTime}"
                HorizontalOptions="Fill"
                IsVisible="{Binding IsBusy, Converter={x:StaticResource NotConverter}}"
                TransformView="{x:Reference BtnBackground}"
                VerticalOptions="Fill" />

        </draw:SkiaLayout>

    </draw:Canvas>

    <draw:Canvas
        Grid.Row="3"
        Gestures="Enabled"
        RenderingMode="Accelerated"
        HorizontalOptions="FillAndExpand"
        MinimumHeightRequest="50"
        VerticalOptions="FillAndExpand">

        <draw:SkiaLayout
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <drawn:DrawnFrame />

            <draw:SkiaScroll
                x:Name="MainScroll"
                Margin="26,10,26,4"
                AutoScrollingSpeedMs="600"
                ChangeVelocityScrolled="1.35"
                HorizontalOptions="Fill"
                LockChildrenGestures="Enabled"
                OrderedScroll="{Binding ScrollToIndex}"
                RefreshDistanceLimit="4"
                Tag="MainScroll"
                VerticalOptions="Fill">

                <draw:SkiaLayout
                    HorizontalOptions="Fill"
                    ItemsSource="{Binding MeasureResults}"
                    LockChildrenGestures="Enabled"
                    RecyclingTemplate="Enabled"
                    Spacing="0"
                    Split="1"
                    Tag="StackItems"
                    Type="Wrap"
                    UseCache="Image">

                    <draw:SkiaLayout.ItemTemplate>
                        <DataTemplate x:DataType="interfaces:IHasDetailedDisplay">

                            <partials:DrawnIHasDetailedDisplayCell
                                Padding="0,0,0,0"
                                HeightRequest="35"
                                HorizontalOptions="Fill"
                                IsClippedToBounds="True"
                                UseCache="Image">

                                <draw:SkiaLabel
                                    Padding="4,0,0,1"
                                    FontFamily="FontText"
                                    FontSize="16.0"
                                    HorizontalOptions="Start"
                                    MaxLines="1"
                                    Tag="LabelDisplay1"
                                    TextColor="#E8E3D7"
                                    TranslationY="0"
                                    VerticalOptions="Center" />

                                <draw:SkiaLabel
                                    Padding="0,0,4,1"
                                    FontFamily="FontText"
                                    FontSize="16.0"
                                    HorizontalOptions="End"
                                    MaxLines="1"
                                    Tag="LabelDisplay2"
                                    TextColor="#E8E3D7"
                                    TranslationY="0"
                                    VerticalOptions="Center" />

                                <!--  SEPARATOR LINE  -->
                                <draw:SkiaShape
                                    BackgroundColor="Black"
                                    CornerRadius="0"
                                    HeightRequest="1"
                                    HorizontalOptions="Fill"
                                    IsClippedToBounds="True"
                                    StrokeWidth="0"
                                    VerticalOptions="End">
                                    <draw:SkiaShape.FillGradient>

                                        <draw:SkiaGradient
                                            EndXRatio="1"
                                            EndYRatio="0"
                                            StartXRatio="0"
                                            StartYRatio="0"
                                            Type="Linear">
                                            <draw:SkiaGradient.Colors>
                                                <Color>#00E8E3D7</Color>
                                                <Color>#99E8E3D7</Color>
                                                <Color>#00E8E3D7</Color>
                                            </draw:SkiaGradient.Colors>
                                        </draw:SkiaGradient>

                                    </draw:SkiaShape.FillGradient>
                                </draw:SkiaShape>

                            </partials:DrawnIHasDetailedDisplayCell>

                        </DataTemplate>
                    </draw:SkiaLayout.ItemTemplate>

                </draw:SkiaLayout>


            </draw:SkiaScroll>

            <!--  HINT  -->
            <draw:SkiaLabel
                Margin="32"
                FontFamily="FontTextBold"
                FontSize="16"
                HorizontalOptions="FillAndExpand"
                HorizontalTextAlignment="Center"
                InputTransparent="True"
                Opacity="0.41"
                Style="{x:StaticResource SkiaLabelDefaultStyle}"
                Text="{Binding MainHint}"
                TranslationY="0"
                UseCache="Operations"
                VerticalOptions="Fill"
                VerticalTextAlignment="Center" />

            <!--  OVERLAY ANIMATION  -->
            <!--<partials:StatusEffect
                AutoPlay="False"
                BindableTrigger="{Binding LastMeasureResultCode}"
                HorizontalOptions="Fill"
                InputTransparent="True"
                LockRatio="1"
                Opacity="0.90"
                Tag="EffectOkay"
                UseCache="Operations"
                VerticalOptions="Fill" />-->

        </draw:SkiaLayout>

    </draw:Canvas>


    <!--<Button
        Grid.Row="3"
        BackgroundColor="Green"
        Command="{Binding CommandAddMockResult}"
        HorizontalOptions="End"
        Text="+"
        WidthRequest="44"
        VerticalOptions="Start" />

    <Button
        Grid.Row="3"
        BackgroundColor="Green"
        Command="{Binding CommandClearMockResults}"
        HorizontalOptions="End"
        Text="0"
        Margin="0,0,50,0"
        WidthRequest="44"
        VerticalOptions="Start" />

    <Button
        Grid.Row="3"
        BackgroundColor="Green"
        Command="{Binding CommandTestEffects}"
        HorizontalOptions="End"
        Text="E"
        Margin="0,0,100,0"
        WidthRequest="44"
        VerticalOptions="Start" />-->


 

</Grid>
