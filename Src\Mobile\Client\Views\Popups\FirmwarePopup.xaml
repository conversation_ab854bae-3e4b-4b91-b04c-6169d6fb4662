<?xml version="1.0" encoding="utf-8" ?>
<pages:PopupPage
    x:Class="Racebox.Views.Popups.FirmwarePopup"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:pages="clr-namespace:Mopups.Pages;assembly=Mopups"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:shared="clr-namespace:Racebox.Shared.Strings;assembly=Racebox.Shared"
    CloseWhenBackgroundIsClicked="False"
    xmlns:drawn="clr-namespace:Racebox.Views.Drawn">

    <draw:Canvas
        x:Name="MainCanvas"
        BackgroundColor="#99000000"
        Gestures="Lock"
        RenderingMode="Accelerated"
        HorizontalOptions="Fill"
        Tag="Warning"
        VerticalOptions="Fill">

        <draw:SkiaLayout HorizontalOptions="Fill">
        <draw:SkiaScroll
            Margin="0,64"
            BackgroundColor="Black"
            Tag="Warning"
            HorizontalOptions="Fill"
            VerticalOptions="Start">
            <draw:SkiaLayout HorizontalOptions="Fill">

                <draw:SkiaLayout
                    Padding="12"
                    HorizontalOptions="Fill"
                    Tag="Problem"
                    Type="Column">

                    <draw:SkiaLabel
                        FontFamily="FontText"
                        FontSize="14"
                        HorizontalOptions="Center"
                        Tag="Warning"
                        Text="{x:Static shared:ResStrings.FirmwareWarningTitle}"
                        TextColor="Red" />

                    <draw:SkiaMarkdownLabel
                        LinkTapped="SkiaMarkdownLabel_OnLinkTapped"
                        FontFamily="FontText"
                        FontSize="15"
                        HorizontalOptions="Center"
                        Tag="Me"
                        LinkColor="CornflowerBlue"
                        Text="{x:Static shared:ResStrings.FirmwareWarningHelp}"
                        TextColor="#E8E3D7" />

                    <!--  BTN  -->
                    <drawn:SmallButton
                        Margin="0,16"
                        Tapped="SkiaButton_OnTapped"
                        HorizontalOptions="Center"
                        Text="{x:Static shared:ResStrings.BtnClose}" />

                </draw:SkiaLayout>

            </draw:SkiaLayout>
        </draw:SkiaScroll>
            </draw:SkiaLayout>

    </draw:Canvas>


</pages:PopupPage>
