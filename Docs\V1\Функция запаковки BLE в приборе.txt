
void send_BLE_msg(const uint8_t msgType) { // send data
	static uint8_t message[20] = {0, };
	// memset(message, 0, sizeof message);
	uint8_t hdop;
	uint8_t sats = (gps.satellites.value() > 15) ? 15 : gps.satellites.value();
	if (gps.hdop.isValid()) {
		hdop = (gps.hdop.value() < 1000) ? gps.hdop.value() / 10 : 99;	
	} else {
		hdop = 0xFF;
	}
	if (msgType == NAV_BLE_DATA) {
		if (gps.date.isValid() && gps.time.isValid()) {
			message[0] = (uint8_t)(((gps.date.year() - 2018) % 32) | ((gps.date.month() % 8) << 5));
			message[1] = (uint8_t)((gps.date.month() >> 3) | (hdop << 1));
			message[2] = (uint8_t)(gps.date.day() | ((gps.time.hour() % 8) << 5));
			message[3] = (uint8_t)((gps.time.hour() >> 3) | (gps.time.minute() << 2));
			message[4] = (uint8_t)(gps.time.second() | (((gps.time.centisecond() * 10) % 4) << 6));
			message[5] = (uint8_t)((gps.time.centisecond() * 10) >> 2);
		} else {
			message[0] = 0xFF;
			message[1] = (uint8_t)(1 | (hdop << 1));
			message[2] = 0xFF;
			message[3] = 0xFF;
			message[4] = 0xFF;
			message[5] = 0xFF;
		}
		if (gps.course.isValid()) {
			message[6] = (uint8_t)((sats % 16) | (((gps.course.intDeg() / 10) % 16) << 4));
			message[7] = (uint8_t)((gps.course.intDeg() / 10) >> 4);
		} else {
			message[6] = (uint8_t)((sats % 16) | (0xF << 4));
			message[7] = 0xFF;
		}
		if (gps.location.isValid()) {
			message[8] = (uint8_t)(gps.location.intLat() % 256);
			message[9] = (uint8_t)((gps.location.intLat() >> 8) % 256);
			message[10] = (uint8_t)((gps.location.intLat() >> 16) % 256);
			message[11] = (uint8_t)(gps.location.intLat() >> 24);
			message[12] = (uint8_t)(gps.location.intLon() % 256);
			message[13] = (uint8_t)((gps.location.intLon() >> 8) % 256);
			message[14] = (uint8_t)((gps.location.intLon() >> 16) % 256);
			message[15] = (uint8_t)(gps.location.intLon() >> 24);
		} else {
			message[8] = 0xFF;
			message[9] = 0xFF;
			message[10] = 0xFF;
			message[11] = 0xFF;
			message[12] = 0xFF;
			message[13] = 0xFF;
			message[14] = 0xFF;
			message[15] = 0xFF;
		}
		if (gps.speed.isValid()) {
			if (gps.speed.kmph() < 655) {
				message[16] = (uint8_t)((uint16_t)(gps.speed.kmph() * 100) % 256);
				message[17] = (uint8_t)((uint16_t)(gps.speed.kmph() * 100) >> 8);
			} else {
				message[16] = 0xDC;
				message[17] = 0xFF;
			}
		} else {
			message[16] = 0xFF;
			message[17] = 0xFF;
		}
		if (gps.altitude.isValid()) {
			if (gps.altitude.meters() < 6053) {
				message[18] = (uint8_t)((uint16_t)((gps.altitude.meters() + 500) * 10) % 256);
				message[19] = (uint8_t)((uint16_t)((gps.altitude.meters() + 500) * 10) >> 8);
			} else {
				message[18] = 0xFA;
				message[19] = 0xFF;
			}
		} else {
			message[18] = 0xFF;
			message[19] = 0xFF;
		}
	}
    if (bleTxReady) {
		bleTxReady = false;
		HAL_UART_Transmit_IT(&huart2, (uint8_t *) message, sizeof(message));
        #ifdef DEBUG_BLE_APP
        for (uint8_t i = 0; i < 20; i++) {
            debug_msg("%X %s", message[i], (i == 19) ? "\n" : "");
        }
		#endif
    } else {
        #ifdef DEBUG_BLE_APP
        debug_msg("BLE TX not ready\n");
        #endif
    }
}