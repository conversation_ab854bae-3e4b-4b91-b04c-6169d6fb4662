﻿using Racebox.Helpers.Validation;
using System.Globalization;
using AppoMobi.Framework.Maui.Converters;

namespace Racebox.Converters;

public class StatesConverter : ConverterBase, IValueConverter
{
    public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is IEnumerable<string> states)
        {
            var state = parameter as string;
            var found = states.Contains(state);
            return found || state == "Normal";
        }
        return value;
    }

}

public class ValidatorErrorMessageConverter : ConverterBase, IValueConverter
{
    public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is Dictionary<string, FieldValidator> validators)
        {
            var validator = validators[parameter as string];
            return validator.ErrorMessage;
        }
        return value;
    }
}


public class ValidatorMessageNotEmptyConverter : ConverterBase, IValueConverter
{
    public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is Dictionary<string, FieldValidator> validators)
        {
            var validator = validators[parameter as string];
            return !string.IsNullOrEmpty(validator.ErrorMessage);
        }
        return value;
    }
}

public class ValidatorIsValidConverter : ConverterBase, IValueConverter
{
    public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is Dictionary<string, FieldValidator> validators)
        {
            var validator = validators[parameter as string];
            return validator.IsValid;
        }
        return value;
    }
}

public class ValidatorIsNotValidConverter : ConverterBase, IValueConverter
{
    public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is Dictionary<string, FieldValidator> validators)
        {
            var validator = validators[parameter as string];
            return validator.IsValid;
        }
        return value;
    }
}
