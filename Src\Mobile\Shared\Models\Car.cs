﻿namespace Racebox.Shared.Models;

public class Car : BaseEntity
{
    #region FK
    public int AppUserId { get; set; }

    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]
    public AppUser AppUser { get; set; }

    #endregion

    /// <summary>
    /// todo
    /// </summary>
    public string Brand { get; set; }

    public string Model { get; set; }

    public string Description { get; set; }

}