//
//  BLEFrame.swift
//  Racebox
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 06/10/2019.
//  Copyright © 2019 <PERSON>. All rights reserved.
//

import Foundation
import UIKit

var fix = BLEFrame()

extension Data {
    var hexString: String {
        return self.reduce("", { $0 + String(format: "%02x", $1) })
    }
}

extension Date {
    var millisecondsSince1970: UInt64 {
        return UInt64((self.timeIntervalSince1970 * 1000.0).rounded())
    }
}

//MARK: BLE Frame Class

struct Location {
    var latitude: Double?
    var longitude: Double?
    var latitudeInt: Int32?
    var longitudeInt: Int32?
    var isValid: Bool { return latitude != nil || longitude != nil }
    
    func distanceM (to location: Location) -> Double? {
        guard location.isValid && self.isValid else {
            return nil
        }
        return 0
    }
}


class FrameCircularBuffer {
    var timeStamp: [Int] = Array(repeating: 0, count: BufferSize)
    var speed: [Float] = Array(repeating: 0.0, count: B<PERSON><PERSON>Size)
    
    func update(timeStamp: Int, speed: Float?) {
        self.timeStamp.append(timeStamp)
        self.timeStamp.removeFirst()
        if let s = speed {
            self.speed.append(s)
            self.speed.removeFirst()
        } else {
            self.speed.append(self.speed[self.speed.endIndex - 1])
            self.speed.removeFirst()
        }
    }
    
    func averageSpeed() -> Float {
        return (speed.reduce(0, +) / Float(speed.count))
    }
    
    func refrashRate() -> Float {
        var total: Int = 0
        for i in timeStamp.startIndex + 1 ... timeStamp.endIndex - 1 {
            total += timeStamp[i] - timeStamp[i - 1]
        }
        guard total > 0 else {
            return 0
        }
        let rate = 1000.0 / (Float(total) / Float(timeStamp.count - 1))
        return rate
    }
    
}

class BLEFrame {
    
    static var buffer = FrameCircularBuffer()
    static var firstMillis: UInt64 = 0
    static var frameSkip: Int = 0
    
    var year: Int?
    var month: Int?
    var day: Int?
    var hour: Int?
    var minute: Int?
    var second: Int?
    var ms: Int?
    var millis: Int?
    
    var sats: UInt8 = 0
    var HDOP: Float?
    
    var location = Location()
    
    var speed_kph: Float?
    var heading: Float?
    var altitude: Float?
    
    var lonAccelG: Float?
    var latAccelG: Float?

    func parseFrameBLE(rawData: Data) {
        year = (Int(rawData[0] % 32) > 0 && Int(rawData[0] % 32) < 31) ? Int(rawData[0] % 32) + 2018 : nil
        month = (Int(rawData[0]) >> 5 | Int((rawData[1] % 2)) << 3 != 15) ? Int(rawData[0]) >> 5 | Int((rawData[1] % 2)) << 3 : nil
        day = (rawData[2] % 32 > 0) ? Int(rawData[2] % 32) : nil
        hour = (Int(rawData[2]) >> 5 | Int((rawData[3] % 4)) << 3 < 24) ? Int(rawData[2]) >> 5 | Int((rawData[3] % 4)) << 3 : nil
        minute = (rawData[3] >> 2 < 60) ? Int(rawData[3]) >> 2 : nil
        second = (rawData[4] % 64 < 60) ? Int(rawData[4] % 64) : nil
        ms = (Int(rawData[4]) >> 6 | Int(rawData[5]) << 2 < 1000) ? Int(rawData[4]) >> 6 | Int(rawData[5]) << 2 : nil
        millis = getMillis()
        sats = rawData[6] % 16
        HDOP = (rawData[1] >> 1 < 100) ? Float(rawData[1] >> 1) / 10 : nil
        location.latitude = (UInt32(rawData[8]) | UInt32(rawData[9]) << 8 | UInt32(rawData[10]) << 16 | UInt32(rawData[11]) << 24 < 0xFFFFFFFF) ? Double(UInt32(rawData[8]) | UInt32(rawData[9]) << 8 | UInt32(rawData[10]) << 16 | UInt32(rawData[11]) << 24) / 10000000 : nil
        location.latitudeInt = (UInt32(rawData[8]) | UInt32(rawData[9]) << 8 | UInt32(rawData[10]) << 16 | UInt32(rawData[11]) << 24 < 0xFFFFFFFF) ? Int32(rawData[8]) | Int32(rawData[9]) << 8 | Int32(rawData[10]) << 16 | Int32(rawData[11]) << 24 : nil
        location.longitude = (UInt32(rawData[12]) | UInt32(rawData[13]) << 8 | UInt32(rawData[14]) << 16 | UInt32(rawData[15]) << 24 < 0xFFFFFFFF) ? Double(UInt32(rawData[12]) | UInt32(rawData[13]) << 8 | UInt32(rawData[14]) << 16 | UInt32(rawData[15]) << 24) / 10000000 : nil
        location.longitudeInt = (UInt32(rawData[12]) | UInt32(rawData[3]) << 8 | UInt32(rawData[14]) << 16 | UInt32(rawData[15]) << 24 < 0xFFFFFFFF) ? Int32(rawData[12]) | Int32(rawData[13]) << 8 | Int32(rawData[14]) << 16 | Int32(rawData[15]) << 24 : nil
        speed_kph = (UInt16(rawData[16]) | UInt16(rawData[17]) << 8 < 0xFFFF) ? Float(UInt16(rawData[16]) | UInt16(rawData[17]) << 8) / 100 : nil
        heading = (Int(rawData[6]) >> 4 | Int(rawData[7]) << 4 < 4095) ? Float(Int(rawData[6]) >> 4 | Int(rawData[7]) << 4) / 10 : nil
        altitude = (UInt16(rawData[18]) | UInt16(rawData[19]) << 8 < 0xFFFF) ? Float(UInt16(rawData[18]) | UInt16(rawData[19]) << 8) / 10 - 500 : nil
        if millis != nil {
            BLEFrame.buffer.update(timeStamp: millis!, speed: speed_kph)
        }
    }
    
    func date() -> Date? {
        guard year != nil && month != nil && day != nil && hour != nil && minute != nil && second != nil && ms != nil else {
            return nil
        }
        let stringDate = String(format: "%d-%02d-%02dT%02d:%02d:%02d.%03d", year!, month!, day!, hour!, minute!, second!, ms!)
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSS"
        return dateFormatter.date(from: stringDate)
    }
    
    func getMillis() -> Int? {
        guard date() != nil else {
            return nil
        }
        if BLEFrame.firstMillis == 0 {
           BLEFrame.firstMillis = date()!.millisecondsSince1970
        }
        let millis = Int(truncatingIfNeeded: (date()!.millisecondsSince1970 - BLEFrame.firstMillis))
        guard millis >= BLEFrame.buffer.timeStamp.last! else {
            BLEFrame.frameSkip += 1
            return nil
        }
        return millis
    }
    
}
