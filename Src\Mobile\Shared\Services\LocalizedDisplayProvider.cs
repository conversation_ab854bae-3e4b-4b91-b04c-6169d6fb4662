﻿using Racebox.Shared.Enums;
using Racebox.Shared.Strings;

namespace Racebox.Shared.Services;

/// <summary>
/// Recreate the instance if you change app language..
/// </summary>
public class LocalizedDisplayProvider
{
    private static LocalizedDisplayProvider _instance;

    public static string HoursFormat = "HH";
    public static void Create()
    {
        _instance = new LocalizedDisplayProvider();
    }

    public static LocalizedDisplayProvider Instance
    {
        get
        {
            if (_instance == null)
            {
                Create();
            }
            return _instance;
        }
    }

    public string GetDistanceDisplay(OptionsUnits units)
    {
        if (units == OptionsUnits.US)
            return ResStrings.FeetShort;
        return ResStrings.MetersShort;
    }

    public string GetSpeedDisplay(OptionsUnits units)
    {
        if (units == OptionsUnits.US)
            return ResStrings.Mph;
        return ResStrings.Kmh;
    }

    public string GetAddSpeedDisplay(OptionsUnits units)
    {
        if (units == OptionsUnits.US)
            return ResStrings.MphAdd;
        return ResStrings.KmhAdd;
    }


    public string GetNullableTimeDisplay(TimeSpan? time, bool addUnits = true)
    {
        if (time == null)
        {
            if (addUnits)
                return $"- {ResStrings.SecondsShort.Trim()}";
            else
                return $" ";
        }

        return GetTimeDisplay(time.Value, addUnits);
    }

    public string GetTimeDisplay(TimeSpan time, bool addUnits = true)
    {
        var culture = ResStrings.Culture;
        var separator = culture.NumberFormat.NumberDecimalSeparator;


        if (addUnits)
        {
            if (time.Hours > 0)
            {
                return $"{time.ToString(HoursFormat)}{ResStrings.HoursShort} {time:mm}{ResStrings.MinutesShort} {time.ToString($"s'{separator}'ff", culture)}{ResStrings.SecondsShort}";
            }
            if (time.Minutes > 0)
            {
                return $"{time:mm}{ResStrings.MinutesShort} {time.ToString($"s'{separator}'ff", culture)}{ResStrings.SecondsShort}";
            }


            return $"{time.ToString($"s'{separator}'ff", culture)}{ResStrings.SecondsShort}";
        }
        else
        {
            if (time.Hours > 0)
            {
                return $"{time.ToString(HoursFormat)} {time:mm} {time.ToString($"s'{separator}'ff", culture)}";
            }
            if (time.Minutes > 0)
            {
                return $"{time:mm} {time.ToString($"s'{separator}'ff", culture)}";
            }

            return $"{time.ToString($"s'{separator}'ff", culture)}";
        }


    }

    public string GetTimeDisplayOld(TimeSpan time)
    {
        if (time.Hours > 0)
        {
            return $"{time.ToString(HoursFormat)}{time:':'mm':'ss':'ff}";
        }
        if (time.Minutes > 0)
        {
            return $"{time:mm}{ResStrings.MinutesShort} {time:s':'ff''}{ResStrings.SecondsShort}";
        }

        return $"{time:s'.'ff''}{ResStrings.SecondsShort}";
    }


    public string GetMeasureActionDisplay(bool value)
    {
        return value ? ResStrings.BtnStop : ResStrings.BtnStart;
    }

    public string GetConnectActionDisplay(bool value)
    {
        return value ? ResStrings.BtnDisconnect : ResStrings.BtnConnect;
    }

    public string GetOnOffDisplay(bool value)
    {
        return value ? ResStrings.On : ResStrings.Off;
    }

    public string GetMainConnectionStatus(bool isBusy, bool isConnected, bool gpsOk)
    {
        if (isBusy)
            return ResStrings.StatusConnecting;

        if (isConnected)
        {
            if (!gpsOk)
                return ResStrings.StatusGpsLow;

            return string.Empty;
        }

        return ResStrings.StatusBluetoothOff;
    }

    public string GetMeasureTitle(bool value)
    {
        return value ? ResStrings.MeasureInstant : ResStrings.Measure;
    }

}