﻿using Racebox.Helpers.Validation;
using Racebox.Shared.Strings;
using System.Windows.Input;

namespace Racebox.ViewModels
{
    public class EditUserViewModel : ProjectViewModel
    {
        public EditUserViewModel(LocalUser item, Func<LocalUser, Task<int>> callback)
        {

            _callback = callback;
            _item = item;

            ValidationInitialize();

            Name = item.Name;
        }

        public ICommand CommandSubmitForm => new Command(async (object context) =>
        {

            UpdateValidator();
            if (!CanSubmit)
            {
                return;
            }

            try
            {
                IsBusy = true;
                await Task.Delay(10); //update UI

                var outItem = new LocalUser
                {
                    Id = _item.Id,
                    Name = Name.ToTitleCase()
                };

                var errorCode = await _callback(outItem);

                if (errorCode > 0)
                {
                    //if (errorCode == 1)
                    //{
                    //    throw new ApplicationException(ResStrings.ErrorMetricsAlreadyExist);
                    //}
                    throw new ApplicationException(ResStrings.ErrorFailedToSaveRecord);
                }


                this.NavbarModel.CommandGoBack.Execute(null);

            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                App.Instance.UI.ShowToast($"{ResStrings.Error}: {e.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        });


        private string _Name;
        public string Name
        {
            get
            {
                return _Name;
            }
            set
            {
                if (_Name != value)
                {
                    _Name = value;
                    OnPropertyChanged();
                    UpdateValidator();
                }
            }
        }





        #region VALIDATION

        private void ValidationInitialize()
        {

            FieldValidators["name"] = new RequiredStringValidator((validator, value) =>
            {
                if (string.IsNullOrEmpty(value) || value.Length < 2 || value.Length > 128)
                {
                    return "Некорректное количество символов";
                }
                return null;
            });

        }

        void UpdateValidator()
        {
            Validate();
            OnPropertyChanged("CanSubmit");
        }

        private bool _IsBusy;
        private readonly Func<LocalUser, Task<int>> _callback;
        private readonly LocalUser _item;

        public new bool IsBusy
        {
            get { return _IsBusy; }
            set
            {
                if (_IsBusy != value)
                {
                    _IsBusy = value;
                    OnPropertyChanged();
                    OnPropertyChanged("CanSubmit");
                }
            }
        }


        public bool CanSubmit
        {
            get
            {
                var validated = Validate();
                return validated && !IsBusy;
            }
        }

        bool Validate()
        {
            int valid = 1;
            valid *= Convert.ToInt32(FieldValidators["name"].Validate(Name));
            OnPropertyChanged("FieldValidators");
            return valid > 0;
        }

        public Dictionary<string, FieldValidator> FieldValidators { get; } = new();


        #endregion


    }
}
