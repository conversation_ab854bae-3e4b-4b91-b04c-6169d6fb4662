import serial
import csv
import time
import glob
import time
from pynput import keyboard
import serial.tools.list_ports


idle = 1
exit = 0
restart = None
speed = 0


# Ctrl + Up, Ctrl + Down, Ctrl + R, Ctrl + X
HOTKEYS = [{keyboard.Key.ctrl_l, keyboard.Key.up}, {keyboard.Key.ctrl_l, keyboard.Key.down}, {keyboard.KeyCode(char='\x12')}, {keyboard.KeyCode(char='\x18')}, {keyboard.Key.ctrl_l, keyboard.Key.space}]
current = set()


def on_press(key):
    global idle, exit, restart, speed
    # print(key)
    for comb in HOTKEYS:
        if key in comb:
            current.add(key)
            if all(k in current for k in comb):
                # print (HOTKEYS.index(comb))
                if HOTKEYS.index(comb) == 0:
                    speed = speed + 1 if speed < 4 else speed
                    print(f'Speed: {speed}')
                elif HOTKEYS.index(comb) == 1:
                    speed = speed - 1 if speed > -4 else speed
                    print(f'Speed: {speed}')
                elif HOTKEYS.index(comb) == 2:
                    print(' > Restart and idle')
                    restart = 1
                elif HOTKEYS.index(comb) == 3:
                    print(' > Exit')
                    exit = 1
                elif HOTKEYS.index(comb) == 4:
                    idle ^= 1
                    print('%s' % ' > NMEA in idle' if idle == 1 else ' > NMEA started')


def on_release(key):
    try:
        current.remove(key)
    except KeyError:
        pass    
  

def getGNGNS(row) -> str:
    time_s = f"{time.strftime('%H%M%S')}.{str(time.time() - int(time.time()))[2:4]}"
    #time_s = "000000.00"
    # time_s = "".join(row['Time'].split(':'))
    lon = '0' + str(int(float(row['Lon'])) * 100 + (float(row['Lon']) -
                                                    int(float(row['Lon']))) * 60).ljust(12, '0')[0:10]
    lat = str(int(float(row['Lat'])) * 100 + (float(row['Lat']) -
                                                    int(float(row['Lat']))) * 60).ljust(12, '0')[0:10]
    NS = 'N' if float(row['Lat']) >= 0 else 'S'
    EW = 'E' if float(row['Lon']) >= 0 else 'W'
    numSV = row['Sats'].rjust(2, '0')
    HDOP = row['HDOP'].ljust(4, '0')
    alt = row['Alt (m)']
    gstr = f'GNGNS,{time_s},{lat},{NS},{lon},{EW},AANN,{numSV},{HDOP},{alt},0.0,,,'
    cs = 0
    for c in bytes(gstr, encoding='ascii'):
        cs ^= c
    return f'${gstr}*{cs:02X}\n'


def getGNRMC(row) -> str:
    time_s = f"{time.strftime('%H%M%S')}.{str(time.time() - int(time.time()))[2:4]}"
    #time_s = "000000.00"
    # time_s = "".join(row['Time'].split(':'))
    lon = '0' + str(int(float(row['Lon'])) * 100 + (float(row['Lon']) -
                                                    int(float(row['Lon']))) * 60).ljust(12, '0')[0:10]
    lat = str(int(float(row['Lat'])) * 100 + (float(row['Lat']) -
                                              int(float(row['Lat']))) * 60).ljust(12, '0')[0:10]
    NS = 'N' if float(row['Lat']) >= 0 else 'S'
    EW = 'E' if float(row['Lon']) >= 0 else 'W'
    spd = str((float(row['Speed (km/h)']) *
               0.539957).__round__(3)).ljust(7, '0')[0:5]
    cog = row['Course (deg)']
    date = time.strftime("%d%m%y")
    gstr = f'GNRMC,{time_s},A,{lat},{NS},{lon},{EW},{spd},{cog},{date},,,A,V'
    cs = 0
    for c in bytes(gstr, encoding='ascii'):
        cs ^= c
    return f'${gstr}*{cs:02X}\n'


def getGNGSA(row) -> str:
    opMode = 'A'
    navMode = '3'
    PDOP = '1.23'
    HDOP = row["HDOP"] + '0'
    VDOP = '1.23'
    sysID = '1'
    gstr = f'GNGSA,{opMode},{navMode},,,,,,,,,,,,,{PDOP},{HDOP},{VDOP},{sysID}'
    cs = 0
    for c in bytes(gstr, encoding='ascii'):
        cs ^= c
    return f'${gstr}*{cs:02X}\n'
  

def sendNMEA(row, ser):
    global idle, exit, restart
    rmc = getGNRMC(row)
    gns = getGNGNS(row)
    gsa = getGNGSA(row)
    if idle == 0:
        print(rmc, end='')
        print(gns, end='')
        print(gsa, end='')
    if ser is not None:
        ser.write(bytes(rmc, encoding='ascii'))
        ser.write(bytes(gns, encoding='ascii'))
        ser.write(bytes(gsa, encoding='ascii'))
    last_time = time.monotonic()
    while (time.monotonic() <= last_time + 0.06):
        pass


def parseCSV(log_file, ser):
    global idle, exit, restart
    skip_count = speed
    with open(log_file, newline='') as csvfile:
        reader = csv.DictReader(csvfile, delimiter=';')
        prevRow = None
        for row in reader: 
            if exit == 1:
                return
            next_row_need = 0
            while(next_row_need == 0):
                if idle == 1 and prevRow != None:
                    row = prevRow
                else:
                    next_row_need = 1
                if restart == 1:
                    idle = 1
                    restart = 0
                    csvfile.close()
                    return
                if skip_count > 0:
                    skip_count -= 1
                elif skip_count < 0:
                    while skip_count < 0: 
                        sendNMEA(row, ser) # TODO: Make interpolation between current and next rows
                        skip_count += 1
                    skip_count = speed    
                else:
                    skip_count = speed
                    sendNMEA(row, ser)
                prevRow = row
        print(' > Press R to restart')
        idle = 1
        while(restart == 0):
            sendNMEA(prevRow)


def main():
    ser = None
    print("Available serial ports:")
    ports = list(serial.tools.list_ports.comports())
    for p in ports:
        print (f"{ports.index(p) + 1} - {p}")
    p_selected = int(input(" > Select port: "))
    try:
        ser = serial.Serial(ports[p_selected - 1].device, 57600)
    except Exception as e:
        ser = None
        print(f"{e} -> Serial port is not available")
    listener = keyboard.Listener(on_press=on_press, on_release=on_release)
    listener.start()
    csv_list = glob.glob("Logs/*.csv")
    csv_list.sort()
    while True:
        exit = 0
        if len(csv_list) == 0:
            print("No logs found")
        else: 
            print("Logs found in current dir:")
            for file in csv_list:
                print(f'{csv_list.index(file) + 1} - {file}')
        log_file_index = input(" > Select log file: ")
        log_file = csv_list[int(log_file_index) - 1]
        print(" > Press Ctrl+Space to run and idle NMEA stream, press Ctrl+R to restart")
        while (exit == 0):
            parseCSV(log_file, ser)


if __name__ == "__main__":
    main()