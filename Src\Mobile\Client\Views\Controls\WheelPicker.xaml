﻿<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaLayout
    x:Class="Racebox.Views.Drawn.WheelPicker"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    x:Name="Container"
    BackgroundColor="Gray"
    HeightRequest="200"
    HorizontalOptions="Center"
    UseCache="None"
    WidthRequest="108"
    ZIndex="10">


    <draw:SkiaLayout
        HorizontalOptions="Fill"
        UseCache="Image"
        VerticalOptions="Fill">
        <draw:SkiaControl.FillGradient>

            <draw:SkiaGradient
                EndXRatio="0"
                EndYRatio="1"
                StartXRatio="0"
                StartYRatio="0"
                Type="Linear">
                <draw:SkiaGradient.Colors>
                    <Color>#000000</Color>
                    <Color>#232323</Color>
                    <Color>#000000</Color>
                </draw:SkiaGradient.Colors>
            </draw:SkiaGradient>

        </draw:SkiaControl.FillGradient>
    </draw:SkiaLayout>

    <!--  MODERN  -->
    <draw:SkiaWheelScroll
        x:Name="Scroller"
        Margin="0,0,0,0"
        Bounces="True"
        FrictionScrolled="0.8"
        Fade="True"
        HorizontalOptions="Fill"
        LinesColor="#33666666"
        Loop="True"
        FadeStrength="1.6"
        Orientation="Vertical"
        SnapToChildren="Center"
        Tag="WheelScroll"
        TrackIndexPosition="Center"
        VerticalOptions="Fill"
        ZIndex="1">

        <draw:SkiaWheelStack
            x:Name="Wheel"
            HorizontalOptions="Fill"
            Spacing="0"
            Type="Column"
            VerticalOptions="Fill"
            WidthRequest="-1">
            <draw:SkiaLayout.ItemTemplate>
                <DataTemplate>

                    <draw:ScrollPickerLabelLayout
                        UseCache="Operations"
                        ColorText="#E8E3D7"
                        ColorTextSelected="{StaticResource Accent}"
                        BackgroundColor="Transparent"
                        HeightRequest="50"
                        HorizontalOptions="Fill"
                        Spacing="0">

                        <draw:SkiaLabel
                            FontFamily="FontTextBold"
                            FontSize="24"
                            HorizontalOptions="Fill"
                            HorizontalTextAlignment="Center"
                            Text="{Binding .}"
                            TextColor="#E8E3D7"
                            VerticalOptions="Fill"
                            VerticalTextAlignment="Center" />

                    </draw:ScrollPickerLabelLayout>

                </DataTemplate>

            </draw:SkiaLayout.ItemTemplate>
        </draw:SkiaWheelStack>

    </draw:SkiaWheelScroll>


    <!--  neverending scroll with items  -->
    <!--<draw:SkiaScrollLooped
        x:Name="Scroller"
        AutoScrollingSpeedMs="200"
        ChangeDistancePanned="1.35"
        ChangeVelocityScrolled="0.85"
        FrictionScrolled="0.75"
        HorizontalOptions="Fill"
        LockChildrenGestures="Enabled"
        Orientation="Vertical"
        SnapToChildren="Center"
        Tag="WheelScroll"
        TrackIndexPosition="Center"
        VerticalOptions="Fill">

 
        <draw:ScrollPickerWheel
            DistortionAngle="2.8"
            x:Name="Wheel"
            BackgroundColor="Transparent"
            HorizontalOptions="Fill"
            Spacing="0"
            Type="Column"
            WidthRequest="-1">

            <draw:SkiaLayout.ItemTemplate>
                <DataTemplate>

 
                    <draw:ScrollPickerLabelContainer
               
                        BackgroundColor="Transparent"
                        ColorText="#E8E3D7"
                        ColorTextSelected="{StaticResource Accent}"
                        HeightRequest="50"
                        HorizontalOptions="Fill"
                        Spacing="0">

                        <draw:SkiaLabel
                            FontFamily="FontTextBold"
                            FontSize="24"
                            HorizontalOptions="Fill"
                            HorizontalTextAlignment="Center"
                            Text="{Binding .}"
                            TextColor="#ffffff"
                            VerticalOptions="Fill"
                            VerticalTextAlignment="Center" />

                    </draw:ScrollPickerLabelContainer>

                </DataTemplate>

            </draw:SkiaLayout.ItemTemplate>

        </draw:ScrollPickerWheel>

    </draw:SkiaScrollLooped>-->

</draw:SkiaLayout>