<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BtnSave" xml:space="preserve">
    <value>Sauvegarder</value>
  </data>
  <data name="BtnCancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="BtnClose" xml:space="preserve">
    <value>Fermer</value>
  </data>
  <data name="BtnApply" xml:space="preserve">
    <value>Appliquer</value>
  </data>
  <data name="Inputs" xml:space="preserve">
    <value>Contributions</value>
  </data>
  <data name="Device" xml:space="preserve">
    <value>Appareil</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="Options" xml:space="preserve">
    <value>Possibilités</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Erreur</value>
  </data>
  <data name="FeetShort" xml:space="preserve">
    <value>pi</value>
  </data>
  <data name="MetersShort" xml:space="preserve">
    <value>m</value>
  </data>
  <data name="Mph" xml:space="preserve">
    <value>mph</value>
  </data>
  <data name="Kmh" xml:space="preserve">
    <value>km/h</value>
  </data>
  <data name="MinutesShort" xml:space="preserve">
    <value>min</value>
  </data>
  <data name="SecondsShort" xml:space="preserve">
    <value>s</value>
  </data>
  <data name="BtnStart" xml:space="preserve">
    <value>Commencer</value>
  </data>
  <data name="BtnStop" xml:space="preserve">
    <value>Arrêt</value>
  </data>
  <data name="BtnConnect" xml:space="preserve">
    <value>Connecter</value>
  </data>
  <data name="BtnDisconnect" xml:space="preserve">
    <value>Déconnecter</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>Sur</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>Désactivé</value>
  </data>
  <data name="StatusConnecting" xml:space="preserve">
    <value>De liaison...</value>
  </data>
  <data name="StatusBluetoothOff" xml:space="preserve">
    <value>Le Bluetooth est désactivé</value>
  </data>
  <data name="Measure" xml:space="preserve">
    <value>La mesure</value>
  </data>
  <data name="MeasureInstant" xml:space="preserve">
    <value>Exécution instantanée</value>
  </data>
  <data name="VendorTitle" xml:space="preserve">
    <value>Boîte de course</value>
  </data>
  <data name="SignalStrength" xml:space="preserve">
    <value>Force du signal</value>
  </data>
  <data name="HintConnectNow" xml:space="preserve">
    <value>Cliquez sur l'icône Bluetooth en haut de l'écran pour vous connecter à l'appareil</value>
  </data>
  <data name="HintStartMeasuring" xml:space="preserve">
    <value>Appuyez sur le bouton Démarrer pour commencer à mesurer</value>
  </data>
  <data name="HintMeasureFailed" xml:space="preserve">
    <value>Échec de la mesure</value>
  </data>
  <data name="HintMonitoring" xml:space="preserve">
    <value>En attendant le début de la mesure...</value>
  </data>
  <data name="HintGo" xml:space="preserve">
    <value>ALLER!!!</value>
  </data>
  <data name="Racing" xml:space="preserve">
    <value>Courir</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>Tableau de bord</value>
  </data>
  <data name="History" xml:space="preserve">
    <value>Histoire</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Paramètres</value>
  </data>
  <data name="Altitude" xml:space="preserve">
    <value>Altitude</value>
  </data>
  <data name="Heading" xml:space="preserve">
    <value>Titre</value>
  </data>
  <data name="Latitude" xml:space="preserve">
    <value>Latitude</value>
  </data>
  <data name="Longitude" xml:space="preserve">
    <value>Longitude</value>
  </data>
  <data name="Frequency" xml:space="preserve">
    <value>Fréquence</value>
  </data>
  <data name="MaxSpeed" xml:space="preserve">
    <value>Vitesse maximale</value>
  </data>
  <data name="Incline" xml:space="preserve">
    <value>Inclinaison</value>
  </data>
  <data name="AllCars" xml:space="preserve">
    <value>Toutes les voitures</value>
  </data>
  <data name="Filtered" xml:space="preserve">
    <value>Filtré</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filtre</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Tous</value>
  </data>
  <data name="Cars" xml:space="preserve">
    <value>Véhicules</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Sélectionner</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Commande</value>
  </data>
  <data name="MeasuringSystem" xml:space="preserve">
    <value>Système de mesure</value>
  </data>
  <data name="CustomMetrics" xml:space="preserve">
    <value>Mesurer les paramètres</value>
  </data>
  <data name="Sound" xml:space="preserve">
    <value>Son</value>
  </data>
  <data name="RollOut" xml:space="preserve">
    <value>Déployer (1 pied)</value>
  </data>
  <data name="Car" xml:space="preserve">
    <value>Véhicule</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Utilisateur</value>
  </data>
  <data name="EditList" xml:space="preserve">
    <value>Liste d'édition</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Utilisateurs</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>Marque</value>
  </data>
  <data name="Model" xml:space="preserve">
    <value>Modèle</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="Deleting" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="AreYouSure" xml:space="preserve">
    <value>Es-tu sûr?</value>
  </data>
  <data name="CannotDeleteSingle" xml:space="preserve">
    <value>Impossible de supprimer le dernier enregistrement</value>
  </data>
  <data name="CannotDeleteUsed" xml:space="preserve">
    <value>Ceci est réellement utilisé</value>
  </data>
  <data name="USA" xml:space="preserve">
    <value>Etats-Unis</value>
  </data>
  <data name="Europe" xml:space="preserve">
    <value>L'Europe </value>
  </data>
  <data name="DefaultBrand" xml:space="preserve">
    <value>Ton</value>
  </data>
  <data name="DefaultModel" xml:space="preserve">
    <value>Voiture</value>
  </data>
  <data name="OrderOldFirst" xml:space="preserve">
    <value>Plus vieux d'abord</value>
  </data>
  <data name="OrderNewFirst" xml:space="preserve">
    <value>Le plus récent en premier</value>
  </data>
  <data name="SpeedRange" xml:space="preserve">
    <value>Plage de vitesse</value>
  </data>
  <data name="Distance" xml:space="preserve">
    <value>Distance</value>
  </data>
  <data name="Length" xml:space="preserve">
    <value>Distance</value>
  </data>
  <data name="Start" xml:space="preserve">
    <value>Commencer</value>
  </data>
  <data name="End" xml:space="preserve">
    <value>Fin</value>
  </data>
  <data name="ValidationStartValue" xml:space="preserve">
    <value>Une valeur &gt;=0 et &lt;=5000 est requise</value>
  </data>
  <data name="ValidationEndValue" xml:space="preserve">
    <value>Une valeur &gt;0 et &lt;=5000 est requise</value>
  </data>
  <data name="ValidationDifferentValues" xml:space="preserve">
    <value>La valeur de fin doit être supérieure à la valeur de début</value>
  </data>
  <data name="MeasuringUnits" xml:space="preserve">
    <value>Unités</value>
  </data>
  <data name="Feet" xml:space="preserve">
    <value>Pieds</value>
  </data>
  <data name="Meters" xml:space="preserve">
    <value>Mètres</value>
  </data>
  <data name="AlertTurnOnBluetooth" xml:space="preserve">
    <value>Veuillez activer le Bluetooth.</value>
  </data>
  <data name="AlertNeedGpsPermissionsForBluetooth" xml:space="preserve">
    <value>Veuillez accorder des autorisations de localisation (exigence du système Android pour utiliser Bluetooth).</value>
  </data>
  <data name="AlertNeedGpsOnForBluetooth" xml:space="preserve">
    <value>Veuillez activer le GPS (exigence du système Android pour utiliser Bluetooth).</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Emplacement</value>
  </data>
  <data name="AlertBluetoothUnsupported" xml:space="preserve">
    <value>Votre appareil ne prend pas en charge Bluetooth.</value>
  </data>
  <data name="HintGpsLow" xml:space="preserve">
    <value>Essayez de changer l'emplacement de votre appareil pour une meilleure réception GPS</value>
  </data>
  <data name="StatusGpsLow" xml:space="preserve">
    <value>Signal GPS faible</value>
  </data>
  <data name="AlertBluetoothPermissionsOff" xml:space="preserve">
    <value>Aucune autorisation pour utiliser Bluetooth.</value>
  </data>
  <data name="Hz" xml:space="preserve">
    <value>Hz</value>
  </data>
  <data name="KmhAdd" xml:space="preserve">
    <value>km/h</value>
  </data>
  <data name="MphAdd" xml:space="preserve">
    <value>mph</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Langue</value>
  </data>
  <data name="MaxAcceleration" xml:space="preserve">
    <value>Accélération maximale.</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Modifier</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="HoursShort" xml:space="preserve">
    <value>h</value>
  </data>
  <data name="BtnReset" xml:space="preserve">
    <value>Réinitialiser</value>
  </data>
  <data name="BtnCreate" xml:space="preserve">
    <value>Créer</value>
  </data>
  <data name="Acceleration" xml:space="preserve">
    <value>Accélération</value>
  </data>
  <data name="AccelerationSide" xml:space="preserve">
    <value>Accélération latérale.</value>
  </data>
  <data name="InclineMax" xml:space="preserve">
    <value>Inclinaison maximale</value>
  </data>
  <data name="IsValid" xml:space="preserve">
    <value>Est valable</value>
  </data>
  <data name="IsValidYes" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="IsValidNo" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="AlertBluetoothWillNotBeAvailable" xml:space="preserve">
    <value>Le Bluetooth ne sera pas disponible.</value>
  </data>
  <data name="SystemSettings" xml:space="preserve">
    <value>Les paramètres du système</value>
  </data>
  <data name="Time24" xml:space="preserve">
    <value>Format horaire 24h</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>À propos</value>
  </data>
  <data name="BtnQuestion" xml:space="preserve">
    <value>Poser une question</value>
  </data>
  <data name="AboutAppText" xml:space="preserve">
    <value>Racebox est une application compagnon pour les appareils Racebox Pro et Pro+ créée pour les mesures des performances des véhicules.
L'application est destinée à être utilisée uniquement sur les pistes de course. L'auteur n'est pas responsable des blessures ou des dommages matériels pouvant survenir lors de l'utilisation de ce logiciel.</value>
  </data>
  <data name="Copyright" xml:space="preserve">
    <value>© 2025 Équipe Racebox</value>
  </data>
  <data name="AlertNeedLocationForBluetooth" xml:space="preserve">
    <value>Nous aurions besoin que vous fournissiez les autorisations nécessaires pour utiliser votre position GPS (exigence du système Android pour utiliser Bluetooth).</value>
  </data>
  <data name="PressBACKOnceAgain" xml:space="preserve">
    <value>Appuyez à nouveau sur RETOUR pour quitter l'application</value>
  </data>
  <data name="ExportFormat" xml:space="preserve">
    <value>Format des journaux</value>
  </data>
  <data name="ErrorFailedToSaveRecord" xml:space="preserve">
    <value>Échec de la sauvegarde de l'enregistrement</value>
  </data>
  <data name="ErrorMetricsAlreadyExist" xml:space="preserve">
    <value>De telles mesures existent déjà</value>
  </data>
  <data name="CompatibleDevicesNotFound" xml:space="preserve">
    <value>Aucun appareil Racebox trouvé à proximité.</value>
  </data>
  <data name="DemoMode" xml:space="preserve">
    <value>Le mode de démonstration</value>
  </data>
  <data name="DemoWarning" xml:space="preserve">
    <value>Dans ce mode, l'application cessera de se connecter à un véritable module Bluetooth et jouera à la place des pistes de mouvement préenregistrées. Etes-vous sûr de vouloir activer le mode démo ?</value>
  </data>
  <data name="DemoStartHint" xml:space="preserve">
    <value>Cliquez sur Speed Game pour démarrer la simulation</value>
  </data>
  <data name="BadMeasureNotValid" xml:space="preserve">
    <value>Mesure invalide (grande pente)</value>
  </data>
  <data name="BadMeasureTooLong" xml:space="preserve">
    <value>Le temps de mesure est supérieur à ACCTIMEOUT</value>
  </data>
  <data name="BadMeasureLowAccel" xml:space="preserve">
    <value>L'accélération est en baisse</value>
  </data>
  <data name="BadMeasureInstant" xml:space="preserve">
    <value>Exécution instantanée et la vitesse est inférieure à la vitesse initiale de la première paire et le temps de mesure est supérieur à 1 seconde</value>
  </data>
  <data name="BadMeasureGps" xml:space="preserve">
    <value>Réception GPS de mauvaise qualité</value>
  </data>
  <data name="BadMeasureNoRanges" xml:space="preserve">
    <value>Aucune plage de vitesse mesurée</value>
  </data>
  <data name="BadMeasureNoDistances" xml:space="preserve">
    <value>Aucune distance mesurée</value>
  </data>
  <data name="BadMeasureLowSpeed" xml:space="preserve">
    <value>Vitesse inférieure à READYSPEEDTHRESHOLDUNIT</value>
  </data>
  <data name="BadMeasureIncline" xml:space="preserve">
    <value>Le résultat n'est pas valide : l'inclinaison était trop importante !</value>
  </data>
  <data name="Report" xml:space="preserve">
    <value>Rapport</value>
  </data>
  <data name="MeasuringStateFinished" xml:space="preserve">
    <value>Mesure terminée</value>
  </data>
  <data name="MeasuringStateReady" xml:space="preserve">
    <value>Commencer!</value>
  </data>
  <data name="MeasuringStateActive" xml:space="preserve">
    <value>Allons-y!</value>
  </data>
  <data name="MeasuringStateMonitoring" xml:space="preserve">
    <value>L'appareil est allumé</value>
  </data>
  <data name="MeasuringStateDisabled" xml:space="preserve">
    <value>L'appareil est éteint</value>
  </data>
  <data name="MeasuringStateError" xml:space="preserve">
    <value>Échec de mesure</value>
  </data>
  <data name="Weather" xml:space="preserve">
    <value>Météo</value>
  </data>
  <data name="X_Power" xml:space="preserve">
    <value>Batterie</value>
  </data>
  <data name="Satellites" xml:space="preserve">
    <value>Satellites</value>
  </data>
  <data name="DeviceLedBrightness" xml:space="preserve">
    <value>Luminosité de l'écran</value>
  </data>
  <data name="DeviceTransmissionType" xml:space="preserve">
    <value>Type de transmission</value>
  </data>
  <data name="DeviceMaps" xml:space="preserve">
    <value>Plans</value>
  </data>
  <data name="DeviceTone" xml:space="preserve">
    <value>Signaux sonores</value>
  </data>
  <data name="DeviceUtc" xml:space="preserve">
    <value>Fuseau horaire</value>
  </data>
  <data name="DeviceLogFormat" xml:space="preserve">
    <value>Format des journaux</value>
  </data>
  <data name="DeviceLogRate" xml:space="preserve">
    <value>Fréquence d'enregistrement</value>
  </data>
  <data name="DevicePrediction" xml:space="preserve">
    <value>Mode prédictif</value>
  </data>
  <data name="DeviceRandomStart" xml:space="preserve">
    <value>Heure de début arbitraire</value>
  </data>
  <data name="DeviceMinDistance" xml:space="preserve">
    <value>Min. distance d'affichage</value>
  </data>
  <data name="DeviceCarWeight" xml:space="preserve">
    <value>Poids du véhicule</value>
  </data>
  <data name="DeviceDragRatio" xml:space="preserve">
    <value>Coef. traîner</value>
  </data>
  <data name="DeviceFrontal" xml:space="preserve">
    <value>Aire transversale</value>
  </data>
  <data name="DeviceObdLog" xml:space="preserve">
    <value>Enregistrement du journal OBD</value>
  </data>
  <data name="DeviceShiftTime" xml:space="preserve">
    <value>Mesure du temps de commutation</value>
  </data>
  <data name="DeviceObdPidAuto" xml:space="preserve">
    <value>Détection automatique PID-OBD</value>
  </data>
  <data name="DeviceObdPids" xml:space="preserve">
    <value>Sélection des PID OBD à lire</value>
  </data>
  <data name="DeviceObdProtocol" xml:space="preserve">
    <value>Protocole OBD</value>
  </data>
  <data name="DeviceGnssSecondarySource" xml:space="preserve">
    <value>2ème source GNSS</value>
  </data>
  <data name="DeviceGnssUartOut" xml:space="preserve">
    <value>Sortie de données GNSS via USB</value>
  </data>
  <data name="DeviceGnssColdStart" xml:space="preserve">
    <value>Redémarrage à froid du GNSS</value>
  </data>
  <data name="DeviceGnssReconfig" xml:space="preserve">
    <value>Reconfiguration GNSS</value>
  </data>
  <data name="DeviceResetConfiguration" xml:space="preserve">
    <value>Réinitialiser la configuration</value>
  </data>
  <data name="ResetDevice" xml:space="preserve">
    <value>Redémarrer l'appareil</value>
  </data>
  <data name="WarningCannotUndo" xml:space="preserve">
    <value>Cette opération ne peut pas être annulée.</value>
  </data>
  <data name="DeviceSettings" xml:space="preserve">
    <value>Réglages de l'appareil</value>
  </data>
  <data name="FirmwareWarningHelp" xml:space="preserve">
    <value>Pour mettre à jour le micrologiciel de votre appareil __Racebox__, procédez comme suit :
 1. Téléchargez la nouvelle version depuis le site Web [racebox.ru](https://racebox.ru/obnovlenie-po).
 2. Copiez le fichier au format `HEX` correspondant au modèle de votre appareil dans le répertoire racine de la carte mémoire.
 3. Insérez la carte mémoire dans l'appareil lorsqu'il est éteint.
 4. Tout en maintenant le bouton `Mode`, connectez l'appareil à l'alimentation via le câble USB.
 5. Attendez que la carte mémoire soit initialisée et que le message `HOLD TO UPDATE X.XX` apparaisse (où X.XX est la version du logiciel téléchargé)
 6. Appuyez et maintenez le bouton `Mode` pour commencer le processus de mise à jour.
 7. Attendez la fin de la mise à jour et assurez-vous que la nouvelle version du micrologiciel s'affiche lorsque vous allumez l'appareil.
 8. Redémarrez l'application __Racebox__.</value>
  </data>
  <data name="FirmwareHowToTitle" xml:space="preserve">
    <value>Mise à jour du micrologiciel</value>
  </data>
  <data name="FirmwareWarningTitle" xml:space="preserve">
    <value>Firmware obsolète</value>
  </data>
  <data name="SettingsCanSpeak" xml:space="preserve">
    <value>Dire la vitesse</value>
  </data>
</root>