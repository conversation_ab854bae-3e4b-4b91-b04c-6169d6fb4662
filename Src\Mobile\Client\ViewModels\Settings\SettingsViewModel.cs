﻿#define DEV


using DrawnUi;
using DrawnUi.Models;
using MapsterMapper;
using Racebox.Shared.Enums;
using Racebox.Shared.Strings;

namespace Racebox.ViewModels;

public class SettingsViewModel : ContentViewModel
{

#if IOS || MACCATALYST
    private const double delayTap = 150;
#else
    private const double delayTap = 150;
#endif


    public SettingsViewModel(UserManager manager,
        IUIAssistant ui,
        RaceBoxDeviceViewModel vm,
        IMapper mapper)
        : base(manager, ui, mapper)
    {
        _device = vm;
        Initialize();
    }

    public void Initialize()
    {
        _Sound = _userManager.User.Options.Sound;
        OnPropertyChanged(nameof(Sound));

        _CanSay = _userManager.User.Options.CanSay;
        OnPropertyChanged(nameof(CanSay));

        _RollOut = _userManager.User.Options.Rollout;
        OnPropertyChanged(nameof(RollOut));

        _IsDemo = _userManager.User.Options.IsDemo;
        OnPropertyChanged(nameof(IsDemo));

        _Time24 = _userManager.User.Options.Time24;
        OnPropertyChanged(nameof(Time24));

        LoadCustomMetrics();
    }


    public Command CommandDeviceSettings => new Command(async () =>
    {
        if (CheckLockAndSet())
            return;

        //#if DEV && DEBUG
        //        if (true)
        //#else
        //        if (_device.IsConnected)
        //#endif

        if (_device.IsConnected)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                await NavbarModel.Shell.GoToAsync(AppRoutes.DeviceSettings.Route, true);
            });
        }
    });

    public Command CommandAboutApp => new Command(async () =>
    {
        if (CheckLockAndSet())
            return;

        App.OpenPage(new PageAbout());

    });

    public Command CommandFirmwareHelp => new Command(async () =>
    {
        if (CheckLockAndSet())
            return;

        App.OpenPage(new PageFirmwareHelp());

    });

    public Command CommandOpenLogs => new Command(async () =>
    {
        if (CheckLockAndSet("CommandOpenLogs"))
            return;

        App.OpenPage(new PageLog());

    });

    public Command CommandSystemSettings => new Command(async () =>
    {
        if (CheckLockAndSet("CommandSystemSettings"))
            return;

        MainThread.BeginInvokeOnMainThread(async () =>
        {
            App.Native.OpenSettings();
        });

    });

    public Command CommandToggleDemo => new Command(async () =>
    {

        MainThread.BeginInvokeOnMainThread(async () =>
        {
            if (!IsDemo)
            {
                var ok = await UI.Prompt(ResStrings.VendorTitle, ResStrings.DemoWarning, ResStrings.Yes, ResStrings.No);
                if (!ok)
                    return;
            }

            IsDemo = !IsDemo;
        });

    });

    public Command CommandToggleRollOut => new Command(async () =>
    {
        RollOut = !RollOut;
    });

    public Command CommandToggleSound => new Command(async () =>
    {
        Sound = !Sound;
    });

    public Command CommandToggleSpeak => new Command(async () =>
    {
        CanSay = !CanSay;
    });

    public Command CommandToggleTime24 => new Command(async () =>
    {
        Time24 = !Time24;
    });

    /// <summary>
    /// delay to save after any value was modified by user
    /// </summary>
    const int SaveDelayMs = 2000;

    void SaveUser()
    {
        _userManager.SaveLocally();
    }



    public void SetMetrics(OptionsUnits type)
    {
        //todo
        _userManager.User.Options.Units = type;

        OnPropertyChanged(nameof(DisplayMetrics));

        LauchRestartingTimer_Save(SaveDelayMs);
    }

    public Command CommandSelectMetricsUnit => new Command(async () =>
    {
        //var check = Super.IsMainThread;

        List<SelectableAction> options = new()
        {
            new SelectableAction
            {
                Id = OptionsUnits.EU.ToString(),
                Title = ResStrings.Europe,
                Action = () =>
                {
                    SetMetrics(OptionsUnits.EU);
                }
            },
            new SelectableAction
            {
                Id = OptionsUnits.US.ToString(),
                Title = ResStrings.USA,
                Action = () =>
                {
                    SetMetrics(OptionsUnits.US);
                }
            },
        };

        App.SelectAction(options, ResStrings.MeasuringSystem);
    });




    #region LOGS FORMAT

    public string DisplayLogFormat
    {
        get
        {
            return _userManager.User.Options.LogFormat.ToString();
        }
    }

    public Command CommandSelectLogFormat => new Command(async () =>
    {


        List<SelectableAction> options = new()
        {
            new SelectableAction
            {
                Id = OptionsLogFormat.CSV.ToString(),
                Title = OptionsLogFormat.CSV.ToString(),
                Action = () =>
                {
                    SetLogFormat(OptionsLogFormat.CSV);
                }
            },
            new SelectableAction
            {
                Id = OptionsLogFormat.VBO.ToString(),
                Title = OptionsLogFormat.VBO.ToString(),
                Action = () =>
                {
                    SetLogFormat(OptionsLogFormat.VBO);
                }
            },
        };

        App.SelectAction(options, ResStrings.MeasuringSystem);

    });

    public void SetLogFormat(OptionsLogFormat type)
    {
        //todo
        _userManager.User.Options.LogFormat = type;

        OnPropertyChanged(nameof(DisplayLogFormat));

        LauchRestartingTimer_Save(SaveDelayMs);
    }

    #endregion


    #region SPEED RANGES

    void LoadCustomMetrics()
    {
        var loaded = new List<OptionItem>();
        loaded.AddRange(_userManager.User.Options.SpeedRanges
            .OrderBy(o => o.Start)
            .Select(x =>
                new LocalSpeedRange(x.Start, x.End, x.Units, x.Id, x.IsReadOnly)));

        loaded.AddRange(_userManager.User.Options.Distances
            .OrderBy(o => o.End)
            .Select(x =>
                new LocalDistance(x.Start, x.End, x.Units, x.Id, x.IsReadOnly)));

        LoadedMetrics = loaded.OrderBy(o => o.IsReadOnly).ToList();
    }

    protected List<OptionItem> LoadedMetrics;


    private ListEditorViewModel _editorCustomMetrics;

    void EditCustomMetrics()
    {
        LoadCustomMetrics();

        _editorCustomMetrics = new ListEditorViewModel(
            ResStrings.CustomMetrics,
            new Command(async () =>
            {
                if (CheckLockAndSet("CommandAddItem"))
                    return;

                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    List<ISelectableOption> options = new()
                    {
                        new SelectableAction
                        {
                            Title = ResStrings.SpeedRange,
                            Action = async () =>
                            {
                                var vm = new EditSpeedRangeViewModel(new()
                                {
                                    Units = _userManager.User.Options.Units,
                                    Title = ResStrings.SpeedRange
                                }, UpdateSpeedRangesWithOne);
                                vm.Title = ResStrings.SpeedRange;
                                await NavbarModel.Shell.Navigation.PushAsync(new PageEditForm(vm));
                            }
                        },
                        new SelectableAction
                        {
                            Title = ResStrings.Distance,
                            Action = async () =>
                            {
                                var vm =  new EditDistanceViewModel(new()
                                {
                                    Units = _userManager.User.Options.Units,
                                    Title = ResStrings.Distance
                                }, UpdateDistancesWithOne);
                                vm.Title = ResStrings.Distance;
                                await NavbarModel.Shell.Navigation.PushAsync(new PageEditForm(vm));
                            }
                        },
                    };

                    var selected = await App.Instance.UI.PresentSelection(options, ResStrings.BtnCreate) as SelectableAction;
                    selected?.Action?.Invoke();
                });
            }),
            new Command(async (context) =>
            {
                if (CheckLockAndSet("CommandSelectItem"))
                    return;

                if (context is OptionItem option)
                {
                    var item = LoadedMetrics.FirstOrDefault(x => x.Id == option.Id);
                    if (item != null)
                    {
                        if (item is LocalSpeedRange range)
                        {
                            if (!range.IsReadOnly)
                            {
                                //we are creating vm in background thread for not blocking ui
                                var vm = new EditSpeedRangeViewModel(range, UpdateSpeedRangesWithOne);
                                vm.Title = ResStrings.SpeedRange;
                                await Task.Delay(400); //for tap anim
                                MainThread.BeginInvokeOnMainThread(async () =>
                                {
                                    await NavbarModel.Shell.Navigation.PushAsync(new PageEditForm(vm));
                                });
                            }
                        }
                        else
                        if (item is LocalDistance distance)
                        {
                            if (!distance.IsReadOnly)
                            {
                                //we are creating vm in background thread for not blocking ui
                                var vm = new EditDistanceViewModel(distance, UpdateDistancesWithOne)
                                {
                                    Title = ResStrings.Distance
                                };
                                await Task.Delay(400); //for tap anim
                                MainThread.BeginInvokeOnMainThread(async () =>
                                {
                                    await NavbarModel.Shell.Navigation.PushAsync(new PageEditForm(vm));
                                });
                            }
                        }
                    }
                }
            }),
            new Command(async (context) =>
                {
                    if (CheckLockAndSet("CommandDeleteItem"))
                        return;

                    if (context is LocalSpeedRange item)
                    {
                        if (!item.IsReadOnly)
                        {
                            MainThread.BeginInvokeOnMainThread(async () =>
                            {
                                var ok = await UI.Prompt(ResStrings.Deleting,
                                    ResStrings.AreYouSure,
                                    ResStrings.Yes, ResStrings.No);
                                if (ok)
                                {

                                    _userManager.User.Options.SpeedRanges.RemoveAll(x =>
                                        x.Units == item.Units
                                        && x.Start == item.Start
                                        && x.End == item.End);
                                    SaveUser();

                                    LoadCustomMetrics();

                                    _editorCustomMetrics.UpdateState(true);
                                }
                            });

                        }

                    }
                    else
                    if (context is LocalDistance distance)
                    {
                        if (!distance.IsReadOnly)
                        {
                            MainThread.BeginInvokeOnMainThread(async () =>
                            {
                                var ok = await UI.Prompt(ResStrings.Deleting,
                                    ResStrings.AreYouSure,
                                    ResStrings.Yes, ResStrings.No);
                                if (ok)
                                {

                                    _userManager.User.Options.Distances.RemoveAll(x =>
                                        x.Units == distance.Units
                                        && x.Start == distance.Start
                                        && x.End == distance.End);

                                    SaveUser();

                                    LoadCustomMetrics();

                                    _editorCustomMetrics.UpdateState(true);
                                }
                            });

                        }

                    }
                }),
            new Command(async (context) =>
            {
                if (CheckLockAndSet("CommandEditItem"))
                    return;


                if (context is OptionItem option)
                {
                    var item = LoadedMetrics.FirstOrDefault(x => x.Id == option.Id);
                    if (item != null)
                    {
                        if (item is LocalSpeedRange range)
                        {
                            //we are creating vm in background thread for not blocking ui
                            var vm = new EditSpeedRangeViewModel(range, UpdateSpeedRangesWithOne);
                            vm.Title = ResStrings.SpeedRange;
                            await Task.Delay(400); //for tap anim
                            MainThread.BeginInvokeOnMainThread(async () =>
                            {
                                await NavbarModel.Shell.Navigation.PushAsync(new PageEditForm(vm));
                            });
                        }
                        else
                        if (item is LocalDistance distance)
                        {
                            //we are creating vm in background thread for not blocking ui
                            var vm = new EditDistanceViewModel(distance, UpdateDistancesWithOne)
                            {
                                Title = ResStrings.Distance
                            };
                            await Task.Delay(400); //for tap anim
                            MainThread.BeginInvokeOnMainThread(async () =>
                            {
                                await NavbarModel.Shell.Navigation.PushAsync(new PageEditForm(vm));
                            });
                        }
                    }
                }
            }), //edit
            () => LoadedMetrics
        );

        //        await Task.Delay(200);

        var page = new PageEditList(_editorCustomMetrics);
        App.OpenPage(page);

    }

    public async Task<int> UpdateDistancesWithOne(OptionItem item)
    {
        var distance = item as LocalDistance;

        var existingMetric = _userManager.User.Options.Distances.Any(x =>
            x.Units == distance.Units
            && x.Start == distance.Start
            && x.End == distance.End);

        if (existingMetric)
        {
            return 1;
        }

        var existing = _userManager.User.Options.Distances.FirstOrDefault(x => x.Id == item.Id);
        if (existing == null)
        {
            _userManager.User.Options.Distances.Add(new LocalDistance(distance.Start, distance.End, distance.Units));
        }
        else
        {
            Reflection.MapProperties(item, existing);
            //_mapper.Map(item, existing);
        }

        SaveUser();

        LoadCustomMetrics();

        _editorCustomMetrics.UpdateState(true);

        return 0;
    }


    public async Task<int> UpdateSpeedRangesWithOne(OptionItem item)
    {
        var range = item as LocalSpeedRange;

        var existingMetric = _userManager.User.Options.SpeedRanges.Any(x =>
            x.Units == range.Units
            && x.Start == range.Start
            && x.End == range.End);

        if (existingMetric)
        {
            return 1;
        }

        var existing = _userManager.User.Options.SpeedRanges.FirstOrDefault(x => x.Id == range.Id);
        if (existing == null)
        {
            _userManager.User.Options.SpeedRanges.Add(new LocalSpeedRange(range.Start, range.End, range.Units));
        }
        else
        {
            Reflection.MapProperties(item, existing);
        }

        SaveUser();

        LoadCustomMetrics();

        return 0;
    }



    public Command CommandEditCustomMetrics => new Command(async () =>
    {
        if (CheckLockAndSet("CommandEditCustomMetrics"))
            return;

        EditCustomMetrics();

    });


    #endregion




    #region EDIT CARS

    private ListEditorViewModel _editorCars;

    void EditCars()
    {
        _editorCars = new ListEditorViewModel(
            ResStrings.Cars,
            new Command(async () =>
            {
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    await NavbarModel.Shell.Navigation.PushAsync(new PageEditCar(new LocalCar(), UpdateCarsWithOne));
                });
            }), //add
            new Command(async (context) =>
            {
                if (context is OptionItem option)
                {
                    var newId = option.Id.ToInteger();
                    if (newId != _userManager.User.Options.CarId)
                    {
                        foreach (var selectableOption in LoadedCars)
                        {
                            selectableOption.Selected = selectableOption.Id == option.Id;
                        }
                        SelectCar(newId);
                    }
                }
            }), //select
            new Command(async (context) =>
            {
                if (_userManager.User.Cars.Count > 1)
                {
                    if (context is OptionItem item)
                    {
                        if (_userManager.User.Options.CarId == item.Id.ToInteger())
                        {
                            App.Instance.UI.ShowToast(ResStrings.CannotDeleteUsed);
                            return;
                        }

                        MainThread.BeginInvokeOnMainThread(async () =>
                        {
                            var ok = await UI.Prompt(ResStrings.Deleting,
                                ResStrings.AreYouSure,
                                ResStrings.Yes, ResStrings.No);
                            if (ok)
                            {

                                _userManager.User.Cars.RemoveAll(x =>
                                    x.Id.ToString() == item.Id);
                                SaveUser();

                                await InsureCarsLoaded(true);

                                _editorCars.UpdateState(true);
                            }
                        });

                    }
                }
                else
                {
                    UI.ShowToast(ResStrings.CannotDeleteSingle);
                }
            }), //del
            new Command(async (context) =>
            {
                if (context is OptionItem option)
                {
                    var item = _userManager.User.Cars.FirstOrDefault(x => x.Id == option.Id.ToInteger());
                    if (item != null)
                    {
                        MainThread.BeginInvokeOnMainThread(async () =>
                        {
                            await NavbarModel.Shell.Navigation.PushAsync(new PageEditCar(item, UpdateCarsWithOne));
                        });
                    }
                }
            }), //edit
            () => LoadedCars
        );

        var page = new PageEditList(_editorCars);

        App.OpenPage(page);

        //MainThread.BeginInvokeOnMainThread(async () =>
        //{
        //    await NavbarModel.Shell.Navigation.PushAsync(page);
        //});

    }

    public async Task<int> UpdateCarsWithOne(LocalCar item)
    {
        //AddReplaceItem(item);

        var existing = _userManager.User.Cars.FirstOrDefault(x => x.Id == item.Id);
        if (existing == null || existing.Id == 0)
        {
            _userManager.User.Cars.Add(item);
        }
        else
        {
            _mapper.Map(item, existing);
        }

        await _userManager.SyncUser();

        await InsureCarsLoaded(true);

        _editorCars.UpdateState(true);

        OnPropertyChanged(nameof(DisplayCar));
        App.Instance.Messager.All("User", "Changed");

        return 0;
    }


    public Command CommandEditCars => new Command(async () =>
    {
        EditCars();
    });




    #endregion

    #region EDIT USERS



    async Task InsureUsersLoaded(bool reload = false)
    {
        await _userManager.InsureUserIsInDatabase();

        if (LoadedUsers == null || reload)
        {
            var users = await _userManager.GetUsers();

            LoadedUsers = users
                .Select(x =>
                    new OptionItem(x.Id.ToString(), x.Name)
                    {
                        Selected = x.Id == _userManager.User.Id
                    }).ToList();
        }
    }

    protected List<OptionItem> LoadedUsers;

    private ListEditorViewModel _editorUsers;
    async void EditUsers()
    {
        await InsureUsersLoaded();

        _editorUsers = new ListEditorViewModel(
            ResStrings.Users,
            new Command(async () =>
            {
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    await NavbarModel.Shell.Navigation.PushAsync(new PageEditUser(new LocalUser(), UpdateUsersWithOne));
                });
            }),
            new Command((context) =>
            {
                if (context is OptionItem option)
                {
                    var newId = option.Id.ToInteger();
                    if (newId != _userManager.User.Id)
                    {
                        foreach (var selectableOption in LoadedUsers)
                        {
                            selectableOption.Selected = selectableOption.Id == option.Id;
                        }
                        SelectUser(newId);
                    }
                }
            }), //select
            new Command((context) =>
            {
                if (LoadedUsers.Count > 1)
                {
                    if (context is OptionItem item)
                    {
                        MainThread.BeginInvokeOnMainThread(async () =>
                        {
                            if (_userManager.User.Id == item.Id.ToInteger())
                            {
                                App.Instance.UI.ShowToast(ResStrings.CannotDeleteUsed);
                                return;
                            }

                            var ok = await UI.Prompt(ResStrings.Deleting,
                                ResStrings.AreYouSure,
                                ResStrings.Yes, ResStrings.No);
                            if (ok)
                            {
                                await _userManager.DeleteUser(item.Id.ToInteger());

                                await InsureUsersLoaded(true);

                                _editorUsers.UpdateState(true);
                            }
                        });

                    }
                }
                else
                {
                    UI.ShowToast(ResStrings.CannotDeleteSingle);
                }
            }),
            new Command((context) =>
            {
                if (context is OptionItem option)
                {
                    var item = _userManager.CachedUsers.FirstOrDefault(x => x.Id == option.Id.ToInteger());
                    if (item != null)
                    {
                        MainThread.BeginInvokeOnMainThread(async () =>
                        {
                            await NavbarModel.Shell.Navigation.PushAsync(new PageEditUser(item, UpdateUsersWithOne));
                        });
                    }
                }
            }),
            () => LoadedUsers);

        App.OpenPage(new PageEditList(_editorUsers));

    }

    public async Task<int> UpdateUsersWithOne(LocalUser item)
    {
        //AddReplaceItem(item);
        if (item.Id > 0)
        {
            await _userManager.UpdateUser(item);
            if (item.Id == _userManager.User.Id)
            {
                await _userManager.ReloadUser();
            }
        }
        else
        {
            await _userManager.AddUser(item);
        }

        await InsureUsersLoaded(true);

        _editorUsers.UpdateState(true);

        OnPropertyChanged(nameof(DisplayUser));
        OnPropertyChanged(nameof(DisplayCar));
        App.Instance.Messager.All("User", "Changed");

        return 0;
    }


    public Command CommandEditUsers => new Command(() =>
    {
        EditUsers();
    });




    #endregion





    #region USERS

    async void SelectUser(int id)
    {
        if (id != _userManager.User.Id)
        {
            var user = _userManager.CachedUsers.FirstOrDefault(x => x.Id == id);
            if (user != null)
            {
                _userManager.SetUser(user);
                App.Instance.Messager.All("User", "Changed");
            }
            OnPropertyChanged(nameof(DisplayUser));
            OnPropertyChanged(nameof(DisplayCar));

            Initialize();
        }
    }



    public Command CommandSelectUser => new Command(async () =>
    {
        if (CheckLockAndSet("CommandSelectUser"))
            return;

        await InsureUsersLoaded();

        EditUsers();

        return;


    });



    #endregion

    #region CARS

    void SelectCar(int id)
    {
        var car = _userManager.User.Cars.FirstOrDefault(x => x.Id == id);
        if (car != null)
        {
            if (_userManager.User.Options.CarId == car.Id)
                return;

            _userManager.User.Options.CarId = car.Id;
            SaveUser();
            OnPropertyChanged(nameof(DisplayCar));
            App.Instance.Messager.All("User", "Changed");
        }
    }

    public Command CommandSelectCar => new Command(async () =>
    {
        if (CheckLockAndSet("CommandSelectCar"))
            return;

        await InsureCarsLoaded();
        EditCars();
        return;

        await Task.Run(async () =>
        {


            Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(delayTap), async () =>
            {
                await Task.Run(async () =>
                {

                    await InsureCarsLoaded();
                    EditCars();

                }).ConfigureAwait(false);

                return false;
            });


        }).ConfigureAwait(false);




    });


    #endregion

    public bool Initialized { get; set; }

    public void Init()
    {
        Initialized = true;


    }

    public string DisplayMetrics
    {
        get
        {
            if (_userManager.User.Options.Units == OptionsUnits.US)
                return ResStrings.USA;
            return ResStrings.Europe;
            //            return _userManager.User.Options.Units.ToString();
        }
    }



    public string DisplayCar
    {
        get
        {
            if (_userManager.User.SelectedCar == null)
            {
                return ResStrings.Select;
            }
            return _userManager.User.SelectedCar.Title;
        }
    }

    public string DisplayUser
    {
        get
        {
            return _userManager.User.Name;
        }
    }

    private bool _Time24;
    public bool Time24
    {
        get
        {
            return _Time24;
        }
        set
        {
            if (_Time24 != value)
            {
                _Time24 = value;
                OnPropertyChanged();
                _userManager.User.Options.Time24 = value;
                LauchRestartingTimer_Save(SaveDelayMs);
            }
        }
    }

    private bool _Sound;
    public bool Sound
    {
        get
        {
            return _Sound;
        }
        set
        {
            if (_Sound != value)
            {
                _Sound = value;
                OnPropertyChanged();
                _userManager.User.Options.Sound = value;
                LauchRestartingTimer_Save(SaveDelayMs);
            }
        }
    }

    private bool _CanSay;
    public bool CanSay
    {
        get
        {
            return _CanSay;
        }
        set
        {
            if (_CanSay != value)
            {
                _CanSay = value;
                OnPropertyChanged();
                _userManager.User.Options.CanSay = value;
                LauchRestartingTimer_Save(SaveDelayMs);
            }
        }
    }

    private bool _RollOut;


    public bool RollOut
    {
        get
        {
            return _RollOut;
        }
        set
        {
            if (_RollOut != value)
            {
                _RollOut = value;
                OnPropertyChanged();
                _userManager.User.Options.Rollout = value;
                LauchRestartingTimer_Save(SaveDelayMs);
            }
        }
    }

    private bool _IsDemo;


    public bool IsDemo
    {
        get
        {
            return _IsDemo;
        }
        set
        {
            if (_IsDemo != value)
            {
                _IsDemo = value;
                OnPropertyChanged();
                _userManager.User.Options.IsDemo = value;
                LauchRestartingTimer_Save(SaveDelayMs);
            }
        }
    }

    #region save on timer

    protected RestartingTimer<object> TimerUpdateLocked;
    private readonly RaceBoxDeviceViewModel _device;


    protected void LauchRestartingTimer_Save(int ms)
    {
        if (TimerUpdateLocked == null)
        {
            TimerUpdateLocked = new RestartingTimer<object>(TimeSpan.FromMilliseconds(ms), (context) =>
            {
                SaveUser();
            });
            TimerUpdateLocked.Start(null);
        }
        else
        {
            TimerUpdateLocked.Restart(null);
        }
    }


    #endregion



}