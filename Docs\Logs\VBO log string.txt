Формирование строки в VBO-логе

sprintf(str, "%03d %02d%02d%02d.%02d %s%05ld.%06lu %s%05ld.%06lu %03d.%02d %03d.%02d %s%04ld.%02d %s%1d.%02d %s%1d.%02d %s%d.%1d %s%d.%1d %05d %03d %s%03d %s%03d\n", (uint8_t)gps->satellites.value(), getFixHour(gps->time.hour()), gps->time.minute(), gps->time.second(), gps->time.centisecond(), (gps->location.intLat() < 0) ? "-": "+", abs(gps->location.intLat()) / 166667UL, abs(gps->location.intLat()) % 166667UL * 6, (gps->location.intLon() > 0) ? "-": "+", abs(gps->location.intLon()) / 166667UL, abs(gps->location.intLon()) % 166667UL * 6, (uint16_t)(spd_kph), (uint16_t)(spd_kph * 100) % 100, validHeading / 100, validHeading % 100 / 10, (gps->altitude.cm() < 0) ? "-" : "+", abs(gps->altitude.cm()) / 100, (uint8_t)(abs(gps->altitude.cm()) % 100), (frame->accelG < 0) ? "-" : "+", (uint8_t)(abs(frame->accelG) / 100), abs(frame->accelG) % 100, (frame->latAccelG < 0) ? "-" : "+", (uint16_t)(abs(frame->latAccelG) / 100), abs(frame->latAccelG) % 100, (frame->yawGyro < 0) ? "-" : "+", abs(frame->yawGyro) / 10, abs(frame->yawGyro) % 10, (frame->driftAngle < 0) ? "-" : "+", abs(frame->driftAngle) / 10, abs(frame->driftAngle) % 10, frame->engRPM, frame->thrPos, (frame->coolTemp < 0) ? "-" : "", abs(frame->coolTemp), (frame->intakeTemp < 0) ? "-" : "", abs(frame->intakeTemp));