﻿namespace Racebox.ViewModels;

public class HistoryViewModel : ContentViewModel
{
    public HistoryViewModel(UserManager manager, IUIAssistant ui, IMapper mapper) : base(manager, ui, mapper)
    {
        OrderOptions = new List<SelectableAction>
        {
            new (ResStrings.OrderNewFirst,  () =>
            {
                Order = "-Id";
                LoadData(true);
            }) ,
            new (ResStrings.OrderOldFirst,() =>
            {
                Order = "Id";
                LoadData(true);
            }) ,
            //new ("По макс. скорости",  () =>
            //{
            //    Order = "-MaxSpeed";
            //    LoadData(true);
            //}) ,
        };

        FilterOptions = new List<OptionItem>
        {
            //new ("ok", "todo") ,
            //new ("failed","todo") ,
        };

        PageNumber = 1;
        PageSize = 10;

        Loader.Init(async (bool reload, string filter, string search, string order, int page, int pageSize) =>
        {
            if (string.IsNullOrEmpty(Order))
            {
                var defaultOrder = OrderOptions.First() as SelectableAction;
                defaultOrder.Action.Invoke();
            }

            var args = new FilteredListRequestDto
            {
                PageNumber = page,
                PageSize = pageSize,
                Order = this.Order,
                Filter = BuildFilterString($"AppUserId == {_userManager.User.Id}"),
                SearchFields = null// example: "UserName, FirstName, LastName, PhoneNumber"
            };

            if (!string.IsNullOrEmpty(SearchText))
            {
                args.Search = SearchText;
            }

            await _userManager.InsureUserIsInDatabase();

            var db = GetDatabase();
            var result = await db.GetMeasuredResultsPagedList(args, _mapper);

            #region MOCK

            if (DeviceInfo.DeviceType == DeviceType.Virtual
                && result.TotalItemCount == 0) // emulator / simulator
            {
                var split = "\r\n"; //Environment.NewLine
                result.Items = new List<HistoryCellData>()
            {
                new HistoryCellData()
                {
                    DisplayInfo = $"CarTitle{split}{DateTime.Now:d}{split}{DateTime.Now:hh:mm:ss}",
                    DisplayResults=$"1{split}2{split}3",
                    IsValid=true
                },
                new HistoryCellData()
                {
                    DisplayInfo = $"CarTitle{split}{DateTime.Now:d}{split}{DateTime.Now:hh:mm:ss}",
                    DisplayResults=$"1{split}2{split}3"
                },
                new HistoryCellData()
                {
                    DisplayInfo = $"CarTitle{split}{DateTime.Now:d}{split}{DateTime.Now:hh:mm:ss}",
                    DisplayResults=$"1{split}2{split}3",
                    IsValid=true
                },
                new HistoryCellData()
                {
                    DisplayInfo = $"CarTitle{split}{DateTime.Now:d}{split}{DateTime.Now:hh:mm:ss}",
                    DisplayResults=$"1{split}2{split}3"
                },
                new HistoryCellData()
                {
                    DisplayInfo = $"CarTitle{split}{DateTime.Now:d}{split}{DateTime.Now:hh:mm:ss}",
                    DisplayResults=$"1{split}2{split}3"
                },
                new HistoryCellData()
                {
                    DisplayInfo = $"CarTitle{split}{DateTime.Now:d}{split}{DateTime.Now:hh:mm:ss}",
                    DisplayResults=$"1{split}2{split}3"
                },
                new HistoryCellData()
                {
                    DisplayInfo = $"CarTitle{split}{DateTime.Now:d}{split}{DateTime.Now:hh:mm:ss}",
                    DisplayResults=$"1{split}2{split}3"
                },
                new HistoryCellData()
                {
                    DisplayInfo = $"CarTitle{split}{DateTime.Now:d}{split}{DateTime.Now:hh:mm:ss}",
                    DisplayResults=$"1{split}2{split}3"
                },
            };
            }
            #endregion

            //if (reload || DetectEntitiesListChanged(Items, result.Items))
            //{
            //    MainThread.BeginInvokeOnMainThread(async () =>
            //    {
            //        Items.Clear();
            //        await Task.Delay(10); //avoid bugging ui on ios
            //        Items.AddRange(result.Items);
            //    });
            //}

            return result;
        });

    }

    public PagedListLoader<HistoryCellData> Loader { get; protected set; } = new();


    protected bool DetectEntitiesListChanged<T>(IList<T> a, IList<T> b) where T : IHasIntId
    {

        var added = b.Where(p => a.All(p2 => p2.Id != p.Id));
        if (added.Any())
            return true;

        var removed = a.Where(p => b.All(p2 => p2.Id != p.Id)).ToArray();
        if (removed.Any())
            return true;

        var intersection = a.Where(p => b.Any(x => x.Id == p.Id)).ToArray();
        var moved = intersection.Where(x => b[a.IndexOf(x)].Id != x.Id && !removed.Contains(x)).ToArray();
        if (moved.Any())
            return true;


        /*
          List<T> newlyInserted = new List<T>();
          foreach (var item in removed)
          {
              //Newly inserted into the list - D1
              newlyInserted.Add(b[a.IndexOf(item)]);
              //Index of D1 if required
              var indexOfNewlyAddedItem = a.IndexOf(item);
          }
        */
        return false;
    }

    protected bool DetectHasChanges<T>(IList<T> a, IList<T> b)
    {
        var removed = a.Except(b).ToArray();
        var moved = a.Where(x => !b[a.IndexOf(x)].Equals(x) && !removed.Contains(x)).ToArray();
        /*
          List<T> newlyInserted = new List<T>();
          foreach (var item in removed)
          {
              //Newly inserted into the list - D1
              newlyInserted.Add(b[a.IndexOf(item)]);
              //Index of D1 if required
              var indexOfNewlyAddedItem = a.IndexOf(item);
          }
        */
        return removed.Any() || moved.Any();
    }

    public ICommand CommandItemTapped
    {
        get
        {
            return new Command(async (object context) =>
            {
                if (CheckLockAndSet("CommandItemTapped"))
                    return;

                if (context is HistoryCellData model)
                {



                    var route = $"result";
                    route += $"?id={model.Id}";

                    await NavbarModel.Shell.GoToAsync(route, true);

                }
            });
        }
    }

    public override void OnSubscribing(bool subscribe)
    {
        if (subscribe)
        {
            App.Instance.Messager.Subscribe<string>(this, "User", async (sender, arg) =>
            {
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    Loader.Reset();
                    //Items.Clear();
                    Initialized = false;
                });
            });
        }
        else
        {
            App.Instance.Messager.Unsubscribe(this, "User");
        }

        base.OnSubscribing(subscribe);
    }


    //public ObservableRangeCollection<HistoryCellData> Items { get; } = new();
    public async Task UpdateState()
    {
        if (!Initialized)
        {
            Initialized = true;
            await LoadData(true);
        }
        else
        {
            await LoadData(false);
        }

    }

    #region FILTER UI

    private bool? _isValid;

    private string _selectedCar;
    public ICommand CommandSelectFilterCar
    {
        get
        {
            return new Command(async (object context) =>
            {
                if (CheckLockAndSet("CommandSelectFilterCar"))
                    return;

                await InsureCarsLoaded();

                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    List<SelectableAction> options = new();

                    foreach (var item in _userManager.User.Cars)
                    {
                        options.Add(new SelectableAction
                        {
                            Title = item.Title,
                            Action = () =>
                            {
                                _selectedCar = item.Id.ToString();
                                OnPropertyChanged(nameof(DisplayFilterCars));
                            }
                        });
                    }
                    options.Add(new SelectableAction
                    {
                        Title = ResStrings.All,
                        Action = () =>
                        {
                            _selectedCar = null;
                            OnPropertyChanged(nameof(DisplayFilterCars));
                        }
                    });


                    var selected = await _ui.PresentSelection(options, ResStrings.Cars) as SelectableAction;
                    selected?.Action?.Invoke();

                });


            });
        }
    }

    public ICommand CommandFilter
    {
        get
        {
            return new Command(async (object context) =>
            {
                if (CheckLockAndSet("CommandFilter"))
                    return;

                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    _selectedCar = FilterCars;
                    _isValid = FilterIsValid;
                    await NavbarModel.Shell.Navigation.PushAsync(new PageFilterResults(this));
                });

            });
        }
    }

    public ICommand CommandSubmitFilter
    {
        get
        {
            return new Command(async (object context) =>
            {
                if (CheckLockAndSet("CommandSubmitFilter"))
                    return;

                FilterCars = _selectedCar;
                FilterIsValid = _isValid;
                NavbarModel.CommandGoBack.Execute(null);
            });
        }
    }

    public ICommand CommandResetFilter
    {
        get
        {
            return new Command(async (object context) =>
            {
                if (CheckLockAndSet("CommandResetFilter"))
                    return;

                FilterCars = null;
                NavbarModel.CommandGoBack.Execute(null);
            });
        }
    }

    public string FilterCars { get; set; }
    public bool? FilterIsValid { get; set; }
    public string DisplayFilterCars
    {
        get
        {
            if (!string.IsNullOrEmpty(_selectedCar))
            {
                var item = LoadedCars.FirstOrDefault(x => x.Id == _selectedCar);
                if (item != null)
                {
                    return item.Title;
                }
            }
            return ResStrings.All;
        }
    }
    public string DisplayIsValid
    {
        get
        {
            if (_isValid.HasValue)
            {
                return _isValid.Value ? ResStrings.IsValidYes : ResStrings.IsValidNo;
            }
            return ResStrings.All;
        }
    }

    public ICommand CommandSelectFilterIsValid
    {
        get
        {
            return new Command(async (object context) =>
            {

                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    List<SelectableAction> options = new()
                    {
                        new SelectableAction
                        {
                            Title = ResStrings.IsValidYes,
                            Action = () =>
                            {
                                _isValid = true;
                                OnPropertyChanged(nameof(DisplayIsValid));
                            }
                        },
                        new SelectableAction
                        {
                            Title = ResStrings.IsValidNo,
                            Action = () =>
                            {
                                _isValid = false;
                                OnPropertyChanged(nameof(DisplayIsValid));
                            }
                        },
                        new SelectableAction
                        {
                            Title = ResStrings.All,
                            Action = () =>
                            {
                                _isValid = null;
                                OnPropertyChanged(nameof(DisplayIsValid));
                            }
                        },
                    };

                    var selected = await _ui.PresentSelection(options, ResStrings.IsValid) as SelectableAction;
                    selected?.Action?.Invoke();
                });

            });
        }
    }


    #endregion

    #region ORDER UI

    private string Order { get; set; }

    public ICommand CommandOrder
    {
        get
        {
            return new Command(async (object context) =>
            {
                if (CheckLockAndSet("CommandOrder"))
                    return;

                MainThread.BeginInvokeOnMainThread(async () =>
                {

                    var selected = await App.Instance.UI.PresentSelection(OrderOptions, ResStrings.Order) as SelectableAction;
                    selected?.Action?.Invoke();

                });

            });
        }
    }

    #endregion

    string BuildFilterString(string custom)
    {
        if (string.IsNullOrEmpty(FilterCars) && !FilterIsValid.HasValue)
        {
            Title = ResStrings.AllCars;
        }
        else
        {
            Title = ResStrings.Filtered;
        }

        var listTags = new List<string>();

        if (!string.IsNullOrEmpty(custom))
        {
            listTags.Add(custom);
        }

        if (!string.IsNullOrEmpty(FilterCars))
        {
            listTags.Add($"CarId == {FilterCars}");
        }

        if (FilterIsValid.HasValue)
        {
            listTags.Add($"IsValid == {FilterIsValid}");
        }

        return string.Join(" && ", listTags);
    }

    public ICommand CommandLoadMore
    {
        get
        {
            return new Command(async (object context) =>
            {
                if (!IsBusy)
                {
                    await Loader.LoadMoreData();
                }
            });
        }
    }

    public async Task LoadData(bool reload)
    {

        if (IsBusy)
        {
            return;
        }

        try
        {
            IsBusy = true;

            await Loader.LoadData(reload);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
        }
        finally
        {
            IsBusy = false;
        }
    }





    #region REQUEST OPTIONS

    public async void OnNeedChangePage(int page)
    {
        PageNumber = page;
        await LoadData(true);
    }

    private async void SubmitSearch(string text)
    {
        SearchText = text;

        await LoadData(true);
    }
    public IEnumerable<ISelectableOption> FilterOptions { get; set; }

    public ISelectableOption Filter { get; set; }

    private int _PageSize = 10;


    public int PageSize
    {
        get { return _PageSize; }
        set
        {
            if (_PageSize != value)
            {
                _PageSize = value;
                OnPropertyChanged();
            }
        }
    }

    public int PageNumber { get; set; }

    private async void SelectFilter(ISelectableOption option)
    {
        Filter = option;

        await LoadData(true);
    }

    public IEnumerable<ISelectableOption> OrderOptions { get; set; }
    public string SearchText { get; set; }



    #endregion

    public bool Initialized { get; set; }




}
