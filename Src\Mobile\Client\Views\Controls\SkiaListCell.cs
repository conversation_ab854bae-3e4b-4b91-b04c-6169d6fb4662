
using DrawnUi;
using System.ComponentModel;

namespace Racebox.Views.Partials;

public class SkiaListCell : Canvas
{
    public SkiaListCell()
    {
        if (BindingContext != null)
        {
            InitBindingContext();
        }
    }

    protected override void OnBindingContextChanged()
    {

        base.OnBindingContextChanged();

        InitBindingContext();
    }

    protected void InitBindingContext()
    {
        if (OldContext != null)
            OldContext.PropertyChanged -= OnContextPropertyChanged;

        var bindable = BindingContext as INotifyPropertyChanged;
        if (bindable != null)
        {
            bindable.PropertyChanged += OnContextPropertyChanged;
            OldContext = bindable;
        }

        var item = this.BindingContext as IHasStringId;
        if (item != null)
        {
            if (string.IsNullOrEmpty(item.Id))
            {
                IsVisible = false;
            }
            else
            {
                IsVisible = true;
            }
        }

        SetContentFull();
    }

    public virtual void SetContentFull()
    {
        Update();
    }

    protected virtual void OnContextPropertyChanged(object sender, PropertyChangedEventArgs e)
    {

    }

    protected INotifyPropertyChanged OldContext;




}