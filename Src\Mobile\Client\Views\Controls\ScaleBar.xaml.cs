namespace Racebox.Views.Partials;

public partial class ScaleBar
{
    public ScaleBar()
    {
        UpdateControl();
        InitializeComponent();
    }

    protected override void OnUpdating()
    {
        base.OnUpdating();

        Bar?.Update();
    }


    public override void UpdateControl()
    {
        var value = ValueScaled;

        var delta = 1.0 - ValueSideOffset * 2.0;

        foreach (var point in GradientPoints)
        {
            if (point.IsFixed)
            {
                point.Value = point.Base;
                continue;
            }

            if (value == 0.5)
                delta = 1.0;

            var set = point.Base - 0.5 + value * delta;

            SetValueSafe(point, set);

        }
        OnPropertyChanged(nameof(Points));
    }



}