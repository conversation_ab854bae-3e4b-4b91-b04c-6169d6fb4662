//
//  InfoViewController.swift
//  CompassCompanion
//
//  Created by <PERSON> on 04/07/2016.
//  Copyright © 2016 <PERSON>. All rights reserved.
//

import UIKit

class InfoViewController: UIViewController {
    
   // var fix = BLEFrame()
    
    let bleNotification = Notification.Name(rawValue: BLE_NOTIFICATION)
    
    @IBOutlet weak var speedString: UILabel!
    @IBOutlet weak var latString: UILabel!
    @IBOutlet weak var lonString: UILabel!
    @IBOutlet weak var satsString: UILabel!
    @IBOutlet weak var hdopString: UILabel!
    @IBOutlet weak var refrashRate: UILabel!
    @IBOutlet weak var dateString: UILabel!
    @IBOutlet weak var timeString: UILabel!
    @IBOutlet weak var altitudeString: UILabel!
    @IBOutlet weak var headingString: UILabel!
    @IBOutlet weak var disconnectString: UILabel!
    
    override func viewDidLoad() {
        super.viewDidLoad()
        UIApplication.shared.isIdleTimerDisabled = true
        createObservers()
    }
    
    func createObservers() {
        NotificationCenter.default.addObserver(self, selector: #selector(InfoViewController.updateLabels), name: bleNotification, object: nil)
    }
    
    @objc func updateLabels() {
        if let year = fix.year, let month = fix.month, let day = fix.day {
            dateString.text = String(format: "Дата: %d-%02d-%02d", year, month, day)
        } else {
            dateString.text = "Данные не получены"
        }
        if let hour = fix.hour, let minute = fix.minute, let second = fix.second, let ms = fix.ms {
            timeString.text = String(format: "Время: %02d:%02d:%02d.%02d", hour, minute, second, ms / 10)
        } else {
            timeString.text = "Данные не получены"
        }
        //speedString.text = (fix.speed_kph != nil) ? "\(fix.speed_kph!)" : "--"
        speedString.text = String(format: "%.1f", BLEFrame.buffer.averageSpeed())
        latString.text = (fix.location.isValid) ? String(format: "Широта:\n %.5f", fix.location.latitude!) : "Широта:\n --"
        lonString.text = (fix.location.isValid) ? String(format: "Долгота:\n %.5f", fix.location.longitude!) : "Долгота:\n --"
        satsString.text = "Спутники: \(fix.sats)"
        hdopString.text = (fix.HDOP != nil) ? "HDOP: \(fix.HDOP!)" : "HDOP: --"
        refrashRate.text = String(format: "Частота: %.1f Гц", BLEFrame.buffer.refrashRate())
        altitudeString.text = (fix.altitude != nil) ? String(format: "Высота: %.0f м", fix.altitude!) : "Высота: -- м"
        headingString.text = (fix.heading != nil) ? String(format: "Курс: %.0f", fix.heading!) : "Курс: --"
        disconnectString.text = String(BLEController.disconnectCount)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
}
