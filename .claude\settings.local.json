{"permissions": {"allow": ["Bash(dotnet new:*)", "Bash(dotnet add:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(copy:*)", "Bash(cp:*)", "Bash(dotnet run:*)", "Bash(git checkout:*)", "Bash(git add:*)", "Bash(git rm:*)", "Bash(git grep:*)", "Bash(dotnet clean:*)", "Bash(dotnet list package:*)", "Bash(find:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(dotnet nuget locals:*)", "Bash(rm:*)", "Bash(dotnet test:*)", "Bash(dotnet build)", "Bash(dotnet build:*)", "<PERSON><PERSON>(timeout:*)"], "deny": [], "ask": []}}