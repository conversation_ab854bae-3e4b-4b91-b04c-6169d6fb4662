﻿using FluentAssertions;
using MapsterMapper;
using Newtonsoft.Json;
using Racebox.SDK;
using Racebox.Shared;
using Racebox.Shared.Models;
using Racebox.Tests.Models;
using System;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;


namespace Racebox.Tests
{
    public class ProtocolTests
    {
        [Test]
        [TestCase("JhC3VRjrjcUrvFIhwMtMFo0D5Ro=")]
        [TestCase("JhC3VQkU7cliu1IhA81MFgkA5Ro=")]
        [TestCase("JhC3UbqE7cliu1IhA81MFgkA5Ro=")]
        public async Task DecodeAppData(string base64data)
        {
            // arrange
            byte[] bytes = Convert.FromBase64String(base64data);
            var target = new RaceBoxState();

            // act
            bytes.DecodeTo(target);

            // assert
            target.Day.Should().NotBe(null);
            TestContext.WriteLine($"{JsonConvert.SerializeObject(target)}");
        }


        static string BytesToHex(byte[] input, string separator = "")
        {
            var bytesDesc = "";
            foreach (var one in input)
            {
                bytesDesc += string.Format("{0:X2}", one) + separator;
            }

            return bytesDesc;
        }

        //[TestCase("ANII7g==")]
        [TestCase("AHwI8A==")]
        public async Task DecodeAdditionalData(string base64data)
        {
            // arrange
            byte[] bytes = Convert.FromBase64String(base64data);
            TestContext.WriteLine($"Processing bytes: {BytesToHex(bytes)}");
            var target = new RaceBoxExtendedState();

            // act
            bytes.DecodeTo(target);

            // assert
            target.DeviceType.Should().NotBe(ModelType.Unknown);
            TestContext.WriteLine($"{JsonConvert.SerializeObject(target)}");
        }

        [TestCase("IGaQQQagjGH1HwA=")]
        public async Task DecodeSettingsData(string base64data)
        {
            // arrange
            byte[] bytes = Convert.FromBase64String(base64data);
            TestContext.WriteLine($"Processing bytes: {BytesToHex(bytes)}");
            var target = new RaceBoxSettingsState();

            // act
            bytes.DecodeTo(target);

            // assert
            target.Should().NotBe(null);
            TestContext.WriteLine($"{JsonConvert.SerializeObject(target)}");
        }

        [TestCase("IGaQQQagjGH1HwA=")]
        public async Task EncodeSettingsData(string base64data)
        {
            // arrange
            byte[] bytes = Convert.FromBase64String(base64data);
            TestContext.WriteLine($"Processing bytes: {BytesToHex(bytes)}");
            var target = new RaceBoxSettingsState();
            var target2 = new RaceBoxSettingsState();

            // act
            bytes.DecodeTo(target);
            var encoded = target.Encode();
            encoded.DecodeTo(target2);

            var compare = Convert.ToBase64String(encoded);
            var result = CompareObjects(target, target2);

            // assert
            TestContext.WriteLine($"Comparing {compare} to {base64data}");

            result.Should().BeNull();
            compare.Should().BeEquivalentTo(base64data);
        }

        public static string CompareObjects<T>(T obj1, T obj2)
        {
            if (obj1 == null || obj2 == null)
            {
                throw new ArgumentNullException("Both objects must be non-null.");
            }

            PropertyInfo[] properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
            foreach (var property in properties)
            {
                object value1 = property.GetValue(obj1);
                object value2 = property.GetValue(obj2);

                if (value1 == null && value2 == null)
                {
                    continue; // Both are null, considered equal.
                }

                if (value1 == null || value2 == null || !value1.Equals(value2))
                {
                    return $"Property '{property.Name}' differs. Obj1: {value1}, Obj2: {value2}";
                }
            }

            return null;
        }
    }
}
