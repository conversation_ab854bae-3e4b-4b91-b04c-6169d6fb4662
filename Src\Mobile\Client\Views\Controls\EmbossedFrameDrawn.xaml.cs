using SkiaSharp;
using System.Runtime.CompilerServices;

namespace Racebox.Views.Drawn;

//[ContentProperty(nameof(Content))]
public partial class EmbossedFrameDrawn : SkiaLayout
{
    public EmbossedFrameDrawn()
    {
        InitializeComponent();
    }

    public override SKPath CreateClip(object arguments, bool usePosition, SKPath path = null)
    {
        return FrameShape.CreateClip(arguments, usePosition, path);
    }

    //protected override SizeRequest OnMeasure(double widthConstraint, double heightConstraint)
    //{
    //    var measured = base.OnMeasure(widthConstraint, heightConstraint);

    //    if (this.Content is SkiaControl drawn)
    //    {
    //        drawn.Update();
    //    }

    //    return measured;
    //}

    public static readonly BindableProperty CornerRadiusProperty = BindableProperty.Create(
        nameof(CornerRadius),
        typeof(double), typeof(EmbossedFrameDrawn), 16.0);
    public double CornerRadius
    {
        get { return (double)GetValue(CornerRadiusProperty); }
        set { SetValue(CornerRadiusProperty, value); }
    }

    protected override void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        base.OnPropertyChanged(propertyName);

        if (propertyName == nameof(CornerRadius))
        {
            FrameShape.CornerRadius = CornerRadius;
        }

    }
}