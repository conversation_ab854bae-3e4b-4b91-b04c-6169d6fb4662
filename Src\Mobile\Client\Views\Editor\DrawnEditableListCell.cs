
using DrawnUi;
using DrawnUi.Extensions;
using SkiaSharp;
using System.ComponentModel;
using System.Windows.Input;

namespace Racebox.Views.Partials;

public class DrawnOptionItemCell : SkiaLayout, ISkiaGestureListener//, ISkiaCell
{
    public DrawnOptionItemCell()
    {
        if (_styleDefault == null)
        {
            _styleDefault = App.Current.Resources.Get<Style>("SkiaLabelDefaultStyle");
            _styleDisabled = App.Current.Resources.Get<Style>("SkiaLabelDisabledStyle");

            _colorDisabled = _styleDisabled.GetSetterValue<Color>(SkiaLabel.TextColorProperty);
            _colorEnabled = _styleDefault.GetSetterValue<Color>(SkiaLabel.TextColorProperty);
        }
    }

    public void OnScrolled()
    { }

    static Style _styleDefault;
    static Style _styleDisabled;
    static Color _colorDisabled;
    static Color _colorEnabled;

    private INotifyPropertyChanged _lastContext;

    public override void OnDisposing()
    {
        base.OnDisposing();

        FreeContext();
    }

    void FreeContext()
    {
        if (_lastContext != null)
        {
            _lastContext.PropertyChanged -= Item_PropertyChanged;
            _lastContext = null;
        }
    }

    //void AttachContext()
    //{
    //	var attach = BindingContext as BindableObject;
    //	if (attach != null)
    //		attach.PropertyChanged += Item_PropertyChanged;
    //}
    void AttachContext()
    {
        if (BindingContext != null)
        {
            _lastContext = BindingContext as INotifyPropertyChanged;
            if (_lastContext != null)
                _lastContext.PropertyChanged += Item_PropertyChanged;
        }
    }

    public void ApplyBindingContext()
    {
        if (BindingContext != _lastContext && !_isAttaching)
        {
            _isAttaching = true;

            FreeContext();

            if (_lastContext == null) //todo _isAttaching lock with cancel token !
            {

                SetContent();

                AttachContext();

            }


            _isAttaching = false;
        }
    }

    protected override void OnBindingContextChanged()
    {
        base.OnBindingContextChanged();

        ApplyBindingContext();
    }

    private static IEnumerable<Setter> _styles;

    protected SkiaLabel labelTitle;
    protected SkiaSvg svgCheck;

    public void SetContent()
    {


        if (BindingContext is OptionItem item)
        {
            LockUpdate(true);


            if (labelTitle == null)
            {
                labelTitle = this.FindView<SkiaLabel>("LabelTitle");
            }

            if (labelTitle != null)
            {

                labelTitle.LockUpdate(true);

                if (item.IsReadOnly)
                {
                    labelTitle.TextColor = _colorDisabled;
                }
                else
                {
                    labelTitle.TextColor = _colorEnabled;
                }
                labelTitle.Text = item.Title;

                labelTitle.LockUpdate(false);

            }

            if (svgCheck == null)
            {
                svgCheck = FindView<SkiaSvg>("SvgCheck");
            }

            if (svgCheck != null)
            {
                svgCheck.IsVisible = item.Selected;
            }

            LockUpdate(false);
            Update();

        }
    }

    private void Item_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(OptionItem.Selected) || e.PropertyName == nameof(OptionItem.Title))
        {
            SetContent();
        }
    }

    public override ISkiaGestureListener ProcessGestures(SkiaGesturesParameters args, GestureEventProcessingInfo apply)
    {
        if (args.Type == TouchActionResult.Tapped)
        {
            PlayRippleAnimation(Colors.White, args.Event.Location.X / Super.Screen.Density, args.Event.Location.Y / Super.Screen.Density);

            if (CommandTapped != null)
            {
                Tasks.StartDelayedAsync(TimeSpan.FromMilliseconds(250), async () =>
                {
                    await Task.Run(() => { CommandTapped?.Execute(this.BindingContext); }).ConfigureAwait(false);
                });
            }

            return this;
        }

        if (args.Type == TouchActionResult.LongPressing)
        {

            CommandLongPressing?.Execute(this.BindingContext);

            return this;
        }

        return base.ProcessGestures(args, apply);
    }

    //-------------------------------------------------------------
    // CommandTapped
    //-------------------------------------------------------------
    public static readonly BindableProperty CommandTappedProperty = BindableProperty.Create(nameof(CommandTapped), typeof(ICommand), typeof(DrawnOptionItemCell),
        null);
    public ICommand CommandTapped
    {
        get { return (ICommand)GetValue(CommandTappedProperty); }
        set { SetValue(CommandTappedProperty, value); }
    }

    //-------------------------------------------------------------
    // CommandLongPressing
    //-------------------------------------------------------------
    public static readonly BindableProperty CommandLongPressingProperty = BindableProperty.Create(nameof(CommandLongPressing), typeof(ICommand), typeof(DrawnOptionItemCell),
        null);


    private bool _isAttaching;

    public ICommand CommandLongPressing
    {
        get { return (ICommand)GetValue(CommandLongPressingProperty); }
        set { SetValue(CommandLongPressingProperty, value); }
    }


    private bool _firstAppearence;


    //public override void OnVisibilityChanged(bool newvalue)
    //{
    //    base.OnVisibilityChanged(newvalue);

    //    if (newvalue)
    //    {
    //        FadeToAsync(1.0, 300, Easing.CubicIn);
    //    }
    //    else
    //    {
    //        _firstAppearence = false;
    //    }
    //}

    //protected override void Draw(SkiaDrawingContext context, SKRect destination, float scale)
    //{
    //    if (!_firstAppearence)
    //    {
    //        _firstAppearence = true;

    //        //Opacity = 1;
    //        Opacity = 0;
    //        FadeToAsync(1.0, 300, Easing.CubicIn);

    //        //cancellationTokenSource?.Cancel();
    //        //cancellationTokenSource = new CancellationTokenSource();
    //        //AnimateRangeAsync((value) =>
    //        //{
    //        //    Opacity = value;
    //        //}, 0.0, 1.0, 500, Easing.Linear, cancellationTokenSource.Token);
    //    }
    //    else
    //    {
    //        Opacity = 1;
    //    }

    //    base.Draw(context, destination, scale);
    //}




}