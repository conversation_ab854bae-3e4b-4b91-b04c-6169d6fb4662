﻿global using AppoMobi.Maui.Infrastructure.Controls.Navigation.Tabs;
global using AppoMobi.Maui.Infrastructure.Interfaces;
global using AppoMobi.Specials.Abstractions;
global using AppoMobi.Specials.Helpers;
global using CommunityToolkit.Maui;
global using Racebox.Enums;
global using Racebox.Helpers;
global using Racebox.Models;
global using Racebox.Services;
global using Racebox.Shared.Interfaces;
global using Racebox.Shared.Models;
global using Racebox.ViewModels;
global using Racebox.Views;
using AppoMobi.Maui.BLE;
using AppoMobi.Maui.Infrastructure;
using AppoMobi.Maui.Infrastructure.Extensions;
using Mapster;
using MapsterMapper;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.LifecycleEvents;
using Racebox.SDK;
using Racebox.Shared.Services;
using Racebox.ViewModels.Navigation;
using IPreferences = AppoMobi.Maui.Infrastructure.Interfaces.IPreferences;
using Preferences = Racebox.Services.Preferences;
using ResourceDictionary = Microsoft.Maui.Controls.ResourceDictionary;


#if WINDOWS

using static Microsoft.Maui.LifecycleEvents.WindowsLifecycle;
using Microsoft.UI.Xaml;
using Application = Microsoft.UI.Xaml.Application;

#endif

namespace Racebox;


public static class MauiProgram
{
	public static MauiApp CreateMauiApp()
	{


		//RaceBoxProtocol.Test();

		var builder = MauiApp.CreateBuilder();

		builder
			.UseMauiApp<App>()

			.ConfigureFonts(fonts =>
			{
				fonts.AddFont("FordAntennaWGL-Regular.otf", "FontText");
				fonts.AddFont("FordAntennaWGL-Light.otf", "FontTextLight");
				fonts.AddFont("FordAntennaWGL-Medium.otf", "FontTextBold");
				fonts.AddFont("fa-regular-400.ttf", "Fa");
				fonts.AddFont("fa-solid-900.ttf", "FaSolid");
			});

#if DEBUG
		builder.Logging.AddDebug();
#endif

		//EXTERNAL LIBS
		builder
			.UseBlootoothLE()
			.UseAppoMobiFramework(typeof(App))
			.UseMauiCommunityToolkit();

		//MAPSTER
		builder.Services.AddSingleton(GetConfiguredMappingConfig()); //todo optimize
		builder.Services.AddScoped<IMapper, ServiceMapper>();

		//APP INFRASTRUCTURE
		builder.Services.AddSingleton<IPreferences, Preferences>();
		builder.Services.AddSingleton<IUIAssistant, UIAssistant>();
		builder.Services.AddSingleton<IInAppMessager, Messager>();
		builder.Services.AddSingleton<NavigationViewModel>();

		//BUSINESS LOGIC
		builder.Services.AddSingleton<UserManager>();
		builder.Services.AddSingleton<RaceBoxConnector>();
		builder.Services.AddSingleton<RaceBoxDeviceViewModel>();
		builder.Services.AddSingleton<RaceBoxStateProcessor>();

		//SCREENS
		builder.Services.AddTransient<DevicesPageViewModel>();
		builder.Services.AddTransient<HistoryViewModel>();
		builder.Services.AddTransient<SettingsViewModel>();
		builder.Services.AddTransient<ResultDisplayViewModel>();

		//DATABASE
		//можно сменить имя если надо сбросить все данные для новой версии
		//настройки программы НЕ хранятся в бд, только замеры!
		//бд медленее, чем хранилище IAppPreferences, куда можно писать в т.ч. json
		builder.Services.AddTransient<LocalDatabase>((services) =>
		{
			return new LocalDatabase(Path.Combine(FileSystem.AppDataDirectory, "SQLite002.db3"));
		});



		builder.ConfigureLifecycleEvents(AppLifecycle =>
		{

#if WINDOWS

			AppLifecycle.AddEvent<OnLaunched>("OnLaunched", (Application application, LaunchActivatedEventArgs args) =>
			{
				Super.Screen.Density = Microsoft.Maui.Devices.DeviceDisplay.Current.MainDisplayInfo.Density;
			});

#endif

		});

#if IOS
		builder.ConfigureMauiHandlers(collection => collection
			.AddHandler(typeof(ScrollView), typeof(Racebox.Platforms.iOS.FixedScrollViewHandler))



		);
#endif


#if MACCATALYST

Microsoft.Maui.Handlers.WindowHandler.Mapper.AppendToMapping(nameof(IWindow),
            (handler, view) =>
            {

                 var size = new CoreGraphics.CGSize(600, 900);
                 handler.PlatformView.WindowScene.SizeRestrictions.MinimumSize = size;
                 handler.PlatformView.WindowScene.SizeRestrictions.MaximumSize = size;
    });

#endif


		var mauiApp = builder.Build();

		Super.Services = mauiApp.Services;

#if WINDOWS

		//preload System.Net.Security
		var check = new HttpClient();

		//preload Microsoft.CSharp
		try
		{
			var check1 = new ResourceDictionary().Get<string>("SvgStatusOk");
		}
		catch (Exception e)
		{
		}

#endif



		return mauiApp;
	}



	/// <summary>
	/// Mapster(Mapper) global configuration settings
	/// To learn more about Mapster,
	/// see https://github.com/MapsterMapper/Mapster
	/// </summary>
	/// <returns></returns>
	private static TypeAdapterConfig GetConfiguredMappingConfig()
	{
		var config = TypeAdapterConfig.GlobalSettings;

		// Поскольку нам важна скорость запуска мы не используем конфиги
		// мапера в отдельных класса с IRegister а все прописываем в одном
		var registers = new List<IRegister>() { new MapperConfiguration() }; //config.Scan(Assembly.GetExecutingAssembly());

		config.Apply(registers);

		return config;
	}



}


