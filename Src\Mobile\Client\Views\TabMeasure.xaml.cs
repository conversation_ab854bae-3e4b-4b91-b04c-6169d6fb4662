using AppoMobi.Maui.Navigation;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using AppoMobi.Framework.Maui.Controls.Navigation.Tabs;

namespace Racebox.Views;

public partial class TabMeasure : ILazyTab
{
	private readonly RaceBoxDeviceViewModel _viewModel;

	public bool IsDisposed { get; }
	public TabMeasure()
	{
		try
		{
			BindingContext = _viewModel = App.Instance.Services.GetService<RaceBoxDeviceViewModel>();

			InitializeComponent();

			//BindableLayout.SetItemsSource(StackResults, _viewModel.MeasureResults);

		}
		catch (Exception e)
		{
			Designer.DisplayException(this, e);
		}
	}

	protected override void OnPropertyChanged([CallerMemberName] string propertyName = null)
	{
		base.OnPropertyChanged(propertyName);

		if (propertyName == nameof(IsVisible))
		{
			if (IsVisible)
			{
				Debugger.Break();
			}
		}

	}

	protected override void OnSizeAllocated(double width, double height)
	{
		base.OnSizeAllocated(width, height);
	}

	//can bind this to touch effect local tap handler

	//public TouchActionEventHandler ButtonTappedHandler
	//{
	//    get
	//    {
	//        return OnTapped_Button;
	//    }
	//}

	//public void OnTapped_Button(object sender, TouchActionEventArgs args)
	//{
	//    if (sender is DrawnView control)
	//    {
	//        BtnBackground.PlayRippleAnimation(control, Colors.White, args.Location.X, args.Location.Y);
	//    }
	//}


	public void Dispose()
	{
		//todo
	}

	public void UpdateControls(DeviceRotation orientation)
	{

	}


	public void OnViewAppearing()
	{
		if (!_viewModel.Initialized)
		{
			Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(3000), async () =>
			{
				_viewModel.Init();


				return false;
			});
		}

#if IOS

        Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(50), async () =>
        {

            Super.SetWhiteTextStatusBar();

            return false;// Don't repeat the timer 
        });

#endif

	}

	public void OnViewDisappearing()
	{

	}

	public void KeyboardResized(double size)
	{

	}
}