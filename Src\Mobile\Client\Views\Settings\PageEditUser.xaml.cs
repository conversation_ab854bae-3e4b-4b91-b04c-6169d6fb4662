

namespace Racebox.Views;

public partial class PageEditUser : ILazyPage
{
    private readonly EditUserViewModel _vm;

    public PageEditUser(LocalUser item, Func<LocalUser, Task<int>> callback)
    {
        BindingContext = _vm = new EditUserViewModel(item, callback);

        InitializeComponent();
    }

    protected override void OnAppearing()
    {
        base.OnAppearing();

        //Settings.UpdateStatusBarUponTheme();

        var canUpdate = BindingContext as IUpdateUIState;
        canUpdate?.UpdateState(true);
    }

    public void OnViewAppearing()
    {
        OnAppearing();
    }

    public void OnViewDisappearing()
    {
        OnDisappearing();
    }

    public void UpdateControls(DeviceRotation orientation)
    {

    }


    //public TouchActionEventHandler ButtonTappedHandler
    //{
    //    get
    //    {
    //        return OnTapped_Button;
    //    }
    //}

    //public void OnTapped_Button(object sender, TouchActionEventArgs args)
    //{
    //    if (sender is DrawnView control)
    //    {
    //        BtnBackground.PlayRippleAnimation(Colors.White, args.Location.X / Super.Screen.Density, args.Location.Y / Super.Screen.Density);
    //    }
    //}

}