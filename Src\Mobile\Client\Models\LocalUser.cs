﻿using Racebox.Shared.Models;
using System.ComponentModel;
using System.Runtime.CompilerServices;


namespace Racebox.Models;

/// <summary>
/// For saving locally
/// </summary>
public class LocalUser : IFromDto
{
	public string Key { get; set; } = Guid.NewGuid().ToString();
	public int Id { get; set; }
	public string Name { get; set; }
	public UserOptionsMeta Options { get; set; } = new();
	public List<LocalCar> Cars { get; set; } = new();


	[Newtonsoft.Json.JsonIgnore]
	[System.Text.Json.Serialization.JsonIgnore]
	public LocalCar SelectedCar
	{
		get
		{
			try
			{
				var found = Cars.FirstOrDefault(x => x.Id == Options.CarId);
				if (found == null)
				{
					return Cars.First();
				}
				return found;
			}
			catch { }
			return null;
		}
	}

	#region IFromDto

	public void Init()
	{
		//put any code you'd want to exec after dto's been imported
		// for example to fill any new prop with data derived from what you received 

	}

	public void RaiseProperties()
	{
		var props = this.GetType().GetProperties();
		foreach (var property in props)
		{
			if (property.CanRead)
			{
				OnPropertyChanged(property.Name);
			}
		}
	}

	public event PropertyChangedEventHandler PropertyChanged;
	protected void OnPropertyChanged([CallerMemberName] string propertyName = "")
	{
		PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
	}

	#endregion

}