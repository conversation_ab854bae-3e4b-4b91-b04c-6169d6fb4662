﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" DefaultTargets="Build">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{97198cf5-aa3f-4b8d-a56d-571327dfbe3a}</ProjectGuid>
    <ProjectHome />
    <StartupFile>gen.py</StartupFile>
    <SearchPath />
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <ProjectTypeGuids>{888888a0-9f3d-457c-b088-3a5042f75d52}</ProjectTypeGuids>
    <LaunchProvider>Standard Python launcher</LaunchProvider>
    <InterpreterId />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'" />
  <PropertyGroup Condition="'$(Configuration)' == 'Release'" />
  <PropertyGroup>
    <VisualStudioVersion Condition=" '$(VisualStudioVersion)' == '' ">10.0</VisualStudioVersion>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="gen.py" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Logs\" />
    <Folder Include="Results\" />
    <Folder Include="Results\3\" />
    <Folder Include="Results\3\Best\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Logs\Log_0_100_0.csv" />
    <Content Include="Logs\Log_0_150.csv" />
    <Content Include="Logs\Log_0_150_0.csv" />
    <Content Include="Logs\Log_0_150_0_incline.csv" />
    <Content Include="Logs\Log_0_150_100.csv" />
    <Content Include="Logs\Log_0_60.csv" />
    <Content Include="Logs\Log_70_150.csv" />
    <Content Include="Logs\Log_Aircraft.csv" />
    <Content Include="Logs\Log_City.csv" />
    <Content Include="Logs\Log_Laps_1.csv" />
    <Content Include="Logs\Log_Laps_2.csv" />
    <Content Include="Logs\Log_No_GPS.csv" />
    <Content Include="Results\3\AccelLog_2022-10-27_11-47-32.csv" />
    <Content Include="Results\3\AccelRep_2022-10-27_11-47-32.html" />
    <Content Include="Results\3\Accel_Results_0.csv" />
    <Content Include="Results\3\Best\Best_Dist_1000m.html" />
    <Content Include="Results\3\Best\Best_Dist_201m.html" />
    <Content Include="Results\3\Best\Best_Dist_402m.html" />
    <Content Include="Results\3\Best\Best_Dist_60ft.html" />
    <Content Include="Results\3\Best\Best_Spd_100kmh.html" />
    <Content Include="Results\3\Best\Best_Spd_150kmh.html" />
    <Content Include="Results\3\Best\Best_Spd_60kmh.html" />
    <Content Include="run_nmea.cmd" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Python Tools\Microsoft.PythonTools.targets" />
</Project>