﻿using Newtonsoft.Json.Serialization;
using Racebox.ApiClient.Interfaces;

namespace Racebox.ApiClient;

public class ContextResolver : CamelCasePropertyNamesContractResolver
{

    IInjectContext context;

    public ContextResolver(IInjectContext context)
    {
        this.context = context;
    }

    protected override JsonObjectContract CreateObjectContract(Type objectType)
    {
        try
        {
            JsonObjectContract contract = base.CreateObjectContract(objectType);

            var stop = true;

            contract.DefaultCreator = () =>
            {
                object instance = null;
                try
                {
                    instance = Activator.CreateInstance(objectType, context);
                }
                catch (Exception e)
                {
                    instance = Activator.CreateInstance(objectType);
                }

                return instance;
            };

            return contract;
        }
        catch (Exception e)
        {
            Console.WriteLine($"[ContextResolver] Cannot create contract for {objectType.Name}");
            Console.WriteLine(e);
            throw;
        }


        //if (diMeta.IsRegistred(objectType))
        //{
        //    JsonObjectContract contract = DIResolveContract(objectType);
        //    contract.DefaultCreator = () => sp.GetService(objectType);

        //    return contract;
        //}

        //return base.CreateObjectContract(objectType);
    }

}