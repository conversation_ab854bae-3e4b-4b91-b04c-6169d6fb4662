﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="Racebox.UI.Dev.PageDev"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:Racebox.Views.Controls"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:drawn="clr-namespace:Racebox.Views.Drawn"
    xmlns:partials="clr-namespace:Racebox.Views.Partials"
    xmlns:strings="clr-namespace:Racebox.Shared.Strings;assembly=Racebox.Shared"
    xmlns:touch="clr-namespace:AppoMobi.Maui.Gestures;assembly=AppoMobi.Maui.Gestures"
    xmlns:viewModels="clr-namespace:Racebox.ViewModels"
    x:DataType="viewModels:DevPageViewModel">

    <ContentPage.Content>

        <Grid
            Margin="32"
            BackgroundColor="Black"
            HorizontalOptions="FillAndExpand"
            RowDefinitions="50*,50*"
            VerticalOptions="FillAndExpand">

            <draw:Canvas
                BackgroundColor="#343C45"
                Gestures="Enabled"
                
                RenderingMode="Accelerated"
                HeightRequest="-1"
                HorizontalOptions="Fill"
                IsVisible="False"
                VerticalOptions="Fill">


                <!--  absolute layout wrapper  -->
                <draw:SkiaLayout
                    BackgroundColor="Black"
                    HorizontalOptions="Fill"
                    AddMarginBottom="32"
                    VerticalOptions="Start">

                    <!--<draw:ZoomContent
                            PanSpeed="1.75"
                            ZoomSpeed="1.5"
                            ZoomMax="1000"
                            ZoomMin="1">

                            <draw:SkiaLayout

                                HorizontalOptions="Fill"
                                VerticalOptions="Fill">

                                <draw:SkiaImage
                                    Aspect="AspectFit"
                                    HorizontalOptions="Fill"
                                    Source="https://upload.wikimedia.org/wikipedia/commons/1/16/HDRI_Sample_Scene_Balls_%28JPEG-HDR%29.jpg"
                                    VerticalOptions="Fill"/>

                            </draw:SkiaLayout>

                        </draw:ZoomContent>-->

                    <!--  TEST WHEEL  -->
                    <draw:SkiaLayout
                        x:Name="Form"
                        Padding="16"
                        HorizontalOptions="Fill"
                        Spacing="16"
                        Type="Column">

                        <draw:SkiaLabel
                            Style="{StaticResource SkiaEditorFieldTitle}"
                            Text="{x:Static strings:ResStrings.Length}" />

                        <draw:SkiaLayout
                            BackgroundColor="#33000000"
                            HorizontalOptions="Fill">

                            <drawn:WheelPicker
                                x:Name="Picker"
                                DataSource="{Binding EditDistance.ItemsList}"
                                HorizontalOptions="Center"
                                SelectedIndex="{Binding EditDistance.SelectedIndex}">
                            </drawn:WheelPicker>

                        </draw:SkiaLayout>

                        <drawn:SmallButton
                            Margin="0,16"
                            HorizontalOptions="Center"
                            Text="{x:Static strings:ResStrings.BtnSave}" />

                    </draw:SkiaLayout>




                    <!--  GRID TEST  -->
                    <!--<draw:SkiaDecoratedGrid
                    HeightRequest=250
                    x:Name="TestGrid"
                    ColumnDefinitions="33*,33*,33*"
                    RowDefinitions="33*,33*,33*"
                    Type="Grid"
                    VerticalOptions="Fill">

                    <draw:SkiaShape
                        Grid.Row="0"
                        Grid.Column="0"
                        BackgroundColor="Red"
                        HorizontalOptions="Fill"
                        VerticalOptions="Fill"
                        ZIndex="-1" />

                    <draw:SkiaShape
                        Grid.Row="1"
                        Grid.Column="1"
                        BackgroundColor="Green"
                        HorizontalOptions="Fill"
                        VerticalOptions="Fill" />

                    <draw:SkiaShape
                        Grid.Row="2"
                        Grid.Column="2"
                        BackgroundColor="Blue"
                        HorizontalOptions="Fill"
                        VerticalOptions="Fill" />

                    <draw:SkiaLabel
                        x:Name="TestChild"
                        Grid.Row="0"
                        Grid.Column="0"
                        Grid.ColumnSpan="2"
                        AutoSize="FillHorizontal"
                        FontSize="24"
                        HorizontalOptions="Center"
                        MonoForDigits="8"
                        Text="Hello 12345678"
                        TextColor="White"
                        VerticalOptions="Center" />

                </draw:SkiaDecoratedGrid>-->



                    <!--  FPS  -->
                    <draw:SkiaLabelFps
                        Margin="0,0,4,84"
                        BackgroundColor="DarkRed"
                        ForceRefresh="False"
                        HorizontalOptions="End"
                        Rotation="-45"
                        TextColor="White"
                        VerticalOptions="End" />

                </draw:SkiaLayout>

            </draw:Canvas>

            <draw:Canvas
                x:Name="Container"
                BackgroundColor="#343C45"
                Gestures="Enabled"
                RenderingMode="Accelerated"
                HeightRequest="44"
                HorizontalOptions="Fill"
                Tag="ConnectionBar"
                VerticalOptions="Center">

                <partials:SkiaSignalContainer
                    x:Name="BarContainer"
                    Padding="4,6"
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill">

                    <!--  col 0 - Satellites  -->
                    <draw:SkiaLayout
                        Margin="6,0,0,0"
                        Tag="Bugged"
                        UseCache="Operations"
                        VerticalOptions="Fill"
                        WidthRequest="51">

                        <draw:SkiaLabel
                            FontSize="16"
                            HorizontalOptions="End"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Text="{Binding DisplaySatellites}"
                            VerticalOptions="Center" />


                        <draw:SkiaSvg
                            HeightRequest="24"
                            HorizontalOptions="Start"
                            SvgString="{StaticResource SvgSatellitesSign}"
                            Tag="Sats"
                            TintColor="#E8E3D7"
                            TranslationY="-1"
                            VerticalOptions="Center"
                            WidthRequest="24" />

                    </draw:SkiaLayout>

                    <!--  LINE VERTICAL  -->
                    <draw:SkiaShape
                        BackgroundColor="Black"
                        CornerRadius="0"
                        HorizontalOptions="Start"
                        StrokeWidth="0"
                        TranslationX="64"
                        UseCache="Operations"
                        VerticalOptions="Fill"
                        WidthRequest="1.1">
                        <draw:SkiaShape.FillGradient>

                            <draw:SkiaGradient
                                EndXRatio="0"
                                EndYRatio="1"
                                StartXRatio="0"
                                StartYRatio="0"
                                Type="Linear">
                                <draw:SkiaGradient.Colors>
                                    <Color>#00E8E3D7</Color>
                                    <Color>#78E8E3D7</Color>
                                    <Color>#78E8E3D7</Color>
                                    <Color>#00E8E3D7</Color>
                                </draw:SkiaGradient.Colors>
                                <draw:SkiaGradient.ColorPositions>
                                    <x:Double>0.0</x:Double>
                                    <x:Double>0.2</x:Double>
                                    <x:Double>0.8</x:Double>
                                    <x:Double>1.0</x:Double>
                                </draw:SkiaGradient.ColorPositions>
                            </draw:SkiaGradient>

                        </draw:SkiaShape.FillGradient>
                    </draw:SkiaShape>

                    <draw:SkiaLayout
                        Margin="65,0,0,0"
                        ColumnDefinitions="*, Auto, Auto"
                        ColumnSpacing="0"
                        HorizontalOptions="Fill"
                        Type="Grid"
                        VerticalOptions="Fill">

                        <!--  col 0 - SignalStrength  -->
                        <draw:SkiaLayout
                            x:Name="SkiaSignal"
                            HorizontalOptions="Fill"
                            Tag="SignalBar"
                            VerticalOptions="Fill">

                            <!--<draw:SkiaLabel
                                FontFamily="FontText"
                                FontSize="11"
                                HorizontalOptions="Center"
                                MaxLines="1"
                                Text="{x:Static strings:ResStrings.SignalStrength}"
                                TextColor="#E8E3D7"
                                TranslationY="-5"
                                VerticalOptions="Center" />-->

                            <!--  SIGNAL LINE  -->

                            <!--  TRACK BAR  -->
                            <draw:SkiaShape
                                Margin="8"
                                BackgroundColor="#15191E"
                                CornerRadius="3"
                                HeightRequest="6"
                                HorizontalOptions="Fill"
                                StrokeColor="Black"
                                StrokeWidth="1.0"
                                Tag="ShapeTrackSignal"
                                TranslationY="8"
                                VerticalOptions="Center">

                                <draw:SkiaShape.FillGradient>

                                    <draw:SkiaGradient
                                        EndXRatio="1"
                                        EndYRatio="0"
                                        Opacity="0.60"
                                        StartXRatio="0"
                                        StartYRatio="0"
                                        Type="Linear">

                                        <draw:SkiaGradient.Colors>
                                            <Color>#bbD8281D</Color>
                                            <Color>#bbF0E70B</Color>
                                            <Color>#bb65D210</Color>
                                            <Color>#bb65D210</Color>
                                        </draw:SkiaGradient.Colors>

                                        <draw:SkiaGradient.ColorPositions>
                                            <x:Double>0.0</x:Double>
                                            <x:Double>0.25</x:Double>
                                            <x:Double>0.50</x:Double>
                                            <x:Double>1.0</x:Double>
                                        </draw:SkiaGradient.ColorPositions>

                                    </draw:SkiaGradient>

                                </draw:SkiaShape.FillGradient>

                                <draw:SkiaShape.Shadows>

                                    <draw:SkiaShadow
                                        Blur="1.5"
                                        Opacity="0.15"
                                        X="2.5"
                                        Y="2.5"
                                        Color="#FFFFFF" />

                                    <draw:SkiaShadow
                                        Blur="1.5"
                                        Opacity="0.49"
                                        X="-2.5"
                                        Y="-2.5"
                                        Color="#000000" />

                                </draw:SkiaShape.Shadows>

                                <draw:SkiaShape.StrokeGradient>

                                    <draw:SkiaGradient
                                        EndXRatio="0.2"
                                        EndYRatio="0.8"
                                        StartXRatio="0.2"
                                        StartYRatio="0.2"
                                        Type="Linear">
                                        <draw:SkiaGradient.Colors>
                                            <Color>#15191E</Color>
                                            <Color>#42464B</Color>
                                        </draw:SkiaGradient.Colors>
                                    </draw:SkiaGradient>

                                </draw:SkiaShape.StrokeGradient>

                                <!--  INVERTED PLUS <=  -->
                                <!--
                                    "{Binding Source={x:Reference ThisControlBar},
                                    Path=Value}"
                                    ColorPositions="{Binding Source={x:Reference ThisControlBar}, Path=Points}"
                                -->
                                <partials:SignalInverter
                                    x:Name="Inverter"
                                    BackgroundColor="Black"
                                    HorizontalOptions="End"
                                    VerticalOptions="Fill"
                                    ZIndex="100"
                                    Value="0.5">

                                    <draw:SkiaControl.FillGradient>
                                        <draw:SkiaGradient
                                            ColorPositions="{Binding Source={x:Reference Inverter}, Path=Points}"
                                            EndXRatio="1"
                                            EndYRatio="0"
                                            Opacity="1"
                                            StartXRatio="0"
                                            StartYRatio="0"
                                            Type="Linear">
                                            <draw:SkiaGradient.Colors>
                                                <Color>#0015191E</Color>
                                                <Color>#15191E</Color>
                                                <Color>#15191E</Color>
                                            </draw:SkiaGradient.Colors>

                                            <!--<draw:SkiaGradient.ColorPositions>
                                 <x:Double>0.0</x:Double>
                                 <x:Double>0.05</x:Double>
                                 <x:Double>1.0</x:Double>
                             </draw:SkiaGradient.ColorPositions>-->

                                        </draw:SkiaGradient>
                                    </draw:SkiaControl.FillGradient>


                                </partials:SignalInverter>

                            </draw:SkiaShape>

                        </draw:SkiaLayout>

                        <!--  col 1 - Power Icon  -->
                        <draw:SkiaLayout
                            Grid.Column="1"
                            IsVisible="{Binding HasBattery}"
                            Tag="Power"
                            VerticalOptions="Fill"
                            WidthRequest="41">

                            <!--  LINE VERTICAL  -->
                            <draw:SkiaShape
                                BackgroundColor="Black"
                                CornerRadius="0"
                                StrokeWidth="0"
                                UseCache="Operations"
                                VerticalOptions="Fill"
                                WidthRequest="1.1">
                                <draw:SkiaShape.FillGradient>

                                    <draw:SkiaGradient
                                        EndXRatio="0"
                                        EndYRatio="1"
                                        StartXRatio="0"
                                        StartYRatio="0"
                                        Type="Linear">
                                        <draw:SkiaGradient.Colors>
                                            <Color>#00E8E3D7</Color>
                                            <Color>#78E8E3D7</Color>
                                            <Color>#78E8E3D7</Color>
                                            <Color>#00E8E3D7</Color>
                                        </draw:SkiaGradient.Colors>
                                        <draw:SkiaGradient.ColorPositions>
                                            <x:Double>0.0</x:Double>
                                            <x:Double>0.2</x:Double>
                                            <x:Double>0.8</x:Double>
                                            <x:Double>1.0</x:Double>
                                        </draw:SkiaGradient.ColorPositions>
                                    </draw:SkiaGradient>

                                </draw:SkiaShape.FillGradient>
                            </draw:SkiaShape>

                            <draw:SkiaSvg
                                x:Name="IconPower"
                                BackgroundColor="Transparent"
                                HeightRequest="24"
                                HorizontalOptions="Center"
                                AddMarginLeft="1"
                                SvgString="{StaticResource SvgBattery}"
                                Tag="IconPower"
                                TintColor="#E8E3D7"
                                UseCache="Operations"
                                VerticalOptions="Center"
                                WidthRequest="27" />

                            <!--  BATTERY LEVEL  -->
                            <draw:SkiaShape
                                BackgroundColor="#65D210"
                                HeightRequest="16"
                                HeightRequestRatio="0.5"
                                HorizontalOptions="Center"
                                AddMarginBottom="7"
                                VerticalOptions="End"
                                WidthRequest="6" />

                            <draw:SkiaHotspot
                                CommandTapped="{Binding CommandSwitchConnect, Mode=OneTime}"
                                IsVisible="{Binding IsBusy, Converter={x:StaticResource NotConverter}}"
                                AddMarginLeft="1"
                                TransformView="{x:Reference BarContainer}" />

                        </draw:SkiaLayout>

                        <!--  col 2 - BLE Icon  -->
                        <draw:SkiaLayout
                            Grid.Column="2"
                            BackgroundColor="Transparent"
                            Tag="BT"
                            VerticalOptions="Fill"
                            WidthRequest="41">

                            <!--  LINE VERTICAL  -->
                            <draw:SkiaShape
                                BackgroundColor="Black"
                                CornerRadius="0"
                                StrokeWidth="0"
                                UseCache="Operations"
                                VerticalOptions="Fill"
                                WidthRequest="1.1">
                                <draw:SkiaShape.FillGradient>

                                    <draw:SkiaGradient
                                        EndXRatio="0"
                                        EndYRatio="1"
                                        StartXRatio="0"
                                        StartYRatio="0"
                                        Type="Linear">
                                        <draw:SkiaGradient.Colors>
                                            <Color>#00E8E3D7</Color>
                                            <Color>#78E8E3D7</Color>
                                            <Color>#78E8E3D7</Color>
                                            <Color>#00E8E3D7</Color>
                                        </draw:SkiaGradient.Colors>
                                        <draw:SkiaGradient.ColorPositions>
                                            <x:Double>0.0</x:Double>
                                            <x:Double>0.2</x:Double>
                                            <x:Double>0.8</x:Double>
                                            <x:Double>1.0</x:Double>
                                        </draw:SkiaGradient.ColorPositions>
                                    </draw:SkiaGradient>

                                </draw:SkiaShape.FillGradient>
                            </draw:SkiaShape>

                            <draw:SkiaSvg
                                x:Name="IconBT"
                                BackgroundColor="Transparent"
                                HeightRequest="27"
                                HorizontalOptions="Center"
                                AddMarginLeft="1"
                                SvgString="{StaticResource SvgBleSign}"
                                Tag="IconBT"
                                TintColor="Gray"
                                UseCache="Operations"
                                VerticalOptions="Center"
                                WidthRequest="27" />

                            <draw:SkiaHotspot
                                CommandTapped="{Binding CommandSwitchConnect, Mode=OneTime}"
                                IsVisible="{Binding IsBusy, Converter={x:StaticResource NotConverter}}"
                                AddMarginLeft="1"
                                TransformView="{x:Reference BarContainer}" />

                        </draw:SkiaLayout>

                    </draw:SkiaLayout>




                </partials:SkiaSignalContainer>

            </draw:Canvas>

            <Button
                Grid.Row="1"
                Clicked="TappedButton"
                HeightRequest="40"
                Text="Debug"
                WidthRequest="150" />


        </Grid>
    </ContentPage.Content>

</ContentPage>