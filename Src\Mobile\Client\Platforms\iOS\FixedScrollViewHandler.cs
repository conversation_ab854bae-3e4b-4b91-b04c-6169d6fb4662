﻿using Microsoft.Maui.Handlers;
using Microsoft.Maui.Platform;
using UIKit;
using ContentView = Microsoft.Maui.Platform.ContentView;

namespace Racebox.Platforms.iOS
{
    public class ContentPageRenderer : PageHandler
    {
        protected override ContentView CreatePlatformView()
        {
            //Microsoft.Maui.Handlers.PageHandler.PlatformViewFactory

            return new CustomContentView();  //base.CreatePlatformView();
        }

        public class CustomContentView : ContentView
        {

        }

    }

    public class FixedScrollViewHandler : ScrollViewHandler
    {
        private static bool Initialized;

        protected override UIScrollView CreatePlatformView()
        {
            if (!Initialized)
            {
                //fixed BUG https://github.com/dotnet/maui/issues/9209
                Microsoft.Maui.Handlers.ScrollViewHandler.Mapper.AppendToMapping("ContentSize", (handler, view) =>
                {
                    try
                    {
                        // native UIScrollView.contentSize != maui ScrollView.ContentSize.. Why?..
                        handler.PlatformView.UpdateContentSize(handler.VirtualView.ContentSize);
                        PlatformArrange(handler.PlatformView.Frame.ToRectangle());
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex);
                    }
                });
                Initialized = true;
            }

            var view = base.CreatePlatformView();

            view.ShowsVerticalScrollIndicator = false;
            view.ShowsHorizontalScrollIndicator = false;

            return view;
        }
    }
}
