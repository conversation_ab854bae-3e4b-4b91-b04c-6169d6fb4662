# Racebox Chart API Integration Feature

## Overview
To integrate the Racebox mobile app with a remote chart generation API to automatically create visual analytics from measurement data. 
Implementation approach: send data to server, receive generated chart image, save locally, and display in UI.

## API Specification

### Endpoint
- **URL**: `https://chart.racebox.cc:5000`
- **Method**: POST
- **SSL Verification**: Disabled (verify=False)
- **Response Time**: ~2 seconds

### Request Format
The API expects a multipart form with two fields:

1. **file**: Binary CSV log data
2. **metadata**: JSON metadata as application/json

### Request Data Structure

#### CSV Log Format
The CSV file contains time-series measurement data with semicolon separators:
```csv
Total Time (s);Lat;Lon;Speed (km/h);LonAccel (G);Distance (m);Alt (m);Incline (%);Course (deg);HDOP;Sats
0.18;56.7916267;60.5735093;2.5;0.06;1.9;268.8;0.0;296;0.7;15
```

#### Metadata JSON Structure
```json
{
    "name": "AccelLog_2021-09-04_134010.csv",
    "size": 44147,
    "type": "accel",
    "meta": {
        "lang": "ru",
        "user": "User Name",
        "vehicle": "BMW X1 F48",
        "descr": "Stock",
        "share": 0,
        "date": "04/09/2024 13:40:09"
    },
    "spd": {
        "0-60": "3.86",
        "0-100": "7.44",
        "0-150": "14.87"
    },
    "dst": {
        "60 ft": ["3.03", "48"],
        "201 m": ["10.33", "122"],
        "402 m": ["15.63", "154"]
    },
    "rng": {},
    "shift": {}
}
```

### Metadata Fields Description

#### Root Level
- `name`: CSV filename (used in returned image filename)
- `size`: CSV file size in bytes
- `type`: Measurement type ("accel")

#### Meta Object
- `lang`: Language for chart rendering ("ru"/"en")
- `user`: User name for display
- `vehicle`: Vehicle name/model
- `descr`: Vehicle description
- `share`: Telegram group sharing flag (always 0 for mobile app)
- `date`: Measurement date in "DD/MM/YYYY hh:mm:ss" format

#### Performance Metrics
- `spd`: Speed metrics dictionary (0-60, 0-100, 0-150, etc.) with acceleration times
- `dst`: Distance metrics dictionary (60 ft, 201 m, 402 m, etc.) with [time, speed] arrays
- `rng`: Speed range metrics dictionary (100-200, etc.) with acceleration times
- `shift`: Not used in mobile app

### Response Format

#### Success (HTTP 200)
- **Content-Type**: image/png
- **Content-Disposition**: Contains filename for generated chart
- **Body**: PNG image data

#### Error (HTTP 400)
- **Content-Type**: application/json
- **Body**: `{"error": "Error description"}`

## Current App Integration Points

### Existing Export System
The app already has a robust export system in `Racebox.Shared.Services.Exporter`:

- **CSV Generation**: `WriteCsv()` method creates semicolon-separated CSV data
- **Filename Generation**: `GenerateLogFileName()` creates standardized filenames
- **Data Models**: `MeasureResult` contains all necessary data including:
  - `MeasuredLogLine` collection for time-series data
  - `MeasuredDistance` and `MeasuredRange` for performance metrics
  - User and vehicle information

### Data Mapping Strategy

#### CSV Data Generation
Use existing `Exporter.WriteCsv()` method with current `MeasureResult` data.

**Note**: Current CSV format has different column order than API example:
- **Current**: `Total Time (s);Lat;Lon;Speed (km/h);Distance (m);LatAccel (G);LonAccel (G);Alt (m);Incline (%);Course (deg);HDOP;Sats`
- **API Expected**: `Total Time (s);Lat;Lon;Speed (km/h);LonAccel (G);Distance (m);Alt (m);Incline (%);Course (deg);HDOP;Sats`

**Action Required**: Verify CSV column order compatibility or adjust export format.

#### Metadata Generation
Create new service method to generate metadata JSON from `MeasureResult`:

```csharp
public static object CreateChartMetadata(MeasureResult result, string csvFilename, long csvSize)
{
    return new
    {
        name = csvFilename,
        size = csvSize,
        type = "accel",
        meta = new
        {
            lang = GetCurrentLanguage(), // "ru" or "en"
            user = result.AppUser?.Name ?? "User",
            vehicle = GetVehicleName(result.CarId),
            descr = GetVehicleDescription(result.CarId),
            share = 0,
            date = result.CreatedTimeUtc.ToString("dd/MM/yyyy HH:mm:ss")
        },
        spd = CreateSpeedMetrics(result),
        dst = CreateDistanceMetrics(result),
        rng = CreateRangeMetrics(result),
        shift = new {}
    };
}
```

## Implementation Plan

### Phase 1: Core API Client
1. Create `ChartApiService` class for API communication
2. Implement multipart form data posting with HttpClient
3. Add response handling (success/error scenarios)
4. Implement local image storage

### Phase 2: Data Preparation
1. Extend `Exporter` class with metadata generation
2. Create performance metrics extraction methods
3. Implement CSV/metadata validation
4. Add language and vehicle information resolution

### Phase 3: UI Integration
1. Add "Generate Chart" button to result view pages
2. Implement loading indicator during API call
3. Create chart image display component
4. Add error handling and user feedback

### Phase 4: Storage & Caching
1. Implement local chart image storage
2. Add chart availability indicators
3. Implement offline chart viewing
4. Add chart regeneration functionality

## Technical Considerations

### Security
- API uses HTTPS but requires SSL verification bypass
- Consider implementing certificate pinning for production

### Performance
- 2-second API response time requires loading indicators
- Implement background processing to avoid UI blocking
- Consider caching mechanisms for generated charts

### Error Handling
- Network connectivity issues
- API service unavailability
- Invalid data format errors
- File storage permissions

### Localization
- Support "ru" and "en" languages for chart generation
- Ensure date format consistency
- Localize user-facing error messages

## File Structure Changes

### New Files
- `Services/ChartApiService.cs` - API communication
- `Models/ChartMetadata.cs` - Metadata model classes
- `Views/Partials/ChartImageView.xaml` - Chart display component

### Modified Files
- `Services/Exporter.cs` - Add metadata generation
- `ViewModels/History/ResultDisplayViewModel.cs` - Add chart generation command
- `Views/PageHistoryResult.xaml` - Add chart UI elements

## Success Criteria
1. Successfully generate and display charts for measurement results
2. Proper error handling for API failures
3. Local storage and offline viewing of generated charts
4. Smooth user experience with appropriate loading states
5. Support for both Russian and English chart languages

---

## ✅ Implementation Progress & Testing Results

### Phase 1: API Testing (COMPLETED ✅)

**TestApi Project Created**: `C:\Dev\Cases\GitHub\Racebox\Src\Mobile\TestApi\TestApi\`

#### Test Results Summary
- **Date**: 2025-08-18
- **API Endpoint**: `https://chart.racebox.cc:5000` ✅ Working
- **Response Time**: 3.30 seconds
- **Generated Chart**: 341,737 bytes PNG image
- **Sample Chart Saved**: `C:\Dev\Cases\GitHub\Racebox\generated_chart_sample.png`

#### Technical Validation
✅ **HttpClient with MultipartFormDataContent** - Works perfectly  
✅ **SSL Certificate Bypass** - Required and functional  
✅ **CSV Data Format** - API accepts existing export format  
✅ **JSON Metadata** - Sample metadata structure validated  
✅ **Image Response Handling** - PNG download and save successful  
✅ **Filename Extraction** - Content-Disposition header parsing works  

#### Key Code Implementation (TestApi)
```csharp
// SSL bypass for API
using var handler = new HttpClientHandler()
{
    ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
};

// Multipart form with CSV + JSON metadata
using var form = new MultipartFormDataContent();
var csvContent = new ByteArrayContent(csvBytes);
csvContent.Headers.ContentType = MediaTypeHeaderValue.Parse("text/csv");
form.Add(csvContent, "file", "racebox_log.csv");

var jsonContent = new StringContent(metadataJson, Encoding.UTF8, "application/json");
form.Add(jsonContent, "metadata");

var response = await httpClient.PostAsync(ApiUrl, form);
```

#### Sample API Response
- **Status**: HTTP 200 OK
- **Content-Type**: image/png
- **Content-Disposition**: `filename="AccelLog_2021-09-04_134010.png"`
- **Chart Generated**: Professional acceleration/speed graph with metrics overlay

### Next Steps: Mobile App Integration

**Proven Technology Stack**:
- .NET 9 HttpClient ✅
- MultipartFormDataContent ✅  
- Newtonsoft.Json ✅
- SSL bypass handling ✅

**Ready for Integration**:
1. Copy HttpClient code to mobile app service layer
2. Connect with existing `Exporter.WriteCsv()` functionality  
3. Create metadata generation from `MeasureResult` objects
4. Add UI components for chart display and generation
5. Implement local chart storage and caching

**Confidence Level**: HIGH - API integration fully validated and working

### Phase 2: Service Implementation (COMPLETED ✅)

**ChartApiService Created**: Robust, production-ready service with comprehensive error handling

#### Service Features
✅ **Cancellation Token Support** - Can cancel long-running requests  
✅ **Comprehensive Error Handling** - Returns structured results instead of throwing exceptions  
✅ **Input Validation** - Validates CSV data, JSON metadata, and output directory  
✅ **Proper Resource Management** - Implements IDisposable for HttpClient cleanup  
✅ **SSL Certificate Bypass** - Handles API's SSL requirements automatically  
✅ **Structured Result Model** - ChartApiResult with success/error information  

#### Service API
```csharp
public class ChartApiService : IDisposable
{
    public async Task<ChartApiResult> GenerateChartAsync(
        byte[] csvData, 
        string metadataJson, 
        string outputDirectory,
        CancellationToken cancellationToken = default)
}

public class ChartApiResult
{
    public bool IsSuccess { get; }
    public string? ErrorMessage { get; }
    public string? FilePath { get; }
    public string? Filename { get; }
    public long FileSize { get; }
    public TimeSpan? ResponseTime { get; }
}
```

#### Error Handling Scenarios
- **Network Issues**: "Network error: {details}"
- **Request Timeout**: "Request timed out"  
- **Request Cancellation**: "Request was cancelled"
- **Invalid Inputs**: "CSV data is null or empty", "Invalid metadata JSON"
- **API Errors**: "API Error (400): {server error message}"
- **Unexpected Errors**: "Unexpected error: {exception message}"

#### Usage Example
```csharp
using var chartService = new ChartApiService();
using var cancellationTokenSource = new CancellationTokenSource();

var result = await chartService.GenerateChartAsync(
    csvData: csvBytes,
    metadataJson: jsonString,
    outputDirectory: outputPath,
    cancellationToken: cancellationTokenSource.Token
);

if (result.IsSuccess)
{
    Console.WriteLine($"Chart saved: {result.FilePath}");
    Console.WriteLine($"File size: {result.FileSize:N0} bytes");
    Console.WriteLine($"Response time: {result.ResponseTime?.TotalSeconds:F2}s");
}
else
{
    Console.WriteLine($"Error: {result.ErrorMessage}");
}
```

### Manual Testing Instructions

To run the TestApi manually and generate charts:

#### Prerequisites
- .NET 9 SDK installed
- Internet connection for API access

#### Steps to Run Test
```bash
# Navigate to TestApi project directory
cd "C:\Dev\Cases\GitHub\Racebox\Src\Mobile\TestApi\TestApi"

# Build and run the test
dotnet run
```

#### Expected Output
```
🏁 Racebox Chart API Test Client
================================
📡 Testing Chart API with Service...
📄 Loading CSV: [path to CSV]
📄 Loading Metadata: [path to metadata]
📊 CSV Size: 44,147 bytes
📊 Metadata: 669 characters
🚀 Calling Chart API Service...
✅ Chart generated successfully!
📁 Filename: AccelLog_2021-09-04_134010.png
🖼️  File Size: 341,737 bytes
⏱️  Response Time: 2.82 seconds
💾 Chart saved to: C:\Dev\Cases\GitHub\Racebox\Src\Mobile\TestApi\TestApi\GeneratedCharts\AccelLog_2021-09-04_134010.png
📂 Relative path: GeneratedCharts\AccelLog_2021-09-04_134010.png
🎉 Test completed successfully!
```

#### Generated Files
- **Chart Image**: `GeneratedCharts\AccelLog_2021-09-04_134010.png` 
- **Filename Format**: Same as server filename (no timestamp)
- **File Overwrites**: Files with same name will be overwritten

#### Test Data Location
- **CSV Data**: `TestData\racebox_log.csv` (44KB sample acceleration log)
- **Metadata**: `TestData\racebox_metadata.json` (BMW X1 F48 sample metadata)

#### Troubleshooting
- **Network Issues**: Ensure internet connection and API server availability
- **SSL Errors**: Code includes SSL bypass - should work automatically  
- **File Not Found**: Ensure TestData files are copied to output directory
- **Timeout**: Default timeout is 1 minute - increase if needed

#### Customizing Test Data
Replace files in `TestData\` directory:
- `racebox_log.csv` - Your CSV measurement data
- `racebox_metadata.json` - Your metadata with vehicle/user info

The test will automatically use your custom data for chart generation.

#### File Organization
- **Generated Charts Location**: `GeneratedCharts\` folder within TestApi project
- **Filename Format**: Uses exact server filename (no timestamp modifications)
- **File Overwrites**: Charts with same name will overwrite previous versions
- **Easy Access**: Charts saved in organized project directory structure

#### Project Structure
```
TestApi/
├── ChartApiService.cs          # Production-ready service
├── Program.cs                  # Test console application  
├── TestData/
│   ├── racebox_log.csv        # Sample CSV data
│   └── racebox_metadata.json  # Sample metadata (legacy)
└── GeneratedCharts/
    └── AccelLog_2025-08-19_092443.png  # Generated chart
```

### Phase 3: Enhanced Data Processing (COMPLETED ✅)

**CSV to JSON Generation**: Dynamic metadata generation from CSV data instead of static JSON files

#### Key Enhancements
✅ **Dynamic Metadata Generation** - Metadata is now calculated from CSV data, not loaded from static files  
✅ **Performance Metrics Calculation** - Speed, distance, and range metrics extracted from CSV data  
✅ **Mobile App Identification** - Added `data_source: "app"` field to identify requests from mobile app  
✅ **Timestamp-based Filenames** - Generated filenames use current timestamp format  
✅ **Robust CSV Parsing** - Handles various CSV formats with proper error handling  

#### Enhanced Implementation Features

**CSV Data Processing**:
```csharp
// Analyzes CSV data to extract performance metrics
var performanceMetrics = CalculatePerformanceMetrics(dataLines);

// Generates dynamic metadata with mobile app identification
var metadata = new {
    name = $"AccelLog_{timestamp}.csv",
    size = fileSize,
    type = "accel",
    data_source = "app", // Mobile app identifier
    meta = { /* dynamic user/vehicle data */ },
    spd = calculatedSpeedMetrics,
    dst = calculatedDistanceMetrics,
    rng = calculatedRangeMetrics
};
```

**Performance Metrics Calculation**:
- **Speed Achievements**: 0-60, 0-100, 0-150, 0-200 km/h times
- **Distance Achievements**: 60 ft, 201m, 402m with time and speed
- **Speed Range**: 100-200 km/h acceleration time
- **Error Handling**: Graceful fallback for invalid or missing data

**Mobile App Integration Benefits**:
- **No Static Dependencies**: No need for pre-generated JSON files
- **Real-time Processing**: Metadata generated from actual measurement data
- **API Server Recognition**: `data_source: "app"` allows server-side mobile app handling
- **Flexible Data**: Adapts to different CSV formats and data quality

#### Testing Results
- **CSV Size**: 44,814 bytes processed successfully
- **Generated Metadata**: 645 characters with complete performance metrics
- **API Response**: 2.69 seconds with 350,793 bytes PNG image
- **Dynamic Filename**: `AccelLog_2025-08-19_092443.png` with timestamp
- **Data Source Field**: Successfully included `data_source: "app"`

#### Implementation Status
**Ready for Mobile App Integration**: 
1. ✅ CSV data processing from existing `MeasureResult` objects
2. ✅ Dynamic JSON metadata generation with performance metrics
3. ✅ Mobile app identification via `data_source` field
4. ✅ Comprehensive error handling and validation
5. ✅ Production-ready ChartApiService with cancellation support

**Next Integration Steps**:
1. Copy enhanced logic to mobile app service layer
2. Connect with existing `Exporter.WriteCsv()` functionality
3. Add UI components for chart generation and display
4. Implement local chart storage and caching

### Phase 4: Production-Ready Unit Testing (COMPLETED ✅)

**Professional Test Suite**: Converted console application to comprehensive unit testing project

#### Architecture Improvements
✅ **Proper Unit Test Project** - Converted from console app to MSTest project with proper test dependencies  
✅ **Service Layer Separation** - Extracted business logic into dedicated service classes with interfaces  
✅ **Cross-Platform Compatibility** - Services designed for .NET, Android (Xamarin/MAUI), and iOS (Xamarin/MAUI)  
✅ **Comprehensive Test Coverage** - 23 unit tests covering all scenarios including error conditions  
✅ **Integration Testing** - Real API testing with proper fallback handling  

#### Project Structure (Refactored)
```
TestApi/
├── Services/
│   ├── IChartApiService.cs           # Service interface for DI
│   ├── ChartApiService.cs            # Cross-platform HTTP service
│   ├── ICsvMetadataGenerator.cs      # Metadata generator interface
│   └── CsvMetadataGenerator.cs       # CSV parsing and metrics calculation
├── Models/
│   ├── ChartApiResult.cs            # API response model
│   └── ChartMetadata.cs             # Metadata models with performance metrics
├── Tests/
│   ├── ChartApiServiceTests.cs       # HTTP service unit tests (13 tests)
│   ├── CsvMetadataGeneratorTests.cs  # CSV processing unit tests (10 tests)
│   └── ChartApiIntegrationTests.cs   # End-to-end integration tests (3 tests)
├── TestData/
│   └── racebox_log.csv              # Real test data for integration tests
└── Program.cs                       # Legacy console runner
```

#### Cross-Platform Service Features

**IChartApiService Implementation**:
- ✅ Async/await pattern for non-blocking operations
- ✅ CancellationToken support for request cancellation
- ✅ Comprehensive error handling with structured results
- ✅ SSL certificate bypass handling (configurable for production)
- ✅ Cross-platform file system operations
- ✅ Proper resource disposal (IDisposable)

**ICsvMetadataGenerator Implementation**:
- ✅ Culture-invariant number parsing (cross-platform safe)
- ✅ Robust CSV parsing with error handling
- ✅ Performance metrics calculation from raw data
- ✅ Configurable metadata fields (user, vehicle, language)
- ✅ Mobile app identification via `data_source: "app"`

#### Test Coverage Summary
**Unit Tests (23 total)**:
- **ChartApiService**: Input validation, error handling, cancellation, disposal
- **CsvMetadataGenerator**: CSV parsing, performance calculations, edge cases
- **Integration**: End-to-end workflow with real data and API calls

**Test Results**:
```
Test Run Successful.
Total tests: 23
     Passed: 23
 Total time: 1.08 Minutes
```

#### Mobile App Integration Benefits
1. **Type Safety**: Strong-typed models and interfaces prevent runtime errors
2. **Testability**: All components are unit tested and mockable via interfaces
3. **Error Handling**: Structured error responses instead of exceptions
4. **Platform Independence**: No platform-specific dependencies
5. **Async Operations**: Non-blocking network operations suitable for mobile UI
6. **Cancellation**: User can cancel long-running operations
7. **Performance**: Efficient CSV parsing and metadata generation

#### Command Usage
```bash
# Run all unit tests
dotnet test

# Run with detailed output
dotnet test --logger "console;verbosity=normal"

# Run legacy console test
dotnet run
```

**Production Readiness**: The service layer is now ready for integration into Xamarin.Forms, .NET MAUI, or any .NET mobile application with full test coverage and cross-platform compatibility.

---

## 📱 Mobile App Integration Guide

### Step-by-Step Integration Instructions

This section provides detailed steps to integrate the Chart API services into the Racebox mobile application. Follow these steps in order and update this guide as the service evolves.

#### Step 1: Reference the Shared Project

1. **Add Project Reference** in your mobile project:
   ```xml
   <ItemGroup>
     <ProjectReference Include="..\Shared\Racebox.Shared.csproj" />
   </ItemGroup>
   ```

2. **The Chart API services are already available** in the Shared project at:
   - `Racebox.Shared.Services.ChartApiService` - Main chart generation service
   - `Racebox.Shared.ChartApi.ChartApiExtensions` - Service registration extensions
   - `Racebox.Shared.ChartApi.Models.ChartApiResult` - API response models
   - `Racebox.Shared.ChartApi.Dto.ChartMetadata` - Metadata models

3. **No file copying required** - the services are already integrated into the shared project structure

#### Step 2: Register Services for Dependency Injection

In your `MauiProgram.cs` or equivalent service configuration:

```csharp
using Racebox.Shared.ChartApi;

public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        
        // Other configuration...
        
        // Add Chart API service (includes HTTP client configuration)
        builder.Services.AddChartApi();
        
        return builder.Build();
    }
}
```

**Note**: `AddChartApi()` automatically registers:
- `ChartApiService` as singleton
- HTTP client factory with proper SSL configuration for chart.racebox.cc
- All required dependencies

#### Step 3: Use Chart Generation in Your Services

The `ChartApiService` works directly with `MeasureResult` objects. Here's how to use it:

```csharp
using Racebox.Shared.Services;
using Racebox.Shared.ChartApi.Models;

public class SomeService
{
    private readonly ChartApiService _chartService;
    
    public SomeService(ChartApiService chartService)
    {
        _chartService = chartService;
    }
    
    public async Task<ChartApiResult> GenerateChartAsync(
        MeasureResult measureResult, 
        string outputDirectory,
        CancellationToken cancellationToken = default)
    {
        // The service handles everything automatically:
        // - Generates CSV from MeasureResult.Logs using existing Exporter methods
        // - Creates metadata with performance metrics from MeasureResult.Distances/Ranges
        // - Calls the chart API
        // - Returns structured result with file path
        
        return await _chartService.GenerateChartAsync(measureResult, outputDirectory, cancellationToken);
    }
}
```

**Key Benefits:**
- **No CSV generation needed** - service uses existing `Exporter.CreateCsvHeader()` and `ToCsvRow()` methods
- **Automatic metadata** - extracts performance metrics from `MeasureResult.Distances` and `MeasureResult.Ranges`
- **Proper error handling** - returns structured `ChartApiResult` implementing `IResult<ChartResponse>`

#### Step 4: Add Chart Generation to ViewModels

In your `ResultDisplayViewModel` or equivalent:

```csharp
using Racebox.Shared.Services;
using Racebox.Shared.ChartApi.Models;

public class ResultDisplayViewModel : ViewModelBase
{
    private readonly ChartApiService _chartService;
    private ChartApiResult? _lastChartResult;
    private bool _isGeneratingChart;
    private CancellationTokenSource? _chartCancellationSource;

    public ResultDisplayViewModel(ChartApiService chartService)
    {
        _chartService = chartService;
        GenerateChartCommand = new AsyncCommand(GenerateChartAsync, () => !_isGeneratingChart);
        CancelChartCommand = new Command(CancelChart, () => _isGeneratingChart);
    }

    public IAsyncCommand GenerateChartCommand { get; }
    public ICommand CancelChartCommand { get; }
    
    public bool IsGeneratingChart
    {
        get => _isGeneratingChart;
        set => SetProperty(ref _isGeneratingChart, value);
    }

    public string? ChartImagePath => _lastChartResult?.Data?.FilePath;
    public bool HasChart => _lastChartResult?.IsSuccess == true;

    private async Task GenerateChartAsync()
    {
        if (CurrentResult == null) return;

        try
        {
            IsGeneratingChart = true;
            _chartCancellationSource = new CancellationTokenSource();
            
            // Define output directory (app's Documents folder)
            var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            var chartsDirectory = Path.Combine(documentsPath, "RaceboxCharts");
            
            // Generate chart directly from MeasureResult
            _lastChartResult = await _chartService.GenerateChartAsync(
                CurrentResult, 
                chartsDirectory, 
                _chartCancellationSource.Token);

            if (_lastChartResult.IsSuccess)
            {
                // Show success message
                await App.Current.MainPage.DisplayAlert(
                    "Chart Generated", 
                    $"Chart saved successfully!\nFile: {_lastChartResult.Data.Filename}\nSize: {_lastChartResult.Data.FileSize:N0} bytes", 
                    "OK");
                
                OnPropertyChanged(nameof(ChartImagePath));
                OnPropertyChanged(nameof(HasChart));
            }
            else
            {
                // Show error message
                await App.Current.MainPage.DisplayAlert(
                    "Chart Generation Failed", 
                    _lastChartResult.ErrorMessage, 
                    "OK");
            }
        }
        catch (Exception ex)
        {
            await App.Current.MainPage.DisplayAlert("Error", $"Unexpected error: {ex.Message}", "OK");
        }
        finally
        {
            IsGeneratingChart = false;
            _chartCancellationSource?.Dispose();
            _chartCancellationSource = null;
        }
    }
    
    private void CancelChart()
    {
        _chartCancellationSource?.Cancel();
    }
}
```

#### Step 6: Update UI (XAML)

Add chart generation UI to your result display page:

```xml
<!-- In your PageHistoryResult.xaml or equivalent -->
<StackLayout>
    
    <!-- Your existing result UI -->
    
    <!-- Chart Generation Section -->
    <Frame BackgroundColor="LightBlue" Padding="10" Margin="5">
        <StackLayout>
            <Label Text="Performance Chart" FontSize="18" FontAttributes="Bold" />
            
            <!-- Generate Chart Button -->
            <Button Text="Generate Chart" 
                    Command="{Binding GenerateChartCommand}"
                    IsVisible="{Binding IsGeneratingChart, Converter={StaticResource InverseBooleanConverter}}"
                    BackgroundColor="Green" 
                    TextColor="White" />
                    
            <!-- Loading Indicator -->
            <StackLayout Orientation="Horizontal" 
                        IsVisible="{Binding IsGeneratingChart}">
                <ActivityIndicator IsRunning="True" Color="Blue" />
                <Label Text="Generating chart..." VerticalOptions="Center" />
                <Button Text="Cancel" 
                        Command="{Binding CancelChartCommand}" 
                        BackgroundColor="Red" 
                        TextColor="White" />
            </StackLayout>
            
            <!-- Chart Image Display -->
            <Frame IsVisible="{Binding HasChart}" 
                   BackgroundColor="White" 
                   Padding="5">
                <Image Source="{Binding ChartImagePath}" 
                       Aspect="AspectFit" 
                       HeightRequest="200" />
            </Frame>
            
        </StackLayout>
    </Frame>
    
</StackLayout>
```

#### Step 7: Handle Permissions (Android/iOS)

**Android** - Add to `Platforms/Android/AndroidManifest.xml`:
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

**iOS** - Add to `Platforms/iOS/Info.plist`:
```xml
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
</dict>
```

#### Step 8: Testing Integration

1. **Unit Test Integration** - Create tests for your enhanced Exporter:
   ```csharp
   [Test]
   public async Task GenerateChartAsync_ValidMeasureResult_ReturnsSuccess()
   {
       // Arrange
       var result = CreateTestMeasureResult();
       var exporter = new Exporter(_mockMetadataGenerator, _mockChartService);
       
       // Act
       var chartResult = await exporter.GenerateChartAsync(result, _testOutputPath);
       
       // Assert
       Assert.IsTrue(chartResult.IsSuccess);
   }
   ```

2. **Manual Testing Steps**:
   - Run app on device/emulator
   - Navigate to a measurement result
   - Tap "Generate Chart" button
   - Verify loading indicator shows
   - Confirm chart image displays when complete
   - Test cancellation functionality
   - Verify error handling with no internet

#### Step 9: Production Configuration

For production deployment, consider:

1. **SSL Certificate Handling** - Replace SSL bypass with proper certificate validation
2. **API Endpoint Configuration** - Make API URL configurable via app settings  
3. **Error Logging** - Integrate with your logging framework
4. **Offline Handling** - Cache charts and show offline status
5. **File Management** - Implement chart cleanup/rotation policies

### Integration Checklist

- [ ] Shared project referenced in mobile app
- [ ] Chart API service registered with `AddChartApi()`
- [ ] ViewModel updated with ChartApiService injection
- [ ] ViewModel updated with chart commands
- [ ] UI updated with chart generation controls
- [ ] Platform permissions configured
- [ ] Testing completed (unit + manual)
- [ ] Production configuration reviewed

### Current Status ✅

**Chart API Service Successfully Integrated into Mobile App!**

- **✅ Location**: `C:\Dev\Cases\GitHub\Racebox\Src\Mobile\Shared\`
- **✅ Structure**: Following WeatherService patterns with proper DI registration
- **✅ Testing**: 30/31 unit tests passing (1 timeout on real API integration test)
- **✅ Compatibility**: Full cross-platform support (.NET, Android, iOS, MAUI)
- **✅ Integration**: Uses existing Exporter CSV methods and MeasureResult models
- **✅ Mobile Implementation**: Successfully integrated into HistoryResultViewModel
- **✅ Build Status**: All compilation errors resolved - project builds successfully
- **✅ Service Registration**: Chart API service registered in MauiProgram.cs
- **✅ UI Integration**: Chart generation commands and UI state management implemented

### Common Integration Issues

**Issue**: "SSL certificate validation failed"
**Solution**: Ensure iOS Info.plist includes NSAppTransportSecurity settings

**Issue**: "Directory access denied" 
**Solution**: Use app-specific directories and check platform permissions

**Issue**: "JSON serialization error"
**Solution**: Verify all DateTime formatting uses InvariantCulture

**Issue**: "UI freezing during chart generation"
**Solution**: Ensure all service calls are properly awaited with async/await

---

*This integration guide will be updated whenever the service architecture changes. Last updated: Phase 4 - Production-Ready Unit Testing*