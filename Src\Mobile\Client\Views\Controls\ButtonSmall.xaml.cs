using DrawnUi;
using System.Windows.Input;

namespace Racebox.Views.Partials;

public partial class ButtonSmall : Canvas
{
    public ButtonSmall()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty CommandTappedProperty = BindableProperty.Create(nameof(CommandTapped), typeof(ICommand),
        typeof(ButtonSmall),
        null);
    public ICommand CommandTapped
    {
        get { return (ICommand)GetValue(CommandTappedProperty); }
        set { SetValue(CommandTappedProperty, value); }
    }

    public static readonly BindableProperty IsDisabledProperty = BindableProperty.Create(nameof(IsDisabled),
        typeof(bool),
        typeof(ButtonSmall),
        true);
    public bool IsDisabled
    {
        get { return (bool)GetValue(IsDisabledProperty); }
        set { SetValue(IsDisabledProperty, value); }
    }

    public static readonly BindableProperty TextProperty = BindableProperty.Create(
        nameof(Text), typeof(string), typeof(ButtonSmall),
        string.Empty);
    public string Text
    {
        get { return (string)GetValue(TextProperty); }
        set { SetValue(TextProperty, value); }
    }


}