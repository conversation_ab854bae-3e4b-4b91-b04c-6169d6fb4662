﻿using Racebox.Shared.Enums;

namespace Racebox.Shared.Models;

/// <summary>
/// User related options
/// </summary>
public class UserOptionsMeta
{
    public int CarId { get; set; }
    public List<LocalSpeedRange> SpeedRanges { get; set; } = new();
    public List<LocalDistance> Distances { get; set; } = new();

    public OptionsUnits Units { get; set; }

    public OptionsLogFormat LogFormat { get; set; }
    public bool Rollout { get; set; }

    public bool IsDemo { get; set; }
    public bool Sound { get; set; }
    public bool Time24 { get; set; }

    public int Version { get; set; }
    public bool CanSay { get; set; }
}