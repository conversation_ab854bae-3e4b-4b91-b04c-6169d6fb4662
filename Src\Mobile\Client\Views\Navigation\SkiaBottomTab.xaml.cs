using SkiaSharp;
using System.Runtime.CompilerServices;
using System.Windows.Input;

namespace Racebox.Views.Navigation;


public partial class SkiaBottomTab : IHasAfterEffects
{
    //can bind this to touch effect local tap handler




    public void OnTapped_Button(object sender, ControlTappedEventArgs controlTappedEventArgs)
    {
        ReportTap();
    }

    public SkiaBottomTab()
    {
        InitializeComponent();
        InitializeBase();
    }

    private bool _ShowThisNotifications;
    public bool ShowThisNotifications
    {
        get { return _ShowThisNotifications; }
        set
        {
            if (_ShowThisNotifications != value)
            {
                _ShowThisNotifications = value;
                OnPropertyChanged();
            }
        }
    }


    protected override void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        base.OnPropertyChanged(propertyName);


        if (propertyName == nameof(ShowNotifications)
            || propertyName == nameof(NotificationsCount))
        {
            if (!ShowNotifications || NotificationsCount < 1)
            {
                ShowThisNotifications = false;
                return;
            }
            ShowThisNotifications = true;
        }

        if (propertyName.SafeContainsInLower("scale")
            || propertyName.SafeContainsInLower("zoom")
            || propertyName == nameof(IconSize))
        {
            OnPropertyChanged("AdjustedSize");
        }


    }

    public double AdjustedSize
    {
        get
        {
            var overflow = Math.Max(IconScaleX * IconZoom, IconScaleY * IconZoom);
            return IconSize * overflow;

            if (overflow > 1.0)
                return IconSize * overflow;

            return IconSize;
        }
    }

    public ICommand CommandTapped
    {
        get
        {
            return new Command(
                (context) =>
                {
                    ReportTap();
                }
            );
        }
    }


    //public async Task PlayAfterEffect(IAfterEffect animation, uint lengthMs, Easing easing, bool removePrevious)
    //{
    //    if (Wrapper != null)
    //    {
    //        await Wrapper.PlayAfterEffect(animation, lengthMs, easing, removePrevious);
    //    }
    //}

    public void PlayRippleAnimation(Color color, double x, double y, bool removePrevious = true)
    {
        Wrapper?.PlayRippleAnimation(color, x, y, removePrevious);
    }

    public void PlayShimmerAnimation(Color color, float shimmerWidth, float shimmerAngle, int speedMs = 1000, bool removePrevious = false)
    {
        Wrapper?.PlayShimmerAnimation(color, shimmerWidth, shimmerAngle, speedMs, removePrevious);
    }

    public SKPoint GetOffsetInsideControlInPoints(PointF argsLocation, SKPoint childOffset)
    {
        return default;
    }


    private void OnDown(object sender, TouchActionEventArgs e)
    {
        //App.Instance.PlaySoundFile("Sound/glassmetal.mp3");
    }
}