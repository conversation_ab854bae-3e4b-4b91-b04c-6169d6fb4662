﻿using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Polly;
using Polly.Timeout;
using Racebox.ApiClient.Interfaces;
using Racebox.ApiClient.Models;
using System.Diagnostics;
using System.Net;
using System.Net.Http.Headers;
using System.Security.Authentication;
using System.Text;

namespace Racebox.ApiClient.Extensions;

public static class ApiExtensions
{

    public static IServiceCollection AddApiClient(this IServiceCollection services, string apiBase, string clientName)
    {
        services.AddTransient<HttpHandler>();

        var retryPolicy = Policy
            .HandleResult<HttpResponseMessage>(r =>
                r.StatusCode == HttpStatusCode.GatewayTimeout
                || r.StatusCode == HttpStatusCode.RequestTimeout)
            .Or<HttpRequestException>()
            .Or<TimeoutRejectedException>()
            .WaitAndRetryAsync(new[]
            {
                TimeSpan.FromSeconds(2),
                TimeSpan.FromSeconds(3),
            });

        services.AddHttpClient(clientName, client =>
            {
                client.BaseAddress = new Uri(apiBase);
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                client.DefaultRequestHeaders.Add("User-Agent", clientName);
            })
            .ConfigurePrimaryHttpMessageHandler(() =>
            {
                var handler = new HttpClientHandler();
                if (handler.SupportsAutomaticDecompression)
                {
                    handler.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip;
                }
                //sometimes it just bugs so we override ssl
                //#if DEBUG
                //                handler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) =>
                //                {
                //                    return true;
                //                };
                //                handler.ClientCertificateOptions = ClientCertificateOption.Manual;
                //#endif
                handler.SslProtocols = SslProtocols.Tls12;
                return handler;
            })
            .AddPolicyHandler(retryPolicy);

        return services;
    }

    public static JsonSerializerSettings DefaultSettingsSerialize
    {
        get
        {
            return new JsonSerializerSettings
            {
                // Ignore null values in the object being serialized
                NullValueHandling = NullValueHandling.Ignore,

                // If a JSON property isn't found on the object, don't throw an exception
                MissingMemberHandling = MissingMemberHandling.Ignore,

                // Use camel case for property names
                ContractResolver = new CamelCasePropertyNamesContractResolver(),

                // If a cycle is detected, output null
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,

                // Use the ISO 8601 format for date values
                DateFormatHandling = DateFormatHandling.IsoDateFormat,

                // Convert longs to string (useful for systems that can't handle big integers)
                Converters = new List<JsonConverter> { new Newtonsoft.Json.Converters.StringEnumConverter() },

                // Pretty-print the output
                Formatting = Formatting.Indented
            };
        }
    }

    public static async Task<IResult<T>> RequestAsync<T>(this HttpClient client, HttpMethod method,
        string url,
        object model,
        JsonSerializerSettings settings = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await client.SendRequestAsync(method, url, model, settings, cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var dto = await response.Content.ReadAsStringAsync();
                if (typeof(T) == typeof(object))
                {
                    return new Result<T>()
                    {
                        Data = (T)(object)dto,
                        Success = true,
                        Code = (int)response.StatusCode,
#if DEBUG
                        Meta = dto
#endif
                    };
                }

#if DEBUG
                if (HttpClientHelper.IsDebug)
                {
                    Debug.WriteLine("---------------------------------");
                    Debug.WriteLine(PrettyJson(dto));
                    Debug.WriteLine("---------------------------------");
                }
#endif

                var data = JsonConvert.DeserializeObject<T>(dto, settings);
                return new Result<T>()
                {
                    Success = true,
                    Data = data,
                    Code = (int)response.StatusCode,
#if DEBUG
                    Meta = dto
#endif
                };



            }
            else
            {
                return new Result<T>()
                {
                    Code = (int)response.StatusCode,
                    Errors = new()
                    {
                        $"{(int)response.StatusCode} {response.ReasonPhrase}"
                    }
                };
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            return new Result<T>()
            {
                Errors = new()
                {
                    e.Message
                }
            };
        }
    }

    public static string PrettyJson(string unPrettyJson)
    {
        try
        {
            var obj = Newtonsoft.Json.JsonConvert.DeserializeObject(unPrettyJson);
            var ret = Newtonsoft.Json.JsonConvert.SerializeObject(obj, Newtonsoft.Json.Formatting.Indented);
            return ret;
        }
        catch (Exception e)
        {
            return "UNABLE TO DESERIALIZE";
        }
    }


    public static async Task<IResult> RequestAsync(this HttpClient client, HttpMethod method,
        string url, object model,
        JsonSerializerSettings settings = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await client.SendRequestAsync(method, url, model, settings, cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                return new Result()
                {
                    Success = true,
                    Code = (int)response.StatusCode
                };
            }
            else
            {
                return new Result()
                {
                    Code = (int)response.StatusCode,
                    Errors = new()
                    {
                        $"{(int)response.StatusCode} {response.ReasonPhrase}"
                    }
                };
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            return new Result()
            {
                Errors = new()
                {
                    e.Message
                }
            };
        }
    }

    public static async Task<HttpResponseMessage> SendRequestAsync(this HttpClient client,
        HttpMethod method,
        string url,
        object model,
        JsonSerializerSettings settings,
        CancellationToken cancellationToken)
    {
        HttpResponseMessage response;
        if (model != null)
        {
            settings ??= DefaultSettingsSerialize;
            var json = JsonConvert.SerializeObject(model, settings);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            return await SendRequestAsync(client, method, url, content, cancellationToken);
        }

        var request = new HttpRequestMessage(method, url);
        response = await client.SendAsync(request);
        return response;
    }

    public static byte[] ToBytes(this Stream input)
    {
        using (MemoryStream ms = new MemoryStream())
        {
            input.CopyTo(ms);
            return ms.ToArray();
        }
    }

    public static async Task<byte[]> ToBytesAsync(this Stream input)
    {
        using (MemoryStream ms = new MemoryStream())
        {
            await input.CopyToAsync(ms);
            return ms.ToArray();
        }
    }

    //public static async Task<IResult> UploadData(byte[] data, string url, params KeyValuePair<string, string>[] headers)
    //{
    //    try
    //    {

    //    }
    //    catch (Exception e)
    //    {
    //        Console.WriteLine(e);
    //        throw;
    //    }
    //}

    public static async Task<HttpResponseMessage> SendRequestAsync(this HttpClient client,
        HttpMethod method,
        string url,
        HttpContent content,
        CancellationToken cancellationToken,
        Dictionary<string, string> headers = null)
    {
        HttpResponseMessage response;
        var request = new HttpRequestMessage(method, url);
        if (headers != null)
        {
            foreach (var header in headers)
            {
                request.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }
        }

        if (content != null)
        {
            request.Content = content;
        }

        var contentType = request.Content.Headers.ContentType;

        return await client.SendAsync(request, cancellationToken);
    }


}

