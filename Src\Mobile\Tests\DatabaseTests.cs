using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Racebox.Shared.Models;
using Racebox.Tests.Models;
using System;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Racebox.Tests;

public class DatabaseTests : TestsBase
{
	private readonly UserManager _usermanager;
	private LocalDatabase _context { get; }

	public DatabaseTests()
	{
		_context = TestHost.Services.GetService<LocalDatabase>();
		_usermanager = TestHost.Services.GetService<UserManager>();
	}

	async Task Seed()
	{
		await _usermanager.InsureUserIsInDatabase();
		var result = new MeasureResult()
		{
			AppUser = _usermanager.User,
			CarId = _usermanager.User.Cars.First().Id,
			Units = _usermanager.Options.Units,
			StartTime = DateTime.Now,
			CreatedTimeUtc = DateTime.Now,
			Lattitude = 20.1002,
			Longitude = 30.2001
		};
		result.Logs.Add(new()
		{
			Lattitude = 36.5566823,
			Longitude = 31.9680766,
			TotalSeconds = 0.36,
			Distance = 0.1,
			AccelerationLon = 0.06,
			Altitude = 0.15,
			Speed = 2.3,
			Heading = 117.5,
			SatellitesCount = 10,
		});
		result.Logs.Add(new()
		{
			Lattitude = 36.5566821,
			Longitude = 31.9680773,
			TotalSeconds = 0.42,
			Distance = 0.2,
			AccelerationLon = 0.07,
			Altitude = 0.18,
			Speed = 2.7,
			Heading = 113.8,
			SatellitesCount = 10
		});
		_usermanager.User.MeasureResults.Add(result);
		await _usermanager.UpdateUser(_context);
	}

	[Test]
	public async Task ExportCsv()
	{
		await Seed();

		var data = _context.Results.First();

		var filename = Exporter.GenerateLogFileName(data.CreatedTimeUtc.ToLocalTime(), "csv");
		var fullFilename = Path.Combine("../", filename);

		if (File.Exists(fullFilename))
		{
			File.Delete(fullFilename);
		}

		using (var handler = new FileStream(fullFilename, System.IO.FileMode.OpenOrCreate))
		using (StreamWriter s = new StreamWriter(handler, Encoding.UTF8))
		{
			Exporter.WriteCsv(s, data);
		}

		var fileText = File.ReadLines(fullFilename);
		foreach (var line in fileText)
		{
			TestContext.WriteLine($"{line}");
		}

		fileText.Should().HaveCountGreaterThan(1);
	}

	[Test]
	public async Task ExportVbo()
	{
		await Seed();

		var data = _context.Results.First();

		var filename = Exporter.GenerateLogFileName(data.CreatedTimeUtc.ToLocalTime(), "vbo");
		var fullFilename = Path.Combine("../", filename);

		if (File.Exists(fullFilename))
		{
			File.Delete(fullFilename);
		}

		using (var handler = new FileStream(fullFilename, System.IO.FileMode.OpenOrCreate))
		using (StreamWriter s = new StreamWriter(handler, Encoding.UTF8))
		{
			Exporter.WriteVbo(s, data);
		}

		var fileText = File.ReadLines(fullFilename);
		foreach (var line in fileText)
		{
			TestContext.WriteLine($"{line}");
		}

		fileText.Should().HaveCountGreaterThan(1);
	}

	[Test]
	public async Task UserIsAvailableInDb()
	{
		await _usermanager.InsureUserIsInDatabase();

		var check = await _context.Users.CountAsync();

		TestContext.WriteLine($"Check: {check}");

		check.Should().Be(1);

		//			TestContext.WriteLine($"Check: {Json(check)}");
	}

}