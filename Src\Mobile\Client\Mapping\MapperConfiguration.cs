﻿using Mapster;
using MapsterMapper;
using Racebox.Shared.Dto;
using Racebox.Shared.Extensions;
using Racebox.Shared.Interfaces;
using Racebox.Shared.Models;
using Racebox.Shared.Services;
using Racebox.Shared.Strings;

namespace Racebox;

/// <summary>
/// Поскольку нам важна скорость запуска мы не используем конфиги
/// мапера в отдельных класса с IRegister а все прописываем в одном
/// </summary>
public class MapperConfiguration : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        //IMPORT
        config.NewConfig<LocalUser, AppUser>()
            .IgnoreNullValues(true)
            .Ignore(dest => dest.Id)
            .Ignore(dest => dest.MeasureResults)
        .AfterMappingAsync(async (src, dest) =>
        {
            var newMapper = MapContext.Current.GetService<IMapper>();
            dest.Cars = await src.Cars.ProjectToListAsync<LocalCar, Car>(newMapper);
        }).Compile(); ;


        //EXPORT
        config.NewConfig<AppUser, LocalUser>()
            .IgnoreNullValues(true)
        .AfterMappingAsync(async (src, dest) =>
        {
            var newMapper = MapContext.Current.GetService<IMapper>();
            dest.Cars = await src.Cars.ProjectToListAsync<Car, LocalCar>(newMapper);
        }).Compile(); ;

        config.NewConfig<MeasureResult, MeasureResultDto>()
            .Map(dest => dest.Results,
                src => src.Ranges.Concat<IHasDisplayFromDb>(src.Distances)
                    .OrderBy(x => x.Id)
                    .Select(s => s.Display).ToList()).Compile(); ;

        config.NewConfig<MeasureResult, HistoryCellData>()
            .Map(dest => dest.Results,
                src => src.Ranges.OrderBy(x => x.Id)
                    .Concat<IHasDisplayFromDb>(src.Distances.OrderBy(x => x.Id))
                    .Select(s => s.Display).ToList())
                    .AfterMappingAsync(async (src, dest) =>
                    {
                        var culture = ResStrings.Culture;
                        //var separator = culture.NumberFormat.NumberDecimalSeparator;

                        var split = "\r\n"; //Environment.NewLine
                        var buildLines = string.Empty;
                        var nb = 0;
                        var db = MapContext.Current.GetService<LocalDatabase>();

                        //results
                        foreach (var line in dest.Results)
                        {
                            if (nb > 0)
                            {
                                buildLines += split;
                            }
                            buildLines += line;
                            nb++;
                            if (nb == 4)
                                break;
                        }
                        dest.DisplayResults = buildLines;

                        //info
                        var car = db.Cars.FirstOrDefault(x => x.Id == src.CarId);
                        if (car != null)
                        {
                            dest.CarTitle = $"{car.Brand} {car.Model}".Trim();
                            dest.DisplayInfo = $"{dest.CarTitle}{split}{src.CreatedTimeUtc.ToLocalTime().ToString("d", culture)}{split}{src.CreatedTimeUtc.ToLocalTime().ToString($"{LocalizedDisplayProvider.HoursFormat}:mm:ss", culture)}";
                            dest.IsValid = src.IsValid;
                        }
                        else
                        {
                            dest.CarTitle = $"{ResStrings.Car} {src.CarId}".Trim();
                            dest.DisplayInfo = $"{ResStrings.Error}{split}{ResStrings.Car} {src.CarId}";
                            dest.IsValid = false;
                        }
                    }).Compile(); ;

    }
}