<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaLayout
    x:Class="Racebox.Views.Partials.FormEditDistanceDrawn"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:drawn="clr-namespace:Racebox.Views.Drawn"
    xmlns:strings="clr-namespace:Racebox.Shared.Strings;assembly=Racebox.Shared"
    xmlns:viewModels="clr-namespace:Racebox.ViewModels"
    Padding="16"
    x:DataType="viewModels:EditDistanceViewModel"
    HorizontalOptions="Fill"
    VerticalOptions="Fill">

    <!--  vertical stack  -->
    <draw:SkiaScroll
        FrictionScrolled="0.5"
        HorizontalOptions="Fill"
        VerticalOptions="Fill">

        <draw:SkiaLayout
            x:Name="Form"
            Padding="16"
            HorizontalOptions="Fill"
            Spacing="16"
            UseCache="ImageComposite"
            Split="1"
            Type="Wrap">

            <!--  EDIT METRIC  -->
            <draw:SkiaLabel
                Style="{StaticResource SkiaEditorFieldTitle}"
                Text="{x:Static strings:ResStrings.Length}" />

            <!--  wheel inside darkened container  -->
            <draw:SkiaLayout
                BackgroundColor="#33000000"
                HorizontalOptions="Fill">

                <drawn:WheelPicker
                    x:Name="Picker"
                    DataSource="{Binding ItemsList}"
                    HorizontalOptions="Center"
                    SelectedIndex="{Binding SelectedIndex, Mode=TwoWay}" />

            </draw:SkiaLayout>

            <!--  EDIT UNITS  -->
            <draw:SkiaLabel
                Style="{StaticResource SkiaEditorFieldTitle}"
                Text="{x:Static strings:ResStrings.MeasuringUnits}" />

            <!--  Tapable label  -->
            <draw:SkiaLabel
                Padding="8,2"
                draw:AddGestures.CommandTapped="{Binding CommandSelectMetricsUnit}"
                BackgroundColor="#33000000"
                HorizontalOptions="Fill"
                Text="{Binding DisplayUnits}"
                UseCache="Operations" />

        

            <!--  SAVE BTN  -->
            <drawn:SmallButton
                Margin="16"
                CommandTapped="{Binding CommandSubmitForm, Mode=OneTime}"
                HorizontalOptions="Center"
                IsEnabled="{Binding CanSubmit}"
                Text="{x:Static strings:ResStrings.BtnSave}" />
            
            <!--  VALIDATION ERRORS  -->
            <draw:SkiaLayout
                Margin="32,0"
                HorizontalOptions="Fill"
                InputTransparent="True"
                Type="Column">

                <!--<draw:SkiaLabel
                InputTransparent="True"
                IsVisible="{Binding Source={RelativeSource Self}, Path=Text, Converter={StaticResource StringNotEmptyConverter}}"
                Style="{StaticResource SkiaValidationErrorLabelHover}"
                Text="{Binding FieldValidators, Converter={StaticResource ValidatorErrorMessageConverter}, ConverterParameter=start}" />-->

                <draw:SkiaLabel
                    InputTransparent="True"
                    IsVisible="{Binding Source={RelativeSource Self}, Path=Text, Converter={StaticResource StringNotEmptyConverter}}"
                    Style="{StaticResource SkiaValidationErrorLabelHover}"
                    Text="{Binding FieldValidators, Converter={StaticResource ValidatorErrorMessageConverter}, ConverterParameter=end}" />

            </draw:SkiaLayout>

        </draw:SkiaLayout>

    </draw:SkiaScroll>

    <!--  FPS  -->
    <draw:SkiaLabelFps
        Margin="0,0,4,24"
        BackgroundColor="DarkRed"
        ForceRefresh="False"
        HorizontalOptions="End"
        IsVisible="{Binding IsDebug}"
        Rotation="-45"
        TextColor="White"
        VerticalOptions="End" />

</draw:SkiaLayout>
