﻿<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools"
          android:versionCode="16001">
	<application
		android:allowBackup="true" android:supportsRtl="true"></application>
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.INTERNET" />

	<uses-feature android:name="android.hardware.bluetooth" android:required="false" tools:node="replace" />
	<uses-feature android:name="android.hardware.bluetooth_le" android:required="false" tools:node="replace" />

	<!-- Request legacy Bluetooth permissions on older devices. -->
	<uses-permission android:name="android.permission.BLUETOOTH"
	                 android:maxSdkVersion="30" />
	<uses-permission android:name="android.permission.BLUETOOTH_ADMIN"
	                 android:maxSdkVersion="30" />

	<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"
	                 android:maxSdkVersion="28" />

	<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"
	                 android:minSdkVersion="29" android:maxSdkVersion="30" />


	<!--<uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION"
	                 android:minSdkVersion="29" android:maxSdkVersion="30" />-->

	<!-- Needed only if your app looks for Bluetooth devices.
         If your app doesn't use Bluetooth scan results to derive physical
         location information, you can strongly assert that your app
         doesn't derive physical location. -->
	<!--WARNING: If you include neverForLocation in your android:usesPermissionFlags, 
		some BLE beacons are filtered from the scan results.-->
	<uses-permission android:name="android.permission.BLUETOOTH_SCAN"
	                 android:usesPermissionFlags="neverForLocation" />
	<!-- Needed only if your app communicates with already-paired Bluetooth
         devices. -->
	<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
	<!-- Needed only if your app makes the device discoverable to Bluetooth
         devices. -->
	<!--<uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />-->

	<!--FOR SAVING TMP LOGS BEFORE EXPORT-->
	<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
	<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>

</manifest>
