using System.Net.Http.Headers;
using System.Text;
using Newtonsoft.Json;
using TestApi.Models;

namespace TestApi.Services;

/// <summary>
/// Cross-platform service for generating charts via Racebox Chart API
/// Compatible with .NET, Android (Xamarin/MAUI), and iOS (Xamarin/MAUI)
/// </summary>
public class ChartApiService : IChartApiService
{
    private readonly HttpClient _httpClient;
    private readonly string _apiUrl;

    public ChartApiService(string apiUrl = "https://chart.racebox.cc:5000")
    {
        _apiUrl = apiUrl;
        
        // Create HTTP client with SSL bypass
        // Note: In production, consider proper certificate validation
        var handler = new HttpClientHandler()
        {
            ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
        };
        
        _httpClient = new HttpClient(handler)
        {
            Timeout = TimeSpan.FromSeconds(6)
        };
        
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "RaceboxMobileApp/1.0");
    }

    public async Task<ChartApiResult> GenerateChartAsync(
        byte[] csvData, 
        string metadataJson, 
        string outputDirectory,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate inputs
            if (csvData == null || csvData.Length == 0)
                return ChartApiResult.Error("CSV data is null or empty");
            
            if (string.IsNullOrWhiteSpace(metadataJson))
                return ChartApiResult.Error("Metadata JSON is null or empty");
            
            if (string.IsNullOrWhiteSpace(outputDirectory))
                return ChartApiResult.Error("Output directory is null or empty");

            // Validate JSON metadata
            try
            {
                JsonConvert.DeserializeObject(metadataJson);
            }
            catch (Exception ex)
            {
                return ChartApiResult.Error($"Invalid metadata JSON: {ex.Message}");
            }

            // Create output directory if it doesn't exist (cross-platform safe)
            if (!Directory.Exists(outputDirectory))
            {
                Directory.CreateDirectory(outputDirectory);
            }

            // Create multipart form content
            using var form = new MultipartFormDataContent();
            
            // Add CSV file
            var csvContent = new ByteArrayContent(csvData);
            csvContent.Headers.ContentType = MediaTypeHeaderValue.Parse("text/csv");
            form.Add(csvContent, "file", "racebox_log.csv");
            
            // Add JSON metadata
            var jsonContent = new StringContent(metadataJson, Encoding.UTF8, "application/json");
            form.Add(jsonContent, "metadata");

            // Send request to API
            var startTime = DateTime.Now;
            var response = await _httpClient.PostAsync(_apiUrl, form, cancellationToken);
            var elapsed = DateTime.Now - startTime;

            if (response.IsSuccessStatusCode)
            {
                return await HandleSuccessResponse(response, outputDirectory, elapsed);
            }
            else
            {
                return await HandleErrorResponse(response, elapsed);
            }
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            return ChartApiResult.Error("Request timed out");
        }
        catch (TaskCanceledException)
        {
            return ChartApiResult.Error("Request was cancelled");
        }
        catch (HttpRequestException ex)
        {
            return ChartApiResult.Error($"Network error: {ex.Message}");
        }
        catch (UnauthorizedAccessException ex)
        {
            return ChartApiResult.Error($"File access denied: {ex.Message}");
        }
        catch (DirectoryNotFoundException ex)
        {
            return ChartApiResult.Error($"Directory not found: {ex.Message}");
        }
        catch (Exception ex)
        {
            return ChartApiResult.Error($"Unexpected error: {ex.Message}");
        }
    }

    private async Task<ChartApiResult> HandleSuccessResponse(
        HttpResponseMessage response, 
        string outputDirectory, 
        TimeSpan elapsed)
    {
        // Get filename from Content-Disposition header
        string filename = "chart.png";
        if (response.Content.Headers.ContentDisposition?.FileName != null)
        {
            filename = response.Content.Headers.ContentDisposition.FileName.Trim('"');
        }

        // Read image data
        var imageBytes = await response.Content.ReadAsByteArrayAsync();
        
        // Save image with server filename (cross-platform path handling)
        var outputPath = Path.Combine(outputDirectory, filename);
        await File.WriteAllBytesAsync(outputPath, imageBytes);

        return ChartApiResult.Success(
            filePath: outputPath,
            filename: filename,
            fileSize: imageBytes.Length,
            responseTime: elapsed
        );
    }

    private async Task<ChartApiResult> HandleErrorResponse(HttpResponseMessage response, TimeSpan elapsed)
    {
        var errorContent = await response.Content.ReadAsStringAsync();
        
        try
        {
            var errorObj = JsonConvert.DeserializeObject<dynamic>(errorContent);
            if (errorObj?.error != null)
            {
                return ChartApiResult.Error($"API Error ({response.StatusCode}): {errorObj.error}", elapsed);
            }
        }
        catch
        {
            // JSON parsing failed, use raw content
        }

        return ChartApiResult.Error($"API Error ({response.StatusCode}): {errorContent}", elapsed);
    }

    public void Dispose()
    {
        _httpClient?.Dispose();
    }
}