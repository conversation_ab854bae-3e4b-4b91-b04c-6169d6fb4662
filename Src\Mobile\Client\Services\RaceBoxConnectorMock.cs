﻿using AppoMobi.Maui.BLE.Connector;
using AppoMobi.Maui.BLE.Enums;
using AppoMobi.Maui.BLE.EventArgs;
using Microsoft.Maui.Controls.Internals;
using Racebox.SDK;
using System.Collections.ObjectModel;

namespace Racebox.Services;

[Preserve(AllMembers = true)]
public class RaceBoxConnectorMock : BaseViewModel, IRaceBoxConnector
{
    public bool Initialized
    {
        get
        {
            return _initialized;
        }

        set
        {
            if (_initialized != value)
            {
                _initialized = value;
                OnPropertyChanged();
            }
        }
    }
    bool _initialized;

    public bool IsConnected
    {
        get
        {
            return _isConnected;
        }

        set
        {
            if (_isConnected != value)
            {
                _isConnected = value;
                OnPropertyChanged();
            }
        }
    }
    bool _isConnected;

    public string Status
    {
        get
        {
            return _status;
        }

        set
        {
            if (_status != value)
            {
                _status = value;
                OnPropertyChanged();
            }
        }
    }
    string _status;

    public string LastName
    {
        get
        {
            return _lastName;
        }

        set
        {
            if (_lastName != value)
            {
                _lastName = value;
                OnPropertyChanged();
            }
        }
    }
    string _lastName;

    public string LastSerial
    {
        get
        {
            return _lastSerial;
        }

        set
        {
            if (_lastSerial != value)
            {
                _lastSerial = value;
                OnPropertyChanged();
            }
        }
    }
    string _lastSerial;

    public string Serial
    {
        get
        {
            return _serial;
        }

        set
        {
            if (_serial != value)
            {
                _serial = value;
                OnPropertyChanged();
            }
        }
    }
    string _serial;

    public Task ConnectToSerialDevice(bool needThrow = false)
    {
        var device = FoundDevices.First();
        LastName = device.Device.Name;
        LastSerial = device.Id;
        Serial = device.Id;

        IsConnected = true;
        ConnectionChanged?.Invoke(this, EventArgs.Empty);

        DeviceConnectionChanged?.Invoke(this, true);

        return Task.CompletedTask;
    }

    public Task Disconnect()
    {
        IsConnected = false;

        ConnectionChanged?.Invoke(this, EventArgs.Empty);

        DeviceConnectionChanged?.Invoke(this, false);

        //StateChanged?.Invoke(this, new BluetoothStateChangedArgs(BluetoothState.On, BluetoothState.Off));
        return Task.CompletedTask;
    }

    public async Task<bool> ScanForCompatibleDevices()
    {
        StateChanged?.Invoke(this, new BluetoothStateChangedArgs(BluetoothState.Unknown, BluetoothState.On));

        await Task.Delay(1000);

        MainThread.BeginInvokeOnMainThread(() =>
        {
            FoundDevices.Clear();
            FoundDevices.Add(new BleDeviceViewModel
            {
                Id = "Racebox#Demo",
                Rssi = 100,
                State = ConnectionState.Connected,
                Description = "Mock Description",
                Title = "Racebox Simulator",
                Device = new(Guid.NewGuid(), "Racebox#Demo", 100)
            });
        });

        await Task.Delay(1000);

        return true;
    }

    public void Init(Page mainPage, string appTitile)
    {

    }

    public event EventHandler<BluetoothStateChangedArgs> StateChanged;
    public event EventHandler<bool> DeviceConnectionChanged;
    public event EventHandler ConnectionChanged;
    public event EventHandler<RaceBoxState> OnDecoded;
    public event EventHandler<RaceBoxExtendedState> OnDecodedExtended;
    public event EventHandler<RaceBoxSettingsState> OnDecodedSettings;

    public Task<bool> WriteSettings(RaceBoxSettingsState settings)
    {

        return Task.FromResult(true);
    }

    public Task<bool> RequestExtendedInfo()
    {
        return Task.FromResult(false);
    }

    public Task<bool> RequestSettings(Action<RaceBoxSettingsState> callback = null)
    {
        return Task.FromResult(true);
    }

    public Task<bool> RequestSettings()
    {
        return Task.FromResult(false);
    }

    public ObservableCollection<BleDeviceViewModel> FoundDevices { get; } = new();


}