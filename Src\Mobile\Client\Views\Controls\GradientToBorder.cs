using SkiaSharp;
using SkiaSharp.Views.Maui;

namespace Racebox.Views.Partials;

public class GradientToBorder : SkiaLayout
{
    public GradientToBorder()
    {
        _color1 = Color.Parse("#343C45");
        _color2 = Color.Parse("#11161D");

        Tag = "GradientBorder";
    }

    private SkiaShape _shapeGradient;

    void SetGradient(Color color)
    {
        if (_shapeGradient == null)
        {
            _shapeGradient = new SkiaShape()
            {
                Tag = "ThisGradient",
                ZIndex = -100,
                UseCache = SkiaCacheType.Image,
                BackgroundColor = Colors.Gold,
                HorizontalOptions = LayoutOptions.Fill,
                VerticalOptions = LayoutOptions.Fill,
            };
        }

        bool hasGradient = this.Views.Any(x => x == _shapeGradient);

        _shapeGradient.FillGradient = new()
        {
            EndXRatio = 0,
            EndYRatio = 1,
            StartXRatio = 0,
            StartYRatio = 0,
            Type = GradientType.Linear,
            ColorPositions = new double[] { 0.0, 1.0 },
            Colors = new List<Color>()
            {
                _color1, color
            }
        };

        if (!hasGradient)
        {
            AddSubView(_shapeGradient);
        }
    }

    private static void NeedRecalcColors(BindableObject bindable, object oldvalue, object newvalue)
    {
        var view = bindable as GradientToBorder;
        view?.RecalcColors();
    }

    private void RecalcColors()
    {
        var totalHeight = this.Height + MissingHeight;

        if (totalHeight != 0 && Height > 0)
        {
            var ratio = this.Height / totalHeight;

            if (ratio > 0)
            {
                var endColor = CalculateBorderColor(_color1.ToSKColor(), _color2.ToSKColor(), (float)ratio);

                SetGradient(endColor.ToMauiColor());
            }
        }
    }

    protected override void OnPropertyChanged(string propertyName = "")
    {
        base.OnPropertyChanged(propertyName);

        if (propertyName == nameof(Height))
        {
            RecalcColors();
        }
    }

    protected override void OnMeasured()
    {
        base.OnMeasured();

        RecalcColors();
    }

    public static readonly BindableProperty MissingHeightProperty = BindableProperty.Create(nameof(MissingHeight),
        typeof(double),
        typeof(GradientToBorder),
        0.0,
        propertyChanged: NeedRecalcColors);

    private readonly Color _color2;
    private readonly Color _color1;

    public double MissingHeight
    {
        get { return (double)GetValue(MissingHeightProperty); }
        set { SetValue(MissingHeightProperty, value); }
    }

    public static SKColor CalculateBorderColor(SKColor startColor, SKColor endColor, float ratio)
    {
        float r = startColor.Red + (endColor.Red - startColor.Red) * ratio;
        float g = startColor.Green + (endColor.Green - startColor.Green) * ratio;
        float b = startColor.Blue + (endColor.Blue - startColor.Blue) * ratio;
        float a = startColor.Alpha + (endColor.Alpha - startColor.Alpha) * ratio;
        return new SKColor((byte)r, (byte)g, (byte)b, (byte)a);
    }



}