﻿using Racebox.Helpers.Validation;
using Racebox.Shared.Strings;
using System.Windows.Input;

namespace Racebox.ViewModels
{
    public class EditCarViewModel : ProjectViewModel
    {
        public EditCarViewModel(LocalCar item, Func<LocalCar, Task<int>> callback)
        {

            _callback = callback;
            _item = item;

            ValidationInitialize();

            Brand = item.Brand;
            Model = item.Model;
            Description = item.Description;
        }

        public ICommand CommandSubmitForm => new Command(async (object context) =>
        {

            UpdateValidator();
            if (!CanSubmit)
            {
                return;
            }

            try
            {
                IsBusy = true;
                await Task.Delay(10); //update UI

                var outItem = new LocalCar()
                {
                    Id = _item.Id,
                    Brand = Brand.ToTitleCase(),
                    Model = Model.ToTitleCase(),
                    Description = Description
                };

                var errorCode = await _callback(outItem);

                if (errorCode > 0)
                {
                    //if (errorCode == 1)
                    //{
                    //    throw new ApplicationException(ResStrings.ErrorMetricsAlreadyExist);
                    //}
                    throw new ApplicationException(ResStrings.ErrorFailedToSaveRecord);
                }

                this.NavbarModel.CommandGoBack.Execute(null);

            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                App.Instance.UI.ShowToast($"{ResStrings.Error}: {e.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        });


        private string _Brand;
        public string Brand
        {
            get
            {
                return _Brand;
            }
            set
            {
                if (_Brand != value)
                {
                    _Brand = value;
                    OnPropertyChanged();
                    UpdateValidator();
                }
            }
        }


        private string _Model;
        public string Model
        {
            get
            {
                return _Model;
            }
            set
            {
                if (_Model != value)
                {
                    _Model = value;
                    OnPropertyChanged();
                    UpdateValidator();
                }
            }
        }

        private string _Description;
        public string Description
        {
            get
            {
                return _Description;
            }
            set
            {
                if (_Description != value)
                {
                    _Description = value;
                    OnPropertyChanged();
                    UpdateValidator();
                }
            }
        }


        #region VALIDATION

        private void ValidationInitialize()
        {

            FieldValidators["brand"] = new RequiredStringValidator((validator, value) =>
            {
                if (string.IsNullOrEmpty(value) || value.Length < 2 || value.Length > 128)
                {
                    return "Некорректное количество символов";
                }
                return null;
            });

            FieldValidators["model"] = new RequiredStringValidator((validator, value) =>
            {
                if (string.IsNullOrEmpty(value) || value.Length < 1 || value.Length > 128)
                {
                    return "Некорректное количество символов";
                }
                return null;
            });

            FieldValidators["description"] = new RequiredStringValidator((validator, value) =>
            {
                // Description is optional, so only validate length if provided
                if (!string.IsNullOrEmpty(value) && value.Length > 500)
                {
                    return "Некорректное количество символов";
                }
                return null;
            });

        }

        void UpdateValidator()
        {
            Validate();
            OnPropertyChanged("CanSubmit");
        }

        private bool _IsBusy;
        private readonly Func<LocalCar, Task<int>> _callback;
        private readonly LocalCar _item;

        public new bool IsBusy
        {
            get { return _IsBusy; }
            set
            {
                if (_IsBusy != value)
                {
                    _IsBusy = value;
                    OnPropertyChanged();
                    OnPropertyChanged("CanSubmit");
                }
            }
        }


        public bool CanSubmit
        {
            get
            {
                var validated = Validate();
                return validated && !IsBusy;
            }
        }

        bool Validate()
        {
            int valid = 1;
            valid *= Convert.ToInt32(FieldValidators["brand"].Validate(Brand));
            valid *= Convert.ToInt32(FieldValidators["model"].Validate(Model));
            valid *= Convert.ToInt32(FieldValidators["description"].Validate(Description));
            OnPropertyChanged("FieldValidators");
            return valid > 0;
        }

        public Dictionary<string, FieldValidator> FieldValidators { get; } = new();


        #endregion


    }
}
