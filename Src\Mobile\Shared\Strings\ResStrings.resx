﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BtnSave" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="BtnCancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="BtnClose" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="BtnApply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="Inputs" xml:space="preserve">
    <value>Inputs</value>
  </data>
  <data name="Device" xml:space="preserve">
    <value>Device</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="Options" xml:space="preserve">
    <value>Options</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="FeetShort" xml:space="preserve">
    <value> ft</value>
  </data>
  <data name="MetersShort" xml:space="preserve">
    <value> m</value>
  </data>
  <data name="Mph" xml:space="preserve">
    <value>mph</value>
  </data>
  <data name="Kmh" xml:space="preserve">
    <value>km/h</value>
  </data>
  <data name="MinutesShort" xml:space="preserve">
    <value> min</value>
  </data>
  <data name="SecondsShort" xml:space="preserve">
    <value> s</value>
  </data>
  <data name="BtnStart" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="BtnStop" xml:space="preserve">
    <value>Stop</value>
  </data>
  <data name="BtnConnect" xml:space="preserve">
    <value>Connect</value>
  </data>
  <data name="BtnDisconnect" xml:space="preserve">
    <value>Disconnect</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>On</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>Off</value>
  </data>
  <data name="StatusConnecting" xml:space="preserve">
    <value>Connecting...</value>
  </data>
  <data name="StatusBluetoothOff" xml:space="preserve">
    <value>Bluetooth is off</value>
  </data>
  <data name="Measure" xml:space="preserve">
    <value>Measurement</value>
  </data>
  <data name="MeasureInstant" xml:space="preserve">
    <value>Instant Run</value>
  </data>
  <data name="VendorTitle" xml:space="preserve">
    <value>Racebox</value>
  </data>
  <data name="SignalStrength" xml:space="preserve">
    <value>Signal Strength</value>
  </data>
  <data name="HintConnectNow" xml:space="preserve">
    <value>Click the Bluetooth icon at the top of the screen to connect to the device</value>
  </data>
  <data name="HintStartMeasuring" xml:space="preserve">
    <value>Press the Start button to start measuring</value>
  </data>
  <data name="HintMeasureFailed" xml:space="preserve">
    <value>Measurement failed</value>
  </data>
  <data name="HintMonitoring" xml:space="preserve">
    <value>Waiting for measurement to start...</value>
  </data>
  <data name="HintGo" xml:space="preserve">
    <value>GO!!!</value>
  </data>
  <data name="Racing" xml:space="preserve">
    <value>Run</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>Dashboard</value>
  </data>
  <data name="History" xml:space="preserve">
    <value>History</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="Altitude" xml:space="preserve">
    <value>Altitude</value>
  </data>
  <data name="Heading" xml:space="preserve">
    <value>Heading</value>
  </data>
  <data name="Latitude" xml:space="preserve">
    <value>Latitude</value>
  </data>
  <data name="Longitude" xml:space="preserve">
    <value>Longitude</value>
  </data>
  <data name="Frequency" xml:space="preserve">
    <value>Frequency</value>
  </data>
  <data name="MaxSpeed" xml:space="preserve">
    <value>Max Speed</value>
  </data>
  <data name="Incline" xml:space="preserve">
    <value>Incline</value>
  </data>
  <data name="AllCars" xml:space="preserve">
    <value>All Cars</value>
  </data>
  <data name="Filtered" xml:space="preserve">
    <value>Filtered</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="Cars" xml:space="preserve">
    <value>Vehicles</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Order</value>
  </data>
  <data name="MeasuringSystem" xml:space="preserve">
    <value>Measuring System</value>
  </data>
  <data name="CustomMetrics" xml:space="preserve">
    <value>Measuring Metrics</value>
  </data>
  <data name="Sound" xml:space="preserve">
    <value>Sound</value>
  </data>
  <data name="RollOut" xml:space="preserve">
    <value>Roll Out (1 ft)</value>
  </data>
  <data name="Car" xml:space="preserve">
    <value>Vehicle</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="EditList" xml:space="preserve">
    <value>Edit List</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Users</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>Brand</value>
  </data>
  <data name="Model" xml:space="preserve">
    <value>Model</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Deleting" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="AreYouSure" xml:space="preserve">
    <value>Are You sure?</value>
  </data>
  <data name="CannotDeleteSingle" xml:space="preserve">
    <value>Cannot delete last record</value>
  </data>
  <data name="CannotDeleteUsed" xml:space="preserve">
    <value>This is being actually used</value>
  </data>
  <data name="USA" xml:space="preserve">
    <value>USA</value>
  </data>
  <data name="Europe" xml:space="preserve">
    <value>Europe</value>
  </data>
  <data name="DefaultBrand" xml:space="preserve">
    <value>Your</value>
  </data>
  <data name="DefaultModel" xml:space="preserve">
    <value>Car</value>
  </data>
  <data name="OrderOldFirst" xml:space="preserve">
    <value>Older first</value>
  </data>
  <data name="OrderNewFirst" xml:space="preserve">
    <value>Newer first</value>
  </data>
  <data name="SpeedRange" xml:space="preserve">
    <value>Speed Range</value>
  </data>
  <data name="Distance" xml:space="preserve">
    <value>Distance</value>
  </data>
  <data name="Length" xml:space="preserve">
    <value>Distance</value>
  </data>
  <data name="Start" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="End" xml:space="preserve">
    <value>End</value>
  </data>
  <data name="ValidationStartValue" xml:space="preserve">
    <value>Value &gt;=0 and &lt;=5000 is required</value>
  </data>
  <data name="ValidationEndValue" xml:space="preserve">
    <value>Value &gt;0 and &lt;=5000 is required</value>
  </data>
  <data name="ValidationDifferentValues" xml:space="preserve">
    <value>End value must be higher than start</value>
  </data>
  <data name="MeasuringUnits" xml:space="preserve">
    <value>Units</value>
  </data>
  <data name="Feet" xml:space="preserve">
    <value>Feet</value>
  </data>
  <data name="Meters" xml:space="preserve">
    <value>Meters</value>
  </data>
  <data name="StatusPleaseWait" xml:space="preserve">
    <value></value>
  </data>
  <data name="AlertTurnOnBluetooth" xml:space="preserve">
    <value>Please turn on Bluetooth.</value>
  </data>
  <data name="AlertNeedGpsPermissionsForBluetooth" xml:space="preserve">
    <value>Please grant location permissions (Android system requirement to use Bluetooth).</value>
  </data>
  <data name="AlertNeedGpsOnForBluetooth" xml:space="preserve">
    <value>Please enable GPS (Android system requirement to use Bluetooth).</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="AlertBluetoothUnsupported" xml:space="preserve">
    <value>Your device does not support Bluetooth.</value>
  </data>
  <data name="HintGpsLow" xml:space="preserve">
    <value>Try to change location of your device for better GPS reception</value>
  </data>
  <data name="StatusGpsLow" xml:space="preserve">
    <value>Low GPS signal</value>
  </data>
  <data name="AlertBluetoothPermissionsOff" xml:space="preserve">
    <value>No permissions to use Bluetooth.</value>
  </data>
  <data name="Hz" xml:space="preserve">
    <value>Hz</value>
  </data>
  <data name="KmhAdd" xml:space="preserve">
    <value> km/h</value>
  </data>
  <data name="MphAdd" xml:space="preserve">
    <value> mph</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="MaxAcceleration" xml:space="preserve">
    <value>Max Accel.</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="HoursShort" xml:space="preserve">
    <value> h</value>
  </data>
  <data name="BtnReset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="BtnCreate" xml:space="preserve">
    <value>Create</value>
  </data>
  <data name="Acceleration" xml:space="preserve">
    <value>Acceleration</value>
  </data>
  <data name="AccelerationSide" xml:space="preserve">
    <value>Lateral Accel.</value>
  </data>
  <data name="InclineMax" xml:space="preserve">
    <value>Max Incline</value>
  </data>
  <data name="IsValid" xml:space="preserve">
    <value>Is Valid</value>
  </data>
  <data name="IsValidYes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="IsValidNo" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="AlertBluetoothWillNotBeAvailable" xml:space="preserve">
    <value>Bluetooth will not be available.</value>
  </data>
  <data name="SystemSettings" xml:space="preserve">
    <value>System Settings</value>
  </data>
  <data name="Time24" xml:space="preserve">
    <value>Time Format 24h</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>About</value>
  </data>
  <data name="BtnQuestion" xml:space="preserve">
    <value>Ask A Question</value>
  </data>
  <data name="AboutAppText" xml:space="preserve">
    <value>Racebox is a companion app for the Racebox Pro and Pro+ devices created for vehicle performance measurements. 
The app is intended for use on race tracks only. The author is not responsible for any injuries or property damage that may occur during use of this software.</value>
  </data>
  <data name="Copyright" xml:space="preserve">
    <value>© 2025 Racebox Team</value>
  </data>
  <data name="AlertNeedLocationForBluetooth" xml:space="preserve">
    <value>We would need you to provide permissions to use your GPS location (Android system requirement to use Bluetooth).</value>
  </data>
  <data name="PressBACKOnceAgain" xml:space="preserve">
    <value>Press BACK once again to quit the app</value>
  </data>
  <data name="ExportFormat" xml:space="preserve">
    <value>Logs Format</value>
  </data>
  <data name="ErrorFailedToSaveRecord" xml:space="preserve">
    <value>Failed to save record</value>
  </data>
  <data name="ErrorMetricsAlreadyExist" xml:space="preserve">
    <value>Such metrics already exist</value>
  </data>
  <data name="CompatibleDevicesNotFound" xml:space="preserve">
    <value>No Racebox devices found nearby.</value>
  </data>
  <data name="DemoMode" xml:space="preserve">
    <value>Demo Mode</value>
  </data>
  <data name="DemoWarning" xml:space="preserve">
    <value>
In this mode, the application will stop connecting to a real Bluetooth module and will play pre-recorded motion tracks instead. Are you sure you want to enable the demo mode?</value>
  </data>
  <data name="DemoStartHint" xml:space="preserve">
    <value>Click on Speed Gauge to start simulation</value>
  </data>
  <data name="BadMeasureNotValid" xml:space="preserve">
    <value>Invalid measurement (big incline)</value>
  </data>
  <data name="BadMeasureTooLong" xml:space="preserve">
    <value>Measurement time is above ACC_TIMEOUT</value>
  </data>
  <data name="BadMeasureLowAccel" xml:space="preserve">
    <value>Acceleration is down</value>
  </data>
  <data name="BadMeasureInstant" xml:space="preserve">
    <value>Instant Run and the speed is below the initial speed of the first pair and the measurement time is more than 1 second</value>
  </data>
  <data name="BadMeasureGps" xml:space="preserve">
    <value>Low quality GPS reception</value>
  </data>
  <data name="BadMeasureNoRanges" xml:space="preserve">
    <value>No speed ranges measured</value>
  </data>
  <data name="BadMeasureNoDistances" xml:space="preserve">
    <value>No distances measured</value>
  </data>
  <data name="BadMeasureLowSpeed" xml:space="preserve">
    <value>Speed below READY_SPEED_THRESHOLD_UNIT</value>
  </data>
  <data name="BadMeasureIncline" xml:space="preserve">
    <value>Result is not valid: incline was too important!</value>
  </data>
  <data name="Report" xml:space="preserve">
    <value>Report</value>
  </data>
  <data name="MeasuringStateError" xml:space="preserve">
    <value>Measurement failed</value>
  </data>
  <data name="MeasuringStateFinished" xml:space="preserve">
    <value>Measurement completed</value>
  </data>
  <data name="MeasuringStateReady" xml:space="preserve">
    <value>Start!</value>
  </data>
  <data name="MeasuringStateActive" xml:space="preserve">
    <value>Racing!</value>
  </data>
  <data name="MeasuringStateMonitoring" xml:space="preserve">
    <value>Device is on</value>
  </data>
  <data name="MeasuringStateDisabled" xml:space="preserve">
    <value>Device is off</value>
  </data>
  <data name="Weather" xml:space="preserve">
    <value>Weather</value>
  </data>
  <data name="X_Power" xml:space="preserve">
    <value>Power</value>
  </data>
  <data name="Satellites" xml:space="preserve">
    <value>Satellites</value>
  </data>
  <data name="DeviceLedBrightness" xml:space="preserve">
    <value>Display brightness</value>
  </data>
  <data name="DeviceTransmissionType" xml:space="preserve">
    <value>Transmission type</value>
  </data>
  <data name="DeviceMaps" xml:space="preserve">
    <value>Maps</value>
  </data>
  <data name="DeviceTone" xml:space="preserve">
    <value>Sound signals</value>
  </data>
  <data name="DeviceUtc" xml:space="preserve">
    <value>Timezone</value>
  </data>
  <data name="DeviceLogFormat" xml:space="preserve">
    <value>Logs Format</value>
  </data>
  <data name="DeviceLogRate" xml:space="preserve">
    <value>Log frequency</value>
  </data>
  <data name="DevicePrediction" xml:space="preserve">
    <value>Predictive mode</value>
  </data>
  <data name="DeviceRandomStart" xml:space="preserve">
    <value>Arbitrary start time</value>
  </data>
  <data name="DeviceMinDistance" xml:space="preserve">
    <value>Min. display distance</value>
  </data>
  <data name="DeviceCarWeight" xml:space="preserve">
    <value>Vehicle weight</value>
  </data>
  <data name="DeviceDragRatio" xml:space="preserve">
    <value>Coef. drag</value>
  </data>
  <data name="DeviceFrontal" xml:space="preserve">
    <value>Cross-sectional area</value>
  </data>
  <data name="DeviceObdLog" xml:space="preserve">
    <value>OBD Log Recording</value>
  </data>
  <data name="DeviceShiftTime" xml:space="preserve">
    <value>Switching time measurement</value>
  </data>
  <data name="DeviceObdPidAuto" xml:space="preserve">
    <value>Auto PID OBD detection</value>
  </data>
  <data name="DeviceObdPids" xml:space="preserve">
    <value>Selecting OBD PIDs to Read</value>
  </data>
  <data name="DeviceObdProtocol" xml:space="preserve">
    <value>OBD protocol</value>
  </data>
  <data name="DeviceGnssSecondarySource" xml:space="preserve">
    <value>2nd GNSS source</value>
  </data>
  <data name="DeviceGnssUartOut" xml:space="preserve">
    <value>GNSS data output via USB</value>
  </data>
  <data name="DeviceGnssColdStart" xml:space="preserve">
    <value>GNSS cold restart</value>
  </data>
  <data name="DeviceGnssReconfig" xml:space="preserve">
    <value>GNSS reconfiguration</value>
  </data>
  <data name="DeviceResetConfiguration" xml:space="preserve">
    <value>Reset configuration</value>
  </data>
  <data name="ResetDevice" xml:space="preserve">
    <value>Restarting the device</value>
  </data>
  <data name="WarningCannotUndo" xml:space="preserve">
    <value>This operation cannot be undone.</value>
  </data>
  <data name="DeviceSettings" xml:space="preserve">
    <value>Device Settings</value>
  </data>
  <data name="FirmwareWarningHelp" xml:space="preserve">
    <value>To update the device software, follow these steps:
 1. Download the new version of the software from the website [racebox.ru](https://racebox.ru/obnovlenie-po).
 2. Copy the firmware file in `HEX` format corresponding to your device model to the root directory of the memory card.
 3. Insert the memory card into the device when it is turned off.
 4. While holding the `Mode` button, connect the device to power via the USB cable.
 5. Wait until the memory card is initialized and the message `HOLD TO UPDATE X.XX` appears (where X.XX is the version of the downloaded software)
 6. Press and hold the `Mode` button to begin the update process.
 7. Wait for the update to complete and make sure that the new firmware version is displayed when you turn on the device.
 8. Restart the __Racebox__ application.</value>
  </data>
  <data name="FirmwareHowToTitle" xml:space="preserve">
    <value>Update Firmware</value>
  </data>
  <data name="FirmwareWarningTitle" xml:space="preserve">
    <value>Unsupported Firmware</value>
  </data>
  <data name="SettingsCanSpeak" xml:space="preserve">
    <value>Say Speed</value>
  </data>
  <data name="BtnSearch" xml:space="preserve">
    <value>Search</value>
  </data>
</root>