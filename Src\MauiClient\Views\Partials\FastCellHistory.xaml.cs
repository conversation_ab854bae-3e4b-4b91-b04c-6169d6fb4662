namespace Racebox.Views.Partials;

public class SkiaCellHistory : SkiaLayout
{
    public SkiaLabel LabelId { get; set; }
    public SkiaLabel LabelInfo { get; set; }
    public SkiaLabel LabelResults { get; set; }

    protected override void OnBindingContextChanged()
    {
        base.OnBindingContextChanged();

        if (BindingContext is HistoryCellData model)
        {
            LockUpdate(true);

            LabelId.Text = $"#{model.Id}";
            LabelInfo.Text = model.DisplayInfo;
            LabelResults.Text = model.DisplayResults;

            if (model.IsValid)
            {
                IconValid.SvgString = SvgStatusOk;
                IconValid.TintColor = Colors.Green;
            }
            else
            {
                IconValid.SvgString = SvgStatusFail;
                IconValid.TintColor = Colors.Red;
            }

            LockUpdate(false);
        }

    }
}

public partial class FastCellHistory : SkiaListCell
{
    private static string SvgStatusOk;
    private static string SvgStatusFail;

    public override void SetContentFull()
    {
        if (BindingContext is HistoryCellData model)
        {
            LockUpdate(true);

            LabelId.Text = $"#{model.Id}";
            LabelInfo.Text = model.DisplayInfo;
            LabelResults.Text = model.DisplayResults;

            if (model.IsValid)
            {
                IconValid.SvgString = SvgStatusOk;
                IconValid.TintColor = Colors.Green;
            }
            else
            {
                IconValid.SvgString = SvgStatusFail;
                IconValid.TintColor = Colors.Red;
            }

            LockUpdate(false);
        }

        base.SetContentFull();
    }

    public FastCellHistory()
    {
        InitializeComponent();

        MainFrame = MainContainer;

        AttachGestures();

        if (SvgStatusOk == null)
            SvgStatusOk = App.Instance.Resources.Get<string>("SvgStatusOk");

        if (SvgStatusFail == null)
            SvgStatusFail = App.Instance.Resources.Get<string>("SvgStatusFail");
    }
    private void OnResultsMeasured(object sender, Size size)
    {
        //var need = this.HeightRequest + this.Margin.Top + this.Margin.Bottom + LabelResults.Margin.Top + LabelResults.Margin.Bottom;
        var cellHeight = size.Height + LabelResults.Margin.Top + LabelResults.Margin.Bottom + 3;
        if (cellHeight < 114)
        {
            cellHeight = 114;
        }
        this.HeightRequest = cellHeight;
    }

    public override void OnDisposing()
    {
        DetachGestures();

        base.OnDisposing();
    }



    private TouchEffect _touchHandler;

    protected virtual void TappedProcessed()
    {

    }

    private bool lockTap;

    protected virtual async void OnTapped(object sender, TouchActionEventArgs args)
    {
        if (!CanBeTapped)
            return;

        //Debug.WriteLine($"[TOUCH] Tapped!");

        if (lockTap)
        {
            //Debug.WriteLine($"[TOUCH] Tapped LOCKED!");
            return;
        }

        lockTap = true;

        if (MainFrame != null)
        {
            MainFrame.PlayRippleAnimation(Colors.White, args.Location.X, args.Location.Y);
        }
        //else
        //{
        //	PlayRippleAnimation(Colors.White, args.Location.X, args.Location.Y);
        //}

        await Task.Run(() =>
        {

            //invoke action
            if (CommandTapped != null)
            {
                CommandTapped.Execute(BindingContext);
            }
            else
            {
                if (this.Parent is IItemsContainer container)
                {
                    container.CommandItemTapped?.Execute(BindingContext);
                }
            }

            TappedProcessed();

            Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(500), async () =>
            {
                lockTap = false;
                return false;
            });

        }).ConfigureAwait(false);


        //Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(250), async () =>
        //{

        //    return false;
        //});


        //if (BindingContext is IPatchableDto patchable)
        //{
        //    //patchable.IsBusy = true;
        //    Update();
        //    Device.StartTimer(TimeSpan.FromMilliseconds(2500), () =>
        //    {
        //        patchable.IsBusy = false;
        //        Update();
        //        return false;
        //    });
        //}




    }

    protected virtual void OnUp(object sender, TouchActionEventArgs args)
    {
        //Debug.WriteLine($"[TOUCH] UP");

        //MainFrame.StrokeWidth = 1 / RenderingScale; // 1 precise pixel
        //MainFrame.StrokeColor = StaticResources.DropShadow;
        //Update();
    }

    protected virtual void OnDown(object sender, TouchActionEventArgs args)
    {
        //Debug.WriteLine($"[TOUCH] DOWN");



    }

    protected virtual void OnTouch(object sender, TouchActionEventArgs args)
    {
        //Debug.WriteLine($"[TOUCH] {args.Type} {JsonConvert.SerializeObject(args)}");
    }

    public bool HasGestures
    {
        get
        {
            return _touchHandler != null;
        }
    }

    protected void DetachGestures()
    {
        if (!HasGestures)
            return;

        _touchHandler.Dispose();
    }

    public void EnableGestures()
    {
        AttachGestures();
    }
    protected void AttachGestures()
    {
        if (HasGestures || _touchHandler != null)
            return;

        _touchHandler = new TouchEffect
        {
            Capture = true
        };
        _touchHandler.LongPressing += OnLongPressing;
        _touchHandler.Tapped += OnTapped;
        _touchHandler.Down += OnDown;
        _touchHandler.Up += OnUp;
        _touchHandler.TouchAction += OnTouch;
        this.Effects.Add(_touchHandler);
    }

    protected virtual void OnLongPressing(object sender, TouchActionEventArgs args)
    {



        if (CommandLongPressing != null)
        {
            if (CanBeSelected)
            {
                IsSelected = true;
            }
            Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(2500), async () =>
            {
                if (CanBeSelected)
                {
                    IsSelected = false;
                }
                return false;
            });


            if (CommandLongPressing != null)
            {
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    CommandLongPressing.Execute(BindingContext);
                });
            }
            else
            {
                if (this.Parent is IItemsContainer container)
                {
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        container.CommandLongPressed?.Execute(BindingContext);
                    });
                }
            }


        }

    }

    public bool CanBeSelected { get; set; } = true;

    public bool CanBeTapped { get; set; } = true;



    private bool _IsSelected;
    public bool IsSelected
    {
        get { return _IsSelected; }
        set
        {
            if (_IsSelected != value)
            {
                _IsSelected = value;
                OnPropertyChanged();
                Update();
            }
        }
    }


    public static readonly BindableProperty MainFrameProperty = BindableProperty.Create(nameof(MainFrame),
        typeof(SkiaControl), typeof(SkiaListCell),
        null);
    public SkiaControl MainFrame
    {
        get { return (SkiaControl)GetValue(MainFrameProperty); }
        set { SetValue(MainFrameProperty, value); }
    }


    //-------------------------------------------------------------
    // CommandTapped
    //-------------------------------------------------------------
    private const string nameCommandTapped = "CommandTapped";
    public static readonly BindableProperty CommandTappedProperty = BindableProperty.Create(nameCommandTapped, typeof(ICommand), typeof(SkiaListCell),
        null);
    public ICommand CommandTapped
    {
        get { return (ICommand)GetValue(CommandTappedProperty); }
        set { SetValue(CommandTappedProperty, value); }
    }

    //-------------------------------------------------------------
    // CommandLongPressing
    //-------------------------------------------------------------
    private const string nameCommandLongPressing = "CommandLongPressing";
    public static readonly BindableProperty CommandLongPressingProperty = BindableProperty.Create(nameCommandLongPressing, typeof(ICommand), typeof(SkiaListCell),
        null);
    public ICommand CommandLongPressing
    {
        get { return (ICommand)GetValue(CommandLongPressingProperty); }
        set { SetValue(CommandLongPressingProperty, value); }
    }


}