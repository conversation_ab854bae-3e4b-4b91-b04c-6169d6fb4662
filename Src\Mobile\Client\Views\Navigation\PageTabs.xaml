﻿<?xml version="1.0" encoding="utf-8" ?>
<views3:BasePage
    x:Class="Racebox.Views.Navigation.FastShell.PageTabs"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:d="http://xamarin.com/schemas/2014/forms/design"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:fastShell="clr-namespace:Racebox.Views.Navigation.FastShell"
    xmlns:ios="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.iOSSpecific;assembly=Microsoft.Maui.Controls"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:navigation="clr-namespace:Racebox.Views.Navigation"
    xmlns:navigation1="clr-namespace:Racebox.ViewModels.Navigation"
    xmlns:racebox="clr-namespace:Racebox"
    xmlns:strings="clr-namespace:Racebox.Shared.Strings;assembly=Racebox.Shared"
    xmlns:views="http://schemas.appomobi.com/drawnUi/2023/drawn"
    xmlns:views3="clr-namespace:Racebox.Views"
    xmlns:tabs="clr-namespace:AppoMobi.Framework.Maui.Controls.Navigation.Tabs;assembly=AppoMobi.Framework.Maui"
    xmlns:touch="clr-namespace:AppoMobi.Framework.Maui.Touch;assembly=AppoMobi.Framework.Maui"
    x:Name="ThisPage"
    x:DataType="navigation1:TabsViewModel"
    NavigationPage.HasNavigationBar="false"
    mc:Ignorable="d">


    <ContentPage.Resources>
        <ResourceDictionary>


            <!--  unselected was defined as E8E3D7  -->

            <Style
                x:Key="BottomTabDefault"
                TargetType="navigation:SkiaBottomTab">
                <Setter Property="UnselectedTextColor" Value="#BDB7A7" />
                <Setter Property="UnselectedIconColor" Value="#BDB7A7" />
                <Setter Property="SelectedTextColor" Value="#CB6336" />
                <Setter Property="SelectedIconColor" Value="#CB6336" />
                <Setter Property="TabWidth" Value="70" />
                <Setter Property="TextSize" Value="9" />
                <Setter Property="IconSize" Value="31" />
            </Style>



            <!--<Style
                x:Key="CenterBottomTabStyle"
                BasedOn="{StaticResource BottomTabSmall}"
                TargetType="tabs3:SvgBottomTab">
                <Setter Property="TextSize" Value="10" />
                <Setter Property="IconSize" Value="30" />
                <Setter Property="TabWidth" Value="90" />
            </Style>-->

            <!--<Setter Property="SelectedTabColor" Value="{xaml:SetColor Color={x:StaticResource ColorAccentLight}, Lighter=90}" />-->



            <tabs:ObservableViews x:Key="ViewsForUser">

                <tabs:LazyView x:TypeArguments="views3:TabMeasure" />
                <tabs:LazyView x:TypeArguments="views3:TabMonitor" />
                <tabs:LazyView x:TypeArguments="views3:TabHistory" />
                <tabs:LazyView x:TypeArguments="views3:TabSettings" />

            </tabs:ObservableViews>

            <!--<lazy:ObservableViews x:Key="ViewsForPro">

                    TODO IF YOU WANT

            </lazy:ObservableViews>-->

            <tabs:ObservableViews x:Key="TabsForUser">


                <navigation:SkiaBottomTab
                    IconOffsetY="-8"
                    Route="measure"
                    Style="{StaticResource BottomTabDefault}"
                    SvgString="{StaticResource SvgMeasure}"
                    TabAlign="Center"
                    TabWidth="*"
                    Text="{x:Static strings:ResStrings.Racing}" />

                <navigation:SkiaBottomTab
                    IconOffsetY="-8"
                    Route="measure"
                    Style="{StaticResource BottomTabDefault}"
                    SvgString="{StaticResource SvgMonitor}"
                    TabAlign="Center"
                    TabWidth="*"
                    Text="{x:Static strings:ResStrings.Dashboard}" />

                <navigation:SkiaBottomTab
                    IconOffsetY="-8"
                    Route="measure"
                    Style="{StaticResource BottomTabDefault}"
                    SvgString="{StaticResource SvgHistory}"
                    TabAlign="Center"
                    TabWidth="*"
                    Text="{x:Static strings:ResStrings.History}" />

                <navigation:SkiaBottomTab
                    IconOffsetY="-8"
                    IconScaleX="0.95"
                    IconScaleY="0.95"
                    NotificationsCount="{Binding NavbarModel.NotificationsSupport}"
                    Route="settings"
                    Style="{StaticResource BottomTabDefault}"
                    SvgString="{StaticResource SvgSettings}"
                    TabAlign="Center"
                    TabWidth="*"
                    Text="{x:Static strings:ResStrings.Settings}" />

                <!--<fastShell:SvgBottomTab
                    IconOffsetX="3"
                    IconSize="23.1"
                    Text="Приборы"
                    NotificationsCount="{Binding NavbarModel.NotificationsReqs}"
                    Route="reqs"
                    Style="{StaticResource BottomTabDefault}"
                    SvgString="{StaticResource SvgTabsReqs}"
                    TabAlign="Center"
                    TabIndex="1"
                    TabWidth="*" />-->

            </tabs:ObservableViews>

            <!--<lazy:ObservableViews x:Key="TabsForPro">

              TODO IF YOU WANT

            </lazy:ObservableViews>-->

        </ResourceDictionary>
    </ContentPage.Resources>


    <ContentPage.Content>

        <tabs:ViewsContainer
            x:Name="Container"
            HorizontalOptions="FillAndExpand"
            RowDefinitions="*,Auto"
            VerticalOptions="FillAndExpand">

            <Grid.Background>
                <LinearGradientBrush EndPoint="0,1">
                    <GradientStop Offset="0.0" Color="#343C45" />
                    <GradientStop Offset="1.0" Color="#11161D" />
                </LinearGradientBrush>
            </Grid.Background>

            <!--<views:ScreenBackground />-->

            <!--<Path
                                                    StrokeThickness="0.0"
Fill="White"
                VerticalOptions="Center"
                HorizontalOptions="Center"
                Rotation="90"
                Data="M15.6099294,11.0552456 L12.3765961,7.82357897 C12.2574176,7.70409826 12.0779382,7.66830385 11.9220434,7.73292537 C11.7661485,7.7975469 11.6646275,7.94982156 11.6649294,8.11857897 L11.6649294,21.2502456 C11.6649294,22.4008389 10.7321893,23.333579 9.58159609,23.333579 C8.43100286,23.333579 7.49826276,22.4008389 7.49826276,21.2502456 L7.49826276,8.11857897 C7.49789351,7.95055217 7.39663523,7.79918973 7.24146862,7.73471909 C7.08630201,7.67024846 6.90759527,7.70528741 6.78826276,7.82357897 L3.55492943,11.0552456 C2.74169013,11.8684849 1.42316875,11.8684849 0.609929471,11.0552456 C-0.203309806,10.2420063 -0.203309826,8.92348493 0.609929427,8.11024563 L8.10992943,0.610245632 C8.50036143,0.219527336 9.03007272,0 9.58242943,0 C10.1347861,0 10.6644974,0.219527336 11.0549294,0.610245632 L18.5549294,8.11024563 C19.3681687,8.92348493 19.3681687,10.2420063 18.5549294,11.0552456 C17.7416901,11.8684849 16.4231687,11.8684849 15.6099294,11.0552456 L15.6099294,11.0552456 Z"/>-->

            <!--  LOGO  -->
            <draw:Canvas
                HorizontalOptions="Center"
                Opacity="0.25"
                VerticalOptions="Center">

                <draw:SkiaSvg
                    HeightRequest="200"
                    HorizontalOptions="Center"
                    LockRatio="1"
                    SvgString="{StaticResource SvgLogo}"
                    TintColor="Black"
                    VerticalOptions="Start" />

            </draw:Canvas>


            <!--  PAGES SWITCHER  -->
            <tabs:ViewSwitcher
                x:Name="Switcher"
                AnimateTabs="True"
                AnimationEasing="{x:Static Easing.SinOut}"
                AnimationSpeed="260"
                HorizontalOptions="FillAndExpand"
                IsClippedToBounds="True"
                SelectedIndex="{Binding SelectedIndex, Mode=TwoWay}"
                VerticalOptions="FillAndExpand"
                Views="{StaticResource ViewsForUser}"/>

            <touch:PanAwareHotspot
                x:Name="LeftHotspot"
                Grid.RowSpan="2"
                Margin="0,0,0,44"
                HorizontalOptions="Start"
                Panned="PannedRight"
                Swiped="SwipedRight"
                TranslationX="{Binding Source={x:Reference LeftHotspot}, Path=DistanceX}"
                VerticalOptions="FillAndExpand"
                WidthRequest="24" />

            <touch:PanAwareHotspot
                x:Name="RightHotspot"
                Grid.RowSpan="2"
                Margin="0,0,0,44"
                HorizontalOptions="End"
                Panned="PannedLeft"
                Swiped="SwipedLeft"
                TranslationX="{Binding Source={x:Reference RightHotspot}, Path=DistanceX}"
                VerticalOptions="FillAndExpand"
                WidthRequest="24" />


            <!--  BOTTOM TABBEDBAR  -->
            <fastShell:BottomTabs
                x:Name="TabHost"
                Grid.Row="1"
                CommandTabReselected="{Binding CommandTabReselected}"
                CommandTabSelected="{Binding CommandTabSelected}"
                HeightRequest="{Binding NavbarModel.BottomTabsHeightRequest}"
                HorizontalOptions="FillAndExpand"
                IsVisible="{Binding NavbarModel.IsFullscreen, Converter={StaticResource NotConverter}}"
                SelectedIndex="{Binding Source={x:Reference Switcher}, Path=SelectedIndex, Mode=TwoWay}"
                Tabs="{StaticResource TabsForUser}"
                UseTappedHandler="Custom"
                VerticalOptions="EndAndExpand">
                <!--<fastShell:BottomTabs.Triggers>
                            <DataTrigger
                                Binding="{Binding NavbarModel.InRole, Converter={StaticResource StringIsEqualConverter}, ConverterParameter=user}"
                                TargetType="fastShell:BottomTabs"
                                Value="True">
                                <Setter Property="Tabs" Value="{StaticResource TabsForUser}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding NavbarModel.InRole, Converter={StaticResource StringIsEqualConverter}, ConverterParameter=pro}"
                                TargetType="fastShell:BottomTabs"
                                Value="True">
                                <Setter Property="Tabs" Value="{StaticResource TabsForPro}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding NavbarModel.InRole, Converter={StaticResource StringIsEqualConverter}, ConverterParameter=admin}"
                                TargetType="fastShell:BottomTabs"
                                Value="True">
                                <Setter Property="Tabs" Value="{StaticResource TabsForPro}" />
                            </DataTrigger>
                        </fastShell:BottomTabs.Triggers>-->
            </fastShell:BottomTabs>

            <!--<BoxView
                        BackgroundColor="Black"
                        HeightRequest="{Binding NavbarModel.PaddingBottom}"
                        VerticalOptions="Start" />-->


            <!--  CLIP STATUS  -->
            <!--<ContentView
                BackgroundColor="{x:StaticResource ColorStatusBar}"
                HeightRequest="{Binding NavbarModel.StatusBarHeightRequest}"
                StyleId="StatusBar"
                VerticalOptions="Start" />-->




        </tabs:ViewsContainer>



    </ContentPage.Content>
</views3:BasePage>