﻿<?xml version="1.0" encoding="utf-8"?>

<views:BasePage
    x:Class="Racebox.Views.PageChartDetails"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:drawn="clr-namespace:Racebox.Views.Drawn"
    xmlns:maps="clr-namespace:Racebox.Views.Maps"
    xmlns:partials="clr-namespace:Racebox.Views.Partials"
    xmlns:racebox="clr-namespace:Racebox"
    xmlns:viewModels="clr-namespace:Racebox.ViewModels"
    xmlns:views="clr-namespace:Racebox.Views"
    Title="PageMapDetails"
    x:DataType="viewModels:HistoryResultViewModel">

    <ContentPage.Content>

        <Grid
            HorizontalOptions="FillAndExpand"
            VerticalOptions="FillAndExpand">

            <!--  page background with gradient and frame  -->
            <draw:Canvas
                HorizontalOptions="Fill"
                Tag="BackgroundWithFrame"
                VerticalOptions="Fill">

                <partials:GradientToBorder
                    HorizontalOptions="Fill"
                    MissingHeight="{Binding NavbarModel.BottomTabsHeightRequest}"
                    VerticalOptions="Fill">

                    <drawn:EmbossedFrameDrawn
                        x:Name="DesignFrame"
                        BackgroundColor="Transparent"
                        HorizontalOptions="Fill"
                        VerticalOptions="Fill" />

                </partials:GradientToBorder>
            </draw:Canvas>

            <draw:Canvas
                Gestures="Lock"
                RenderingMode="Accelerated"
                HorizontalOptions="Fill"
                VerticalOptions="FillAndExpand">

                <draw:SkiaLayout
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill">

                    <draw:SkiaLayout
                        Margin="12,16,12,8"
                        ClipWith="{x:Reference DesignFrame}"
                        HorizontalOptions="Fill"
                        Spacing="0"
                        Type="Column"
                        VerticalOptions="Fill">

                        <!--  NAVBAR  -->
                        <partials:SkiaNavBar
                            x:Name="NavBar"
                            Margin="0,0,1,0"
                            HeightRequest="65"
                            HorizontalOptions="Fill">

                            <draw:SkiaSvg
                                Margin="16,0,16,0"
                                HeightRequest="16"
                                HorizontalOptions="Start"
                                SvgString="{StaticResource SvgGoBack}"
                                TintColor="#CB6336"
                                VerticalOptions="Center"
                                WidthRequest="16" />

                            <draw:SkiaHotspot
                                CommandTapped="{Binding NavbarModel.CommandGoBack, Mode=OneTime}"
                                HorizontalOptions="Start"
                                TransformView="{x:Reference NavBar}"
                                WidthRequest="44" />

                            <draw:SkiaLabel
                                Margin="48,0,16,0"
                                FontSize="14"
                                HorizontalOptions="Start"
                                LineBreakMode="TailTruncation"
                                MaxLines="1"
                                Style="{StaticResource SkiaLabelDefaultStyle}"
                                Text="{Binding Title}"
                                TextColor="#E8E3D7"
                                TranslationY="1"
                                VerticalOptions="Center" />

                            <draw:SkiaSvg
                                Margin="0,0,16,0"
                                HeightRequest="19"
                                HorizontalOptions="End"
                                SvgString="{StaticResource SvgShare}"
                                TintColor="#CB6336"
                                VerticalOptions="Center"
                                LockRatio="1" />

                            <draw:SkiaHotspot
                                CommandTapped="{Binding CommandShareChart}"
                                HorizontalOptions="End"
                                TransformView="{x:Reference NavBar}"
                                WidthRequest="44" />

                            <draw:SkiaSvg
                                Margin="0,0,54,0"
                                HeightRequest="16"
                                HorizontalOptions="End"
                                SvgString="{StaticResource SvgBin}"
                                TintColor="#CB6336"
                                VerticalOptions="Center"
                                WidthRequest="16" />

                            <draw:SkiaHotspot
                                Margin="0,0,44,0"
                                CommandTapped="{Binding CommandDeleteChart}"
                                HorizontalOptions="End"
                                TransformView="{x:Reference NavBar}"
                                WidthRequest="44" />

                            <!--  LINE HORIZONTAL  -->
                            <draw:SkiaShape
                                Margin="-16,0"
                                BackgroundColor="Black"
                                CornerRadius="0"
                                HeightRequest="1"
                                HorizontalOptions="Fill"
                                StrokeWidth="0"
                                VerticalOptions="End">
                                <draw:SkiaShape.FillGradient>

                                    <draw:SkiaGradient
                                        EndXRatio="1"
                                        EndYRatio="0"
                                        StartXRatio="0"
                                        StartYRatio="0"
                                        Type="Linear">
                                        <draw:SkiaGradient.Colors>
                                            <Color>#00E8E3D7</Color>
                                            <Color>#99E8E3D7</Color>
                                            <Color>#00E8E3D7</Color>
                                        </draw:SkiaGradient.Colors>
                                    </draw:SkiaGradient>

                                </draw:SkiaShape.FillGradient>
                            </draw:SkiaShape>

                        </partials:SkiaNavBar>

                        <draw:SkiaLayout HorizontalOptions="Fill" VerticalOptions="Fill"
                                         BackgroundColor="White">

                            <draw:ZoomContent
                                PanningMode="OneFinger"
                                HorizontalOptions="Fill"
                                VerticalOptions="Fill"
                                ZoomSpeed="3.5"
                                ZoomMax="10"
                                ZoomMin="1">

                                <draw:SkiaImage
                                    RescalingQuality="High"
                                    Source="{Binding ChartSource}"
                                    HorizontalOptions="Fill"
                                    VerticalOptions="Fill"
                                    x:Name="ChartPreview"
                                    VerticalAlignment="Center"
                                    Aspect="AspectFit"
                                    InputTransparent="False">

                                    <!--<draw:SkiaControl.VisualEffects>

                                    <draw:TintWithAlphaEffect
                                        Alpha="0.15"
                                        ColorTint="Blue" />

                                    <draw:ChainSaturationEffect
                                        Value="0.58" />

                                </draw:SkiaControl.VisualEffects>-->

                                </draw:SkiaImage>

                            </draw:ZoomContent>

                        </draw:SkiaLayout>

                    </draw:SkiaLayout>

                    <draw:SkiaLabelFps
                        Margin="32"
                        ForceRefresh="False"
                        HorizontalOptions="End"
                        IsVisible="{x:Static racebox:MauiProgram.ShowDebugInfo}"
                        Rotation="-45"
                        VerticalOptions="End"
                        ZIndex="100" />

                </draw:SkiaLayout>

            </draw:Canvas>


        </Grid>

    </ContentPage.Content>


</views:BasePage>