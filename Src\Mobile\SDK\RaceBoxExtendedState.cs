﻿using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Racebox.SDK;

public class RaceBoxExtendedState : INotifyPropertyChanged
{
    public static RaceBoxExtendedState FromData(byte[] bytes)
    {
        var data = new RaceBoxExtendedState();
        bytes.DecodeTo(data);
        return data;
    }

    public ChargingStateType? BatteryChargingState
    {
        get => _batteryChargingState;
        set
        {
            if (value == _batteryChargingState) return;
            _batteryChargingState = value;
            OnPropertyChanged();
        }
    }
    private ChargingStateType? _batteryChargingState;

    public int? BatteryLevel
    {
        get => _batteryLevel;
        set
        {
            if (value == _batteryLevel) return;
            _batteryLevel = value;
            OnPropertyChanged();
        }
    }
    private int? _batteryLevel;

    public ModelType DeviceType
    {
        get => _deviceType;
        set
        {
            if (value == _deviceType) return;
            _deviceType = value;
            OnPropertyChanged();
        }
    }
    private ModelType _deviceType;

    public int? VersionMajor
    {
        get => _versionMajor;
        set
        {
            if (value == _versionMajor) return;
            _versionMajor = value;
            OnPropertyChanged();
        }
    }
    private int? _versionMajor;

    public int? VersionMinor
    {
        get => _versionMinor;
        set
        {
            if (value == _versionMinor) return;
            _versionMinor = value;
            OnPropertyChanged();
        }
    }
    private int? _versionMinor;

    #region INotifyPropertyChanged

    public event PropertyChangedEventHandler PropertyChanged;
    protected void OnPropertyChanged([CallerMemberName] string propertyName = "")
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    #endregion
}