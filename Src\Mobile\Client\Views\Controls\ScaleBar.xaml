<?xml version="1.0" encoding="utf-8" ?>
<partials:ScaleBarBase
    x:Class="Racebox.Views.Partials.ScaleBar"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:Racebox.Views.Controls"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:partials="clr-namespace:Racebox.Views.Partials"
    x:Name="ThisControlBar"
    HeightRequest="40"
    HorizontalOptions="Center"
    VerticalOptions="CenterAndExpand">


    <draw:SkiaLayout
        HorizontalOptions="Fill"
        VerticalOptions="Fill">

        <controls:GfxScale
            Margin="8,0"
            BackgroundColor="Transparent"
            HeightRequest="22"
            HorizontalOptions="Fill"
            Limit="{Binding Source={x:Reference ThisControlBar}, Path=Limit}"
            ScaleColor="#50A9A8A2"
            SideOffset="3"
            TextColor="#82E8E3D7"
            VerticalOptions="Start" />

        <draw:SkiaShape
            x:Name="Bar"
            Margin="8,0,8,8"
            BackgroundColor="Black"
            CornerRadius="3"
            HeightRequest="6"
            HorizontalOptions="Fill"
            StrokeColor="Black"
            StrokeWidth="1.0"
            VerticalOptions="End">
            <draw:SkiaShape.FillGradient>


                <!--  ColorPositions="{Binding Source={x:Reference ThisControlBar}, Path=Points}"  -->
                <draw:SkiaGradient
                    ColorPositions="{Binding Source={x:Reference ThisControlBar}, Path=Points}"
                    EndXRatio="1"
                    EndYRatio="0"
                    Opacity="0.60"
                    StartXRatio="0"
                    StartYRatio="0"
                    Type="Linear">

                    <draw:SkiaGradient.Colors>
                        <Color>#bb000000</Color>
                        <Color>#11CB6235</Color>
                        <Color>#99CB6235</Color>
                        <Color>#FF7C44</Color>
                        <Color>#99CB6235</Color>
                        <Color>#11CB6235</Color>
                        <Color>#bb000000</Color>
                    </draw:SkiaGradient.Colors>

                    <!--<draw:SkiaGradient.ColorPositions>
                        <x:Double>0.0</x:Double>
                        <x:Double>0.48</x:Double>
                        <x:Double>0.50</x:Double>
                        <x:Double>0.50</x:Double>
                        <x:Double>0.50</x:Double>
                        <x:Double>0.52</x:Double>
                        <x:Double>1.0</x:Double>
                    </draw:SkiaGradient.ColorPositions>-->

                    <!--<draw:SkiaGradient.ColorPositions>
                        <x:Double>0.0</x:Double>
                        <x:Double>0.48</x:Double>
                        <x:Double>0.50</x:Double>
                        <x:Double>0.50</x:Double>
                        <x:Double>0.50</x:Double>
                        <x:Double>0.52</x:Double>
                        <x:Double>1.0</x:Double>
                    </draw:SkiaGradient.ColorPositions>-->

                </draw:SkiaGradient>

            </draw:SkiaShape.FillGradient>

            <draw:SkiaShape.Shadows>

                <draw:SkiaShadow
                    Blur="1.5"
                    Opacity="0.15"
                    X="2.5"
                    Y="2.5"
                    Color="#FFFFFF" />

                <draw:SkiaShadow
                    Blur="1.5"
                    Opacity="0.49"
                    X="-2.5"
                    Y="-2.5"
                    Color="#000000" />

            </draw:SkiaShape.Shadows>
        </draw:SkiaShape>

    </draw:SkiaLayout>

</partials:ScaleBarBase>
