﻿<?xml version="1.0" encoding="utf-8"?>
<Package
	xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10"
	xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10"
	xmlns:mp="http://schemas.microsoft.com/appx/2014/phone/manifest"
	xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities"
	IgnorableNamespaces="uap rescap">

	<Identity Name="maui-package-name-placeholder" Publisher="CN=AppoMobi" Version="1.0.3.0" />

	<mp:PhoneIdentity PhoneProductId="19F974FC-4ECF-4895-B2AC-991566818A4B" PhonePublisherId="00000000-0000-0000-0000-000000000000"/>

	<Properties>
		<DisplayName>$placeholder$</DisplayName>
		<PublisherDisplayName>User Name</PublisherDisplayName>
		<Logo>$placeholder$.png</Logo>
	</Properties>

	<Dependencies>
		<TargetDeviceFamily Name="Windows.Universal" MinVersion="10.0.17763.0" MaxVersionTested="10.0.19041.0" />
		<TargetDeviceFamily Name="Windows.Desktop" MinVersion="10.0.17763.0" MaxVersionTested="10.0.19041.0" />
	</Dependencies>

	<Resources>
		<Resource Language="x-generate" />
	</Resources>

	<Applications>
		<Application Id="App" Executable="$targetnametoken$.exe" EntryPoint="$targetentrypoint$">
			<uap:VisualElements
				DisplayName="$placeholder$"
				Description="$placeholder$"
				Square150x150Logo="$placeholder$.png"
				Square44x44Logo="$placeholder$.png"
				BackgroundColor="transparent">
				<uap:DefaultTile Square71x71Logo="$placeholder$.png" Wide310x150Logo="$placeholder$.png" Square310x310Logo="$placeholder$.png" />
				<uap:SplashScreen Image="$placeholder$.png" />
			</uap:VisualElements>
		</Application>
	</Applications>

	<Capabilities>
		<rescap:Capability Name="runFullTrust" />
		<DeviceCapability Name="bluetooth" />
	</Capabilities>

</Package>