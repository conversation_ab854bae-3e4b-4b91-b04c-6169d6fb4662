﻿using Microsoft.EntityFrameworkCore;
using Racebox.Shared.Interfaces;
using Racebox.Shared.Models;
using Racebox.Shared.Services;
using System.Linq.Dynamic.Core;

namespace Racebox.Shared.Extensions
{
    public interface IRepository
    {

    }
    public class Repository : IRepository
    {

    }
    public static class DatabaseExtensions
    {

        public static MeasureResult GetLastSavedResult(this LocalDatabase db)
        {
            var lastSavedResult = db.Results
                .Include(i => i.Ranges)
                .Include(i => i.Distances)
                .Include(i => i.Logs)
                .OrderBy(o => o.Id)
                .LastOrDefault();

            return lastSavedResult;
        }

        /// <summary>
        /// Get ALMOST FULL result
        /// </summary>
        /// <param name="db"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public static async Task<MeasureResult> GetResult(this LocalDatabase db, int id)
        {
            return await db.Results
                .Include(w => w.<PERSON><PERSON><PERSON>)
                .Include(i => i.Ranges)
                .Include(i => i.Distances)
                //.Include(i => i.Logs)
                .FirstOrDefaultAsync(x => x.Id == id);
        }

        /// <summary>
        /// Get FULL result
        /// </summary>
        /// <param name="db"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public static async Task<MeasureResult> GetFullResult(this LocalDatabase db, int id)
        {
            return await db.Results
                .Include(w => w.CurrentWeather)
                .Include(i => i.Ranges)
                .Include(i => i.Distances)
                .Include(i => i.Logs)
                .FirstOrDefaultAsync(x => x.Id == id);
        }

        private static readonly Func<LocalDatabase, int, Task<List<MeasuredLogLine>>> CompiledQueryResultLogs =
            EF.CompileAsyncQuery((LocalDatabase db, int id) =>
                db.Results
                    .Include(r => r.Logs)
                    .Where(r => r.Id == id)
                    .Select(r => r.Logs.ToList())
                    .FirstOrDefault());

        public static async Task<List<MeasuredLogLine>> GetResultLogs(this LocalDatabase db, int id)
        {
            //var item = await db.Results
            //	.Include(i => i.Logs)
            //	.FirstOrDefaultAsync(x => x.Id == id);

            //return item.Logs;

            return await CompiledQueryResultLogs(db, id);
        }


        private static readonly Func<LocalDatabase, int, Task<AppUser>> CompiledQueryGetUser =
            EF.CompileAsyncQuery((LocalDatabase context, int id) =>
                context.Users.FirstOrDefault(x => x.Id == id));

        public static async Task<AppUser> GetUser(this LocalDatabase db, int id)
        {
            return await CompiledQueryGetUser(db, id);
            //return await db.Users.FirstOrDefaultAsync(x => x.Id == id);
        }


        public static async Task UpdateResult(this LocalDatabase db, MeasureResult item)
        {
            db.Entry(item).State = EntityState.Modified;
            await db.SaveChangesAsync();
        }

        public static async Task AddResult(this LocalDatabase db, MeasureResult item)
        {
            db.Results.Add(item);
            await db.SaveChangesAsync();
        }

        public static async Task AddCar(this LocalDatabase db, Car item)
        {
            db.Cars.Add(item);
            await db.SaveChangesAsync();
        }

        public static async Task<Car> GetCar(this LocalDatabase db, int id)
        {
            return await db.Cars.FirstOrDefaultAsync(x => x.Id == id);
        }


        public static async Task UpdateCar(this LocalDatabase db, Car item)
        {
            db.Cars.Update(item);
            await db.SaveChangesAsync();
        }

        private static readonly Func<LocalDatabase, FilteredListRequestDto, Task<List<MeasureResult>>> CompiledQueryMeasuredResults =
            EF.CompileAsyncQuery((LocalDatabase db, FilteredListRequestDto args) =>
                db.Results
                    .Include(r => r.Ranges)
                    .Include(r => r.Distances)
                    .Include(r => r.Logs)
                    .ApplyFilteredListOptions(args)
                    .ToList());

        public static async Task<IList<MeasureResult>> GetMeasuredResults(this LocalDatabase db, FilteredListRequestDto args)
        {
            return await CompiledQueryMeasuredResults(db, args);

            //         var query = db.Results//.Where(x => !x.IsDisabled)
            //		.Include(i => i.Ranges)
            //		.Include(i => i.Distances)
            //		.Include(i => i.Logs);

            //return query.ApplyFilteredListOptions(args).ToList();
        }


        /// <summary>
        /// Use Search, FIlter Order and other parameters options
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="query"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public static IQueryable<T> ApplyFilteredListOptions<T>(this IQueryable<T> query, FilteredListRequestDto parameters) where T : IDomainBaseEntity
        {
            if (!string.IsNullOrEmpty(parameters.Filter))
            {
                var newQuery = query.Where(parameters.Filter);
                query = newQuery;
            }

            if (!string.IsNullOrEmpty(parameters.Search))
            {
                var newQuery = query.ProcessSearch(parameters.Search, parameters.SearchFields);
                query = newQuery;
            }

            var ordered = false;
            if (!string.IsNullOrEmpty(parameters.Order))
            {
                var newQuery = query.ApplyOrder(parameters.Order);
                query = newQuery;
                ordered = true;
            }

            if (!ordered)
            {
                query = query.OrderByDescending(x => x.Id);
            }

            return query;
        }

        public static async Task<AppUser> AddUser(this LocalDatabase db, AppUser dbUser)
        {
            db.Users.Add(dbUser);
            await db.SaveChangesAsync();
            return dbUser;
        }



    }
}
