namespace Racebox.Views.Partials;

public partial class ScaleBarV2
{
	public ScaleBarV2()
	{
		UpdateControl();
		InitializeComponent();
	}


	private double minValue;
	private double maxValue;
	private double leftValue;
	private double rightValue;
	private double _point;

	public void ResetMinMax()
	{
		minValue = 0;
		maxValue = 0;
		UpdateMinMax();
	}


	protected override void OnUpdating()
	{
		base.OnUpdating();

		if (Bar != null)
		{
			UpdateMinMax();
			UpdateBars();
		}

	}


	public void UpdateBars()
	{

		if (leftValue < 0)
		{
			IndicatorLeft.IsVisible = true;
			var offset = _point * leftValue;
			IndicatorLeft.TranslationX = offset / 2.0;
			IndicatorLeft.WidthRequest = -offset;
		}
		else
		{
			IndicatorLeft.IsVisible = false;
		}

		if (rightValue > 0)
		{
			IndicatorRight.IsVisible = true;
			var offset = _point * rightValue;
			IndicatorRight.TranslationX = offset / 2.0;
			IndicatorRight.WidthRequest = offset;
		}
		else
		{
			IndicatorRight.IsVisible = false;
		}
	}

	public void UpdateMinMax()
	{
		if (minValue < 0)
		{
			IndicatorMin.IsVisible = true;
			var offset = _point * minValue;
			IndicatorMin.TranslationX = offset;
		}
		else
		{
			IndicatorMin.IsVisible = false;
		}

		if (maxValue > 0)
		{
			IndicatorMax.IsVisible = true;
			var offset = _point * maxValue;
			IndicatorMax.TranslationX = offset;
		}
		else
		{
			IndicatorMax.IsVisible = false;
		}
	}


	public override void UpdateControl()
	{
		//just clamp here

		if (Bar != null)
		{
			_point = (Bar.Width / 2.0 - ValueSideOffset) / Limit;

			var value = Value;

			if (value < 0)
			{
				if (value < -Limit)
				{
					value = -Limit;
				}

				leftValue = value;
				rightValue = 0;

				if (minValue > value)
				{
					minValue = value;
				}
			}
			else
			if (value > 0)
			{
				if (value > Limit)
				{
					value = Limit;
				}

				rightValue = value;
				leftValue = 0;

				if (maxValue < value)
				{
					maxValue = value;
				}
			}
			else
			{
				leftValue = 0;
				rightValue = 0;
			}
		}


	}



}