...
if (gps.course.isValid()) {
	prevHeading = (curHeading == 0.0) ? gps.course.deg() : curHeading;
	curHeading = gps.course.deg();
	angSpeed = s_angle180(prevHeading, curHeading) * DEG_TO_RAD * 1000 / (curMs - lastMs);
	latAccelG = (latAccelG == 0.0) ? (curSpeed * SPEED_TO_MPS) * angSpeed / GRAV_ACCEL : alphaHeading * latAccelG + (1 - alphaHeading) * (curSpeed * SPEED_TO_MPS) * angSpeed / GRAV_ACCEL;
} else {
	curHeading = 0.0;
	angSpeed = 0.0;
}
...


gps.course.deg() - курс в градусах (double)
DEG_TO_RAD = 0.017453292519943295769236907684886
GRAV_ACCEL = 9.81

double s_angle180 (const double h1, const double h2) {
	double diff = h1 - h2;
	if (fabs(diff) <= 180.0) {
		return diff;
	} else {
		return (h2 > h1) ? 360.0 + diff : diff - 360.0;
	}
}