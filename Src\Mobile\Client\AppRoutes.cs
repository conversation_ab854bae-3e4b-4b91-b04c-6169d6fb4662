﻿using Racebox.UI.Dev;
using Racebox.Views.Navigation.FastShell;
using System.Reflection;

namespace Racebox;

/// <summary>
/// For intellisense to help selecting routes.
/// </summary>
public static class AppRoutes
{
    public static readonly (string Route, Type Type) Tabs = ("tabs", typeof(PageTabs));
    public static readonly (string Route, Type Type) Debug = ("dev", typeof(PageDev));
    public static readonly (string Route, Type Type) DeviceSettings = ("deviceset", typeof(PageSettingsDevice));
    public static readonly (string Route, Type Type) Result = ("result", typeof(PageResult));

    public static IEnumerable<(string, Type)> GetRoutes()
    {
        return typeof(AppRoutes)
            .GetFields(BindingFlags.Public | BindingFlags.Static)
            //.Where(f => f.FieldType == typeof((string, Type)))
            .Select(f => ((string, Type))f.GetValue(null));
    }

    public static string RootDefault
    {
        get
        {
            //return "//" + DeviceSettings.Route;
            //return "//" + Debug.Route;
            return "//" + Tabs.Route;
        }
    }
}