using DrawnUi.Extensions;
using System.Runtime.CompilerServices;
using System.Windows.Input;

namespace Racebox.Views.Partials;

public class SkiaCheckBox : SkiaLayout
{

    public SkiaCheckBox()
    {
        HeightRequest = 22;
        WidthRequest = 22;

        Children.Add(new SkiaSvg()
        {
            TintColor = Color.FromArgb("#CB6336"),
            SvgString = App.Instance.Resources.Get<string>("SvgCheckBoxEmpty"),
            HorizontalOptions = LayoutOptions.Fill,
            VerticalOptions = LayoutOptions.Fill
        });

        _check = new SkiaSvg()
        {
            Tag = "Check",
            TintColor = Color.FromArgb("#CB6336"),
            TranslationX = 0.2f,
            TranslationY = -1,
            VerticalOptions = LayoutOptions.Center,
            HorizontalOptions = LayoutOptions.Center,
            HeightRequest = 16,
            WidthRequest = 16,
            SvgString = App.Instance.Resources.Get<string>("SvgCheck")
        };

        UpdateCheck();

        Children.Add(_check);
    }

    public void UpdateCheck()
    {
        _check.IsVisible = IsToggled;
        //Update();
    }

    protected override void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        base.OnPropertyChanged(propertyName);

        if (propertyName == nameof(IsToggled))
        {
            UpdateCheck();
        }
    }

    public ICommand CommandTapped
    {
        get
        {
            return new Command((object context) =>
            {
                if (Command != null)
                {
                    Command.Execute(IsToggled);
                }
            });
        }
    }



    public static readonly BindableProperty CommandProperty = BindableProperty.Create(nameof(Command),
        typeof(ICommand), typeof(SkiaCheckBox),
        null);
    public ICommand Command
    {
        get { return (ICommand)GetValue(CommandProperty); }
        set { SetValue(CommandProperty, value); }
    }


    //-------------------------------------------------------------
    // IsToggled
    //-------------------------------------------------------------
    private const string nameIsToggled = "IsToggled";
    public static readonly BindableProperty IsToggledProperty = BindableProperty.Create(nameIsToggled,
        typeof(bool), typeof(SkiaCheckBox),
        false, BindingMode.TwoWay,
        propertyChanged: UpdateState);

    private readonly SkiaSvg _check;

    public bool IsToggled
    {
        get { return (bool)GetValue(IsToggledProperty); }
        set { SetValue(IsToggledProperty, value); }
    }

    private static void UpdateState(BindableObject bindable, object oldvalue, object newvalue)
    {
        if (bindable is SkiaCheckBox control)
        {
            //control.MoveThumb(); //todo animate at will
            control.Update();
        }
    }

    public TouchActionEventHandler OnTapped
    {
        get
        {
            return OnTapped_Handler;
        }
    }

    public void OnTapped_Handler(object sender, TouchActionEventArgs args)
    {
        if (sender is DrawnView control)
        {
            IsToggled = !IsToggled;
            //Box.PlayRippleAnimation(control, Colors.White, args.Location.X, args.Location.Y);
        }
    }
}