﻿using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;

namespace Racebox.Models;

public class ActionOption : INotifyPropertyChanged
{

    public ActionOption()
    {
        ZoomIcon = 1;
    }


    #region VALUE DIFFEREMT TYPES
    public int PresentationMode { get; set; }

    private bool _BooleanValue;
    public bool BooleanValue
    {
        get
        {
            return _BooleanValue;
        }
        set
        {
            if (_BooleanValue != value)
            {
                _BooleanValue = value;
                OnPropertyChanged();
            }
        }
    }

    private string _SubTitle;
    public string SubTitle
    {
        get
        {
            return _SubTitle;
        }
        set
        {
            if (_SubTitle != value)
            {
                _SubTitle = value;
                OnPropertyChanged();
            }
        }
    }

    #endregion

    public string SvgResource { get; set; }
    public string Title { get; set; }

    public double ZoomIcon { get; set; }

    //    public Action<ActionOption>Action { get; set; }
    public string Hint { get; set; }
    public BindableWrapper Item { get; set; }

    public BindableWrapper Notifications { get; set; }
    public ICommand Command { get; set; }
    public object CommandParameter { get; set; }

    private bool _IsVisible = true;
    public bool IsVisible
    {
        get { return _IsVisible; }
        set
        {
            if (_IsVisible != value)
            {
                _IsVisible = value;
                OnPropertyChanged();
            }
        }
    }

    public Action<object> Update { get; set; }

    #region INotifyPropertyChanged
    public event PropertyChangedEventHandler PropertyChanged;
    protected void OnPropertyChanged([CallerMemberName] string propertyName = "")
    {
        var changed = PropertyChanged;
        if (changed == null)
            return;

        changed.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
    #endregion

}