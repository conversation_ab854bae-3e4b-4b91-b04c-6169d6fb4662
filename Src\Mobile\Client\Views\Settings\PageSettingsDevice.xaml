﻿<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="Racebox.Views.PageSettingsDevice"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:partials="clr-namespace:Racebox.Views.Partials"
    xmlns:racebox="clr-namespace:Racebox"
    xmlns:viewModels="clr-namespace:Racebox.ViewModels"
    xmlns:drawn="clr-namespace:Racebox.Views.Drawn"
    xmlns:views="clr-namespace:Racebox.Views"
    xmlns:models="clr-namespace:Racebox.Models">

    <ContentPage.Resources>
        <ResourceDictionary>

            <draw:SkiaGradient
                x:Key="SkiaSeparatorGradient"
                EndXRatio="1"
                EndYRatio="0"
                StartXRatio="0"
                StartYRatio="0"
                Type="Linear">
                <draw:SkiaGradient.Colors>
                    <Color>#00E8E3D7</Color>
                    <Color>#99E8E3D7</Color>
                    <Color>#00E8E3D7</Color>
                </draw:SkiaGradient.Colors>
            </draw:SkiaGradient>

            <Style
                x:Key="SkiaSeparatorStyle"
                Class="SkiaSeparatorStyle"
                TargetType="draw:SkiaShape">
                <Setter Property="FillGradient" Value="{StaticResource SkiaSeparatorGradient}" />
                <Setter Property="Margin" Value="-20,0,-20,0" />
                <Setter Property="HeightRequest" Value="1" />
                <Setter Property="StrokeWidth" Value="1" />
                <Setter Property="IsClippedToBounds" Value="True" />
                <Setter Property="UseCache" Value="Operations" />
                <Setter Property="VerticalOptions" Value="End" />
                <Setter Property="HorizontalOptions" Value="Fill" />
            </Style>

            <Style
                x:Key="LabelStyleOption"
                TargetType="Label">
                <Setter Property="FontSize" Value="14" />
                <Setter Property="VerticalOptions" Value="Center" />
                <Setter Property="MaxLines" Value="2" />
                <Setter Property="LineBreakMode" Value="TailTruncation" />
            </Style>

            <Style
                x:Key="LabelStyleOptionValue"
                BasedOn="{StaticResource LabelStyleOption}"
                TargetType="Label">
                <Setter Property="HorizontalOptions" Value="FillAndExpand" />
                <Setter Property="HorizontalTextAlignment" Value="End" />
                <Setter Property="TextColor" Value="{StaticResource Accent}" />
            </Style>

            <Style
                x:Key="SkiaLabelStyleOptionValue"
                Class="SkiaLabelStyleOptionValue"
                TargetType="draw:SkiaLabel">
                <Setter Property="FontFamily" Value="FontText" />
                <Setter Property="FontSize" Value="14" />
                <Setter Property="VerticalOptions" Value="Center" />
                <Setter Property="AutoSize" Value="FitHorizontal" />
                <Setter Property="HorizontalOptions" Value="End" />
                <Setter Property="TextColor" Value="{StaticResource Accent}" />
            </Style>

            <Style
                x:Key="SkiaStyleOptionsStack"
                Class="SkiaStyleOptionsStack"
                TargetType="draw:SkiaLayout">
                <Setter Property="UseCache" Value="Image" />
                <Setter Property="IsClippedToBounds" Value="True" />
                <Setter Property="MinimumHeightRequest" Value="64" />
                <Setter Property="HorizontalOptions" Value="Fill" />
                <Setter Property="Padding" Value="16,0" />
            </Style>

        </ResourceDictionary>
    </ContentPage.Resources>

    <Grid
        x:DataType="viewModels:DeviceSettingsViewModel"
        BackgroundColor="Transparent"
        HorizontalOptions="Fill"
        RowDefinitions="*"
        VerticalOptions="FillAndExpand">

        <!--  page background with gradient and frame  -->
        <draw:Canvas
            HorizontalOptions="Fill"
            Tag="BackgroundWithFrame"
            VerticalOptions="Fill">

            <partials:GradientToBorder
                HorizontalOptions="Fill"
                MissingHeight="{Binding NavbarModel.BottomTabsHeightRequest}"
                VerticalOptions="Fill">

                <drawn:EmbossedFrameDrawn
                    x:Name="DesignFrame"
                    BackgroundColor="Transparent"
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill" />

            </partials:GradientToBorder>
        </draw:Canvas>

        <draw:Canvas
            Gestures="Lock"
            RenderingMode="Accelerated"
            HorizontalOptions="Fill"
            Tag="DeviceSettings"
            VerticalOptions="FillAndExpand">

            <draw:SkiaLayout
                HorizontalOptions="Fill"
                VerticalOptions="Fill">

                <draw:SkiaLayout
                    Margin="12,16,12,8"
                    HorizontalOptions="Fill"
                    Spacing="0"
                    Type="Column"
                    VerticalOptions="Fill">

                    <!--  NAVBAR  -->
                    <partials:SkiaNavBar
                        x:Name="NavBar"
                        Margin="0,0,1,0"
                        HeightRequest="65"
                        HorizontalOptions="Fill"
                        UseCache="Image">

                        <draw:SkiaSvg
                            Margin="16,0,16,0"
                            HeightRequest="16"
                            HorizontalOptions="Start"
                            SvgString="{StaticResource SvgGoBack}"
                            TintColor="#CB6336"
                            VerticalOptions="Center"
                            WidthRequest="16" />

                        <draw:SkiaHotspot
                            CommandTapped="{Binding NavbarModel.CommandGoBack, Mode=OneTime}"
                            HorizontalOptions="Start"
                            TransformView="{x:Reference NavBar}"
                            WidthRequest="44" />

                        <draw:SkiaLabel
                            Margin="48,0,16,0"
                            FontSize="14"
                            LineBreakMode="TailTruncation"
                            MaxLines="1"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Tag="NavTitle"
                            Text="{Binding Title}"
                            TextColor="#E8E3D7"
                            TranslationY="1"
                            VerticalOptions="Center" />

                        <draw:SkiaSvg
                            Margin="0,0,20,0"
                            HeightRequest="16"
                            HorizontalOptions="End"
                            IsVisible="{Binding HasChanges}"
                            SvgString="{StaticResource SvgCheck}"
                            TintColor="#CB6336"
                            VerticalOptions="Center"
                            WidthRequest="18" />

                        <draw:SkiaHotspot
                            CommandTapped="{Binding CommandApply, Mode=OneTime}"
                            HorizontalOptions="End"
                            IsVisible="{Binding HasChanges}"
                            TransformView="{x:Reference NavBar}"
                            WidthRequest="56" />

                        <!--  LINE HORIZONTAL  -->
                        <draw:SkiaShape
                            Margin="-16,0"
                            BackgroundColor="Black"
                            CornerRadius="0"
                            HeightRequest="1"
                            HorizontalOptions="Fill"
                            StrokeWidth="0"
                            VerticalOptions="End">
                            <draw:SkiaShape.FillGradient>

                                <draw:SkiaGradient
                                    EndXRatio="1"
                                    EndYRatio="0"
                                    StartXRatio="0"
                                    StartYRatio="0"
                                    Type="Linear">
                                    <draw:SkiaGradient.Colors>
                                        <Color>#00E8E3D7</Color>
                                        <Color>#99E8E3D7</Color>
                                        <Color>#00E8E3D7</Color>
                                    </draw:SkiaGradient.Colors>
                                </draw:SkiaGradient>

                            </draw:SkiaShape.FillGradient>
                        </draw:SkiaShape>

                    </partials:SkiaNavBar>

                    <!--  CONTENT SCROLL  -->
                    <draw:SkiaScroll
                        x:Name="MainScroll"
                        HorizontalOptions="Fill"
                        LockChildrenGestures="PassTap"
                        RefreshDistanceLimit="4"
                        VerticalOptions="Fill">

                        <draw:SkiaLayout
                            HorizontalOptions="Fill"
                            Type="Column">

                            <!--  options  -->
                            <draw:SkiaLayout
                                x:Name="StackOptions"
                                Padding="2"
                                HorizontalOptions="Fill"
                                MeasureItemsStrategy="MeasureAll"
                                ItemsSource="{Binding SettingsFields}"
                                RecyclingTemplate="Disabled"
                                Spacing="0"
                                Tag="ScrollStackWrapper"
                                Type="Column"
                                UseCache="ImageComposite">

                                <draw:SkiaLayout.ItemTemplate>
                                    <DataTemplate x:DataType="models:ActionOption">

                                        <draw:SkiaLayout
                                            draw:AddGestures.AnimationTapped="Ripple"
                                            draw:AddGestures.CommandTapped="{Binding Command, Mode=OneTime}"
                                            draw:AddGestures.TouchEffectColor="White"
                                            IsVisible="{Binding IsVisible}"
                                            Style="{StaticResource SkiaStyleOptionsStack}"
                                            UseCache="Image">

                                            <!--  TITLE  -->
                                            <draw:SkiaLabel
                                                Margin="0,0,120,0"
                                                FontSize="14"
                                                MaxLines="3"
                                                Style="{StaticResource SkiaLabelDefaultStyle}"
                                                Text="{Binding Title}"
                                                TranslationY="0"
                                                UseCache="Operations"
                                                VerticalOptions="Center" />

                                            <!--  SUBTITLE  -->
                                            <draw:SkiaLabel
                                                FontSize="14"
                                                HorizontalOptions="End"
                                                HorizontalTextAlignment="End"
                                                MaxLines="2"
                                                Style="{StaticResource SkiaLabelDefaultStyle}"
                                                Text="{Binding SubTitle}"
                                                TextColor="{StaticResource Accent}"
                                                TranslationX="-24"
                                                TranslationY="0"
                                                UseCache="Operations"
                                                VerticalOptions="Center"
                                                WidthRequest="120" />

                                            <!--  BOOL  -->
                                            <partials:SkiaCheckBox
                                                HorizontalOptions="End"
                                                IsToggled="{Binding BooleanValue}"
                                                IsVisible="False"
                                                UseCache="Operations"
                                                VerticalOptions="Center">
                                                <draw:SkiaControl.Triggers>
                                                    <DataTrigger
                                                        Binding="{Binding PresentationMode, Converter={StaticResource CompareIntegersConverter}, ConverterParameter=1}"
                                                        TargetType="draw:SkiaControl"
                                                        Value="True">
                                                        <Setter Property="IsVisible" Value="True" />
                                                    </DataTrigger>
                                                </draw:SkiaControl.Triggers>
                                            </partials:SkiaCheckBox>

                                            <!--  DEFAULT  -->
                                            <draw:SkiaSvg
                                                HeightRequest="16"
                                                HorizontalOptions="End"
                                                IsVisible="False"
                                                SvgString="{StaticResource SvgOptionNext}"
                                                TintColor="{StaticResource Accent}"
                                                UseCache="Operations"
                                                VerticalOptions="Center"
                                                WidthRequest="16">
                                                <draw:SkiaControl.Triggers>
                                                    <DataTrigger
                                                        Binding="{Binding PresentationMode, Converter={StaticResource CompareIntegersConverter}, ConverterParameter=0}"
                                                        TargetType="draw:SkiaControl"
                                                        Value="True">
                                                        <Setter Property="IsVisible" Value="True" />
                                                    </DataTrigger>
                                                </draw:SkiaControl.Triggers>
                                            </draw:SkiaSvg>

                                            <draw:SkiaShape
                                                Margin="-16,0"
                                                Style="{StaticResource SkiaSeparatorStyle}"
                                                UseCache="Operations" />

                                        </draw:SkiaLayout>

                                    </DataTemplate>
                                </draw:SkiaLayout.ItemTemplate>

                            </draw:SkiaLayout>

                            <draw:SkiaLabel
                                Margin="16"
                                HorizontalOptions="Center"
                                HorizontalTextAlignment="Center"
                                Style="{StaticResource SkiaEditorFieldTitle}"
                                Text="{Binding DeviceDescription}"
                                UseCache="Operations" />

                        </draw:SkiaLayout>

                    </draw:SkiaScroll>

                </draw:SkiaLayout>

                <!--<editor:EditorLed
                    BackgroundColor="Black"
                    HeightRequest="200"
                    IsVisible="True"
                    VerticalOptions="Center" />-->

                <draw:SkiaLabelFps
                    Margin="32"
                    ForceRefresh="False"
                    HorizontalOptions="End"
                    IsVisible="{x:Static racebox:MauiProgram.ShowDebugInfo}"
                    Rotation="-45"
                    VerticalOptions="End"
                    ZIndex="100" />



                <draw:SkiaLayout
                    BackgroundColor="#44000000"
                    HorizontalOptions="Fill"
                    IsVisible="{Binding IsBusy}"
                    VerticalOptions="Fill">

                    <draw:SkiaLottie
                        AutoPlay="True"
                        HorizontalOptions="Center"
                        LockRatio="1"
                        Repeat="-1"
                        Source="Lottie/Loader.json"
                        Tag="Loader"
                        VerticalOptions="Center"
                        WidthRequest="32" />
                </draw:SkiaLayout>

            </draw:SkiaLayout>

        </draw:Canvas>

    </Grid>

</views:BasePage>
