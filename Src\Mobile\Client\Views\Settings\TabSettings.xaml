﻿<?xml version="1.0" encoding="utf-8" ?>
<Grid
    x:Class="Racebox.Views.TabSettings"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:partials="clr-namespace:Racebox.Views.Partials"
    xmlns:racebox="clr-namespace:Racebox"
    xmlns:strings="clr-namespace:Racebox.Shared.Strings;assembly=Racebox.Shared"
    xmlns:viewModels="clr-namespace:Racebox.ViewModels"
    xmlns:drawn="clr-namespace:Racebox.Views.Drawn"
    x:DataType="viewModels:SettingsViewModel"
    HorizontalOptions="Fill"
    RowDefinitions="*"
    VerticalOptions="FillAndExpand">

    <Grid.Resources>
        <ResourceDictionary>

            <draw:SkiaGradient
                x:Key="SkiaSeparatorGradient"
                EndXRatio="1"
                EndYRatio="0"
                StartXRatio="0"
                StartYRatio="0"
                Type="Linear">
                <draw:SkiaGradient.Colors>
                    <Color>#00E8E3D7</Color>
                    <Color>#99E8E3D7</Color>
                    <Color>#00E8E3D7</Color>
                </draw:SkiaGradient.Colors>
            </draw:SkiaGradient>

            <Style
                x:Key="SkiaSeparatorStyle"
                Class="SkiaSeparatorStyle"
                TargetType="draw:SkiaShape">
                <Setter Property="FillGradient" Value="{StaticResource SkiaSeparatorGradient}" />
                <Setter Property="Margin" Value="-20,0,-20,0" />
                <Setter Property="HeightRequest" Value="1" />
                <Setter Property="StrokeWidth" Value="1" />
                <Setter Property="IsClippedToBounds" Value="True" />
                <Setter Property="UseCache" Value="Operations" />
                <Setter Property="VerticalOptions" Value="End" />
                <Setter Property="HorizontalOptions" Value="Fill" />
            </Style>

            <Style
                x:Key="LabelStyleOption"
                TargetType="Label">
                <Setter Property="FontSize" Value="14" />
                <Setter Property="VerticalOptions" Value="Center" />
                <Setter Property="MaxLines" Value="3" />
                <Setter Property="LineBreakMode" Value="TailTruncation" />
            </Style>

            <Style
                x:Key="LabelStyleOptionValue"
                BasedOn="{StaticResource LabelStyleOption}"
                TargetType="Label">
                <Setter Property="HorizontalOptions" Value="FillAndExpand" />
                <Setter Property="HorizontalTextAlignment" Value="End" />
                <Setter Property="TextColor" Value="{StaticResource Accent}" />
            </Style>

            <Style
                x:Key="SkiaLabelStyleOptionValue"
                Class="SkiaLabelStyleOptionValue"
                TargetType="draw:SkiaLabel">
                <Setter Property="FontFamily" Value="FontText" />
                <Setter Property="FontSize" Value="14" />
                <Setter Property="VerticalOptions" Value="Center" />
                <Setter Property="AutoSize" Value="FitHorizontal" />
                <Setter Property="HorizontalOptions" Value="End" />
                <Setter Property="TextColor" Value="{StaticResource Accent}" />
            </Style>

            <Style
                x:Key="SkiaStyleOptionsStack"
                Class="SkiaStyleOptionsStack"
                TargetType="draw:SkiaLayout">
                <Setter Property="UseCache" Value="Image" />
                <Setter Property="IsClippedToBounds" Value="True" />
                <Setter Property="HeightRequest" Value="64" />
                <Setter Property="HorizontalOptions" Value="Fill" />
                <Setter Property="Padding" Value="16,0" />
            </Style>

        </ResourceDictionary>
    </Grid.Resources>

    <!--  page background with gradient and frame  -->
    <draw:Canvas
        HorizontalOptions="Fill"
        Tag="BackgroundWithFrame"
        VerticalOptions="Fill">

        <partials:GradientToBorder
            HorizontalOptions="Fill"
            MissingHeight="{Binding NavbarModel.BottomTabsHeightRequest}"
            VerticalOptions="Fill">

            <drawn:EmbossedFrameDrawn
                x:Name="DesignFrame"
                BackgroundColor="Transparent"
                HorizontalOptions="Fill"
                VerticalOptions="Fill" />

        </partials:GradientToBorder>
    </draw:Canvas>

    <draw:Canvas
        Gestures="Lock"
        RenderingMode="Accelerated"
        HorizontalOptions="Fill"
        Tag="TabSettings"
        VerticalOptions="Fill">

        <draw:SkiaLayout
            HorizontalOptions="Fill"
            Tag="Container"
            VerticalOptions="Fill">



            <!--<drawn:EmbossedFrameDrawn
                BackgroundColor="Transparent"
                HorizontalOptions="Fill"
                UseCache="GPU"
                VerticalOptions="Fill" />-->

            <draw:SkiaScroll
                x:Name="MainScroll"
                Margin="12,16,12,8"
                HorizontalOptions="Fill"
                LockChildrenGestures="PassTap"
                RefreshDistanceLimit="4"
                VerticalOptions="Fill">

                <!--  options  -->
                <draw:SkiaLayout
                    Padding="2"
                    HorizontalOptions="Fill"
                    Spacing="0"
                    Tag="ScrollStackWrapper"
                    Type="Column"
                    UseCache="ImageComposite">

                    <!--  Roll-Out  -->
                    <draw:SkiaLayout
                        draw:AddGestures.AnimationTapped="Ripple"
                        draw:AddGestures.CommandTapped="{Binding CommandToggleRollOut, Mode=OneTime}"
                        draw:AddGestures.TouchEffectColor="White"
                        IsClippedToBounds="True"
                        Style="{StaticResource SkiaStyleOptionsStack}"
                        Tag="StackRollOut"
                        UseCache="Image">

                        <draw:SkiaLabel
                            Margin="0,0,99,0"
                            FontSize="14"
                            IsClippedToBounds="True"
                            MaxLines="2"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Text="{x:Static strings:ResStrings.RollOut}"
                            UseCache="Operations"
                            VerticalOptions="Center" />

                        <partials:SkiaCheckBox
                            Margin="0"
                            HorizontalOptions="End"
                            IsClippedToBounds="True"
                            IsToggled="{Binding RollOut}"
                            UseCache="Operations"
                            VerticalOptions="Center" />

                        <draw:SkiaShape
                            Style="{StaticResource SkiaSeparatorStyle}"
                            Tag="Separator" />

                    </draw:SkiaLayout>

                    <!--  Sound  -->
                    <draw:SkiaLayout
                        draw:AddGestures.AnimationTapped="Ripple"
                        draw:AddGestures.CommandTapped="{Binding CommandToggleSound, Mode=OneTime}"
                        draw:AddGestures.TouchEffectColor="White"
                        IsClippedToBounds="True"
                        Style="{StaticResource SkiaStyleOptionsStack}"
                        UseCache="Image">


                        <draw:SkiaLabel
                            Margin="0,0,99,0"
                            FontSize="14"
                            MaxLines="2"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Text="{x:Static strings:ResStrings.Sound}"
                            TranslationY="0"
                            UseCache="Operations"
                            VerticalOptions="Center" />

                        <partials:SkiaCheckBox
                            HorizontalOptions="End"
                            IsToggled="{Binding Sound}"
                            UseCache="Operations"
                            VerticalOptions="Center" />

                        <draw:SkiaShape
                            Margin="-16,0"
                            Style="{StaticResource SkiaSeparatorStyle}" />

                    </draw:SkiaLayout>

                    <!--  Speak  -->
                    <draw:SkiaLayout
                        draw:AddGestures.AnimationTapped="Ripple"
                        draw:AddGestures.CommandTapped="{Binding CommandToggleSpeak, Mode=OneTime}"
                        draw:AddGestures.TouchEffectColor="White"
                        IsClippedToBounds="True"
                        Style="{StaticResource SkiaStyleOptionsStack}"
                        UseCache="Image">


                        <draw:SkiaLabel
                            Margin="0,0,99,0"
                            FontSize="14"
                            MaxLines="2"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Text="{x:Static strings:ResStrings.SettingsCanSpeak}"
                            TranslationY="0"
                            UseCache="Operations"
                            VerticalOptions="Center" />

                        <partials:SkiaCheckBox
                            HorizontalOptions="End"
                            IsToggled="{Binding CanSay}"
                            UseCache="Operations"
                            VerticalOptions="Center" />

                        <draw:SkiaShape
                            Margin="-16,0"
                            Style="{StaticResource SkiaSeparatorStyle}" />

                    </draw:SkiaLayout>

                    <!--  Time24  -->
                    <draw:SkiaLayout
                        draw:AddGestures.AnimationTapped="Ripple"
                        draw:AddGestures.CommandTapped="{Binding CommandToggleTime24, Mode=OneTime}"
                        draw:AddGestures.TouchEffectColor="White"
                        IsClippedToBounds="True"
                        Style="{StaticResource SkiaStyleOptionsStack}"
                        UseCache="Image">


                        <draw:SkiaLabel
                            Margin="0,0,99,0"
                            FontSize="14"
                            MaxLines="2"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Text="{x:Static strings:ResStrings.Time24}"
                            TranslationY="0"
                            UseCache="Operations"
                            VerticalOptions="Center" />

                        <partials:SkiaCheckBox
                            HorizontalOptions="End"
                            IsToggled="{Binding Time24}"
                            UseCache="Operations"
                            VerticalOptions="Center" />

                        <draw:SkiaShape Style="{StaticResource SkiaSeparatorStyle}" />


                    </draw:SkiaLayout>

                    <!--  CommandEditCustomMetrics  -->
                    <draw:SkiaLayout
                        draw:AddGestures.AnimationTapped="Ripple"
                        draw:AddGestures.CommandTapped="{Binding CommandEditCustomMetrics, Mode=OneTime}"
                        draw:AddGestures.TouchEffectColor="White"
                        IsClippedToBounds="True"
                        Style="{StaticResource SkiaStyleOptionsStack}"
                        UseCache="Image">

                        <draw:SkiaLabel
                            Margin="0,0,90,0"
                            FontSize="14"
                            HorizontalOptions="Fill"
                            MaxLines="2"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Text="{x:Static strings:ResStrings.CustomMetrics}"
                            TranslationY="0"
                            UseCache="Operations"
                            VerticalOptions="Center" />

                        <draw:SkiaSvg
                            HeightRequest="16"
                            HorizontalOptions="End"
                            SvgString="{StaticResource SvgOptionNext}"
                            TintColor="{StaticResource Accent}"
                            UseCache="Operations"
                            VerticalOptions="Center"
                            WidthRequest="16" />



                        <draw:SkiaShape
                            Margin="-16,0"
                            Style="{StaticResource SkiaSeparatorStyle}" />

                    </draw:SkiaLayout>

                    <!--  CommandSelectCar  -->
                    <draw:SkiaLayout
                        draw:AddGestures.AnimationTapped="Ripple"
                        draw:AddGestures.CommandTapped="{Binding CommandSelectCar, Mode=OneTime}"
                        draw:AddGestures.TouchEffectColor="White"
                        IsClippedToBounds="True"
                        Style="{StaticResource SkiaStyleOptionsStack}"
                        UseCache="Image">


                        <draw:SkiaLabel
                            Margin="0,0,99,0"
                            FontSize="14"
                            HorizontalOptions="Fill"
                            MaxLines="2"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Text="{x:Static strings:ResStrings.Car}"
                            TranslationY="0"
                            UseCache="Operations"
                            VerticalOptions="Center" />


                        <draw:SkiaLabel
                            FontSize="14"
                            HorizontalOptions="End"
                            HorizontalTextAlignment="End"
                            MaxLines="2"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Tag="CarName"
                            Text="{Binding DisplayCar}"
                            TextColor="{StaticResource Accent}"
                            TranslationX="-24"
                            TranslationY="0"
                            UseCache="Operations"
                            VerticalOptions="Center"
                            WidthRequest="120" />

                        <draw:SkiaSvg
                            HeightRequest="16"
                            HorizontalOptions="End"
                            SvgString="{StaticResource SvgOptionNext}"
                            TintColor="{StaticResource Accent}"
                            UseCache="Operations"
                            VerticalOptions="Center"
                            WidthRequest="16" />

                        <draw:SkiaShape
                            Margin="-16,0"
                            Style="{StaticResource SkiaSeparatorStyle}" />


                    </draw:SkiaLayout>

                    <!--  CommandSelectUser  -->
                    <draw:SkiaLayout
                        draw:AddGestures.AnimationTapped="Ripple"
                        draw:AddGestures.CommandTapped="{Binding CommandSelectUser, Mode=OneTime}"
                        draw:AddGestures.TouchEffectColor="White"
                        IsClippedToBounds="True"
                        Style="{StaticResource SkiaStyleOptionsStack}"
                        UseCache="Image">

                        <draw:SkiaLabel
                            Margin="0,0,99,0"
                            FontSize="14"
                            HorizontalOptions="Fill"
                            MaxLines="2"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Text="{x:Static strings:ResStrings.User}"
                            TranslationY="0"
                            UseCache="Operations"
                            VerticalOptions="Center" />

                        <draw:SkiaLabel
                            FontSize="14"
                            HorizontalOptions="End"
                            HorizontalTextAlignment="End"
                            MaxLines="2"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Text="{Binding DisplayUser}"
                            TextColor="{StaticResource Accent}"
                            TranslationX="-24"
                            TranslationY="0"
                            UseCache="Operations"
                            VerticalOptions="Center"
                            WidthRequest="120" />

                        <draw:SkiaSvg
                            HeightRequest="16"
                            HorizontalOptions="End"
                            SvgString="{StaticResource SvgOptionNext}"
                            TintColor="{StaticResource Accent}"
                            UseCache="Operations"
                            VerticalOptions="Center"
                            WidthRequest="16" />



                        <draw:SkiaShape
                            Margin="-16,0"
                            Style="{StaticResource SkiaSeparatorStyle}" />

                    </draw:SkiaLayout>

                    <!--  Logs Format  -->
                    <!--<draw:SkiaLayout
                            draw:AddGestures.AnimationTapped="Ripple"
                            draw:AddGestures.CommandTapped="{Binding CommandSelectLogFormat, Mode=OneTime}"
                            draw:AddGestures.TouchEffectColor="White"
                            IsClippedToBounds="True"
                            Style="{StaticResource SkiaStyleOptionsStack}"
                            UseCache="GPU">

                            <draw:SkiaLabel
                                Margin="0,0,99,0"
                                FontSize="14"
                                MaxLines="2"
                                Style="{StaticResource SkiaLabelDefaultStyle}"
                                Text="{x:Static strings:ResStrings.ExportFormat}"
                                TranslationY="0"
                                VerticalOptions="Center" />


                            <draw:SkiaLabel
                                FontSize="14"
                                HorizontalOptions="End"
                                HorizontalTextAlignment="End"
                                MaxLines="2"
                                Style="{StaticResource SkiaLabelDefaultStyle}"
                                Text="{Binding DisplayLogFormat}"
                                TextColor="{StaticResource Accent}"
                                TranslationX="-24"
                                TranslationY="0"
                                VerticalOptions="Center"
                                WidthRequest="75" />

                            <draw:SkiaSvg
                                HeightRequest="16"
                                HorizontalOptions="End"
                                SvgString="{StaticResource SvgOptionNext}"
                                TintColor="{StaticResource Accent}"
                                VerticalOptions="Center"
                                WidthRequest="16" />

                            <draw:SkiaShape
                                Margin="-16,0"
                                Style="{StaticResource SkiaSeparatorStyle}" />



                        </draw:SkiaLayout>-->

                    <!--  CommandSelectMetricsUnit  -->
                    <draw:SkiaLayout
                        draw:AddGestures.AnimationTapped="Ripple"
                        draw:AddGestures.CommandTapped="{Binding CommandSelectMetricsUnit, Mode=OneTime}"
                        draw:AddGestures.TouchEffectColor="White"
                        IsClippedToBounds="True"
                        Style="{StaticResource SkiaStyleOptionsStack}"
                        UseCache="Image">

                        <draw:SkiaLabel
                            Margin="0,0,99,0"
                            FontSize="14"
                            MaxLines="2"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Text="{x:Static strings:ResStrings.MeasuringSystem}"
                            TranslationY="0"
                            UseCache="Operations"
                            VerticalOptions="Center" />


                        <draw:SkiaLabel
                            FontSize="14"
                            HorizontalOptions="End"
                            HorizontalTextAlignment="End"
                            MaxLines="2"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Text="{Binding DisplayMetrics}"
                            TextColor="{StaticResource Accent}"
                            TranslationX="-24"
                            TranslationY="0"
                            UseCache="Operations"
                            VerticalOptions="Center"
                            WidthRequest="75" />

                        <draw:SkiaSvg
                            HeightRequest="16"
                            HorizontalOptions="End"
                            SvgString="{StaticResource SvgOptionNext}"
                            TintColor="{StaticResource Accent}"
                            UseCache="Operations"
                            VerticalOptions="Center"
                            WidthRequest="16" />


                        <draw:SkiaShape
                            Margin="-16,0"
                            Style="{StaticResource SkiaSeparatorStyle}" />



                    </draw:SkiaLayout>

                    <!--  Language  -->
                    <draw:SkiaLayout
                        draw:AddGestures.AnimationTapped="Ripple"
                        draw:AddGestures.CommandTapped="{Binding CommandSelectLanguage, Mode=OneTime}"
                        draw:AddGestures.TouchEffectColor="White"
                        IsClippedToBounds="True"
                        Style="{StaticResource SkiaStyleOptionsStack}"
                        UseCache="Image">

                        <draw:SkiaLabel
                            Margin="0,0,99,0"
                            FontSize="14"
                            MaxLines="2"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Text="{x:Static strings:ResStrings.Language}"
                            TranslationY="0"
                            UseCache="Operations"
                            VerticalOptions="Center" />


                        <draw:SkiaLabel
                            FontSize="14"
                            HorizontalOptions="End"
                            HorizontalTextAlignment="End"
                            MaxLines="2"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Text="{Binding DisplayLanguage}"
                            TextColor="{StaticResource Accent}"
                            TranslationX="-24"
                            TranslationY="0"
                            UseCache="Operations"
                            VerticalOptions="Center"
                            WidthRequest="75" />

                        <draw:SkiaSvg
                            HeightRequest="16"
                            HorizontalOptions="End"
                            SvgString="{StaticResource SvgOptionNext}"
                            TintColor="{StaticResource Accent}"
                            UseCache="Operations"
                            VerticalOptions="Center"
                            WidthRequest="16" />



                        <draw:SkiaShape
                            Margin="-16,0"
                            Style="{StaticResource SkiaSeparatorStyle}" />

                    </draw:SkiaLayout>

                    <!--  DEVICE  -->
                    <draw:SkiaLayout
                        draw:AddGestures.AnimationTapped="Ripple"
                        draw:AddGestures.CommandTapped="{Binding CommandDeviceSettings, Mode=OneTime}"
                        draw:AddGestures.TouchEffectColor="White"
                        IsClippedToBounds="True"
                        Style="{StaticResource SkiaStyleOptionsStack}"
                        UseCache="Image">

                        <draw:SkiaLabel
                            Margin="0,0,99,0"
                            FontSize="14"
                            MaxLines="2"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Text="{x:Static strings:ResStrings.DeviceSettings}"
                            TranslationY="0"
                            UseCache="Operations"
                            VerticalOptions="Center" />

                        <draw:SkiaSvg
                            HeightRequest="16"
                            HorizontalOptions="End"
                            SvgString="{StaticResource SvgOptionNext}"
                            TintColor="{StaticResource Accent}"
                            UseCache="Operations"
                            VerticalOptions="Center"
                            WidthRequest="16" />



                        <draw:SkiaShape
                            Margin="-16,0"
                            Style="{StaticResource SkiaSeparatorStyle}" />

                    </draw:SkiaLayout>

                    <!--  Firmware Help  -->
                    <draw:SkiaLayout
                        draw:AddGestures.AnimationTapped="Ripple"
                        draw:AddGestures.CommandTapped="{Binding CommandFirmwareHelp, Mode=OneTime}"
                        draw:AddGestures.TouchEffectColor="White"
                        IsClippedToBounds="True"
                        Style="{StaticResource SkiaStyleOptionsStack}"
                        Tag="StackApp"
                        UseCache="Image">

                        <draw:SkiaLabel
                            Margin="0,0,99,0"
                            FontSize="14"
                            MaxLines="2"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Text="{x:Static strings:ResStrings.FirmwareHowToTitle}"
                            TranslationY="0"
                            UseCache="Operations"
                            VerticalOptions="Center" />

                        <draw:SkiaSvg
                            HeightRequest="16"
                            HorizontalOptions="End"
                            SvgString="{StaticResource SvgOptionNext}"
                            TintColor="{StaticResource Accent}"
                            UseCache="Operations"
                            VerticalOptions="Center"
                            WidthRequest="16" />


                        <draw:SkiaShape
                            Margin="-16,0"
                            Style="{StaticResource SkiaSeparatorStyle}" />

                    </draw:SkiaLayout>

                    <!--  About App  -->
                    <draw:SkiaLayout
                        draw:AddGestures.AnimationTapped="Ripple"
                        draw:AddGestures.CommandTapped="{Binding CommandAboutApp, Mode=OneTime}"
                        draw:AddGestures.TouchEffectColor="White"
                        IsClippedToBounds="True"
                        Style="{StaticResource SkiaStyleOptionsStack}"
                        Tag="StackApp"
                        UseCache="Image">

                        <draw:SkiaLabel
                            Margin="0,0,99,0"
                            FontSize="14"
                            MaxLines="2"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Text="{x:Static strings:ResStrings.AboutApp}"
                            TranslationY="0"
                            UseCache="Operations"
                            VerticalOptions="Center" />

                        <draw:SkiaSvg
                            HeightRequest="16"
                            HorizontalOptions="End"
                            SvgString="{StaticResource SvgOptionNext}"
                            TintColor="{StaticResource Accent}"
                            UseCache="Operations"
                            VerticalOptions="Center"
                            WidthRequest="16" />




                        <draw:SkiaShape
                            Margin="-16,0"
                            Style="{StaticResource SkiaSeparatorStyle}" />

                    </draw:SkiaLayout>

                    <!--  DEMO  -->
                    <draw:SkiaLayout
                        draw:AddGestures.AnimationTapped="Ripple"
                        draw:AddGestures.CommandTapped="{Binding CommandToggleDemo, Mode=OneTime}"
                        draw:AddGestures.TouchEffectColor="White"
                        IsClippedToBounds="True"
                        Style="{StaticResource SkiaStyleOptionsStack}"
                        UseCache="Image">

                        <draw:SkiaLabel
                            Margin="0,0,99,0"
                            FontSize="14"
                            MaxLines="2"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Text="{x:Static strings:ResStrings.DemoMode}"
                            UseCache="Operations"
                            VerticalOptions="Center" />

                        <partials:SkiaCheckBox
                            Margin="0"
                            HorizontalOptions="End"
                            IsToggled="{Binding IsDemo}"
                            UseCache="Operations"
                            VerticalOptions="Center" />



                        <draw:SkiaShape
                            Margin="-16,0"
                            Style="{StaticResource SkiaSeparatorStyle}" />
                    </draw:SkiaLayout>

                    <!--  System  -->
                    <draw:SkiaLayout
                        draw:AddGestures.AnimationTapped="Ripple"
                        draw:AddGestures.CommandTapped="{Binding CommandSystemSettings, Mode=OneTime}"
                        draw:AddGestures.TouchEffectColor="White"
                        IsClippedToBounds="True"
                        Style="{StaticResource SkiaStyleOptionsStack}"
                        UseCache="Image">

                        <draw:SkiaLabel
                            Margin="0,0,99,0"
                            FontSize="14"
                            MaxLines="2"
                            Style="{StaticResource SkiaLabelDefaultStyle}"
                            Text="{x:Static strings:ResStrings.SystemSettings}"
                            TranslationY="0"
                            UseCache="Operations"
                            VerticalOptions="Center" />

                        <draw:SkiaSvg
                            HeightRequest="16"
                            HorizontalOptions="End"
                            SvgString="{StaticResource SvgOptionNext}"
                            TintColor="{StaticResource Accent}"
                            UseCache="Operations"
                            VerticalOptions="Center"
                            WidthRequest="16" />



                        <draw:SkiaShape
                            Margin="-16,0"
                            Style="{StaticResource SkiaSeparatorStyle}" />
                    </draw:SkiaLayout>

                </draw:SkiaLayout>

            </draw:SkiaScroll>

            <draw:SkiaLabelFps
                Margin="32"
                ForceRefresh="False"
                HorizontalOptions="End"
                IsClippedToBounds="True"
                IsVisible="{x:Static racebox:MauiProgram.ShowDebugInfo}"
                Rotation="-45"
                VerticalOptions="End"
                ZIndex="100" />

            <!--</drawn:EmbossedFrameDrawn>-->

        </draw:SkiaLayout>

    </draw:Canvas>

</Grid>
