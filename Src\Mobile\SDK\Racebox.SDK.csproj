﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net9.0;net9.0-android;net9.0-ios;net9.0-maccatalyst</TargetFrameworks>
		<TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>

		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<SkipValidateMauiImplicitPackageReferences>true</SkipValidateMauiImplicitPackageReferences>

    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.2</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">23.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</SupportedOSPlatformVersion>
    <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</TargetPlatformMinVersion>

  </PropertyGroup>

	<!--<ItemGroup>
	  <ProjectReference Include="..\..\..\..\AppoMobi.Maui.BLE.Connector\lib\AppoMobi.Maui.BLE.Connector.csproj" />
	</ItemGroup>-->

	<ItemGroup>
    <PackageReference Include="Microsoft.Maui.Controls" Version="9.0.70" />
    <PackageReference Include="AppoMobi.Maui.BLE.Connector" Version="8.0.0.1" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="Strings\ResStrings.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>ResStrings.resx</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Update="Strings\ResStrings.resx">
	    <Generator>PublicResXFileCodeGenerator</Generator>
	    <LastGenOutput>ResStrings.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	</ItemGroup>

</Project>
