﻿<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="Racebox.Views.PageAbout"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:partials="clr-namespace:Racebox.Views.Partials"
    xmlns:racebox="clr-namespace:Racebox"
    xmlns:strings="clr-namespace:Racebox.Shared.Strings;assembly=Racebox.Shared"
    xmlns:touch="clr-namespace:AppoMobi.Maui.Gestures;assembly=AppoMobi.Maui.Gestures"
    xmlns:viewModels="clr-namespace:Racebox.ViewModels"
    xmlns:views="clr-namespace:Racebox.Views"
    xmlns:drawn="clr-namespace:Racebox.Views.Drawn"
    x:Name="ThisPage"
    Title="PageAbout"
    x:DataType="viewModels:ProjectViewModel">

    <!--<?xml version="1.0" encoding="utf-8"?>
<svg width="21px" height="18px" viewBox="0 0 21 18" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg">
  <desc>Created with Lunacy</desc>
  <defs>
    <linearGradient x1="0.8578936" y1="0.1415124" x2="0.1243169" y2="1" id="gradient_1">
      <stop offset="0" stop-color="#1A6FCC" />
      <stop offset="1" stop-color="#299BCB" />
    </linearGradient>
  </defs>
  <path d="M0.01 18L21 9L0.01 0L0 7L15 9L0 11L0.01 18Z" id="Path" fill="url(#gradient_1)" stroke="none" />
</svg>-->

    <Grid
        HorizontalOptions="FillAndExpand"
        VerticalOptions="FillAndExpand">

        <!--  page background with gradient and frame  -->
        <draw:Canvas
            HorizontalOptions="Fill"
            Tag="BackgroundWithFrame"
            VerticalOptions="Fill">

            <partials:GradientToBorder
                HorizontalOptions="Fill"
                MissingHeight="{Binding NavbarModel.BottomTabsHeightRequest}"
                VerticalOptions="Fill">

                <drawn:EmbossedFrameDrawn
                    x:Name="DesignFrame"
                    BackgroundColor="Transparent"
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill" />

            </partials:GradientToBorder>
        </draw:Canvas>

        <draw:Canvas
            x:Name="MainCanvas"
            Gestures="Lock"
            RenderingMode="Accelerated"
            HorizontalOptions="Fill"
            VerticalOptions="FillAndExpand">

            <draw:SkiaLayout
                HorizontalOptions="Fill"
                VerticalOptions="Fill">

                    <draw:SkiaLayout
                        Margin="12,16,12,8"
                        HorizontalOptions="Fill"
                        Spacing="0"
                        Type="Column"
                        VerticalOptions="Fill">

                        <!--  NAVBAR  -->
                        <partials:SkiaNavBar
                            x:Name="NavBar"
                            Margin="0,0,1,0"
                            HeightRequest="65"
                            HorizontalOptions="Fill">

                            <draw:SkiaSvg
                                Margin="16,0,16,0"
                                HeightRequest="16"
                                HorizontalOptions="Start"
                                SvgString="{StaticResource SvgGoBack}"
                                TintColor="#CB6336"
                                VerticalOptions="Center"
                                WidthRequest="16" />

                            <draw:SkiaHotspot
                                CommandTapped="{Binding NavbarModel.CommandGoBack, Mode=OneTime}"
                                HorizontalOptions="Start"
                                TransformView="{x:Reference NavBar}"
                                WidthRequest="44" />

                            <draw:SkiaLabel
                                Margin="48,0,16,0"
                                FontSize="14"
                                HorizontalOptions="Start"
                                LineBreakMode="TailTruncation"
                                MaxLines="1"
                                Style="{StaticResource SkiaLabelDefaultStyle}"
                                Text="{x:Static strings:ResStrings.AboutApp}"
                                TextColor="#E8E3D7"
                                TranslationY="1"
                                VerticalOptions="Center" />


                            <!--  LINE HORIZONTAL  -->
                            <draw:SkiaShape
                                Margin="-16,0"
                                BackgroundColor="Black"
                                CornerRadius="0"
                                HeightRequest="1"
                                HorizontalOptions="Fill"
                                StrokeWidth="0"
                                VerticalOptions="End">
                                <draw:SkiaShape.FillGradient>

                                    <draw:SkiaGradient
                                        EndXRatio="1"
                                        EndYRatio="0"
                                        StartXRatio="0"
                                        StartYRatio="0"
                                        Type="Linear">
                                        <draw:SkiaGradient.Colors>
                                            <Color>#00E8E3D7</Color>
                                            <Color>#99E8E3D7</Color>
                                            <Color>#00E8E3D7</Color>
                                        </draw:SkiaGradient.Colors>
                                    </draw:SkiaGradient>

                                </draw:SkiaShape.FillGradient>
                            </draw:SkiaShape>

                        </partials:SkiaNavBar>

                        <!--  CONTENT SCROLL  -->
                        <draw:SkiaScroll
                            x:Name="MainScroll"
                            AutoScrollingSpeedMs="600"
                            ChangeVelocityScrolled="1.35"
                            HorizontalOptions="Fill"
                            LockChildrenGestures="PassTap"
                            RefreshDistanceLimit="4"
                            RefreshEnabled="False"
                            VerticalOptions="Fill">

                            <draw:SkiaLayout
                                x:Name="MainContainer"
                                Padding="18,16"
                                HorizontalOptions="Fill"
                                Spacing="18"
                                Type="Column"
                                UseCache="Image"
                                VerticalOptions="Start">

                                <!--  LOGO  -->
                                <draw:SkiaSvg
                                    HeightRequest="200"
                                    HorizontalOptions="Center"
                                    SvgString="{StaticResource SvgLogo}"
                                    TintColor="Transparent"
                                    UseCache="Image"
                                    VerticalOptions="Start"
                                    WidthRequest="200" />

                                <!--  ABOUT  -->
                                <draw:SkiaLabel
                                    Margin="8,0"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="FillWords"
                                    LineSpacing="1"
                                    ParagraphSpacing="0.25"
                                    Style="{StaticResource SkiaLabelDefaultStyle}"
                                    Text="{x:Static strings:ResStrings.AboutAppText}"
                                    UseCache="Operations" />

                                <!--  LINK  -->
                                <draw:SkiaLayout
                                    x:Name="GridLink"
                                    ColumnDefinitions="Auto"
                                    HorizontalOptions="Center"
                                    RowDefinitions="Auto"
                                    Type="Grid"
                                    UseCache="Operations">

                                    <draw:SkiaLabel
                                        touch:TouchEffect.CommandTapped="{Binding CommandOpenSite}"
                                        FontSize="17"
                                        HorizontalOptions="Center"
                                        Style="{StaticResource SkiaLabelDefaultStyle}"
                                        Text="www.racebox.ru"
                                        TextColor="CornflowerBlue" />

                                    <draw:SkiaHotspot
                                        Margin="-10"
                                        CommandTapped="{Binding CommandOpenSite, Mode=OneTime}"
                                        HorizontalOptions="Fill"
                                        TransformView="{x:Reference GridLink}"
                                        VerticalOptions="Fill"
                                        ZIndex="-1" />

                                </draw:SkiaLayout>

                                <!--  BTN Ask A Question  -->
                                <draw:SkiaLayout
                                    Margin="0,16"
                                    HeightRequest="36"
                                    HorizontalOptions="Center"
                                    UseCache="Image"
                                    VerticalOptions="Start"
                                    WidthRequest="150">

                                    <draw:SkiaShape
                                        x:Name="BtnBackground"
                                        BackgroundColor="#00000000"
                                        CornerRadius="24"
                                        HorizontalOptions="Fill"
                                        StrokeColor="#CB6336"
                                        StrokeWidth="1.85"
                                        VerticalOptions="Fill" />

                                    <draw:SkiaLabel
                                        Margin="12,0"
                                        AutoSize="FitFillHorizontal"
                                        FontSize="16"
                                        HorizontalOptions="Center"
                                        Text="{x:Static strings:ResStrings.BtnQuestion}"
                                        TextColor="#CB6336"
                                        TranslationY="0"
                                        VerticalOptions="Center" />

                                    <draw:SkiaHotspot
                                        AnimationTapped="Ripple"
                                        CommandTapped="{Binding CommandAskQuestion, Mode=OneTime}"
                                        HorizontalOptions="Fill"
                                        Tag="Site"
                                        TransformView="{x:Reference BtnBackground}"
                                        VerticalOptions="Fill" />


                                </draw:SkiaLayout>

                                <!--  Copyright  -->
                                <draw:SkiaLabel
                                    HorizontalOptions="Center"
                                    Style="{StaticResource SkiaEditorFieldTitle}"
                                    Text="{x:Static strings:ResStrings.Copyright}"
                                    UseCache="Operations" />

                                <!--  Build  -->
                                <draw:SkiaLabel
                                    HorizontalOptions="Center"
                                    Style="{StaticResource SkiaEditorFieldTitle}"
                                    Text="{Binding Build, StringFormat='Build {0}'}"
                                    UseCache="Operations" />

                            </draw:SkiaLayout>

                        </draw:SkiaScroll>

                    </draw:SkiaLayout>

                    <draw:SkiaLabelFps
                        Margin="32"
                        ForceRefresh="False"
                        HorizontalOptions="End"
                        IsVisible="{x:Static racebox:MauiProgram.ShowDebugInfo}"
                        Rotation="-45"
                        VerticalOptions="End"
                        ZIndex="100" />


            </draw:SkiaLayout>
        </draw:Canvas>



    </Grid>

</views:BasePage>