<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaLayout
    x:Class="Racebox.Views.Editor.EditorUtc"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:drawn="clr-namespace:Racebox.Views.Drawn"
    xmlns:viewModels="clr-namespace:Racebox.ViewModels"
    HorizontalOptions="Center"
    Tag="EditorContent"
    x:DataType="viewModels:IEditorModel"
    Type="Column">

    <drawn:SkiaSliderMetal
        Margin="24,0"
        AvailableWidthAdjustment="6"
        BackgroundColor="Transparent"
        HorizontalOptions="Fill"
        Max="12"
        EnableRange="False"
        Min="-12"
        End="{Binding Value2}"
        MinMaxStringFormat="0"
        Step="1" />

</draw:SkiaLayout>
