﻿namespace Racebox.UI.Dev;

//public static class MapsUiExtensions
//{


//    public const double Epsilon = 1.11022302462516E-16;

//    /// <summary>
//    /// IsRotated is true, when viewport displays map rotated
//    /// </summary>
//    public static bool IsRotated(this Viewport viewport) =>
//        !double.IsNaN(viewport.Rotation) && viewport.Rotation > Epsilon
//                                         && viewport.Rotation < 360 - Epsilon;

//    private static PropertyInfo? _disposedProperty;

//    public static bool IsDisposed(this SKNativeObject? skNativeObject)
//    {
//        if (skNativeObject == null)
//        {
//            return false;
//        }

//        _disposedProperty ??= typeof(SKNativeObject).GetProperty("IsDisposed", BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.Public);


//        if (_disposedProperty?.GetValue(skNativeObject) is bool disposed)
//        {
//            return disposed;
//        }

//        return false;
//    }
//}