using AppoMobi.Framework.Maui.Controls.Navigation.Tabs;
using AppoMobi.Maui.Navigation;

namespace Racebox.Views;

public partial class PageSettingsDevice : BasePage, ILazyTab
{



    public PageSettingsDevice()
    {
        try
        {
            BindingContext = _viewModel = App.Instance.Services.GetService<DeviceSettingsViewModel>();

            InitializeComponent();

            //to clip touch effect
            /*
			this.OptionsContainer.ClipChildren = (path, rect) =>
			{
				var scale = OptionsContainer.RenderingScale;
				var cornerRadius = (float)(16.0 * scale);
				var area = rect.Clone();
				//var bordersize = (float)(1 * scale);
				//area.Inflate(-bordersize, -bordersize);

				var rrect = new SKRoundRect();
				rrect.SetRectRadii(area, new[]
				{
					new SKPoint(cornerRadius,cornerRadius),
					new SKPoint(cornerRadius, cornerRadius),
					new SKPoint(cornerRadius,cornerRadius),
					new SKPoint(cornerRadius, cornerRadius),
				});
				path.AddRoundRect(rrect);
			};
			*/
        }
        catch (Exception e)
        {
            Designer.DisplayException(this, e);
        }
    }

    private readonly DeviceSettingsViewModel _viewModel;


    public void UpdateControls(DeviceRotation orientation)
    {

    }

    public void OnViewAppearing()
    {
        //if (_viewModel != null && !_viewModel.Initialized)
        //{
        //    Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(1000), async () =>
        //    {
        //        _viewModel.Initialize();
        //        return false;
        //    });
        //}
    }

    public void OnViewDisappearing()
    {

    }


}