﻿using Newtonsoft.Json;
using System.Globalization;

namespace Racebox.ApiClient.Dto;

public class Current
{

    [JsonIgnore]
    public DateTime LastUpdatedUtc
    {
        get
        {
            return DateTimeOffset.FromUnixTimeSeconds(LastUpdatedEpoch).DateTime;
        }
    }

    /// <summary>
    /// Local time when the real time data was updated
    /// </summary>
    [JsonProperty("last_updated")]
    public string LastUpdated { get; set; }

    /// <summary>
    /// Local time when the real time data was updated in unix time
    /// </summary>
    [JsonProperty("last_updated_epoch")]
    public int LastUpdatedEpoch { get; set; }

    /// <summary>
    /// Temperature in Celsius
    /// </summary>
    [JsonProperty("temp_c")]
    public decimal TempC { get; set; }

    /// <summary>
    /// Temperature in Fahrenheit
    /// </summary>
    [JsonProperty("temp_f")]
    public decimal TempF { get; set; }

    /// <summary>
    /// Feels like temperature in Celsius
    /// </summary>
    [JsonProperty("feelslike_c")]
    public decimal FeelslikeC { get; set; }

    /// <summary>
    /// Feels like temperature in Fahrenheit
    /// </summary>
    [JsonProperty("feelslike_f")]
    public decimal FeelslikeF { get; set; }

    /// <summary>
    /// Weather condition
    /// </summary>
    [JsonProperty("condition")]
    public Condition Condition { get; set; }

    /// <summary>
    /// Wind speed in miles per hour
    /// </summary>
    [JsonProperty("wind_mph")]
    public decimal WindMph { get; set; }

    /// <summary>
    /// Wind speed in kilometer per hour
    /// </summary>
    [JsonProperty("wind_kph")]
    public decimal WindKph { get; set; }

    /// <summary>
    /// Wind direction in degrees
    /// </summary>
    [JsonProperty("wind_degree")]
    public int WindDegree { get; set; }

    /// <summary>
    /// Wind direction as 16 point compass
    /// </summary>
    [JsonProperty("wind_dir")]
    public string WindDir { get; set; }

    /// <summary>
    /// Pressure in millibars
    /// </summary>
    [JsonProperty("pressure_mb")]
    public decimal PressureMb { get; set; }

    /// <summary>
    /// Pressure in inches
    /// </summary>
    [JsonProperty("pressure_in")]
    public decimal PressureIn { get; set; }

    /// <summary>
    /// Precipitation amount in millimeters
    /// </summary>
    [JsonProperty("precip_mm")]
    public decimal PrecipMm { get; set; }

    /// <summary>
    /// Precipitation amount in inches
    /// </summary>
    [JsonProperty("precip_in")]
    public decimal PrecipIn { get; set; }

    /// <summary>
    /// Humidity as percentage
    /// </summary>
    [JsonProperty("humidity")]
    public int Humidity { get; set; }

    /// <summary>
    /// Cloud cover as percentage
    /// </summary>
    [JsonProperty("cloud")]
    public int Cloud { get; set; }

    /// <summary>
    /// Whether to show day condition icon or night icon (1 = Yes, 0 = No)
    /// </summary>
    [JsonProperty("is_day")]
    public int IsDay { get; set; }

    /// <summary>
    /// UV Index
    /// </summary>
    [JsonProperty("uv")]
    public decimal Uv { get; set; }

    /// <summary>
    /// Wind gust in miles per hour
    /// </summary>
    [JsonProperty("gust_mph")]
    public decimal GustMph { get; set; }

    /// <summary>
    /// Wind gust in kilometer per hour
    /// </summary>
    [JsonProperty("gust_kph")]
    public decimal GustKph { get; set; }

    /// <summary>
    /// Visibility in kilometers
    /// </summary>
    [JsonProperty("vis_km")]
    public decimal VisKm { get; set; }

    /// <summary>
    /// Visibility in miles
    /// </summary>
    [JsonProperty("vis_miles")]
    public decimal VisMiles { get; set; }
}