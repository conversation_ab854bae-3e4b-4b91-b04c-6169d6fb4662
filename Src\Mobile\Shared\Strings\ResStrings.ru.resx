﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BtnSave" xml:space="preserve">
    <value>Сохранить</value>
  </data>
  <data name="BtnClose" xml:space="preserve">
    <value>Закрыть</value>
  </data>
  <data name="BtnCancel" xml:space="preserve">
    <value>Отмена</value>
  </data>
  <data name="BtnApply" xml:space="preserve">
    <value>Применить</value>
  </data>
  <data name="Inputs" xml:space="preserve">
    <value>Входы</value>
  </data>
  <data name="Device" xml:space="preserve">
    <value>Прибор</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Нет</value>
  </data>
  <data name="Options" xml:space="preserve">
    <value>Опции</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Да</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Ошибка</value>
  </data>
  <data name="FeetShort" xml:space="preserve">
    <value> фт</value>
  </data>
  <data name="MetersShort" xml:space="preserve">
    <value> м</value>
  </data>
  <data name="Kmh" xml:space="preserve">
    <value>км/ч</value>
  </data>
  <data name="Mph" xml:space="preserve">
    <value>миль/ч</value>
  </data>
  <data name="MinutesShort" xml:space="preserve">
    <value> мин</value>
  </data>
  <data name="SecondsShort" xml:space="preserve">
    <value> c</value>
  </data>
  <data name="BtnStop" xml:space="preserve">
    <value>Стоп</value>
  </data>
  <data name="BtnStart" xml:space="preserve">
    <value>Старт</value>
  </data>
  <data name="BtnConnect" xml:space="preserve">
    <value>Подключиться</value>
  </data>
  <data name="BtnDisconnect" xml:space="preserve">
    <value>Отключиться</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>Выкл</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>Вкл</value>
  </data>
  <data name="StatusBluetoothOff" xml:space="preserve">
    <value>Bluetooth отключен</value>
  </data>
  <data name="StatusConnecting" xml:space="preserve">
    <value>Подключение...</value>
  </data>
  <data name="MeasureInstant" xml:space="preserve">
    <value>Замер сходу</value>
  </data>
  <data name="Measure" xml:space="preserve">
    <value>Замер</value>
  </data>
  <data name="VendorTitle" xml:space="preserve">
    <value>Racebox</value>
  </data>
  <data name="SignalStrength" xml:space="preserve">
    <value>Уровень сигнала</value>
  </data>
  <data name="HintConnectNow" xml:space="preserve">
    <value>Нажмите значок Bluetooth наверху экрана для подключения к прибору</value>
  </data>
  <data name="HintStartMeasuring" xml:space="preserve">
    <value>Нажмите кнопку Старт для начала замера</value>
  </data>
  <data name="HintMeasureFailed" xml:space="preserve">
    <value>Замер неудачен</value>
  </data>
  <data name="HintMonitoring" xml:space="preserve">
    <value>Ожидание начала замера...</value>
  </data>
  <data name="HintGo" xml:space="preserve">
    <value>ПОЕХАЛИ!!!</value>
  </data>
  <data name="Racing" xml:space="preserve">
    <value>Замер</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>Приборы</value>
  </data>
  <data name="History" xml:space="preserve">
    <value>История</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Настройки</value>
  </data>
  <data name="Altitude" xml:space="preserve">
    <value>Высота</value>
  </data>
  <data name="Heading" xml:space="preserve">
    <value>Курс</value>
  </data>
  <data name="Latitude" xml:space="preserve">
    <value>Широта</value>
  </data>
  <data name="Longitude" xml:space="preserve">
    <value>Долгота</value>
  </data>
  <data name="MaxSpeed" xml:space="preserve">
    <value>Макс. скорость</value>
  </data>
  <data name="Incline" xml:space="preserve">
    <value>Уклон</value>
  </data>
  <data name="Frequency" xml:space="preserve">
    <value>Частота</value>
  </data>
  <data name="AllCars" xml:space="preserve">
    <value>Все авто</value>
  </data>
  <data name="Filtered" xml:space="preserve">
    <value>Отфильтровано</value>
  </data>
  <data name="Cars" xml:space="preserve">
    <value>Авто</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Выбор</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Сортировка</value>
  </data>
  <data name="CustomMetrics" xml:space="preserve">
    <value>Метрики</value>
  </data>
  <data name="Sound" xml:space="preserve">
    <value>Звук</value>
  </data>
  <data name="RollOut" xml:space="preserve">
    <value>Ролл-аут (1 фт)</value>
  </data>
  <data name="Car" xml:space="preserve">
    <value>Автомобиль</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Описание</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Пользователь</value>
  </data>
  <data name="MeasuringSystem" xml:space="preserve">
    <value>Система измер.</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Пользователи</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Фильтр</value>
  </data>
  <data name="EditList" xml:space="preserve">
    <value>Редактировать список</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Все</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Имя</value>
  </data>
  <data name="Deleting" xml:space="preserve">
    <value>Удаление</value>
  </data>
  <data name="AreYouSure" xml:space="preserve">
    <value>Вы уверены?</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>Марка</value>
  </data>
  <data name="Model" xml:space="preserve">
    <value>Модель</value>
  </data>
  <data name="CannotDeleteUsed" xml:space="preserve">
    <value>Запись используется в данный момент</value>
  </data>
  <data name="CannotDeleteSingle" xml:space="preserve">
    <value>Нельзя удалить единственную запись</value>
  </data>
  <data name="Europe" xml:space="preserve">
    <value>Европа</value>
  </data>
  <data name="USA" xml:space="preserve">
    <value>США</value>
  </data>
  <data name="DefaultBrand" xml:space="preserve">
    <value>Ваше</value>
  </data>
  <data name="DefaultModel" xml:space="preserve">
    <value>авто</value>
  </data>
  <data name="OrderNewFirst" xml:space="preserve">
    <value>Сначала новые</value>
  </data>
  <data name="OrderOldFirst" xml:space="preserve">
    <value>Сначала старые</value>
  </data>
  <data name="Distance" xml:space="preserve">
    <value>Дистанция</value>
  </data>
  <data name="SpeedRange" xml:space="preserve">
    <value>Диапазон скоростей</value>
  </data>
  <data name="Length" xml:space="preserve">
    <value>Дистанция</value>
  </data>
  <data name="End" xml:space="preserve">
    <value>До</value>
  </data>
  <data name="Start" xml:space="preserve">
    <value>От</value>
  </data>
  <data name="ValidationStartValue" xml:space="preserve">
    <value>Требуется значение &gt;=0 и &lt;=5000</value>
  </data>
  <data name="ValidationEndValue" xml:space="preserve">
    <value>Требуется значение &gt;0 и &lt;=5000</value>
  </data>
  <data name="ValidationDifferentValues" xml:space="preserve">
    <value>Должно быть больше стартового значения</value>
  </data>
  <data name="MeasuringUnits" xml:space="preserve">
    <value>Единицы измер.</value>
  </data>
  <data name="Feet" xml:space="preserve">
    <value>Футы</value>
  </data>
  <data name="Meters" xml:space="preserve">
    <value>Метры</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Местоположение</value>
  </data>
  <data name="AlertNeedGpsOnForBluetooth" xml:space="preserve">
    <value>Пожалуйста, включите GPS (требование системы Android для использования Bluetooth).</value>
  </data>
  <data name="AlertNeedGpsPermissionsForBluetooth" xml:space="preserve">
    <value>Пожалуйста, предоставьте разрешения для местоположения (требование системы Android для использования Bluetooth).</value>
  </data>
  <data name="AlertTurnOnBluetooth" xml:space="preserve">
    <value>Пожалуйста, включите Bluetooth.</value>
  </data>
  <data name="AlertBluetoothUnsupported" xml:space="preserve">
    <value>Ваше устройство не поддерживает Bluetooth.</value>
  </data>
  <data name="HintGpsLow" xml:space="preserve">
    <value>Попробуйте поменять расположение устройства для более уверенного приема сигнала GPS</value>
  </data>
  <data name="StatusGpsLow" xml:space="preserve">
    <value>Слабый сигнал GPS</value>
  </data>
  <data name="AlertBluetoothPermissionsOff" xml:space="preserve">
    <value>Нет разрешений на использование Bluetooth.</value>
  </data>
  <data name="Hz" xml:space="preserve">
    <value>Гц</value>
  </data>
  <data name="KmhAdd" xml:space="preserve">
    <value> км/ч</value>
  </data>
  <data name="MphAdd" xml:space="preserve">
    <value> миль/ч</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Язык</value>
  </data>
  <data name="MaxAcceleration" xml:space="preserve">
    <value>Макс. ускорение</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Редактировать</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Удалить</value>
  </data>
  <data name="HoursShort" xml:space="preserve">
    <value> ч</value>
  </data>
  <data name="BtnReset" xml:space="preserve">
    <value>Сброс</value>
  </data>
  <data name="BtnCreate" xml:space="preserve">
    <value>Создать</value>
  </data>
  <data name="Acceleration" xml:space="preserve">
    <value>Ускорение</value>
  </data>
  <data name="AccelerationSide" xml:space="preserve">
    <value>Бок. ускорение</value>
  </data>
  <data name="InclineMax" xml:space="preserve">
    <value>Макс. уклон</value>
  </data>
  <data name="IsValid" xml:space="preserve">
    <value>Статус замера</value>
  </data>
  <data name="IsValidNo" xml:space="preserve">
    <value>Не валидный</value>
  </data>
  <data name="IsValidYes" xml:space="preserve">
    <value>Валидный</value>
  </data>
  <data name="AlertBluetoothWillNotBeAvailable" xml:space="preserve">
    <value>Bluetooth будет недоступен.</value>
  </data>
  <data name="SystemSettings" xml:space="preserve">
    <value>Системные настройки</value>
  </data>
  <data name="Time24" xml:space="preserve">
    <value>Формат времени 24ч</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>О программе</value>
  </data>
  <data name="BtnQuestion" xml:space="preserve">
    <value>Задать вопрос</value>
  </data>
  <data name="AboutAppText" xml:space="preserve">
    <value>Приложение предназначено для замера динамики автомобилей и работает только совместно с приборами Racebox Pro и Pro+.
Приложение должно использоваться только на специализированных автоспортивных трассах. Автор приложения не несёт ответственность за любые травмы или повреждения, которые могут возникнуть при использовании данного приложения.</value>
  </data>
  <data name="Copyright" xml:space="preserve">
    <value>© 2025 Racebox Team</value>
  </data>
  <data name="AlertNeedLocationForBluetooth" xml:space="preserve">
    <value>Нам понадобится разрешение на использование вашего местоположения GPS (системное требование Android для использования Bluetooth).</value>
  </data>
  <data name="PressBACKOnceAgain" xml:space="preserve">
    <value>Нажмите еще раз для выхода из программы</value>
  </data>
  <data name="ExportFormat" xml:space="preserve">
    <value>Формат логов</value>
  </data>
  <data name="ErrorMetricsAlreadyExist" xml:space="preserve">
    <value>Такая метрика уже сушествует</value>
  </data>
  <data name="ErrorFailedToSaveRecord" xml:space="preserve">
    <value>Не удалось сохранить запись</value>
  </data>
  <data name="CompatibleDevicesNotFound" xml:space="preserve">
    <value>Не найдены устройства Racebox поблизости.</value>
  </data>
  <data name="DemoMode" xml:space="preserve">
    <value>Демо-режим</value>
  </data>
  <data name="DemoWarning" xml:space="preserve">
    <value>В этом режиме приложение перестанет подключаться к реальному Bluetooth-модулю и будет проигрывать заранее записанные треки движения. Вы уверены, что хотите включить демо-режим?</value>
  </data>
  <data name="DemoStartHint" xml:space="preserve">
    <value>Нажмите на спидометр для запуска симуляции</value>
  </data>
  <data name="BadMeasureNotValid" xml:space="preserve">
    <value>Невалидный замер (большой уклон)</value>
  </data>
  <data name="BadMeasureLowSpeed" xml:space="preserve">
    <value>Замер с места и скорость ниже READY_SPEED_THRESHOLD_UNIT</value>
  </data>
  <data name="BadMeasureInstant" xml:space="preserve">
    <value>Замер "сходу" и скорость ниже начальной скорости первой пары и время замера больше 1 сек</value>
  </data>
  <data name="BadMeasureGps" xml:space="preserve">
    <value>Низкое качестве приёма GPS</value>
  </data>
  <data name="BadMeasureTooLong" xml:space="preserve">
    <value>Время замера выше ACC_TIMEOUT</value>
  </data>
  <data name="BadMeasureLowAccel" xml:space="preserve">
    <value>Упало ускорение</value>
  </data>
  <data name="BadMeasureNoRanges" xml:space="preserve">
    <value>Нет измеренных любых диапазонов</value>
  </data>
  <data name="BadMeasureNoDistances" xml:space="preserve">
    <value>Нет измеренных любых дистанций</value>
  </data>
  <data name="BadMeasureIncline" xml:space="preserve">
    <value>Невалидный замер (большой уклон)</value>
  </data>
  <data name="Report" xml:space="preserve">
    <value>Отчет</value>
  </data>
  <data name="MeasuringStateDisabled" xml:space="preserve">
    <value>Прибор выключен</value>
  </data>
  <data name="MeasuringStateActive" xml:space="preserve">
    <value>Едем!</value>
  </data>
  <data name="MeasuringStateReady" xml:space="preserve">
    <value>Стартуйте!</value>
  </data>
  <data name="MeasuringStateFinished" xml:space="preserve">
    <value>Замер завершён</value>
  </data>
  <data name="MeasuringStateMonitoring" xml:space="preserve">
    <value>Прибор включен</value>
  </data>
  <data name="MeasuringStateError" xml:space="preserve">
    <value>Замер неудачен</value>
  </data>
  <data name="Weather" xml:space="preserve">
    <value>Погода</value>
  </data>
  <data name="X_Power" xml:space="preserve">
    <value>Батарея</value>
  </data>
  <data name="Satellites" xml:space="preserve">
    <value>Спутники</value>
  </data>
  <data name="DeviceLedBrightness" xml:space="preserve">
    <value>Яркость дисплея</value>
  </data>
  <data name="DeviceTransmissionType" xml:space="preserve">
    <value>Тип трансмиссии</value>
  </data>
  <data name="DeviceMaps" xml:space="preserve">
    <value>Карты</value>
  </data>
  <data name="DeviceTone" xml:space="preserve">
    <value>Звуковые сигналы</value>
  </data>
  <data name="DeviceUtc" xml:space="preserve">
    <value>Часовой пояс</value>
  </data>
  <data name="DeviceLogFormat" xml:space="preserve">
    <value>Формат логов</value>
  </data>
  <data name="DeviceLogRate" xml:space="preserve">
    <value>Частота логов</value>
  </data>
  <data name="DevicePrediction" xml:space="preserve">
    <value>Предиктивный режим</value>
  </data>
  <data name="DeviceRandomStart" xml:space="preserve">
    <value>Произвольное время старта</value>
  </data>
  <data name="DeviceMinDistance" xml:space="preserve">
    <value>Мин. дистанция отображения</value>
  </data>
  <data name="DeviceCarWeight" xml:space="preserve">
    <value>Масса авто</value>
  </data>
  <data name="DeviceDragRatio" xml:space="preserve">
    <value>Коэф. лобового сопротивления</value>
  </data>
  <data name="DeviceFrontal" xml:space="preserve">
    <value>Площадь поперечного сечения</value>
  </data>
  <data name="DeviceObdLog" xml:space="preserve">
    <value>Запись лога OBD</value>
  </data>
  <data name="DeviceShiftTime" xml:space="preserve">
    <value>Замер времени переключения</value>
  </data>
  <data name="DeviceObdPidAuto" xml:space="preserve">
    <value>Автоопределение PID OBD</value>
  </data>
  <data name="DeviceObdPids" xml:space="preserve">
    <value>Выбор читаемых PID OBD</value>
  </data>
  <data name="DeviceObdProtocol" xml:space="preserve">
    <value>Протокол OBD</value>
  </data>
  <data name="DeviceGnssSecondarySource" xml:space="preserve">
    <value>2й источник GNSS</value>
  </data>
  <data name="DeviceGnssUartOut" xml:space="preserve">
    <value>Вывод данных GNSS по USB</value>
  </data>
  <data name="DeviceGnssColdStart" xml:space="preserve">
    <value>Холодный перезапуск GNSS</value>
  </data>
  <data name="DeviceGnssReconfig" xml:space="preserve">
    <value>Переконфигурация GNSS</value>
  </data>
  <data name="DeviceResetConfiguration" xml:space="preserve">
    <value>Сброс конфигурации</value>
  </data>
  <data name="ResetDevice" xml:space="preserve">
    <value>Перезапуск прибора</value>
  </data>
  <data name="WarningCannotUndo" xml:space="preserve">
    <value>Эту операцию невозможно отменить.</value>
  </data>
  <data name="DeviceSettings" xml:space="preserve">
    <value>Настройки устройства</value>
  </data>
  <data name="FirmwareWarningHelp" xml:space="preserve">
    <value>Для обновления ПО прибора выполните следующие шаги:
 1. Скачайте новую версию ПО с сайта [racebox.ru](https://racebox.ru/obnovlenie-po).
 2. Скопируйте файл прошивки в формате `HEX`, соответствующий модели вашего прибора, в корневой каталог карты памяти.
 3. Вставьте карту памяти в выключенный прибор.
 4. Удерживая кнопку `Mode`, подключите прибор к питанию через USB-кабель.
 5. Дождитесь инициализации карты памяти и появления сообщение `HOLD TO UPDATE X.XX` (где X.XX - версия загружаемого ПО)
 6. Нажмите и удерживайте кнопку `Mode` для начала процесса обновления.
 7. Дождитесь окончания обновления и убедитесь, что при включении прибора отображается новая версия ПО.
 8. Перезапустите приложение __Racebox__.</value>
  </data>
  <data name="FirmwareWarningTitle" xml:space="preserve">
    <value>Устаревшая прошивка</value>
  </data>
  <data name="FirmwareHowToTitle" xml:space="preserve">
    <value>Как обновить прошивку</value>
  </data>
  <data name="SettingsCanSpeak" xml:space="preserve">
    <value>Озвучивать скорость</value>
  </data>
</root>