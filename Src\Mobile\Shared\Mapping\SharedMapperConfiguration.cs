﻿using Mapster;
using MapsterMapper;
using Racebox.Shared.Dto;
using Racebox.Shared.Extensions;
using Racebox.Shared.Interfaces;
using Racebox.Shared.Models;
using Racebox.Shared.Services;
using Racebox.Shared.Strings;

namespace Racebox.Shared;

/// <summary>
/// Поскольку нам важна скорость запуска мы не используем конфиги
/// мапера в отдельных класса с IRegister а все прописываем в одном
/// </summary>
public class SharedMapperConfiguration : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        //IMPORT
        config.NewConfig<CurrentWeather, Weather>() //create db entity
            .IgnoreNullValues(true)
            .Ignore(dest => dest.Id)
            .Ignore(dest => dest.MeasureResult)
            .Compile();


        //EXPORT
     

    }
}