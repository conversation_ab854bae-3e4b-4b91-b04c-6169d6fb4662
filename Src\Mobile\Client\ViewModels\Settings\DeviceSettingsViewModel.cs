﻿#define DEV

using AppoMobi.Framework.Maui.Interfaces;
using AppoMobi.Framework.Maui.Models;
using AppoMobi.Specials.Localization;
using DrawnUi.Models;
using MapsterMapper;
using Mopups.Services;
using Racebox.SDK;
using Racebox.Shared.Enums;
using Racebox.Shared.Strings;
using Racebox.Views.Editor;
using Racebox.Views.Popups;

namespace Racebox.ViewModels;

public class DeviceSettingsViewModel : ContentViewModel, IEditorModel, IQueryAttributable
{
    public override void OnSubscribing(bool subscribe)
    {
        base.OnSubscribing(subscribe);

        if (subscribe)
        {
            _device.Connector.DeviceConnectionChanged += OnDeviceConnectionChanged;
        }
        else
        {
            _device.Connector.DeviceConnectionChanged -= OnDeviceConnectionChanged;

            _cancel?.Cancel();
        }
    }

    private void OnDeviceConnectionChanged(object sender, bool e)
    {
        NavbarModel.CommandGoBack.Execute(null);
    }

    #region IQueryAttributable

    private bool _attributesSet;

    /// <summary>
    /// This will work only with SkiaShell !!! MAUI shell cannot pass ojects
    /// </summary>
    /// <param name="query"></param>
    public void ApplyQueryAttributes(IDictionary<string, object> query)
    {
        if (_attributesSet)
            return;

        _attributesSet = true;

        //// The query parameter requires URL decoding.
        //if (query.ContainsKey("id"))
        //    Id = HttpUtility.UrlDecode(query["id"].ToString());

        if (query != null)
        {
            query.TryGetValue("item", out var item);
            if (item != null)
            {
                Item = item as RaceBoxSettingsState;
            }
        }

        OnParametersSet();
    }

    protected virtual void OnParametersSet()
    {
        Initialize();
    }

    #endregion

#if IOS || MACCATALYST
    private const double delayTap = 150;
#else
    private const double delayTap = 150;
#endif

    public RaceBoxStateProcessor Processor { get; }
    private readonly RaceBoxDeviceSettings _settings;

    public DeviceSettingsViewModel(UserManager manager,
        RaceBoxStateProcessor processor,
        RaceBoxDeviceViewModel vm,
        RaceBoxDeviceSettings setts,
        IUIAssistant ui, IMapper mapper)
        : base(manager, ui, mapper)
    {
        Title = ResStrings.DeviceSettings;
        _settings = setts;
        _device = vm;
        Processor = processor;

        //#if DEBUG //todo remove
        //        Item = new();
        //        var base64data = "IGaQQQagjGH1HwA=";
        //        byte[] bytes = Convert.FromBase64String(base64data);
        //        bytes.DecodeTo(Item);
        //#endif

        Initialize();
    }

    RaceBoxSettingsState Item { get; set; }

    #region Single-Instance Editor - IEditorModel

    private string _EditorTitle;
    public string EditorTitle
    {
        get
        {
            return _EditorTitle;
        }
        set
        {
            if (_EditorTitle != value)
            {
                _EditorTitle = value;
                OnPropertyChanged();
            }
        }
    }

    private string _EditorHint;
    public string EditorHint
    {
        get
        {
            return _EditorHint;
        }
        set
        {
            if (_EditorHint != value)
            {
                _EditorHint = value;
                OnPropertyChanged();
            }
        }
    }

    public SkiaLayout EditorContent { get; set; }

    public Action<object> EditorOnOk { get; set; }

    void OpenEditor(SkiaLayout editor,
        Action<object> callback)
    {
        editor.BindingContext = this;
        EditorContent = editor;
        EditorOnOk = callback;
        var popup = new PopupEditor(this);
        MainThread.BeginInvokeOnMainThread(async () =>
        {
            await MopupService.Instance.PushAsync(popup);
            //await App.Current.MainPage.ShowPopupAsync(popup);
        });
    }

    private double _Value1;
    public double Value1
    {
        get
        {
            return _Value1;
        }
        set
        {
            if (_Value1 != value)
            {
                _Value1 = value;
                OnPropertyChanged();
            }
        }
    }

    private double _Value2;
    public double Value2
    {
        get
        {
            return _Value2;
        }
        set
        {
            if (_Value2 != value)
            {
                _Value2 = value;
                OnPropertyChanged();
            }
        }
    }

    private bool _ValueChecked;
    public bool ValueChecked
    {
        get
        {
            return _ValueChecked;
        }
        set
        {
            if (_ValueChecked != value)
            {
                _ValueChecked = value;
                OnPropertyChanged();
            }
        }
    }

    #endregion

    #region TemplatedSettings

    public List<ActionOption> SettingsFields { get; set; }

    public Command CommandApply => new Command(async () =>
    {
        if (CheckLockAndSet())
            return;

        if (!_device.Connector.IsConnected)
        {
            NavbarModel.CommandGoBack.Execute(null);
            return;
        }

        await SaveSettings(true);
    });

    private bool _HasChanges;
    public bool HasChanges
    {
        get
        {
            return _HasChanges;
        }
        set
        {
            if (_HasChanges != value)
            {
                _HasChanges = value;
                OnPropertyChanged();
            }
        }
    }


    void ApplyChanges()
    {
        //we disabled this, now we apply only on button/command
        HasChanges = true;

        return;

        //LauchRestartingTimer_Save(3500);
    }

    void InitSettings()
    {
        if (Item == null)
            return;

        SettingsFields = new List<ActionOption>
            {

                //DeviceLedBrightness
                new()
                {
                    Title = ResStrings.DeviceLedBrightness,
                    Hint = "",
                    Command  = new Command<object>(async (context) =>
                    {
                        List<SelectableAction> options = new()
                        {
                            new SelectableAction
                            {
                                Id = "1",
                                Title = "+1",
                                Action = () =>
                                {
                                    Item.Led =1;
                                    ApplyChanges();
                                    UpdateSettings();
                                }
                            },
                            new SelectableAction
                            {
                                Id = "2",
                                Title = "+2",
                                Action = () =>
                                {
                                    Item.Led =2;
                                    ApplyChanges();
                                    UpdateSettings();
                                }
                            },
                            new SelectableAction
                            {
                                Id = "3",
                                Title = "+3",
                                Action = () =>
                                {
                                    Item.Led =3;
                                    ApplyChanges();
                                    UpdateSettings();
                                }
                            },
                            new SelectableAction
                            {
                                Id = "4",
                                Title = "+4",
                                Action = () =>
                                {
                                    Item.Led =4;
                                    ApplyChanges();
                                    UpdateSettings();
                                }
                            },
                        };
                        App.SelectAction(options, ResStrings.DeviceLedBrightness);
                    }),
                    Update = (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            instance.SubTitle = $"{Item.Led:+0;-0;0}";
                        }
                    }
                },

                //DeviceTone
                new()
                {
                    Title = ResStrings.DeviceTone,
                    PresentationMode = 1,
                    BooleanValue = Item.Tone,
                    Command  = new Command<object>(async (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            Item.Tone = !Item.Tone;
                            ApplyChanges();
                            UpdateSettings();
                        }
                    }),
                    Update = (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            instance.BooleanValue =  Item.Tone;
                        }
                    }
                },

                //DeviceTransmissionType
                new()
                {
                    Title = ResStrings.DeviceTransmissionType,
                    Command  = new Command<object>(async (context) =>
                    {
                        var options = App.GenerateOptionsForEnum<DeviceSettingsTransmission>((selected)=>
                        {
                            Item.Transmission = selected;
                            ApplyChanges();
                            UpdateSettings();
                        });
                        App.SelectAction(options, ResStrings.DeviceTransmissionType);
                    }),
                    Update = (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            instance.SubTitle = LocalizedEnumConverter.ConvertToString(Item.Transmission);
                        }
                    }
                },
                
                //DeviceUtc
                new()
                {
                    Title = ResStrings.DeviceUtc,
                    Command  = new Command<object>(async (context) =>
                    {

                        if (context is ActionOption instance)
                        {
                            // EditorTitle = instance.Title;
                            // EditorHint = instance.Hint;
                            // Value2 = Item.Utc;
                            // OpenEditor(new EditorUtc(Item.Utc), (ret)=>
                            // {
                            //     Item.Utc = (int)Value2;
                            //     UpdateSettings();
                            //     ApplyChanges();
                            // });
                            var values = new List<string>()
                            {
                                "-12","-11","-10","-9","-8","-7","-6","-5","-4","-3","-2","-1",
                                "0",
                                "+1","+2","+3","+4","+5","+6","+7","+8","+9","+10","+11","+12"
                            };
                            var value = $"{Item.Utc:+0;-0;0}";
                            var vm = new EditValueWithWheelViewModel(
                                value,
                                values,
                                async (v)=>
                                {
                                    Item.Utc = v.ToInteger();
                                    ApplyChanges();
                                    UpdateSettings();
                                    return true;
                                });
                            MainThread.BeginInvokeOnMainThread(async () =>
                            {
                                vm.Title = ResStrings.DeviceUtc;
                                await NavbarModel.Shell.Navigation.PushAsync(new PageEditForm(vm));
                            });

                        }
                    }),
                    Update = (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            instance.SubTitle = $"{Item.Utc:+0;-0;0}";
                        }
                    }
                },

                //SpeedRange
                new()
                {
                    Title = ResStrings.SpeedRange,
                    Command  = new Command<object>(async (context) =>
                    {
                        var vm = new EditSpeedRangeViewModel(new LocalSpeedRange(Item.Speed1, Item.Speed2, OptionsUnits.EU), async (item)=>
                        {
                            if ( item is LocalSpeedRange range)
                            {
                                Item.Speed1 = (int)range.Start;
                                Item.Speed2 = (int)range.End;
                                UpdateSettings();
                                ApplyChanges();
                            }
                            return 0;
                        });
                        vm.Title = ResStrings.SpeedRange;
                        MainThread.BeginInvokeOnMainThread(async () =>
                        {
                            await NavbarModel.Shell.Navigation.PushAsync(new PageEditForm(vm));
                        });
                    }),
                    Update = (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            instance.SubTitle = $"{Item.Speed1} - {Item.Speed2}";
                        }
                    }
                },

                //DeviceLogFormat
                new()
                {
                    Title = ResStrings.DeviceLogFormat,
                    Command  = new Command<object>(async (context) =>
                    {
                        List<SelectableAction> options = new()
                        {
                            new SelectableAction
                            {
                                Id = DeviceSettingsLogFormat.VBO.ToString(),
                                Title = DeviceSettingsLogFormat.VBO.ToString(),
                                Action = () =>
                                {
                                    Item.LogFormat = DeviceSettingsLogFormat.VBO;
                                    ApplyChanges();
                                    UpdateSettings();
                                }
                            },
                            new SelectableAction
                            {
                                Id = DeviceSettingsLogFormat.CSV.ToString(),
                                Title = DeviceSettingsLogFormat.CSV.ToString(),
                                Action = () =>
                                {
                                    Item.LogFormat = DeviceSettingsLogFormat.CSV;
                                    ApplyChanges();
                                    UpdateSettings();
                                }
                            },
                            new SelectableAction
                            {
                                Id = DeviceSettingsLogFormat.GPX.ToString(),
                                Title = DeviceSettingsLogFormat.GPX.ToString(),
                                Action = () =>
                                {
                                    Item.LogFormat = DeviceSettingsLogFormat.GPX;
                                    ApplyChanges();
                                    UpdateSettings();
                                }
                            },
                            new SelectableAction
                            {
                                Id = DeviceSettingsLogFormat.Disabled.ToString(),
                                Title = DeviceSettingsLogFormat.Disabled.ToString(),
                                Action = () =>
                                {
                                    Item.LogFormat = DeviceSettingsLogFormat.Disabled;
                                    ApplyChanges();
                                    UpdateSettings();
                                }
                            },
                        };
                        App.SelectAction(options, nameof(Item.LogFormat));
                    }),
                    Update = (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            instance.SubTitle = Item.LogFormat.ToString();
                        }
                    }
                },
                
                //DeviceLogRate
               new()
                {
                 Title = ResStrings.DeviceLogRate,
                 Hint = "",
                 Command  = new Command<object>(async (context) =>
                 {
                     var options = App.GenerateOptionsForEnum<DeviceLograteType>((selected)=>
                     {
                         Item.LogRate = selected;
                         ApplyChanges();
                         UpdateSettings();
                     });
                     App.SelectAction(options, ResStrings.DeviceLogRate);

                 }),
                 Update = (context) =>
                 {
                     if (context is ActionOption instance)
                     {
                         instance.SubTitle = LocalizedEnumConverter.ConvertToString(Item.LogRate);
                     }
                 }
             },

                //Language
                new()
                {
                    Title = ResStrings.Language,
                    Command  = new Command<object>(async (context) =>
                    {
                        List<SelectableAction> options = new()
                        {
                            new SelectableAction
                            {
                                Id = DeviceSettingsLanguage.English.ToString(),
                                Title = DeviceSettingsLanguage.English.ToString(),
                                Action = () =>
                                {
                                    Item.Language = DeviceSettingsLanguage.English;
                                    ApplyChanges();
                                    UpdateSettings();
                                }
                            },
                            new SelectableAction
                            {
                                Id = DeviceSettingsLanguage.Russian.ToString(),
                                Title = DeviceSettingsLanguage.Russian.ToString(),
                                Action = () =>
                                {
                                    Item.Language = DeviceSettingsLanguage.Russian;
                                    ApplyChanges();
                                    UpdateSettings();
                                }
                            },
                        };
                        App.SelectAction(options, ResStrings.Language);
                    }),
                    Update = (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            instance.SubTitle = Item.Language.ToString();
                        }
                    }
                },

                //DeviceMaps
                new()
                {
                    Title = ResStrings.DeviceMaps,
                    Command  = new Command<object>(async (context) =>
                    {
                        List<SelectableAction> options = new()
                        {
                            new SelectableAction
                            {
                                Id = DeviceSettingsMapsType.OSM.ToString(),
                                Title = DeviceSettingsMapsType.OSM.ToString(),
                                Action = () =>
                                {
                                    Item.Maps = DeviceSettingsMapsType.OSM;
                                    ApplyChanges();
                                    UpdateSettings();
                                }
                            },
                            new SelectableAction
                            {
                                Id = DeviceSettingsMapsType.YandexMaps.ToString(),
                                Title = DeviceSettingsMapsType.YandexMaps.ToString(),
                                Action = () =>
                                {
                                    Item.Maps = DeviceSettingsMapsType.YandexMaps;
                                    ApplyChanges();
                                    UpdateSettings();
                                }
                            },
                        };
                        App.SelectAction(options, ResStrings.DeviceMaps);
                    }),
                    Update = (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            instance.SubTitle = Item.Maps.ToString();
                        }
                    }
                },

                //RollOut
                new()
                {
                    Title = ResStrings.RollOut,
                    PresentationMode = 1,
                    BooleanValue = Item.Rollout,
                    Command  = new Command<object>(async (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            instance.BooleanValue = !instance.BooleanValue;
                            ApplyChanges();
                            UpdateSettings();
                        }
                    }),
                    Update = (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            Item.Rollout = instance.BooleanValue;
                        }
                    }
                },

                //DevicePrediction
                new()
                {
                    Title = ResStrings.DevicePrediction,
                    PresentationMode = 1,
                    BooleanValue = Item.Prediction,
                    Command  = new Command<object>(async (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            instance.BooleanValue = !instance.BooleanValue;
                            ApplyChanges();
                            UpdateSettings();
                        }
                    }),
                    Update = (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            Item.Prediction = instance.BooleanValue;
                        }
                    }
                },

                //DeviceRandomStart
                new()
                {
                    Title = ResStrings.DeviceRandomStart,
                    PresentationMode = 1,
                    BooleanValue = Item.RandomStart,
                    Command  = new Command<object>(async (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            instance.BooleanValue = !instance.BooleanValue;
                            ApplyChanges();
                            UpdateSettings();
                        }
                    }),
                    Update = (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            Item.RandomStart = instance.BooleanValue;
                        }
                    }
                },

                //MeasuringUnits
                new()
                {
                    Title = ResStrings.MeasuringUnits,
                    Command  = new Command<object>(async (context) =>
                    {
                        List<SelectableAction> options = new()
                        {
                            new SelectableAction
                            {
                                Id = DeviceSettingsMetricsType.EU.ToString(),
                                Title = DeviceSettingsMetricsType.EU.ToString(),
                                Action = () =>
                                {
                                    Item.Units = DeviceSettingsMetricsType.EU;
                                    ApplyChanges();
                                    UpdateSettings();
                                }
                            },
                            new SelectableAction
                            {
                                Id = DeviceSettingsMetricsType.US.ToString(),
                                Title = DeviceSettingsMetricsType.US.ToString(),
                                Action = () =>
                                {
                                    Item.Units = DeviceSettingsMetricsType.US;
                                    ApplyChanges();
                                    UpdateSettings();
                                }
                            },
                        };
                        App.SelectAction(options, ResStrings.MeasuringUnits);
                    }),
                    Update = (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            instance.SubTitle = Item.Units.ToString();
                        }
                    }
                },

                //DeviceMinDistance
                new()
                {
                    Title = ResStrings.DeviceMinDistance,
                    Command  = new Command<object>(async (context) =>
                    {
                        var options = App.GenerateOptionsForEnum<DeviceSettingsMinShowDistance>((selected)=>
                        {
                            Item.MinDistance = selected;
                            ApplyChanges();
                            UpdateSettings();
                        });
                        App.SelectAction(options, ResStrings.DeviceMinDistance);
                    }),
                    Update = (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            instance.SubTitle = LocalizedEnumConverter.ConvertToString(Item.MinDistance);
                        }
                    }
                },

                //DeviceCarWeight
                new()
                {
                    Title = ResStrings.DeviceCarWeight,
                    Command  = new Command<object>(async (context) =>
                    {
                        var min=10;
                        var max = 5000;
                        var vm = new EditValueDoubleWIthEntryViewModel(
                            Item.Weight,
                            min,
                            max,
                            async (v)=>
                            {
                                Item.Weight = (int)v;
                                UpdateSettings();
                                ApplyChanges();
                                return true;
                            });
                        vm.Hint=$"{min} <-> {max}";
                        MainThread.BeginInvokeOnMainThread(async () =>
                        {
                            vm.Title = ResStrings.DeviceCarWeight;
                            await NavbarModel.Shell.Navigation.PushAsync(new PageEditForm(vm));
                        });
                    }),
                    Update = (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            instance.SubTitle = $"{Item.Weight}";
                        }
                    }
                },

                // DeviceDragRatio
                new()
                {
                    Title = ResStrings.DeviceDragRatio,
                    Command  = new Command<object>(async (context) =>
                    {
                        var min = 0.15;
                        var max = 0.6;
                        var vm = new EditValueDoubleWIthEntryViewModel(
                            Item.Drag,
                            min,
                            max,
                            async (v)=>
                            {
                                Item.Drag = v;
                                UpdateSettings();
                                ApplyChanges();
                                return true;
                            });
                        vm.Hint=$"{min} <-> {max}";
                        MainThread.BeginInvokeOnMainThread(async () =>
                        {
                            vm.Title = ResStrings.DeviceDragRatio;
                            await NavbarModel.Shell.Navigation.PushAsync(new PageEditForm(vm));
                        });
                    }),
                    Update = (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            instance.SubTitle = Item.Drag.ToString();
                        }
                    }
                },

                //DeviceFrontal
                new()
                {
                    Title = ResStrings.DeviceFrontal,
                    Command  = new Command<object>(async (context) =>
                    {
                        var min = 1.0;
                        var max = 5.0;
                        var vm = new EditValueDoubleWIthEntryViewModel(
                            Item.Frontal,
                            min,
                            max,
                            async (v)=>
                            {
                                Item.Frontal = v;
                                UpdateSettings();
                                ApplyChanges();
                                return true;
                            });
                        vm.Hint=$"{min} <-> {max}";
                        MainThread.BeginInvokeOnMainThread(async () =>
                        {
                            vm.Title = ResStrings.DeviceFrontal;
                            await NavbarModel.Shell.Navigation.PushAsync(new PageEditForm(vm));
                        });
                    }),
                    Update = (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            instance.SubTitle = Item.Frontal.ToString();
                        }
                    }
                },

                //ObdLog
                /*
                new()
                {
                    Title = "ObdLog",
                    PresentationMode = 1,
                    BooleanValue = Item.ObdLog,
                    Command  = new Command<object>(async (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            instance.BooleanValue = !instance.BooleanValue;
                            ApplyChanges();
                            UpdateSettings();
                        }
                    }),
                    Update = (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            Item.ObdLog = instance.BooleanValue;
                        }
                    }
                },
                */
                
                //shift
                new()
                {
                    Title = ResStrings.DeviceShiftTime,
                    PresentationMode = 1,
                    BooleanValue = Item.ShiftTime,
                    Command  = new Command<object>(async (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            instance.BooleanValue = !instance.BooleanValue;
                            ApplyChanges();
                            UpdateSettings();
                        }
                    }),
                    Update = (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            Item.ShiftTime = instance.BooleanValue;
                        }
                    }
                },

                //DeviceObdPidAuto
                /*
                new()
                {
                    Title = ResStrings.DeviceObdPidAuto,
                    PresentationMode = 1,
                    BooleanValue = Item.ObdPidAuto,
                    Command  = new Command<object>(async (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            instance.BooleanValue = !instance.BooleanValue;
                            ApplyChanges();
                            UpdateSettings();
                        }
                    }),
                    Update = (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            Item.ObdPidAuto = instance.BooleanValue;
                        }
                    }
                },
                */

                //DeviceGnssUartOut
                /*
                new()
                {
                    Title = ResStrings.DeviceGnssUartOut,
                    PresentationMode = 1,
                    BooleanValue = Item.GnssUartOut,
                    Command  = new Command<object>(async (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            instance.BooleanValue = !instance.BooleanValue;
                            ApplyChanges();
                            UpdateSettings();
                        }
                    }),
                    Update = (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            Item.GnssUartOut = instance.BooleanValue;
                        }
                    }
                },
                */

                //DeviceGnssColdStart
                new()
                {
                    Title =ResStrings.DeviceGnssColdStart,
                    Command  = new Command<object>(async (context) =>
                    {
                        if (context is ActionOption instance)
                        {
                            MainThread.BeginInvokeOnMainThread(async () => {
                                var ok = await UI.Prompt(ResStrings.AreYouSure, ResStrings.WarningCannotUndo, ResStrings.Yes, ResStrings.No);
                                if (!ok)
                                    return;

                                Item.GnssColdStart=true;
                                ApplyChanges();

                                await SaveSettings(false);
                                NavbarModel.CommandGoBack.Execute(null);
                            });
                        }
                    }),
                },

                //ResetDevice
                new()
                {
                    Title = ResStrings.ResetDevice,
                    Command  = new Command<object>(async (context) =>
                    {
                        if (context is ActionOption instance)
                        {

                                 MainThread.BeginInvokeOnMainThread(async () => {
                                     var ok = await UI.Prompt(ResStrings.AreYouSure, ResStrings.WarningCannotUndo, ResStrings.Yes, ResStrings.No);
                                     if (!ok)
                                         return;

                                     Item.ResetDevice=true;
                                     ApplyChanges();

                                     await SaveSettings(false);
                                     NavbarModel.CommandGoBack.Execute(null);

                                    });

                        }
                    })
                },

                //DeviceResetConfiguration
                new()
                {
                    Title = ResStrings.DeviceResetConfiguration,
                    Command  = new Command<object>(async (context) =>
                    {
                        if (context is ActionOption instance)
                        {

                         MainThread.BeginInvokeOnMainThread(async () => {
                             var ok = await UI.Prompt(ResStrings.AreYouSure, ResStrings.WarningCannotUndo, ResStrings.Yes, ResStrings.No);
                             if (!ok)
                                 return;

                             Item.ResetConfiguration=true;
                             ApplyChanges();

                             await SaveSettings(false);

                             NavbarModel.CommandGoBack.Execute(null);

                            });

                        }
                    })
                },

            };

        UpdateSettings();

        DeviceDescription = _device.DeviceDescription;
    }

    public void UpdateSettings()
    {
        if (SettingsFields == null)
        {
            InitSettings();
        }
        else
        {
            foreach (var option in SettingsFields.ToArray())
            {
                if (option.Update != null)
                {
                    option.Update(option);
                }
            }
        }
    }

    #endregion

    private string _DeviceDescription;
    public string DeviceDescription
    {
        get
        {
            return _DeviceDescription;
        }
        set
        {
            if (_DeviceDescription != value)
            {
                _DeviceDescription = value;
                OnPropertyChanged();
            }
        }
    }

    public void Initialize()
    {

        if (!ImportSettings())
        {

            Tasks.StartDelayed(TimeSpan.FromSeconds(2), async () =>
            {
                IsBusy = true;

                await LoadSettings();

                IsBusy = false;

                if (Item == null)
                    NavbarModel.CommandGoBack.Execute(null);

                Initialized = true;
            });

            return;
        }

        InitSettings();

        Initialized = true;
    }


    public bool Initialized { get; set; }

    #region save on timer

    private RaceBoxDeviceViewModel _device;

    protected RestartingTimer<object> TimerUpdateLocked;
    private CancellationTokenSource _cancel;

    protected bool ImportSettings()
    {
        if (Processor.RaceBoxSettingsState == null)
        {
            if (_userManager.User.Options.IsDemo)
            {
                //App.Instance.UI.ShowToast(ResStrings.DemoMode);

                if (Item == null)
                {
                    Item = new();
                }

                return true;
            }
            return false;
        }

        if (Item == null)
        {
            Item = new();
        }
        Reflection.MapProperties(Processor.RaceBoxSettingsState, Item);
        return true;
    }

    protected Task LoadSettings(CancellationToken cancelToken = default)
    {
        if (!_device.Connector.IsConnected)
            return Task.CompletedTask;

        var tcs = new TaskCompletionSource<bool>();

        cancelToken.Register(() =>
        {
            tcs.TrySetCanceled();
        });

        try
        {
            _settings.ObtainSettings(() =>
            {
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    try
                    {
                        ImportSettings();
                        UpdateSettings();
                        HasChanges = false;

                        tcs.SetResult(true);
                    }
                    catch (Exception e)
                    {
                        tcs.SetException(e);
                    }
                });
            });
        }
        catch (Exception e)
        {
            Super.Log(e);
            App.Instance.UI.ShowToast(e.Message);
            IsBusy = false;
            tcs.SetCanceled();
        }

        return tcs.Task;
    }



    protected async Task SaveSettings(bool reload)
    {
        if (IsBusy || !_device.Connector.IsConnected)
            return;

        if (_userManager.User.Options.IsDemo)
        {
            HasChanges = false;
            App.Instance.UI.ShowToast(ResStrings.DemoMode);
            return;
        }

        //todo save maybe locally so if offline can apply later when connected
        IsBusy = true;

        _cancel?.Cancel();

        _cancel = new CancellationTokenSource(TimeSpan.FromSeconds(10));

        try
        {
            Processor.ClearDeviceSettings();

            await _device.Connector.WriteSettings(Item);

            if (reload)
            {
                await Task.Delay(2000);
                await LoadSettings(_cancel.Token);
            }
        }
        catch (Exception e)
        {
            Super.Log(e);
            App.Instance.UI.ShowToast(e.Message);
        }
        finally
        {
            IsBusy = false;
        }
    }

    //protected void LauchRestartingTimer_Save(int ms)
    //{
    //    if (TimerUpdateLocked == null)
    //    {
    //        TimerUpdateLocked = new RestartingTimer<object>(TimeSpan.FromMilliseconds(ms), async (context) =>
    //        {
    //            await SaveSettings();
    //        });
    //        TimerUpdateLocked.Start(null);
    //    }
    //    else
    //    {
    //        TimerUpdateLocked.Restart(null);
    //    }
    //}


    #endregion


}