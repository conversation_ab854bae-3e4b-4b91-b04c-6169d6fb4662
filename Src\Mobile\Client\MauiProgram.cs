﻿global using AppoMobi.Maui.Gestures;
global using AppoMobi.Maui.Navigation;
global using AppoMobi.Specials;
global using AppoMobi.Specials;
global using DrawnUi.Controls;
global using DrawnUi.Draw;
global using DrawnUi.Extensions;
global using DrawnUi.Views;
global using Racebox.Enums;
global using Racebox.Helpers;
global using Racebox.Models;
global using Racebox.Services;
global using Racebox.Shared.Interfaces;
global using Racebox.Shared.Models;
global using Racebox.ViewModels;
global using Racebox.Views;
global using AppoMobi.Framework.Maui.Models;
global using AppoMobi.Framework;
global using AppoMobi.Framework.Maui;
global using AppoMobi.Framework.Maui.Interfaces;
using AppoMobi.Maui.BLE;
using FastPopups;
using Mapster;
using MapsterMapper;
using Microsoft.Extensions.Logging;
using Mopups.Hosting;
using Racebox.SDK;
using Racebox.Shared;
using Racebox.Shared.Services;
using Racebox.Shared.WeatherApi;
using Racebox.Shared.ChartApi;
using Racebox.ViewModels.Navigation;
using AppStorage = Racebox.Services.AppStorage;


#if ANDROID
using Microsoft.Maui.Controls.Compatibility.Platform.Android;
using Racebox.Platforms.Android;
#endif

#if WINDOWS
using static Microsoft.Maui.LifecycleEvents.WindowsLifecycle;
using Microsoft.UI.Xaml;
using Application = Microsoft.UI.Xaml.Application;
#endif

namespace Racebox;

public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();

        builder.UseMauiApp<App>();

#if (IOS || MACCATALYST)
        //disable metal
        Super.CanUseHardwareAcceleration = false;
#endif

        builder.UseDrawnUi(new()
        {
            DesktopWindow = new()
            {
                Height = 700,
                Width = 350,
                //IsFixedSize = true
            }
        });

        TouchEffect.LongPressTimeMsDefault = 1200;

        SkiaMarkdownLabel.ColorLink = Colors.CornflowerBlue;

        builder.ConfigureMopups();

        SkiaHotspot.DelayCallbackMs = 250;

        builder
            .ConfigureFonts(fonts =>
            {
                fonts.AddFont("FordAntennaWGL-Regular.ttf", "FontText");
                fonts.AddFont("FordAntennaWGL-Light.ttf", "FontTextLight");
                fonts.AddFont("FordAntennaWGL-Medium.ttf", "FontTextBold");
                fonts.AddFont("fa-regular-400.ttf", "Fa");
                fonts.AddFont("fa-solid-900.ttf", "FaSolid");
            });

#if DEBUG
        builder.Logging.AddDebug();
#endif

        //EXTERNAL LIBS
        builder
            .UseBlootoothLE() //will inject IBluetoothLE
            .AddPopups();

        //MAPSTER
        builder.Services.AddSingleton(GetConfiguredMappingConfig()); //todo optimize
        builder.Services.AddScoped<IMapper, ServiceMapper>();

        //APP INFRASTRUCTURE
        builder.Services.AddSingleton<IAppStorage, AppStorage>();
        builder.Services.AddSingleton<IUIAssistant, UIAssistant>();
        builder.Services.AddSingleton<IInAppMessager, AppMessager>();
        builder.Services.AddSingleton<NavigationViewModel>();

        //BUSINESS LOGIC
        builder.Services.AddSingleton<UserManager>();
        builder.Services.AddSingleton<RaceBoxConnector>();
        builder.Services.AddSingleton<RaceBoxDeviceSettings>();
        builder.Services.AddSingleton<RaceBoxDeviceViewModel>();
        builder.Services.AddSingleton<RaceBoxStateProcessor>();

        //WEATHER
        builder.Services.AddWeatherApi();

        //CHART API  
        builder.Services.AddChartApi();

        //SCREENS
        builder.Services.AddTransient<DevicesPageViewModel>();
        builder.Services.AddTransient<HistoryViewModel>();
        builder.Services.AddTransient<SettingsViewModel>();
        builder.Services.AddTransient<HistoryResultViewModel>();

        builder.Services.AddTransient<PageSettingsDevice>();
        builder.Services.AddTransient<DeviceSettingsViewModel>();

        //DATABASE
        //можно сменить имя если надо сбросить все данные для новой версии
        //настройки программы НЕ хранятся в бд, только замеры!
        //бд медленее, чем хранилище IAppPreferences, куда можно писать в т.ч. json
        builder.Services.AddTransient<LocalDatabase>((services) =>
        {
            return new LocalDatabase(Path.Combine(FileSystem.AppDataDirectory, "SQLite002.db3"));
        });

#if ANDROID

        builder.ConfigureMauiHandlers(handlers =>
        {
            handlers.AddHandler(typeof(AppFastShell), typeof(FixedFlyoutHandler));
        });

        Microsoft.Maui.Handlers.EntryHandler.Mapper.AppendToMapping("NoUnderline", (h, v) =>
        {
            // Remove underline:
            h.PlatformView.BackgroundTintList = Android.Content.Res.ColorStateList.ValueOf(Colors.Transparent.ToAndroid());
        });

#endif

#if IOS


        //builder.ConfigureMauiHandlers(collection => collection
        //	.AddHandler(typeof(ScrollView), typeof(Racebox.Platforms.iOS.FixedScrollViewHandler))

        //);

        Microsoft.Maui.Handlers.EntryHandler.Mapper.AppendToMapping("NoUnderline", (h, v) =>
            {
                h.PlatformView.BorderStyle = UIKit.UITextBorderStyle.None;
            });


#endif


#if MACCATALYST

        builder.ConfigureMauiHandlers(collection => collection
                    .AddHandler(typeof(FlyoutPage), typeof(Racebox.CustomViewController))

                );

        Microsoft.Maui.Handlers.EntryHandler.Mapper.AppendToMapping("NoUnderline", (h, v) =>
            {
                h.PlatformView.BorderStyle = UIKit.UITextBorderStyle.None;
            });

        Microsoft.Maui.Handlers.WindowHandler.Mapper.AppendToMapping(nameof(IWindow),
                    (handler, view) =>
                    {

                        var size = new CoreGraphics.CGSize(600, 900);
                        handler.PlatformView.WindowScene.SizeRestrictions.MinimumSize = size;
                        handler.PlatformView.WindowScene.SizeRestrictions.MaximumSize = size;
                    });

#endif

#if WINDOWS

        //preload System.Net.Security
        //var check = new HttpClient();

        ////preload Microsoft.CSharp
        //try
        //{
        //    var check1 = new Microsoft.Maui.Controls.ResourceDictionary().Get<string>("SvgStatusOk");
        //}
        //catch (Exception e)
        //{
        //}

        Super.CanUseHardwareAcceleration = false;

#endif

        return builder.Build();

    }


    /// <summary>
    /// Mapster(Mapper) global configuration settings
    /// To learn more about Mapster,
    /// see https://github.com/MapsterMapper/Mapster
    /// </summary>
    /// <returns></returns>
    private static TypeAdapterConfig GetConfiguredMappingConfig()
    {
        var config = TypeAdapterConfig.GlobalSettings;

        // Поскольку нам важна скорость запуска мы не используем конфиги
        // мапера в отдельных класса с IRegister а все прописываем в одном
        var registers = new List<IRegister>()
        {
            new MapperConfiguration(),
            new SharedMapperConfiguration()
        }; //config.Scan(Assembly.GetExecutingAssembly());

        config.Apply(registers);

        return config;
    }


    public static bool UseHardwareAcceleration
    {
        get
        {
            return true;
        }
    }

    public static bool FixLayouts
    {
        get
        {

#if ANDROID || WINDOWS
            return false;
#endif
            return true;
        }
    }

    public static bool ShowDebugInfo
    {
        get
        {
#if DEBUG
            return true;
#endif
            return false;
        }
    }


    public static TouchActionEventHandler TouchAnimateShimmer = (sender, args) =>
    {
        if (sender is IHasAfterEffects animated)
        {
            var totalX = Super.Screen.Density * args.Distance.Total.X;
            var totalY = Super.Screen.Density * args.Distance.Total.Y;
            if (totalX < 10 && totalY < 10)
            {
                Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(10), async () =>
                {
                    animated.PlayShimmerAnimation(Colors.White, 50, 30, 500, true);
                    return false;
                });
            }
        }
    };

    public static TouchActionEventHandler TouchAnimateRipple = (sender, args) =>
    {
        if (sender is IHasAfterEffects animated)
        {
            var totalX = Super.Screen.Density * args.Distance.Total.X;
            var totalY = Super.Screen.Density * args.Distance.Total.Y;
            if (totalX < 10 && totalY < 10)
            {
                Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(10), async () =>
                {
                    animated.PlayRippleAnimation(Colors.White, args.Location.X / Super.Screen.Density, args.Location.Y / Super.Screen.Density);
                    return false;
                });
            }
        }
    };




}


