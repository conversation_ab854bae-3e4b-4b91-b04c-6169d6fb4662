using AppoMobi.Framework.Maui.Controls.Navigation.Tabs;
using AppoMobi.Maui.Navigation;

namespace Racebox.Views;

public partial class TabMonitor : ILazyTab
{
	public bool IsDisposed { get; }
	public TabMonitor()
	{
		try
		{
			BindingContext = _viewModel = App.Instance.Services.GetService<RaceBoxDeviceViewModel>();

			InitializeComponent();
		}
		catch (Exception e)
		{
			Designer.DisplayException(this, e);
		}
	}

	private readonly RaceBoxDeviceViewModel _viewModel;

	public void Dispose()
	{
		//todo
	}

	public void UpdateControls(DeviceRotation orientation)
	{

	}

	public void OnViewAppearing()
	{
		if (!_viewModel.Initialized)
		{
			Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(3000), async () =>
			{
				_viewModel.Init();
				return false;
			});
		}

		MonitorTable.EnableUpdates();
	}

	public void OnViewDisappearing()
	{

	}

	public void KeyboardResized(double size)
	{

	}

	private void OnTappedG(object sender, EventArgs e)
	{

		MainThread.BeginInvokeOnMainThread(() =>
		{
			LetterG.Opacity = 0.5;
			Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(50), async () =>
			{
				MainThread.BeginInvokeOnMainThread(() =>
				{
					LetterG.Opacity = 0.15;
				});
				return false;
			});
		});

		MinMaxBarG.ResetMinMax();
		MinMaxBarGVertical.ResetMinMax();
	}
}