using AppoMobi.Framework.Maui.Controls.Navigation.Tabs;
using AppoMobi.Maui.Navigation;

namespace Racebox.Views;

public partial class PageChartDetails : BasePage, ILazyTab
{
    private readonly HistoryResultViewModel _viewModel;

    public PageChartDetails(HistoryResultViewModel vm)
    {
        BindingContext = _viewModel = vm;

        InitializeComponent();

    }

    public void Dispose()
    {
        //todo
    }

    public void UpdateControls(DeviceRotation orientation)
    {

    }
 
    public void OnViewAppearing()
    {
 
    }

 

    public void OnViewDisappearing()
    {
 
    }

}