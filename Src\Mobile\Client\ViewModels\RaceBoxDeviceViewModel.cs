﻿using AppoMobi.Maui.BLE.EventArgs;
using MapsterMapper;
using Mopups.Services;
using Racebox.SDK;
using Racebox.Shared.Enums;
using Racebox.Shared.Extensions;
using Racebox.Shared.Services;
using Racebox.Shared.Strings;
using Racebox.Views.Popups;
using System.Diagnostics;
using System.Linq;
using System.Windows.Input;
using AppoMobi.Framework.Maui.Interfaces;
using AppoMobi.Framework.Maui.Models;


namespace Racebox.ViewModels
{

    public partial class RaceBoxDeviceViewModel : ContentViewModel
    {


        public RaceBoxDeviceViewModel(
            IServiceProvider services,
            IAppStorage preferences,
            RaceBoxStateProcessor processor,
            WeatherService weather,
            UserManager manager, IUIAssistant ui, IMapper mapper) : base(manager, ui, mapper)
        {
            _preferences = preferences;
            _services = services;
            _weather = weather;

            Processor = processor;// new RaceBoxStateProcessor(this._userManager);

            //MeasureResults.Add(new MeasuredDistance
            //{
            //    Id = intTest++,
            //    Length = 100,
            //    Time = TimeSpan.FromSeconds(12),
            //    Speed = 200
            //});

            UpdateUi();

            SetIsMock();
        }

        public void ShowFirmwareWarning()
        {
            var popup = new FirmwarePopup();
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                await MopupService.Instance.PushAsync(popup);
            });
        }

        public bool UpdateCurrentWeather(bool forceRefresh = false)
        {
            if (!GpsStrengthOk
                || Processor.Latitude == RaceBoxStateProcessor.DataPlaceholder
                || Processor.Longitude == RaceBoxStateProcessor.DataPlaceholder)
                return false;

            Task.Run(async () =>
            {

                var weather = await _weather.GetCurrentWeather(Processor.Latitude, Processor.Longitude, forceRefresh);
                if (weather != null)
                {
                    this.TempC = weather.TempC;
                    this.TempF = weather.TempF;
                    this.WeatherIcon = weather.Icon;

                    SetProperty_Temp();
                }

            }).ConfigureAwait(false);

            return true;
        }


        public Command CommandUpdateWeather => new Command(async () =>
        {
            if (CheckLockAndSet() || _weatherTimer)
                return;

            _weatherTimer = true;

            if (UpdateCurrentWeather(true))
            {
                await Task.Delay(5000);
            }

            _weatherTimer = false;
        });

        protected virtual void OnGpsStrengthOk()
        {
            //delay for coords props to be surely filled with data
            Tasks.StartDelayed(TimeSpan.FromSeconds(1), () => UpdateCurrentWeather());
        }

        public override void OnSubscribing(bool subscribe)
        {
            if (subscribe)
            {
                App.Instance.Messager.Subscribe<string>(this, "User", async (sender, arg) =>
                {
                    ResetUi();

                    //MainThread.BeginInvokeOnMainThread(() =>
                    //{
                    //    Processor.Reset();
                    //    ResetUi();

                    //});
                });
            }
            else
            {
                App.Instance.Messager.Unsubscribe(this, "User");
            }

            base.OnSubscribing(subscribe);
        }



        public void Init()
        {
            if (Initialized)
                return;

            Initialized = true;

            InitializeInfrastructure(); //уже после асинхронного запуска БТ

            //if (DeviceInfo.DeviceType != DeviceType.Virtual) // emulator / simulator
            //    CommandConnect.Execute(null);

        }

        /// <summary>
        /// Launch db migration etc
        /// </summary>
        public async void InitializeInfrastructure()
        {

            //migrate database if needed
            try
            {
                var db = GetDatabase();
                var check = db.Results.Count();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    await App.Instance.UI.Alert(ResStrings.Error, e.ToString());
                    Console.WriteLine(e);
                    // throw null; //намеренно вылетаем ибо критично
                });

            }



        }



        public ObservableRangeCollection<IHasDetailedDisplay> MeasureResults { get; } = new();

        private int _ScrollToIndex = -1;
        public int ScrollToIndex
        {
            get { return _ScrollToIndex; }
            set
            {
                if (_ScrollToIndex != value)
                {
                    _ScrollToIndex = value;

                    if (value != -1)
                        OnPropertyChanged();
                }
            }
        }

        public void ScrollToStart()
        {
            ScrollToIndex = 0;
            LockListScrollDetectionAtStart = false;
        }

        public bool LockListScrollDetectionAtStart { get; set; }

        private IAppStorage _preferences;




        #region CONNECT





        private static int intTest;
        public Command CommandTest => new Command(async () =>
        {
            Console.WriteLine("CommandTest");
        });

        public Command CommandAddMockResult => new Command(async () =>
        {
            intTest++;
            AddResultToUi(new MeasuredRange
            {
                Id = intTest,
                Start = intTest,
                End = 60 + intTest,
                Units = OptionsUnits.US,
                Time = TimeSpan.FromSeconds(10),
            }, true);
        });

        public Command CommandTestEffects => new Command(async () =>
        {
            if (LastMeasureResultCode == 2)
            {
                LastMeasureResultCode = 1;
            }
            else
            {
                LastMeasureResultCode = 2;
            }
        });

        public Command CommandClearMockResults => new Command(async () =>
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                MeasureResults.Clear();
                ScrollToIndex = -1;
            });
        });

        public Command CommandSwitchTestProp => new Command(async () =>
        {
            TestProp = !TestProp;
        });

        public Command CommandConnect => new Command(async () => await Connect());

        public Command CommandSwitchConnect => new Command(async () => await SwitchConnect());
        public Command CommandSwitchMeasuring => new Command(async () => await ToggleMeasuring());

        public async Task ToggleMeasuring()
        {
            if (IsConnected)
            {
                if (!Processor.IsMeasuring)
                {
                    TurnOnMeasuring();
                }
                else
                {
                    TurnOffMeasuring();
                }
            }
        }

        void TurnOnMeasuring()
        {
            ResetUi();
            Processor.IsMeasuring = true;
        }

        /// <summary>
        /// Resets Processor data too !!!
        /// </summary>
        void ResetUi()
        {
            LastMeasureResultCode = 0;

            Processor.Reset();

            SelectedResult = null;
            LastResult = null;

            MainThread.BeginInvokeOnMainThread(() =>
            {
                MeasureResults.Clear();
            });

            SetIsMock();

            UpdateUi();
        }

        void SetIsMock()
        {
            UseMock = _userManager.User.Options.IsDemo;

        }

        private string _LastDeviceId;
        public string LastDeviceId
        {
            get
            {
                return _LastDeviceId;
            }
            set
            {
                if (_LastDeviceId != value)
                {
                    _LastDeviceId = value;
                    OnPropertyChanged();
                    _preferences.Set("LastDevice", LastDeviceId);
                }
            }
        }


        async Task SwitchConnect()
        {
            if (Connector.IsConnected)
            {
                //there is bug in windows connector hanging disconnect!!!
                //todo
                await Disconnect();
            }
            else
            {
                await Connector?.Disconnect();
                LastDeviceId = null;
                await Connect();
            }
        }

        public async Task Disconnect()
        {
            await Connector?.Disconnect();
            LastDeviceId = null;
        }


        private SemaphoreSlim semaphoreConnector = new(1, 1);

        async Task Connect()
        {
            await semaphoreConnector.WaitAsync();

            try
            {
                if (!IsBusy && !Connector.IsBusy)
                {
                    IsBusy = true;

                    LastDeviceId = _preferences.Get("LastDevice", string.Empty);

                    if (string.IsNullOrEmpty(LastDeviceId)) //todo get from prefs last uid
                    {
                        //need find available devices

                        var ok = await Connector.ScanForCompatibleDevices();

                        if (!ok)
                        {
                            throw new Exception("Scan failed");
                        }

                        var devices = Connector.FoundDevices.Where(x => x.Device.Name != null
                        && x.Device.Name.ToLower().Contains("racebox#")).DistinctBy(x => x.Device.Name).ToList();

                        if (devices.Any())
                        {
                            if (devices.Count > 1)
                            {
                                MainThread.BeginInvokeOnMainThread(async () =>
                                {
                                    var options = devices.Select(x => new SelectableAction
                                    {
                                        Action = () =>
                                        {
                                            Connector.Serial = x.Id;
                                        },
                                        Id = x.Id,
                                        Title = x.Device.Name
                                    }).ToList();

                                    var selected = await UI.PresentSelection(options) as SelectableAction;
                                    if (selected != null)
                                    {
                                        try
                                        {
                                            selected?.Action?.Invoke();
                                            await ConnectWithSetup();
                                        }
                                        catch (Exception e)
                                        {
                                            Debug.WriteLine(e);
                                            LastDeviceId = null;
                                        }
                                    }

                                });
                                return;
                            }

                            Connector.Serial = devices.First().Id;
                        }
                        else
                        {
                            MainThread.BeginInvokeOnMainThread(async () =>
                            {
                                await UI.Alert(ResStrings.VendorTitle, ResStrings.CompatibleDevicesNotFound);
                            });

                            return;
                        }

                    }
                    else
                    {
                        //try to connect to last device
                        Connector.Serial = LastDeviceId;
                    }

                    await Connector.ConnectToSerialDevice(true);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine(ex);
                LastDeviceId = null;
            }
            finally
            {
                IsBusy = false;

                semaphoreConnector.Release();

                UpdateUi();
            }

        }

        async Task ConnectWithSetup()
        {
            if (!string.IsNullOrEmpty(Connector.Serial))
            {
                //connect
                await Connector.ConnectToSerialDevice(true);

                LastDeviceId = Connector.Serial;
            }
        }


        #endregion

        private double _Value1;
        public double Value1
        {
            get
            {
                return _Value1;
            }
            set
            {
                if (_Value1 != value)
                {
                    _Value1 = value;
                    OnPropertyChanged();
                }
            }
        }

        public ICommand CommandStartSimulation => new Command(async () =>
        {
            if (CheckLockAndSet("CommandStartSimulation"))
                return;

            if (UseMock && IsConnected)
            {

                if (IsMocking)
                {
                    IsMocking = false;
                    await Task.Delay(100); //let previous timer end
                }

                async Task SelectFile(string fileName, bool pressStart)
                {

                    try
                    {
                        using var stream = await FileSystem.OpenAppPackageFileAsync($"Logs/{fileName}");
                        using var reader = new StreamReader(stream);

                        //var content = await reader.ReadToEndAsync();

                        var lines = await ParseCsvLogs(stream);

                        if (lines.Any())
                        {
                            IsMocking = true;

                            var lineIndex = 0;
                            var hz = 1000 / 62.0;
                            var delay = 1000 / hz;

                            if (pressStart)
                            {
                                TurnOnMeasuring();
                            }

                            Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(delay), async () =>
                            {
                                var repeat = IsMocking;

                                if (lineIndex < lines.Count)
                                {
                                    var line = lines[lineIndex];
                                    Processor.OnNewDataReceived(null, RaceBoxState.FromHardwareLog(line));
                                    lineIndex++;
                                }
                                else
                                {
                                    repeat = false;
                                }

                                return repeat;
                            });
                        }



                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex);
                        IsMocking = false;
                    }


                }

                var logs = App.Native.ListAssets("Logs");

                if (logs.Any())
                {
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {

                        var needStart = false; //await _ui.Prompt(ResStrings.VendorTitle, "Нажать СТАРТ перед симуляцией?", ResStrings.Yes, ResStrings.No);

                        if (logs.Count() > 1)
                        {
                            var options = logs.Select(name => new SelectableAction
                            {
                                Action = async () =>
                                {
                                    await SelectFile(name, needStart);
                                },
                                Title = name
                            }).ToList();
                            var selected = await UI.PresentSelection(options) as SelectableAction;
                            selected?.Action();
                        }
                        else
                        {
                            await SelectFile(logs.First(), needStart);
                        }
                    });

                }
            }


        });

        private bool _IsMocking;
        public bool IsMocking
        {
            get
            {
                return _IsMocking;
            }
            set
            {
                if (_IsMocking != value)
                {
                    _IsMocking = value;
                    OnPropertyChanged();
                }
            }
        }


        public async Task<List<HardwareLogLine>> ParseCsvLogs(Stream stream)
        {
            List<HardwareLogLine> logLines = new();
            Dictionary<string, int> headerMap = new Dictionary<string, int>();

            using var reader = new StreamReader(stream);
            string line;
            bool isFirstLine = true;
            TimeSpan firstTimeSpan = TimeSpan.Zero;
            var currentDate = DateTime.Now;

            while ((line = await reader.ReadLineAsync()) != null)
            {
                if (isFirstLine)
                {
                    isFirstLine = false;
                    var headerFields = line.Split(';');
                    for (int i = 0; i < headerFields.Length; i++)
                    {
                        headerMap[headerFields[i]] = i;
                    }
                    continue;
                }

                var fields = line.Split(';');
                var time = TimeSpan.Parse(fields[headerMap["Time"]]);

                if (firstTimeSpan == TimeSpan.Zero)
                {
                    firstTimeSpan = time;
                }

                DateTime dateTime = new DateTime(
                    currentDate.Year,
                    currentDate.Month,
                    currentDate.Day,
                    time.Hours,
                    time.Minutes,
                    time.Seconds,
                    time.Milliseconds);

                var logLine = new HardwareLogLine
                {
                    Time = dateTime,
                    Lattitude = AppoMobi.Specials.StringExtensions.ToDouble(fields[headerMap["Lat"]]),
                    Longitude = AppoMobi.Specials.StringExtensions.ToDouble(fields[headerMap["Lon"]]),
                    Speed = AppoMobi.Specials.StringExtensions.ToDouble(fields[headerMap["Speed (km/h)"]]),
                    Altitude = AppoMobi.Specials.StringExtensions.ToDouble(fields[headerMap["Alt (m)"]]),
                    Heading = AppoMobi.Specials.StringExtensions.ToDouble(fields[headerMap["Course (deg)"]]),
                    HDOP = AppoMobi.Specials.StringExtensions.ToDouble(fields[headerMap["HDOP"]]),
                    SatellitesCount = AppoMobi.Specials.StringExtensions.ToInteger(fields[headerMap["Sats"]])
                };

                logLines.Add(logLine);
            }

            return logLines;
        }


        /// <summary>
        /// FOR USE IN UI
        /// </summary>
        public RaceBoxStateProcessor Processor { get; }

        private IServiceProvider _services;
        private IRaceBoxConnector _connector;

        private IRaceBoxConnector _connectorMock;
        public IRaceBoxConnector Connector
        {
            get
            {
                if (_connectorMock == null)
                {
                    _connectorMock = new RaceBoxConnectorMock();
                    if (!_connectorMock.Initialized)
                    {
                        _connectorMock.Init(Application.Current.MainPage, ResStrings.VendorTitle);
                    }
                    _connectorMock.StateChanged += OnConnectionChanged;
                    _connectorMock.DeviceConnectionChanged += OnDeviceConnectionChanged;
                }

                if (_connector == null)
                {
                    _connector = _services.GetService<RaceBoxConnector>();
                    if (!_connector.Initialized)
                    {
                        _connector.Init(Application.Current.MainPage, ResStrings.VendorTitle);
                    }
                    _connector.StateChanged += OnConnectionChanged;
                    _connector.DeviceConnectionChanged += OnDeviceConnectionChanged;


                    Processor.Init(_connector);
                    Processor.MeasuringChanged += OnMeasuringChanged;
                    Processor.MeasuredDistance += OnMeasuredDistance;
                    Processor.MeasuredRange += OnMeasuredRange;
                    Processor.MeasureSucces += OnMeasureSuccess;
                    Processor.MeasureFail += OnMeasureFailed;
                    Processor.DataProcessed += OnDataProcessed;
                    Processor.ExtDataChanged += OnExtDataChanged;
                }

                return UseMock ? _connectorMock : _connector;
            }
        }


        private bool _UseMock;
        public bool UseMock
        {
            get
            {
                return _UseMock;
            }
            set
            {
                if (_UseMock != value)
                {
                    _UseMock = value;
                    OnPropertyChanged();
                    Task.Run(() =>
                    {
                        IsMocking = false;
                        Connector?.Disconnect();
                    });
                }
            }
        }



        void UpdateCurrentPath()
        {
            var path = new List<(double, double)>();
            if (_lastPath != null)
            {
                path.AddRange(_lastPath);
            }
            path.Add((Processor.RaceBoxState.Latitude.GetValueOrDefault(), Processor.RaceBoxState.Longitude.GetValueOrDefault()));

            Path = path;

            _lastPath = path;
        }

        private void OnDataProcessed(object sender, EventArgs e)
        {
            UpdateUi();

            //UpdateCurrentPath();
        }

        private IEnumerable<(double, double)> _lastPath;

        private IEnumerable<(double, double)> _Path;
        public IEnumerable<(double, double)> Path
        {
            get
            {
                return _Path;
            }
            set
            {
                if (_Path != value)
                {
                    _Path = value;
                    OnPropertyChanged();
                }
            }
        }

        private void OnMeasureFailed(object sender, MeasureError e)
        {
            //            ResetUi();


            if (!string.IsNullOrEmpty(Processor.EndCause))
            {
                UI.ShowToast(Processor.EndCause);
            }
        }


        public MeasureResult LastResult { get; set; }

        private int _LastMeasureResultCode;
        public int LastMeasureResultCode
        {
            get
            {
                return _LastMeasureResultCode;
            }
            set
            {
                if (_LastMeasureResultCode != value)
                {
                    _LastMeasureResultCode = value;
                    OnPropertyChanged();
                }
            }
        }


        private void OnMeasureSuccess(object sender, MeasureResult result)
        {

            LastResult = result;

            if (LastResult.IsValid)
                LastMeasureResultCode = 1;
            else
            {
                LastMeasureResultCode = 2;
                UI.ShowToast(ResStrings.BadMeasureIncline);
            }

            SelectedResult = result;



            Task.Run(async () => //async in background
             {
                 await _userManager.InsureUserIsInDatabase();

                 result.AppUserId = _userManager.User.Id;
                 if (result.CarId == 0)
                 {
                     result.CarId = _userManager.User.Options.CarId;
                 }

                 var db = GetDatabase();
                 await db.AddResult(result);

                 var weather = _weather.CurrentWeather;
                 if (weather != null)
                 {
                     var save = _mapper.Map<Weather>(weather);
                     save.MeasureResultId = result.Id;
                     result.CurrentWeather = save;
                     await db.UpdateResult(result);
                 }

             }).ConfigureAwait(false);



            UpdateUi();
        }


        /// <summary>
        /// Статус блутус, это НЕ статус подключения к устройству
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void OnConnectionChanged(object sender, BluetoothStateChangedArgs e)
        {
            if (!IsConnected)
            {
                TurnOffMeasuring();

                //clear everything
                ResetUi();
            }

            UpdateUi();
        }

        private bool FirmwareIncompatible;
        private bool FirmwareChecked;



        private void OnExtDataChanged(object sender, RaceBoxExtendedState info)
        {
            if (!FirmwareChecked)
            {
                if (info.VersionMajor == 4 && (info.VersionMinor == 7 || info.VersionMinor == 8))
                {
                    FirmwareIncompatible = true;
                }

                DeviceDescription = $"Racebox {info.DeviceType} {info.VersionMajor}.{info.VersionMinor:00}";
            }

            if (FirmwareIncompatible && !FirmwareChecked)
            {
                //display warning
                FirmwareChecked = true;
                Disconnect().ConfigureAwait(false);
                ShowFirmwareWarning();
            }
        }

        private string _DeviceDescription;
        public string DeviceDescription
        {
            get
            {
                return _DeviceDescription;
            }
            set
            {
                if (_DeviceDescription != value)
                {
                    _DeviceDescription = value;
                    OnPropertyChanged();
                }
            }
        }
        private void OnDeviceConnectionChanged(object sender, bool isConnected)
        {
            //Racebox STATE
            FirmwareIncompatible = false;
            FirmwareChecked = false;

            if (isConnected)
            {
                UpdateUi();
                if (!FirmwareChecked)
                {
                    DeviceDescription = Title;
                }
            }
            else
            {
                DeviceDescription = string.Empty;
                TurnOffMeasuring();
                Processor.ClearExtendedData();
                ResetUi();
            }

        }

        public void TurnOffMeasuring()
        {
            IsMocking = false;
            Processor.IsMeasuring = false;
            //OnMeasuringChanged(this, MeasuringState.Disabled);
        }


        public override void OnDisposing()
        {
            if (_connector != null)
            {
                _connector.StateChanged -= OnConnectionChanged;
                Processor.ExtDataChanged -= OnExtDataChanged;

                _connector.DeviceConnectionChanged -= OnDeviceConnectionChanged;

                Processor.MeasuringChanged -= OnMeasuringChanged;
                Processor.MeasuredDistance -= OnMeasuredDistance;
                Processor.MeasuredRange -= OnMeasuredRange;
                Processor.MeasureSucces -= OnMeasureSuccess;
                Processor.MeasureFail -= OnMeasureFailed;
                Processor.DataProcessed -= OnDataProcessed;
            }

            base.OnDisposing();
        }



        void AddResultToUi(IHasDetailedDisplay item, bool canSpeak)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {

                MeasureResults.Add(item);
                //OnPropertyChanged(nameof(this.ExplainMeasuringSteps));

                ScrollToIndex = MeasureResults.Count - 1;
            });

            if (canSpeak && item is IHasSpeech canSay)
            {
                App.Instance.SpeakAsync(canSay.Say, App.SelectedLang).ConfigureAwait(false);
            }
        }

        private void OnMeasuredRange(object sender, MeasuredRange item)
        {
            AddResultToUi(item, _userManager.User.Options.Sound && _userManager.User.Options.CanSay);
        }

        private void OnMeasuredDistance(object sender, MeasuredDistance item)
        {
            AddResultToUi(item, false);
        }


        public MeasuringState MeasuringState { get; set; }

        private void OnMeasuringChanged(object sender, MeasuringState e)
        {
            if (MeasuringState != e)
            {
                MeasuringState = e;

                switch (e)
                {
                case MeasuringState.Error:
                MeasureState = "НЕУДАЧЕН";
                ResetUi();
                LastMeasureResultCode = 2;

                //App.Instance.SpeakAsync(ResStrings.MeasuringStateError, App.SelectedLang).ConfigureAwait(false);

                break;

                case MeasuringState.Finished:
                MeasureState = "ЗАВЕРШЕН"; //замер удачный

                //App.Instance.SpeakAsync(ResStrings.MeasuringStateFinished, App.SelectedLang).ConfigureAwait(false);
                break;

                case MeasuringState.Ready:
                MeasureState = "- СТАРТУЙТЕ!";

                //App.Instance.SpeakAsync(ResStrings.MeasuringStateReady, App.SelectedLang).ConfigureAwait(false);
                break;

                case MeasuringState.Active:
                MeasureState = "- ЕДЕМ!..";

                //App.Instance.SpeakAsync(ResStrings.MeasuringStateActive, App.SelectedLang).ConfigureAwait(false);
                break;

                case MeasuringState.Monitoring:
                MeasureState = "ВКЛЮЧЕН";

                //UpdateCurrentWeather();

                break;

                default:
                if (sender == this)
                    MeasureState = "ВЫКЛЮЧЕН";//$"{e.ToString()}";

                break;
                }

                UpdateUi();
            }

        }


        private string _MeasureState = "ВЫКЛЮЧЕН";
        public string MeasureState
        {
            get { return _MeasureState; }
            set
            {
                if (_MeasureState != value)
                {
                    _MeasureState = value;
                    OnPropertyChanged();
                }
            }
        }


        bool IsVisualStateEnabled()
        {
            return !VisualStates.Contains("Disabled");
        }

        private bool _HasBattery;
        public bool HasBattery
        {
            get
            {
                return _HasBattery;
            }
            set
            {
                if (_HasBattery != value)
                {
                    _HasBattery = value;
                    OnPropertyChanged();
                }
            }
        }

        private double _DisplayBateryLevel;
        public double DisplayBateryLevel
        {
            get
            {
                return _DisplayBateryLevel;
            }
            set
            {
                if (_DisplayBateryLevel != value)
                {
                    _DisplayBateryLevel = value;
                    OnPropertyChanged();
                }
            }
        }



        private double _DisplayAccelerationValueMin;
        public double DisplayAccelerationValueMin
        {
            get
            {
                return _DisplayAccelerationValueMin;
            }
            set
            {
                if (_DisplayAccelerationValueMin != value)
                {
                    _DisplayAccelerationValueMin = value;
                    OnPropertyChanged();
                }
            }
        }

        private double _DisplayAccelerationValueMax;
        public double DisplayAccelerationValueMax
        {
            get
            {
                return _DisplayAccelerationValueMax;
            }
            set
            {
                if (_DisplayAccelerationValueMax != value)
                {
                    _DisplayAccelerationValueMax = value;
                    OnPropertyChanged();
                }
            }
        }



        private double _DisplayAccelerationSideValueMin;
        public double DisplayAccelerationSideValueMin
        {
            get
            {
                return _DisplayAccelerationSideValueMin;
            }
            set
            {
                if (_DisplayAccelerationSideValueMin != value)
                {
                    _DisplayAccelerationSideValueMin = value;
                    OnPropertyChanged();
                }
            }
        }

        private double _DisplayAccelerationSideValueMax;
        public double DisplayAccelerationSideValueMax
        {
            get
            {
                return _DisplayAccelerationSideValueMax;
            }
            set
            {
                if (_DisplayAccelerationSideValueMax != value)
                {
                    _DisplayAccelerationSideValueMax = value;
                    OnPropertyChanged();
                }
            }
        }





        double CutDouble2(double fourPlaces)
        {
            var ret = fourPlaces - (fourPlaces % 0.01);
            return ret;
        }

        double CutDouble1(double fourPlaces)
        {
            var ret = fourPlaces - (fourPlaces % 0.1);
            return ret;
        }






        public static class SignalStrengthConverter
        {
            static SignalStrengthConverter()
            {
                deltaD = Math.Max(d1, d2) - Math.Min(d1, d2);
                deltaC = Math.Max(c1, c2) - Math.Min(c1, c2);
            }

            public static double Convert(double value)
            {
                var value1 = c1 - value;
                var parts = value1 / deltaC * 100;
                var result = deltaD / 100.0 * parts + d1;

                return result;
            }

            static double d1 = 0.2;
            static double d2 = 1.0;

            static double c1 = 5;
            static double c2 = 1;

            private static double deltaC;
            private static double deltaD;
        }

        //public double AccelerationScaledHorizontal
        //{
        //    get
        //    {
        //        // =(1 + C30/C29)/2

        //        var limit = 1.2;

        //        var value = Processor.SideAccellerationFiltered;

        //        var scaled = (1.0 + value / limit) / 2.0;

        //        return scaled;
        //    }
        //}

        //public double AccelerationScaledVertical
        //{
        //    get
        //    {
        //        // =(1 + C30/C29)/2

        //        var limit = 1.2;

        //        var value = Processor.MeasuringAcceleration;

        //        var scaled = (1.0 + value / limit) / 2.0;

        //        return scaled;
        //    }
        //}



        private bool _IsBusy;
        public new bool IsBusy
        {
            get
            {
                return _IsBusy;
            }
            set
            {
                if (_IsBusy != value)
                {
                    _IsBusy = value;
                    OnPropertyChanged();
                    UpdateUi();
                }
            }
        }


        private string[] _visualStates = new string[] { "Disabled" };
        public string[] VisualStates
        {
            get
            {
                return _visualStates;
            }
            set
            {
                if (_visualStates != value)
                {
                    _visualStates = value;
                    OnPropertyChanged();
                }
            }
        }

        void ReplaceVisualStates(params string[] values)
        {
            VisualStates = values;
        }

        void AddOrRemoveVisualStates(string[] add, string[] remove)
        {
            var old = VisualStates.ToList();
            var result = old.Except(remove).Concat(add).Distinct().ToArray();
            VisualStates = result;
        }

        void SetVisualStyleForIncline(string mainStyle)
        {
            var valid = Math.Abs(Processor.MeasuringIncline) < RaceBoxStateProcessor.INCLINE_THRESHOLD;

            if (!valid)
            {
                ReplaceVisualStates(mainStyle, "BadIncline");
            }
            else
            {
                ReplaceVisualStates(mainStyle);
            }
        }



        private bool _TestProp;
        public bool TestProp
        {
            get { return _TestProp; }
            set
            {
                if (_TestProp != value)
                {
                    _TestProp = value;
                    OnPropertyChanged();
                }
            }
        }






        private bool _Initialized;
        private readonly WeatherService _weather;
        private bool _weatherTimer;

        public bool Initialized
        {
            get { return _Initialized; }
            set
            {
                if (_Initialized != value)
                {
                    _Initialized = value;
                    OnPropertyChanged();
                }
            }
        }


    }
}
