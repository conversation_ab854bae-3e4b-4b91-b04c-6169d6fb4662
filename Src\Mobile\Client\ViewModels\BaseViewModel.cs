﻿namespace Racebox.ViewModels;

public class BaseViewModel : BindableObject, IDisposable
{
    public string UID { get; } = Guid.NewGuid().ToString();

    public DateTime BuildTime
    {
        get
        {
            return App.GetLinkerTime(this.GetType().Assembly);
        }
    }

    public bool IsDebug
    {
        get
        {
#if DEBUG
            return true;
#endif
            return false;
        }
    }


    public string Build
    {
        get
        {
            var ret = VersionTracking.CurrentBuild;
#if DEBUG
            ret += " DEBUG";
#endif
            return ret;
        }
    }

    public string Version
    {
        get
        {
            var ret = VersionTracking.CurrentVersion;
#if DEBUG
            ret += " DEBUG";
#endif
            return ret;
        }
    }

    private string _Title;
    public string Title
    {
        get { return _Title; }
        set
        {
            if (_Title != value)
            {
                _Title = value;
                OnPropertyChanged();
            }
        }
    }

    private bool _IsBusy;
    public bool IsBusy
    {
        get { return _IsBusy; }
        set
        {
            if (_IsBusy != value)
            {
                _IsBusy = value;
                OnPropertyChanged();
            }
        }
    }



    //protected bool SetProperty<T>(ref T storage, T value, [CallerMemberName] string propertyName = null)
    //{
    //    if (EqualityComparer<T>.Default.Equals(storage, value))
    //    {
    //        return false;
    //    }
    //    storage = value;
    //    OnPropertyChanged(propertyName);

    //    return true;
    //}



    //public NavBarViewModel NavbarModel => App.Instance.NavbarModel;

    public virtual void OnSubscribing(bool subscribe)
    {

    }

    void Subscribe(bool subscribe = true)
    {
        OnSubscribing(subscribe);



    }


    bool _disposed;
    public void Dispose()
    {
        if (_disposed)
            return;

        Subscribe(false);

        OnDisposing();

        _disposed = true;
    }

    public virtual void OnDisposing()
    {

    }

    public Command CommandSimple => new Command(async () =>
    {
        Console.WriteLine("DEV COMMAND INVOKED");
    });

}