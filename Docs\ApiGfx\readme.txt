Пример работы с сервером отрисовки графиков Racebox

- Для отрисовки нужен файл лога Racebox в формате CSV и JSON с метаданными
- Состав метаданных:
    - name: имя файла лога (используется в возвращаемом имени картинки)
    - size: размер файла лога
    - meta:
        - lang: ru/en - язык для отрисовки
        - user: имя пользователя
        - vehicle: название машины
        - descr: описание машины
        - share: флаг отправки графика в группу ТГ (=0, в приложении не используется)
        - data: дата замера в формате DD/MM/YYYY hh:mm:ss
    - spd: словарь метрик скорости от 0 ("0-60", "0-100", ...) с временем разгона
    - dst: словарь метрик дистанций ("60 ft" или "XXX m") со списком [время, скорость]
    - rng: словарь метрик диапазонов соростей ("100-200", ...) с временем разгона
    - shift: в приложении не используется
- Сервер крутится на https://chart.racebox.cc:5000
- Отправляем методом POST (пока с отключенной верификацией SSL) словарь {file, metadata}
- Дожидаемся ответа ~ через 2 сек с графиком в формате PNG (код ответа 200) или ошибку 400