﻿using MapsterMapper;
using Racebox.Shared.Enums;
using Racebox.Shared.Extensions;
using Racebox.Shared.Services;
using Racebox.Shared.Strings;
using Racebox.Shared.ChartApi.Models;
using System.Diagnostics;
using System.Text;
using System.Web;
using System.Windows.Input;
using AppoMobi.Framework.Maui.Interfaces;
using Racebox.Shared.ChartApi.Dto;


namespace Racebox.ViewModels
{
    public class HistoryResultViewModel : ContentViewModel, IQueryAttributable
    {
        private readonly IAppStorage _preferences;
        private readonly ChartApiService _chartService;
        private volatile bool _lockLogs;
        private ChartApiResult? _lastChartResult;
        private bool _isGeneratingChart;
        private CancellationTokenSource? _chartCancellationSource;

        public HistoryResultViewModel(
            IAppStorage preferences,
            ChartApiService chartService,
            UserManager manager, IUIAssistant ui, IMapper mapper) : base(manager, ui, mapper)
        {
            _preferences = preferences;
            _chartService = chartService;
            Item = new();
        }

        private string[] _visualStates = new string[] { "Normal" };
        public string[] VisualStates
        {
            get
            {
                return _visualStates;
            }
            set
            {
                if (_visualStates != value)
                {
                    _visualStates = value;
                    OnPropertyChanged();
                }
            }
        }

        void SetVisualStyleForIsValid()
        {
            if (!Item.IsValid)
            {
                ReplaceVisualStates("Invalid");
            }
            else
            {
                ReplaceVisualStates("Normal");
            }
        }

        void ReplaceVisualStates(params string[] values)
        {
            VisualStates = values;
        }

        public ICommand CommandSmallMapTapped
        {
            get
            {
                return new Command(async (object context) =>
                {
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        await NavbarModel.Shell.Navigation.PushAsync(new PageMapDetails(this));
                    });
                });
            }
        }



        public string DisplayMaxSpeed
        {
            get
            {
                var units = ResStrings.Kmh;
                if (Item.Units == OptionsUnits.US)
                {
                    units = ResStrings.Mph;
                }
                if (Item != null)
                {
                    var value = Item.MaxSpeed;
                    return $"{value:0} {units}";
                }
                return $"- {units}";
            }
        }

        public string DisplayIncline
        {
            get
            {
                if (Item != null)
                {
                    return $"{Item.MaxIncline:0.0}%";
                }
                return "-";
            }
        }
        public ICommand CommandLoadItem
        {
            get
            {
                return new Command(async (object context) =>
                {
                    try
                    {
                        IsBusy = true;
                        await Task.Delay(10);

                        var db = GetDatabase();
                        var item = await db.GetResult(Id.ToInteger());
                        if (item != null)
                        {
                            var car = db.Cars.FirstOrDefault(x => x.Id == item.CarId);
                            if (car == null)
                            {
                                CarTitle = ResStrings.Error;
                            }
                            else
                            {
                                CarTitle = $"{car.Brand} {car.Model}".Trim();
                            }
                            SetModel(item);
                        }
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                    }
                    finally
                    {
                        IsBusy = false;
                    }

                });
            }
        }

        private bool _HasData;
        public bool HasData
        {
            get
            {
                return _HasData;
            }
            set
            {
                if (_HasData != value)
                {
                    _HasData = value;
                    OnPropertyChanged();
                }
            }
        }



        private string _CarTitle = "Kia Rio";
        public string CarTitle
        {
            get
            {
                return _CarTitle;
            }
            set
            {
                if (_CarTitle != value)
                {
                    _CarTitle = value;
                    OnPropertyChanged();
                }
            }
        }
        public bool Initialized { get; set; }

        public void Init()
        {
            Initialized = true;

            if (CommandLoadItem.CanExecute(true))
                CommandLoadItem.Execute(Id);

        }

        private string _Id;
        public string Id
        {
            get { return _Id; }
            set
            {
                var newValue = Uri.UnescapeDataString(value ?? string.Empty);
                if (_Id != newValue)
                {
                    _Id = newValue;
                    OnPropertyChanged();
                    OnParametersSet();
                }
            }
        }

        private bool _attributesSet;

        public void ApplyQueryAttributes(IDictionary<string, object> query)
        {
            if (_attributesSet)
                return;

            _attributesSet = true;

            // The query parameter requires URL decoding.
            if (query.ContainsKey("id"))
                Id = HttpUtility.UrlDecode(query["id"].ToString());
        }

        private void OnParametersSet()
        {
            Debug.WriteLine($"[MeasureResult] OnParametersSet Id: {Id}");

            Title = $"{ResStrings.Measure} #{Id}";

            if (NavbarModel.CachedMeasureResult != null
                && NavbarModel.CachedMeasureResult.Id.ToString() == Id)
            {
                //use cache
                SetModel(NavbarModel.CachedMeasureResult);
            }
            else
            {
                // Check for existing chart even when no cached result is available
                //CheckExistingChartById(Id);
            }
        }

        private bool _MapReady;
        public bool MapReady
        {
            get
            {
                return _MapReady;
            }
            set
            {
                if (_MapReady != value)
                {
                    _MapReady = value;
                    OnPropertyChanged();
                }
            }
        }

        void SetModel(MeasureResult model)
        {

            Reflection.MapProperties(model, Item);

            SelectedResult = Item;

            SetVisualStyleForIsValid();

            Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(10), async () =>
            {
                await Task.Run(async () =>
                {
                    if (Item.Logs == null || Item.Logs.Count == 0)
                    {
                        var db = GetDatabase();
                        var logs = await db.GetResultLogs(Id.ToInteger());
                        Item.Logs = logs;
                    }

                    //calling this after Item.Logs are loaded
                    CreateOrReuseChart();

                    var coords = Item.Logs.Select(s => new { s.Lattitude, s.Longitude })
                        .AsEnumerable()
                        .Select(c => (c.Lattitude, c.Longitude))
                        .ToList();

                    MapReady = true;

                    var combined = Item.Ranges.Concat<IHasDetailedDisplay>(Item.Distances)
                        //.OrderBy(x => x.Id)
                        .ToList();

                    var satellites = Item.Logs.First().SatellitesCount;

                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        MeasureResults.Clear();
                        MeasureResults.AddRange(combined);

                        Path = coords;

                        DisplaySatellites = $"{satellites}";

                        HasData = true;
                    });

                }).ConfigureAwait(false);
                return false;
            });

            UpdateState();
            

        }

        private IEnumerable<(double, double)> _Path;
        private string _displaySatellites = _placeholder;
        private bool _HasChart;
        private string _ChartSource;
        private bool _CanLoadChart;

        public IEnumerable<(double, double)> Path
        {
            get
            {
                return _Path;
            }
            set
            {
                if (_Path != value)
                {
                    _Path = value;
                    OnPropertyChanged();
                }
            }
        }


        public string ExplainMeasuringSteps
        {
            get
            {
                var gotUserRanges = this.MeasureResults.Where(w => w is MeasuredRange).ToList().Cast<MeasuredRange>().Count(c => c.IsUserDefined);
                return $"{gotUserRanges}/{_userManager.User.Options.SpeedRanges.Count}";
            }
        }

        public ObservableRangeCollection<IHasDetailedDisplay> MeasureResults { get; } = new();

        public LocalResult Item { get; set; }

        public void UpdateState(bool forceReload = false)
        {
            Item.RaiseProperties();

            OnPropertyChanged(nameof(DisplayMaxSpeed));
            OnPropertyChanged(nameof(DisplayIncline));
            OnPropertyChanged(nameof(ExplainRollOut));
            OnPropertyChanged(nameof(ExplainIsInstant));
            OnPropertyChanged(nameof(ExplainIsValid));
            OnPropertyChanged(nameof(ExplainDate));
            OnPropertyChanged(nameof(ExplainTime));
            OnPropertyChanged(nameof(WeatherIcon));
            OnPropertyChanged(nameof(DisplayTemp));
        }

        public string DisplaySatellites
        {
            get => _displaySatellites;
            set
            {
                if (value == _displaySatellites) return;
                _displaySatellites = value;
                OnPropertyChanged();
            }
        }

        static string _placeholder = "-";

        public string WeatherIcon
        {
            get
            {
                if (Item.CurrentWeather != null)
                {
                    return Item.CurrentWeather.Icon;
                }

                return null;
            }
        }

        public string DisplayTemp
        {
            get
            {
                if (Item.CurrentWeather != null && !string.IsNullOrEmpty(Item.CurrentWeather.Icon))
                {
                    if (_userManager.User.Options.Units == OptionsUnits.EU)
                    {
                        return Item.CurrentWeather.TempC.ToString("+0.0C;-0.0C;0.0C");
                    }
                    else
                    {
                        return Item.CurrentWeather.TempF.ToString("+0.0F;-0.0F;0.0F");
                    }
                }

                return _placeholder;
            }
        }

        public string ExplainDate
        {
            get
            {
                return $"{Item.CreatedTimeUtc.ToLocalTime().ToString("d", ResStrings.Culture)}";
            }
        }

        public string ExplainTime
        {
            get
            {
                return $"{Item.CreatedTimeUtc.ToLocalTime().ToString($"{LocalizedDisplayProvider.HoursFormat}:mm:ss", ResStrings.Culture)}";
            }
        }

        public string ExplainRollOut
        {
            get
            {
                return LocalizedDisplayProvider.Instance.GetOnOffDisplay(Item.RollOut);
            }
        }

        public string ExplainIsInstant
        {
            get
            {
                return Item.IsInstant ? ResStrings.Yes : ResStrings.No;
            }
        }

        public string ExplainIsValid
        {
            get
            {
                return Item.IsValid ? ResStrings.IsValidYes : ResStrings.IsValidNo;
            }
        }

        public Command CommandDelete => new Command(async () =>
        {

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                var ok = await UI.Prompt(ResStrings.Deleting,
                    ResStrings.AreYouSure,
                    ResStrings.Yes, ResStrings.No);
                if (ok)
                {
                    var db = GetDatabase();

                    var item = db.Results.FirstOrDefault(x => x.Id == Item.Id);
                    if (item != null)
                    {
                        db.Results.Remove(item);
                        await db.SaveChangesAsync();
                        NavbarModel.CommandGoBack.Execute(null);
                    }

                }
            });

        });

        #region CHART

        public bool HasChart    
        {
            get => _HasChart;
            set
            {
                if (value == _HasChart) return;
                _HasChart = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CanLoadChart));
            }
        }

        public bool CanLoadChart    
        {
            get
            {
                return !HasChart && !IsGeneratingChart;
            }
        }

        public bool IsGeneratingChart
        {
            get => _isGeneratingChart;
            set
            {
                if (value == _isGeneratingChart) return;
                _isGeneratingChart = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CanLoadChart));
            }
        }

        public string? ChartImagePath => _lastChartResult?.Data?.FilePath;

        public Command CommandCancelChart => new Command(() =>
        {
            _chartCancellationSource?.Cancel();
        });

        public Command CommandShareChart => new Command(async () =>
        {
            if (HasChart && !string.IsNullOrEmpty(ChartImagePath))
            {
                try
                {
                    await Share.RequestAsync(new ShareFileRequest
                    {
                        Title = "Share Chart",
                        File = new ShareFile(ChartImagePath)
                    });
                }
                catch (Exception ex)
                {
                    await UI.Prompt("Error", $"Error sharing chart: {ex.Message}", ResStrings.Yes, ResStrings.No);
                }
            }
        });

        public Command CommandDeleteChart => new Command(async () =>
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                if (HasChart)
                {
                    var ok = await UI.Prompt(ResStrings.Deleting,
                        ResStrings.AreYouSure,
                        ResStrings.Yes, ResStrings.No);

                    if (ok)
                    {
                        try
                        {
                            // Delete the chart file
                            if (!string.IsNullOrEmpty(ChartImagePath) && File.Exists(ChartImagePath))
                            {
                                File.Delete(ChartImagePath);
                            }

                            // Clear chart state
                            _lastChartResult = null;
                            HasChart = false;
                            OnPropertyChanged(nameof(ChartImagePath));
                        }
                        catch (Exception ex)
                        {
                            UI.ShowToast($"{ex.Message}");
                        }
                    }
                }
            });

        });

        public Command CommandSmallChartTapped => new Command(async () =>
        {
            if (HasChart)
            {
                // Display fullscreen chart
                //await DisplayChartDetails();
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    await NavbarModel.Shell.Navigation.PushAsync(new PageChartDetails(this));
                });
            }
            else
            {
                if (CanLoadChart)
                {
                    // Generate with ChartApiService
                    _ = GenerateNewChartAsync();
                }
            }
        });

        private async Task GenerateNewChartAsync()
        {
            if (IsGeneratingChart || Item == null) return;

            try
            {
                IsGeneratingChart = true;
                _chartCancellationSource = new CancellationTokenSource();

                // Define output directory - use app's cache directory for charts
                var cacheDir = FileSystem.Current.CacheDirectory;
                var chartsDirectory = System.IO.Path.Combine(cacheDir, "Charts");
                Directory.CreateDirectory(chartsDirectory);

                var carName = "Unknown Vehicle";
                var vehicle = _userManager.User.Cars.FirstOrDefault(x => x.Id == Item.CarId);
                if (vehicle != null)
                {
                    carName = $"{vehicle.Brand} {vehicle.Model}".Trim();
                }

                var meta = new MetaInfo
                {
                    Lang = App.SelectedLang,
                    UserId = _userManager.User.Key,
                    User = _userManager.User.Name,
                    Vehicle = carName,
                    //Descr = GetVehicleDescription(measureResult) ?? "Racebox measurement",
                    Share = 0,
                    Date = Item.CreatedTimeUtc.ToLocalTime().ToString("dd/MM/yyyy HH:mm:ss")
                };

                // Generate chart using service
                _lastChartResult = await _chartService.GenerateChartAsync(
                    Item, 
                    meta,
                    chartsDirectory, 
                    _chartCancellationSource.Token);

                if (_lastChartResult.IsSuccess)
                {
                    Debug.WriteLine("[CART] Remote create SUCCESS!");
                    CheckAndSetExistingChartByDate(Item.CreatedTimeUtc.ToLocalTime());
                }
                else
                {
                    // Log error for debugging
                    Super.Log($"[CHART] Failed: {_lastChartResult.ErrorMessage} (Code: {_lastChartResult.Code})");
                    
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        // Show error message
                        var errorDetails = $"Chart generation failed:\n{_lastChartResult.ErrorMessage}";
                        if (_lastChartResult.Code > 0)
                        {
                            errorDetails += $"\nStatus Code: {_lastChartResult.Code}";
                        }

                        await UI.Alert(
                            "Chart Generation Failed",
                            errorDetails);
                    });
                }
            }
            catch (Exception ex)
            {
                Super.Log(ex);
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    await UI.Prompt("Error", $"Unexpected error: {ex.Message}", ResStrings.Yes, ResStrings.No);
                });
            }
            finally
            {
                IsGeneratingChart = false;
                _chartCancellationSource?.Dispose();
                _chartCancellationSource = null;
            }
        }

        private async Task DisplayChartDetails()
        {
            try
            {
                if (!string.IsNullOrEmpty(ChartImagePath) && File.Exists(ChartImagePath))
                {
                    // TODO: Implement fullscreen image viewer popup
                    // For now, show a simple alert with chart info
                    await UI.Prompt(
                        "Chart",
                        $"Chart available at:\n{System.IO.Path.GetFileName(ChartImagePath)}",
                        ResStrings.Yes, ResStrings.No);
                    
                    // You can implement a proper image viewer popup here
                    // Example: await ShowImagePopup(ChartImagePath);
                }
            }
            catch (Exception ex)
            {
                await UI.Prompt("Error", $"Error displaying chart: {ex.Message}", ResStrings.Yes, ResStrings.No);
            }
        }

        private void CreateOrReuseChart()
        {
            if (Item != null)
            {
                if (!CheckAndSetExistingChartByDate(Item.CreatedTimeUtc.ToLocalTime()))
                {
                    if (CanLoadChart)
                    {
                        _ = GenerateNewChartAsync();
                    }
                }
            }
        }

        private bool CheckAndSetExistingChartByDate(DateTime createdTime)
        {
            try
            {
                var fileName = Exporter.GenerateLogFileName(createdTime, "png");

                // Check if a chart file already exists for this result
                var cacheDir = FileSystem.Current.CacheDirectory;
                var chartsDirectory = System.IO.Path.Combine(cacheDir, "Charts");
                
                if (Directory.Exists(chartsDirectory))
                {
                    var chartFiles = Directory.GetFiles(chartsDirectory, fileName);
                    if (chartFiles.Length > 0)
                    {
                        SetExistingChart(chartFiles);
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error checking existing chart by date: {ex.Message}");
            }

            return false;
        }

        private void CheckExistingChartGeneric()
        {
            try
            {
                // Check if any chart file exists in the Charts directory
                var cacheDir = FileSystem.Current.CacheDirectory;
                var chartsDirectory = System.IO.Path.Combine(cacheDir, "Charts");
                
                if (Directory.Exists(chartsDirectory))
                {
                    var chartFiles = Directory.GetFiles(chartsDirectory, "AccelLog_*.png");
                    if (chartFiles.Length > 0)
                    {
                        SetExistingChart(chartFiles);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error checking existing chart (generic): {ex.Message}");
            }
        }

        private void SetExistingChart(string[] chartFiles)
        {
            // Use the most recent chart file
            var latestChart = chartFiles
                .Select(f => new FileInfo(f))
                .OrderByDescending(f => f.LastWriteTime)
                .FirstOrDefault();
                
            if (latestChart != null)
            {
                // Create a result object to represent the existing chart
                _lastChartResult = ChartApiResult.CreateSuccess(
                    new ChartResponse
                    {
                        FilePath = latestChart.FullName,
                        Filename = latestChart.Name,
                        FileSize = latestChart.Length
                    },
                    TimeSpan.Zero
                );
                
                ChartSource = $"file://{ChartImagePath}";
                Debug.WriteLine($"Setting chat source to {ChartSource} ({latestChart.Length:N0} bytes)");

                HasChart = true;
                OnPropertyChanged(nameof(ChartImagePath));

            }
        }

        public string ChartSource
        {
            get => _ChartSource;
            set
            {
                if (value == _ChartSource) return;
                _ChartSource = value;
                OnPropertyChanged();
            }
        }

        #endregion

    }
}
