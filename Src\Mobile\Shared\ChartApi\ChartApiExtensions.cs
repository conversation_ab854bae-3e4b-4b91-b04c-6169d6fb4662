using Racebox.ApiClient.Extensions;
using Racebox.Shared.Services;

namespace Racebox.Shared.ChartApi;

public static class ChartApiExtensions
{
    /// <summary>
    /// ChartApiService will be added as singleton.
    /// Configures HttpClient for Chart API with SSL bypass for the specific endpoint.
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="baseUrl">Chart API base URL, defaults to https://chart.racebox.cc:5000</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddChartApi(this IServiceCollection services, string baseUrl = "https://chart.racebox.cc:5000")
    {
        services.AddSingleton<ChartApiService>();
        
        // Configure HTTP client with SSL bypass for the chart API
        services.AddHttpClient("chartApi", client =>
        {
            client.BaseAddress = new Uri(baseUrl);
            client.Timeout = TimeSpan.FromSeconds(6); // 6 second timeout for chart generation
            client.DefaultRequestHeaders.Add("User-Agent", "RaceboxMobileApp/1.0");
        })
        .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler()
        {
            // SSL bypass for chart API - consider proper certificate validation in production
            ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
        });
        
        return services;
    }
}