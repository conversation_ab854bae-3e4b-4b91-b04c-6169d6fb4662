using Microsoft.VisualStudio.TestTools.UnitTesting;
using TestApi.Services;
using TestApi.Models;

namespace TestApi.Tests;

[TestClass]
public class CsvMetadataGeneratorTests
{
    private CsvMetadataGenerator _generator = null!;
    private string _sampleCsv = null!;

    [TestInitialize]
    public void Setup()
    {
        _generator = new CsvMetadataGenerator();
        _sampleCsv = GetSampleCsvData();
    }

    [TestMethod]
    public void GenerateFromCsv_ValidCsvData_ReturnsMetadata()
    {
        // Act
        var result = _generator.GenerateFromCsv(_sampleCsv, 1000);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual("app", result.DataSource);
        Assert.AreEqual("accel", result.Type);
        Assert.AreEqual(1000, result.Size);
        Assert.IsTrue(result.Name.Contains("AccelLog_"));
        Assert.IsNotNull(result.Meta);
        Assert.AreEqual("en", result.Meta.Lang);
        Assert.AreEqual("Mobile App User", result.Meta.User);
        Assert.AreEqual(0, result.Meta.Share);
    }

    [TestMethod]
    public void GenerateFromCsv_CustomFileName_UsesProvidedFileName()
    {
        // Arrange
        var customFileName = "CustomAccelLog_123.csv";

        // Act
        var result = _generator.GenerateFromCsv(_sampleCsv, 1000, customFileName);

        // Assert
        Assert.AreEqual(customFileName, result.Name);
    }

    [TestMethod]
    [ExpectedException(typeof(ArgumentException))]
    public void GenerateFromCsv_NullCsvText_ThrowsArgumentException()
    {
        // Act
        _generator.GenerateFromCsv(null!, 1000);
    }

    [TestMethod]
    [ExpectedException(typeof(ArgumentException))]
    public void GenerateFromCsv_EmptyCsvText_ThrowsArgumentException()
    {
        // Act
        _generator.GenerateFromCsv(string.Empty, 1000);
    }

    [TestMethod]
    [ExpectedException(typeof(InvalidOperationException))]
    public void GenerateFromCsv_OnlyHeaderRow_ThrowsInvalidOperationException()
    {
        // Arrange
        var headerOnlyCsv = "Total Time (s);Lat;Lon;Speed (km/h);LonAccel (G);Distance (m);Alt (m);Incline (%);Course (deg);HDOP;Sats";

        // Act
        _generator.GenerateFromCsv(headerOnlyCsv, 1000);
    }

    [TestMethod]
    public void CalculatePerformanceMetrics_ValidData_CalculatesSpeedMetrics()
    {
        // Arrange
        var dataLines = new[]
        {
            "3.34;56.7916267;60.5735093;60.5;0.06;18.5;268.8;0.0;296;0.7;15",
            "7.25;56.7916267;60.5735093;100.2;0.06;50.1;268.8;0.0;296;0.7;15",
            "15.93;56.7916267;60.5735093;150.8;0.06;120.3;268.8;0.0;296;0.7;15"
        };

        // Act
        var result = _generator.CalculatePerformanceMetrics(dataLines);

        // Assert
        Assert.IsNotNull(result);
        Assert.IsTrue(result.SpeedMetrics.ContainsKey("0-60"));
        Assert.IsTrue(result.SpeedMetrics.ContainsKey("0-100"));
        Assert.IsTrue(result.SpeedMetrics.ContainsKey("0-150"));
        Assert.AreEqual("3.34", result.SpeedMetrics["0-60"]);
        Assert.AreEqual("7.25", result.SpeedMetrics["0-100"]);
        Assert.AreEqual("15.93", result.SpeedMetrics["0-150"]);
    }

    [TestMethod]
    public void CalculatePerformanceMetrics_ValidData_CalculatesDistanceMetrics()
    {
        // Arrange
        var dataLines = new[]
        {
            "2.41;56.7916267;60.5735093;46.0;0.06;18.3;268.8;0.0;296;0.7;15", // 60 ft achieved
            "9.78;56.7916267;60.5735093;119.0;0.06;201.5;268.8;0.0;296;0.7;15", // 201m achieved
            "15.18;56.7916267;60.5735093;147.0;0.06;402.2;268.8;0.0;296;0.7;15" // 402m achieved
        };

        // Act
        var result = _generator.CalculatePerformanceMetrics(dataLines);

        // Assert
        Assert.IsNotNull(result);
        Assert.IsTrue(result.DistanceMetrics.ContainsKey("60 ft"));
        Assert.IsTrue(result.DistanceMetrics.ContainsKey("201 m"));
        Assert.IsTrue(result.DistanceMetrics.ContainsKey("402 m"));
        
        var sixtyFt = result.DistanceMetrics["60 ft"];
        Assert.AreEqual("2.41", sixtyFt[0]); // time
        Assert.AreEqual("46", sixtyFt[1]); // speed
    }

    [TestMethod]
    public void CalculatePerformanceMetrics_HighSpeedData_CalculatesRangeMetrics()
    {
        // Arrange
        var dataLines = new[]
        {
            "7.25;56.7916267;60.5735093;100.2;0.06;50.1;268.8;0.0;296;0.7;15", // 100 km/h
            "38.31;56.7916267;60.5735093;200.5;0.06;800.3;268.8;0.0;296;0.7;15" // 200 km/h
        };

        // Act
        var result = _generator.CalculatePerformanceMetrics(dataLines);

        // Assert
        Assert.IsNotNull(result);
        Assert.IsTrue(result.RangeMetrics.ContainsKey("100-200"));
        Assert.AreEqual("31.06", result.RangeMetrics["100-200"]);
    }

    [TestMethod]
    public void CalculatePerformanceMetrics_InvalidData_HandlesGracefully()
    {
        // Arrange
        var invalidDataLines = new[]
        {
            "invalid;data;line",
            "not;enough;fields",
            "text;text;text;text;text;text"
        };

        // Act
        var result = _generator.CalculatePerformanceMetrics(invalidDataLines);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(0, result.SpeedMetrics.Count);
        Assert.AreEqual(0, result.DistanceMetrics.Count);
        Assert.AreEqual(0, result.RangeMetrics.Count);
    }

    [TestMethod]
    public void CalculatePerformanceMetrics_EmptyData_ReturnsEmptyMetrics()
    {
        // Arrange
        var emptyDataLines = Array.Empty<string>();

        // Act
        var result = _generator.CalculatePerformanceMetrics(emptyDataLines);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(0, result.SpeedMetrics.Count);
        Assert.AreEqual(0, result.DistanceMetrics.Count);
        Assert.AreEqual(0, result.RangeMetrics.Count);
    }

    private static string GetSampleCsvData()
    {
        return """
            Total Time (s);Lat;Lon;Speed (km/h);LonAccel (G);Distance (m);Alt (m);Incline (%);Course (deg);HDOP;Sats
            0.18;56.7916267;60.5735093;2.5;0.06;1.9;268.8;0.0;296;0.7;15
            3.34;56.7916267;60.5735093;60.5;0.06;18.5;268.8;0.0;296;0.7;15
            7.25;56.7916267;60.5735093;100.2;0.06;50.1;268.8;0.0;296;0.7;15
            15.93;56.7916267;60.5735093;150.8;0.06;120.3;268.8;0.0;296;0.7;15
            38.31;56.7916267;60.5735093;200.5;0.06;800.3;268.8;0.0;296;0.7;15
            """;
    }
}