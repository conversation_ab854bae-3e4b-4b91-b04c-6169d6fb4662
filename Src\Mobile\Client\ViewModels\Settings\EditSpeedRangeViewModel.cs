﻿using Racebox.Helpers.Validation;
using Racebox.Shared.Enums;
using Racebox.Shared.Strings;
using Racebox.Views.Partials;
using System.Windows.Input;



namespace Racebox.ViewModels
{

    public static class Presets
    {
        static Presets()
        {
            //0-300, с шагом 50м.
            int min = 0;
            int max = 400;
            int step = 10;

            Speeds = Enumerable.Range(min, max + step).Where(i => i <= max && (i % step == 0)).ToList();

            //0-3000, с шагом 50м.
            min = 50;
            max = 3000;
            step = 50;

            Distances = Enumerable.Range(min, max + step).Where(i => i <= max && (i % step == 0)).ToList();
        }

        public static List<int> Speeds;

        public static List<int> Distances;
    }

    public class EditSpeedRangeViewModel : ProjectViewModel, IEditWithWheel2
    {

        public SkiaControl CreateForm()
        {
            return new FormEditSpeedRangeDrawn();
        }

        static EditSpeedRangeViewModel()
        {
            _speeds = Presets.Speeds;
        }

        public static List<int> _speeds;

        private int _selectedIndex = -1;
        public int SelectedIndex
        {
            get
            {
                return _selectedIndex;
            }
            set
            {
                if (_selectedIndex != value)
                {
                    _selectedIndex = value;
                    OnPropertyChanged();

                    if (value >= 0)
                        Start = _speeds[value];
                    else
                    {
                        Start = 0;
                    }
                }
            }
        }


        private List<string> _itemsList;
        public List<string> ItemsList
        {
            get
            {
                return _itemsList;
            }
            set
            {
                if (_itemsList != value)
                {
                    _itemsList = value;
                    OnPropertyChanged();
                }
            }
        }

        private int _selectedIndex2 = -1;
        public int SelectedIndex2
        {
            get
            {
                return _selectedIndex2;
            }
            set
            {
                if (_selectedIndex2 != value)
                {
                    _selectedIndex2 = value;
                    OnPropertyChanged();

                    if (value >= 0)
                        End = _speeds[value];
                    else
                    {
                        End = 0;
                    }
                }
            }
        }


        private List<string> _itemsList2;
        public List<string> ItemsList2
        {
            get
            {
                return _itemsList2;
            }
            set
            {
                if (_itemsList2 != value)
                {
                    _itemsList2 = value;
                    OnPropertyChanged();
                }
            }
        }

        public EditSpeedRangeViewModel(LocalSpeedRange item, Func<OptionItem, Task<int>> callback)
        {

            _callback = callback;
            _item = item;

            ValidationInitialize();

            Start = (int)item.Start;
            End = (int)item.End;
            Units = item.Units;
        }

        public void Bind()
        {
            ItemsList = _speeds.Select(x => x.ToString()).ToList();
            ItemsList2 = _speeds.Select(x => x.ToString()).ToList();

            Start = (int)_item.Start;
            End = (int)_item.End;
            var hasIndex = _speeds.IndexOf(Start);
            if (hasIndex >= 0)
            {
                SelectedIndex = hasIndex;
            }
            else
            {
                SelectedIndex = 0;
            }

            hasIndex = _speeds.IndexOf(End);
            if (hasIndex >= 0)
            {
                SelectedIndex2 = hasIndex;
            }
            else
            {
                SelectedIndex2 = 0;
            }

            Validate();

            IsReady = true;
        }

        private bool _IsReady;
        public bool IsReady
        {
            get
            {
                return _IsReady;
            }
            set
            {
                if (_IsReady != value)
                {
                    _IsReady = value;
                    OnPropertyChanged();
                }
            }
        }


        public ICommand CommandSubmitForm => new Command(async (object context) =>
        {
            UpdateValidator();
            if (!CanSubmit)
            {
                return;
            }

            try
            {
                IsBusy = true;
                await Task.Delay(10); //update UI

                var outItem = new LocalSpeedRange(Start, End, Units);
                outItem.Id = _item.Id;

                var errorCode = await _callback(outItem);

                if (errorCode > 0)
                {
                    if (errorCode == 1)
                    {
                        throw new ApplicationException(ResStrings.ErrorMetricsAlreadyExist);
                    }
                    throw new ApplicationException(ResStrings.ErrorFailedToSaveRecord);
                }

                this.NavbarModel.CommandGoBack.Execute(null);

            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                App.Instance.UI.ShowToast($"{ResStrings.Error}: {e.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        });

        public Command CommandSelectMetricsUnit => new Command(async () =>
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                List<ISelectableOption> options = new()
                {
                    new SelectableAction
                    {
                        Id = OptionsUnits.EU.ToString(),
                        Title = ResStrings.Kmh,
                        Action = () =>
                        {
                            Units = OptionsUnits.EU;
                            OnPropertyChanged(nameof(DisplayUnits));
                        }
                    },
                    new SelectableAction
                    {
                        Id = OptionsUnits.US.ToString(),
                        Title = ResStrings.Mph,
                        Action = () =>
                        {
                            Units = OptionsUnits.US;
                            OnPropertyChanged(nameof(DisplayUnits));
                        }
                    },
                };

                var selected = await App.Instance.UI.PresentSelection(options, ResStrings.Select) as SelectableAction;
                selected?.Action?.Invoke();

            });


        });

        public string DisplayUnits
        {
            get
            {
                if (Units == OptionsUnits.US)
                    return ResStrings.Mph;
                return ResStrings.Kmh;
            }
        }


        private OptionsUnits _units;
        public OptionsUnits Units
        {
            get
            {
                return _units;
            }
            set
            {
                if (_units != value)
                {
                    _units = value;
                    OnPropertyChanged();
                }
            }
        }


        private int _Start;
        public int Start
        {
            get
            {
                return _Start;
            }
            set
            {
                if (_Start != value)
                {
                    _Start = value;
                    OnPropertyChanged();
                    UpdateValidator();
                }
            }
        }

        private int _End;
        public int End
        {
            get
            {
                return _End;
            }
            set
            {
                if (_End != value)
                {
                    _End = value;
                    OnPropertyChanged();
                    UpdateValidator();
                }
            }
        }



        #region VALIDATION

        private void ValidationInitialize()
        {

            //FieldValidators["start"] = new RequiredStringValidator((validator, value) =>
            //{
            //    if (string.IsNullOrEmpty(value) || value.Length < 6 || value.Length > 128)
            //    {
            //        return "Требуется значение выше нуля";
            //    }
            //    return null;
            //});

            FieldValidators["start"] = new RequiredDoubleValidator((validator, value) =>
            {
                if (value < 0 || value > 5000)
                {
                    return ResStrings.ValidationStartValue;
                }
                return null;
            });

            FieldValidators["end"] = new RequiredDoubleValidator((validator, value) =>
            {
                if (value <= 0 || value > 5000)
                {
                    return ResStrings.ValidationEndValue;
                }
                if (value <= Start)
                {
                    return ResStrings.ValidationDifferentValues;
                }
                return null;
            });
        }

        void UpdateValidator()
        {
            Validate();
            OnPropertyChanged("CanSubmit");
        }

        private bool _IsBusy;
        private readonly Func<OptionItem, Task<int>> _callback;
        private readonly LocalSpeedRange _item;

        public new bool IsBusy
        {
            get { return _IsBusy; }
            set
            {
                if (_IsBusy != value)
                {
                    _IsBusy = value;
                    OnPropertyChanged();
                    OnPropertyChanged("CanSubmit");
                }
            }
        }


        public bool CanSubmit
        {
            get
            {
                var validated = Validate();
                return validated && !IsBusy;
            }
        }

        bool Validate()
        {
            int valid = 1;
            //  valid *= Convert.ToInt32(FieldValidators["password"].Validate(Password));
            valid *= Convert.ToInt32(FieldValidators["start"].Validate(Start));
            valid *= Convert.ToInt32(FieldValidators["end"].Validate(End));
            OnPropertyChanged("FieldValidators");
            return valid > 0;
        }

        public Dictionary<string, FieldValidator> FieldValidators { get; } = new();


        #endregion


    }
}
