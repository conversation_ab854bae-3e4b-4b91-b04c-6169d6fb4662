﻿using Mapsui;
using Mapsui.Extensions;
using Mapsui.Layers;
using Mapsui.Nts;
using Mapsui.Nts.Extensions;
using Mapsui.Projections;
using Mapsui.Styles;
using Mapsui.UI.Maui;
using Mapsui.Utilities;
using NetTopologySuite.Geometries;
using SkiaSharp.Views.Maui.Controls;
using System.Diagnostics;
using Easing = Mapsui.Utilities.Easing;

namespace Racebox.Views.Maps;

public class RaceBoxMap : MapView
{
	public ILayer LayerMap { get; protected set; }
	public ILayer LayerPath { get; protected set; }
	public GeometryFeature Path { get; protected set; }

	public RaceBoxMap()
	{
		IsMyLocationButtonVisible = false;
		IsNorthingButtonVisible = false;
		IsZoomButtonVisible = false;
		BackgroundColor = Colors.Black;
		VerticalOptions = LayoutOptions.Fill;
		HorizontalOptions = LayoutOptions.Fill;

		Initialize();
	}

	public void Update()
	{
		var canvas = this.Content as SKCanvasView;
		canvas.InvalidateSurface();
	}

	public void Initialize()
	{
		this.Renderer.StyleRenderers[typeof(RasterStyle)] = new CustomRasterStyleRenderer();

		var map = new Mapsui.Map()
		{
			CRS = "EPSG:3857",
			BackColor = Mapsui.Styles.Color.Black
			//Transformation = new MinimalTransformation()
		};

		try
		{
			LayerMap = OpenCustomStreetMap.CreateTileLayer("raceboxcompanionapp-mobile");

			map.Layers.Add(LayerMap);
			map.Widgets.Clear();
			map.Widgets.Add(new Mapsui.Widgets.ScaleBar.ScaleBarWidget(map)
			{
				TextAlignment = Mapsui.Widgets.Alignment.Center,
				HorizontalAlignment = Mapsui.Widgets.HorizontalAlignment.Left,
				VerticalAlignment = Mapsui.Widgets.VerticalAlignment.Bottom
			});

			//map.Widgets.Add(new ZoomInOutWidget { MarginX = 10, MarginY = 20 });  //adds the +/- zoom widget

			// 48.856663, 2.351556 Paris
			// 59.946466, 30.356859
			var smc = SphericalMercator.FromLonLat(30.356859, 59.946466);
			map.Home = n => n.NavigateTo(new MPoint(smc.x, smc.y), map.Resolutions[12]);  //0 zoomed out-19 zoomed in

			LayerPath = CreateLiveTrackerLineStringLayer(CreateLiveTrackingLineStringStyle());
			map.Layers.Add(LayerPath);

			this.Map = map;

			SetPath(this.PathCoords);
		}
		catch (Exception e)
		{
			Trace.WriteLine(e);
		}

	}

	public static IStyle CreateLiveTrackingLineStringStyle()
	{
		return new VectorStyle
		{
			Fill = null,
			Outline = null,
#pragma warning disable CS8670 // Object or collection initializer implicitly dereferences possibly null member.
			Line = { Color = Mapsui.Styles.Color.FromString("Red"), Width = 3 }
		};
	}

	private ILayer CreateLiveTrackerLineStringLayer(IStyle? style = null)
	{
		LineString lineString = LineString.Empty;
		Path = new GeometryFeature(lineString);

		var layer = new MemoryLayer()
		{
			Features = new[] { Path },
			Name = "LiveTrackerLineStringLayer",
			Style = style
		};

		return layer;
	}


	public void DrawPath()
	{

		SetPath(PathCoords);

	}
	public void SetPath(IEnumerable<(double Latitude, double Longitude)> coords, bool zoomIn = true)
	{
		if (Path == null || LayerPath == null)
			return;

		if (coords == null)
		{
			Path.RenderedGeometry.Clear();
			LayerPath.DataHasChanged();
			return;
		}

		var total = coords.Count();

		if (total > 1)
		{
			var lineString = new LineString(coords
				.Select(s => SphericalMercator.FromLonLat(s.Longitude, s.Latitude).ToCoordinate()).ToArray());

			MainThread.BeginInvokeOnMainThread(() =>
			{
				Path.Geometry = lineString;
				// Clear cache for change to show
				Path.RenderedGeometry.Clear();
				// Trigger DataChanged notification
				LayerPath.DataHasChanged();

				/*
                    string svgString = App.Instance.Resources.Get<string>("SvgPin");
                    var tag = coords.First();
                    Pins.Add(new Pin(this)
                    {
                        Label = $"START",
                        Position = new Mapsui.UI.Maui.Position(tag.Latitude, tag.Longitude),
                        Type = PinType.Svg,
                        Scale = 0.5f,
                        RotateWithMap = true,
                        Svg = svgString
                    });

                    svgString = App.Instance.Resources.Get<string>("SvgCheck");
                    tag = coords.Last();
                    Pins.Add(new Pin(this)
                    {
                        Label = $"END",
                        Position = new Mapsui.UI.Maui.Position(tag.Latitude, tag.Longitude),
                        Type = PinType.Svg,
                        Scale = 0.5f,
                        RotateWithMap = true,
                        Svg = svgString
                    });
                    */

				if (zoomIn)
				{
					ZoomToPath();
				}

			});

		}
	}

	/// <summary>
	/// Fit + Fill
	/// </summary>
	/// <param name="sideOffsetDp"></param>
	public void ZoomToPath(double sideOffsetDp = 24.0)
	{
		if (Width > 0 && Height > 0)
		{
			var viewportWidth = Width - sideOffsetDp * 2;
			var viewportHeight = Height - sideOffsetDp * 2;

			var resolution = ZoomHelper.DetermineResolution(
				Path.Extent.Width, Path.Extent.Height, viewportWidth, viewportHeight, ScaleMethod.Fit);

			Navigator.NavigateTo(Path.Extent.Centroid, resolution, 350, Easing.CubicInOut);
		}
		else
		{
			Console.WriteLine("[Map] cannot zoom to invisible path");
		}
	}

	void LockToPath()
	{

	}



	public class ViewportPosition
	{
		public double Zoom { get; set; }
		public double X { get; set; }
		public double Y { get; set; }
	}

	private static void Swap(ref double xMin, ref double xMax)
	{
		(xMin, xMax) = (xMax, xMin);
	}

	ViewportPosition GetZoom(
		double x1, double y1, double x2, double y2,
		double screenWidth, double screenHeight,
		ScaleMethod scaleMethod = ScaleMethod.Fit)
	{

		var ret = new ViewportPosition()
		{
			Zoom = 8.0
		};

		if (x1 > x2) Swap(ref x1, ref x2);
		if (y1 > y2) Swap(ref y1, ref y2);

		ret.X = (x2 + x1) / 2;
		ret.Y = (y2 + y1) / 2;

		if (scaleMethod == ScaleMethod.Fit)
			ret.Zoom = Math.Max((x2 - x1) / screenWidth, (y2 - y1) / screenHeight);
		else if (scaleMethod == ScaleMethod.Fill)
			ret.Zoom = Math.Min((x2 - x1) / screenWidth, (y2 - y1) / screenHeight);
		else if (scaleMethod == ScaleMethod.FitWidth)
			ret.Zoom = (x2 - x1) / screenWidth;
		else if (scaleMethod == ScaleMethod.FitHeight)
			ret.Zoom = (y2 - y1) / screenHeight;
		else
			throw new Exception("FillMethod not found");

		return ret;
	}

	public static readonly BindableProperty PathCoordsProperty = BindableProperty.Create(nameof(PathCoords),
	typeof(IEnumerable<(double, double)>),
	typeof(RaceBoxMap),
	null, propertyChanged: (bo, vo, nv) =>
	{
		if (bo is RaceBoxMap control)
		{
			control.DrawPath();
		}
	});
	public IEnumerable<(double, double)> PathCoords
	{
		get { return (IEnumerable<(double, double)>)GetValue(PathCoordsProperty); }
		set { SetValue(PathCoordsProperty, value); }
	}





}