<?xml version="1.0" encoding="utf-8" ?>
<draw:Canvas
    x:Class="Racebox.Views.Partials.ButtonSmall"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:strings="clr-namespace:Racebox.Shared.Strings;assembly=Racebox.Shared"
    xmlns:touch="clr-namespace:AppoMobi.Maui.Gestures;assembly=AppoMobi.Maui.Gestures"
    x:Name="ThisControl"
    Gestures="Enabled"
    HeightRequest="36"
    HorizontalOptions="Center"
    Tag="Button"
    VerticalOptions="Start"
    WidthRequest="120">

    <draw:SkiaLayout
        HorizontalOptions="Fill"
        VerticalOptions="Fill">

        <draw:SkiaShape
            x:Name="BtnBackground"
            BackgroundColor="#00000000"
            CornerRadius="24"
            HorizontalOptions="Fill"
            StrokeColor="#CB6336"
            StrokeWidth="1.85"
            VerticalOptions="Fill" />

        <draw:SkiaLabel
            FontSize="16"
            HorizontalOptions="Center"
            Text="{Binding Source={x:Reference ThisControl}, Path=Text}"
            TextColor="#CB6336"
            TranslationY="0"
            VerticalOptions="Center" />

        <draw:SkiaHotspot
            CommandTapped="{Binding Source={x:Reference ThisControl}, Path=CommandTapped}"
            HorizontalOptions="Fill"
            IsVisible="{Binding Source={x:Reference ThisControl}, Path=IsDisabled, Converter={x:StaticResource NotConverter}}"
            TransformView="{x:Reference BtnBackground}"
            VerticalOptions="Fill" />

    </draw:SkiaLayout>

</draw:Canvas>
