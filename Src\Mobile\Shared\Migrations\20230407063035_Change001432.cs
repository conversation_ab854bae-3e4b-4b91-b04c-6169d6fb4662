﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Racebox.Shared.Migrations
{
    /// <inheritdoc />
    public partial class Change001432 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "TimeCreated",
                table: "Users",
                type: "TEXT",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "TimeEdited",
                table: "Users",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "TimeCreated",
                table: "Results",
                type: "TEXT",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "TimeEdited",
                table: "Results",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "TimeCreated",
                table: "Ranges",
                type: "TEXT",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "TimeEdited",
                table: "Ranges",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "TimeCreated",
                table: "Logs",
                type: "TEXT",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "TimeEdited",
                table: "Logs",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "TimeCreated",
                table: "Distances",
                type: "TEXT",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "TimeEdited",
                table: "Distances",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "TimeCreated",
                table: "Cars",
                type: "TEXT",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "TimeEdited",
                table: "Cars",
                type: "TEXT",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TimeCreated",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "TimeEdited",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "TimeCreated",
                table: "Results");

            migrationBuilder.DropColumn(
                name: "TimeEdited",
                table: "Results");

            migrationBuilder.DropColumn(
                name: "TimeCreated",
                table: "Ranges");

            migrationBuilder.DropColumn(
                name: "TimeEdited",
                table: "Ranges");

            migrationBuilder.DropColumn(
                name: "TimeCreated",
                table: "Logs");

            migrationBuilder.DropColumn(
                name: "TimeEdited",
                table: "Logs");

            migrationBuilder.DropColumn(
                name: "TimeCreated",
                table: "Distances");

            migrationBuilder.DropColumn(
                name: "TimeEdited",
                table: "Distances");

            migrationBuilder.DropColumn(
                name: "TimeCreated",
                table: "Cars");

            migrationBuilder.DropColumn(
                name: "TimeEdited",
                table: "Cars");
        }
    }
}
