<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaLayout
    x:Class="Racebox.Views.Reports.ReportDefault"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:interfaces="clr-namespace:Racebox.Shared.Interfaces;assembly=Racebox.Shared"
    xmlns:maps="clr-namespace:Racebox.Views.Maps"
    xmlns:partials="clr-namespace:Racebox.Views.Partials"
    xmlns:strings="clr-namespace:Racebox.Shared.Strings;assembly=Racebox.Shared"
    xmlns:viewModels="clr-namespace:Racebox.ViewModels"
    Padding="16,0"
    x:DataType="viewModels:HistoryResultViewModel"
    BackgroundColor="White"
    HorizontalOptions="Fill"
    Spacing="0"
    Tag="ResultStack"
    Type="Column">

    <!--  HEADER  -->
    <draw:SkiaLayout
        Padding="0,0,1,0"
        HeightRequest="90"
        HorizontalOptions="Fill"
        VerticalOptions="Fill">

        <!--  Car Title  -->
        <draw:SkiaLabel
            FontFamily="FontTextBold"
            FontSize="20"
            HorizontalOptions="Center"
            LineBreakMode="TailTruncation"
            MaxLines="1"
            Text="{Binding CarTitle}"
            TextColor="Black"
            TranslationY="8"
            VerticalOptions="Start" />

        <!--  Time  -->
        <draw:SkiaLabel
            BackgroundColor="Transparent"
            FontFamily="FontTextBold"
            FontSize="16"
            HorizontalOptions="Center"
            LineBreakMode="TailTruncation"
            MaxLines="1"
            Style="{StaticResource SkiaLabelStyleReport}"
            Text="{Binding ExplainDate}"
            TranslationY="-32"
            VerticalOptions="End" />

        <draw:SkiaLabel
            FontFamily="FontTextBold"
            FontSize="16"
            HorizontalOptions="Center"
            LineBreakMode="TailTruncation"
            MaxLines="1"
            Style="{StaticResource SkiaLabelStyleReport}"
            Text="{Binding ExplainTime}"
            TranslationY="-12"
            VerticalOptions="End" />

        <!--  IsValid header 106  -->

        <!--<draw:SkiaLabel
                                Margin="0,0,19,0"
                                FontFamily="FontTextBold"
                                FontSize="16"
                                HorizontalOptions="EndAndExpand"
                                LineBreakMode="TailTruncation"
                                MaxLines="1"
                                Style="{StaticResource SkiaLabelStyle}"
                                Text="{x:Static strings:ResStrings.IsValid}"
                                TranslationY="-48"
                                VerticalOptions="End" />-->

        <!--<draw:SkiaSvg
                                HeightRequest="21"
                                HorizontalOptions="End"
                                TranslationX="3"
                                States="{Binding VisualStates}"
                                TranslationY="-50.0"
                                VerticalOptions="End"
                                WidthRequest="21">
                                <draw:SkiaControl.Styles>
                                    <draw:ConditionalStyle
                                        State="Normal"
                                        Style="{x:StaticResource StyleSkiaIconIsValid}" />
                                    <draw:ConditionalStyle
                                        State="Invalid"
                                        Style="{x:StaticResource StyleSkiaIconNotValid}" />
                                </draw:SkiaControl.Styles>
                            </draw:SkiaSvg>-->

        <!--  LINE HORIZONTAL  -->
        <draw:SkiaShape
            BackgroundColor="Black"
            CornerRadius="0"
            HeightRequest="1"
            HorizontalOptions="Fill"
            StrokeWidth="0"
            VerticalOptions="End">
            <draw:SkiaShape.FillGradient>

                <draw:SkiaGradient
                    EndXRatio="1"
                    EndYRatio="0"
                    StartXRatio="0"
                    StartYRatio="0"
                    Type="Linear">
                    <draw:SkiaGradient.Colors>
                        <Color>#00666666</Color>
                        <Color>#99666666</Color>
                        <Color>#00666666</Color>
                    </draw:SkiaGradient.Colors>
                </draw:SkiaGradient>

            </draw:SkiaShape.FillGradient>
        </draw:SkiaShape>

    </draw:SkiaLayout>


    <!--  RESULTS  -->
    <draw:SkiaLayout
        x:Name="ReportLayout"
        Margin="0,0,0,10"
        ItemsSource="{Binding MeasureResults}"
        Spacing="0"
        Type="Column">

        <draw:SkiaLayout.ItemTemplate>
            <DataTemplate x:DataType="interfaces:IHasDetailedDisplay">

                <partials:DrawnCellMeasurement
                    Margin="0,0"
                    HeightRequest="41"
                    HorizontalOptions="Fill">

                    <draw:SkiaLayout
                        Padding="0,0,0,0"
                        HorizontalOptions="Fill"
                        VerticalOptions="Fill">

                        <!--  Text="{Binding DisplayInfo}"  -->
                        <draw:SkiaLabel
                            Padding="4,0,0,1"
                            FontFamily="FontText"
                            FontSize="16.0"
                            HorizontalOptions="Start"
                            MaxLines="1"
                            Tag="LabelDisplay1"
                            TextColor="Black"
                            TranslationY="0"
                            VerticalOptions="Center" />

                        <draw:SkiaLabel
                            Padding="0,0,4,1"
                            FontFamily="FontText"
                            FontSize="16.0"
                            HorizontalOptions="End"
                            MaxLines="1"
                            Tag="LabelDisplay2"
                            TextColor="Black"
                            TranslationY="0"
                            VerticalOptions="Center" />

                        <!--  LINE HORIZONTAL  -->
                        <draw:SkiaShape
                            BackgroundColor="Black"
                            CornerRadius="0"
                            HeightRequest="1"
                            HorizontalOptions="Fill"
                            StrokeWidth="0"
                            VerticalOptions="End">
                            <draw:SkiaShape.FillGradient>

                                <draw:SkiaGradient
                                    EndXRatio="1"
                                    EndYRatio="0"
                                    StartXRatio="0"
                                    StartYRatio="0"
                                    Type="Linear">
                                    <draw:SkiaGradient.Colors>
                                        <Color>#00666666</Color>
                                        <Color>#99666666</Color>
                                        <Color>#00666666</Color>
                                    </draw:SkiaGradient.Colors>
                                </draw:SkiaGradient>

                            </draw:SkiaShape.FillGradient>
                        </draw:SkiaShape>

                    </draw:SkiaLayout>



                </partials:DrawnCellMeasurement>

            </DataTemplate>
        </draw:SkiaLayout.ItemTemplate>

    </draw:SkiaLayout>

    <!--  STATS  -->
    <draw:SkiaDecoratedGrid
        ColumnDefinitions="*,*"
        ColumnSpacing="1.0"
        RowDefinitions="68,68,68"
        RowSpacing="1.0"
        Type="Grid"
        VerticalOptions="Start">

        <draw:SkiaDecoratedGrid.HorizontalLine>

            <draw:SkiaGradient
                EndXRatio="1"
                EndYRatio="0"
                StartXRatio="0"
                StartYRatio="0"
                Type="Linear">
                <draw:SkiaGradient.Colors>
                    <Color>#00666666</Color>
                    <Color>#99666666</Color>
                    <Color>#00666666</Color>
                </draw:SkiaGradient.Colors>
            </draw:SkiaGradient>

        </draw:SkiaDecoratedGrid.HorizontalLine>

        <draw:SkiaDecoratedGrid.VerticalLine>

            <draw:SkiaGradient
                EndXRatio="1"
                EndYRatio="1"
                StartXRatio="1"
                StartYRatio="0"
                Type="Linear">
                <draw:SkiaGradient.Colors>
                    <Color>#00666666</Color>
                    <Color>#99666666</Color>
                    <Color>#00666666</Color>
                </draw:SkiaGradient.Colors>
            </draw:SkiaGradient>

        </draw:SkiaDecoratedGrid.VerticalLine>
        <!--  row  0  -->

        <!--  MAX SPEED  -->
        <draw:SkiaLayout
            Grid.Row="0"
            Grid.Column="0"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <draw:SkiaLabel
                Style="{StaticResource OutputSkiaLabelStyleReport}"
                Text="{Binding DisplayMaxSpeed}" />

            <draw:SkiaLabel
                HorizontalOptions="Center"
                Style="{StaticResource SkiaLabelStyleReport}"
                Text="{x:Static strings:ResStrings.MaxSpeed}"
                TranslationY="-12"
                VerticalOptions="End" />

        </draw:SkiaLayout>

        <!--  УКЛОН  -->
        <draw:SkiaLayout
            Grid.Row="0"
            Grid.Column="1"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <draw:SkiaLabel
                Style="{StaticResource OutputSkiaLabelStyleReport}"
                Text="{Binding DisplayIncline}" />

            <draw:SkiaLabel
                Style="{StaticResource SkiaLabelStyleReport}"
                Text="{x:Static strings:ResStrings.InclineMax}"
                TranslationY="-12"
                VerticalOptions="End" />

        </draw:SkiaLayout>

        <!--  row 1  -->

        <!--  ACCELERATION  -->
        <draw:SkiaLayout
            Grid.Row="1"
            Grid.Column="0"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <draw:SkiaLabel
                Style="{StaticResource OutputSkiaLabelStyleReport}"
                Text="{Binding Item.MaxAcceleration, StringFormat='{0:0.00}G'}" />

            <draw:SkiaLabel
                HorizontalOptions="Center"
                Style="{StaticResource SkiaLabelStyleReport}"
                Text="{x:Static strings:ResStrings.MaxAcceleration}"
                TranslationY="-12"
                VerticalOptions="End" />

        </draw:SkiaLayout>

        <!--  ROLLOUT  -->
        <draw:SkiaLayout
            Grid.Row="1"
            Grid.Column="1"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <draw:SkiaLabel
                Style="{StaticResource OutputSkiaLabelStyleReport}"
                Text="{Binding ExplainRollOut}" />

            <draw:SkiaLabel
                Style="{StaticResource SkiaLabelStyleReport}"
                Text="{x:Static strings:ResStrings.RollOut}"
                TranslationY="-12"
                VerticalOptions="End" />

        </draw:SkiaLayout>

        <!--  row 2  -->

        <!--  INSTANT  -->
        <draw:SkiaLayout
            Grid.Row="2"
            Grid.Column="0"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <draw:SkiaLabel
                Style="{StaticResource OutputSkiaLabelStyleReport}"
                Text="{Binding ExplainIsInstant}" />

            <draw:SkiaLabel
                HorizontalOptions="Center"
                Style="{StaticResource SkiaLabelStyleReport}"
                Text="{x:Static strings:ResStrings.MeasureInstant}"
                TranslationY="-12"
                VerticalOptions="End" />

        </draw:SkiaLayout>

        <!--  VALID  -->
        <draw:SkiaLayout
            Grid.Row="2"
            Grid.Column="1"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <draw:SkiaLabel
                AutoSize="FitHorizontal"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                Style="{StaticResource OutputSkiaLabelStyleReport}"
                Text="{Binding ExplainIsValid}" />

            <!--  todo if we dont set CENTER it draws god knows where and PDF is corrupt  -->
            <draw:SkiaLabel
                HorizontalOptions="Center"
                Style="{StaticResource SkiaLabelStyleReport}"
                Text="{x:Static strings:ResStrings.IsValid}"
                TranslationY="-12"
                VerticalOptions="End" />

        </draw:SkiaLayout>

    </draw:SkiaDecoratedGrid>

    <!--  MAP  -->
    <!--<draw:SkiaShape
            Margin="12,16"
            draw:AddGestures.CommandTapped="{Binding CommandSmallMapTapped}"
            CornerRadius="12"
            HeightRequest="100"
            HorizontalOptions="Fill"
            IsClippedToBounds="True">

    -->
    <!--<draw:SkiaMauiElement
                AnimateSnapshot="True"
                HorizontalOptions="Fill"
                VerticalOptions="Fill">

                <Grid IsClippedToBounds="True">
                    <Frame
                        Padding="0"
                        BackgroundColor="Black"
                        CornerRadius="12"
                        HorizontalOptions="Fill"
                        IsClippedToBounds="True"
                        VerticalOptions="Fill">
                        <maps:RaceBoxMap
                            x:Name="MainMap"
                            InputTransparent="{OnPlatform iOS=False,
                                                          MacCatalyst=False,
                                                          Android=True}"
                            IsVisible="{OnPlatform Android=True,
                                                   iOS={Binding MapReady},
                                                   MacCatalyst={Binding MapReady}}"
                            NeedPath="True"
                            PathCoords="{Binding Path}" />
                        <Frame.GestureRecognizers>
                            <TapGestureRecognizer
                                Command="{Binding CommandSmallMapTapped}"
                                NumberOfTapsRequired="1" />
                        </Frame.GestureRecognizers>
                    </Frame>
                </Grid>
            </draw:SkiaMauiElement>-->
    <!--
        </draw:SkiaShape>-->


</draw:SkiaLayout>



