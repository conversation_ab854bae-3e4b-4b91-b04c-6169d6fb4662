<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaLayout
    x:Class="Racebox.Views.Partials.FormEditDouble"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:AppoMobi.Maui.Infrastructure.Controls;assembly=AppoMobi.Maui.Infrastructure"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:drawn="clr-namespace:Racebox.Views.Drawn"
    xmlns:strings="clr-namespace:Racebox.Shared.Strings;assembly=Racebox.Shared"
    xmlns:viewModels="clr-namespace:Racebox.ViewModels"
    Padding="16"
    x:DataType="viewModels:IEditorFormDouble"
    VerticalOptions="StartAndExpand">

 
    <draw:SkiaLayout
        HorizontalOptions="Fill"
        AddMarginBottom="32"
        VerticalOptions="Start">

        <!--  vertical stack  -->
        <draw:SkiaLayout
            x:Name="Form"
            Padding="16"
            HorizontalOptions="Fill"
            Spacing="16"
            Type="Column">

            <!--  EDIT METRIC  -->
            <draw:SkiaLabel
                Style="{StaticResource SkiaEditorFieldTitle}"
                Text="{Binding Title}" />

            <!--  todo     Placeholder="..."  -->
            <draw:SkiaMauiEntry
                KeyboardType="Numeric"
                Padding="8,0"
                BackgroundColor="#33000000"
                FontFamily="FontText"
                FontSize="14"
                HeightRequest="40"
                HorizontalOptions="Fill"
                Text="{Binding ValueString}"
                TextColor="#E8E3D7" />

            <draw:SkiaLabel
                Style="{StaticResource SkiaEditorFieldTitle}"
                Text="{Binding Hint}" />

            <!--  Save and Errors  -->
            <draw:SkiaLayout
                HeightRequest="100"
                HorizontalOptions="Fill">

                <!--  SAVE BTN  -->
                <drawn:SmallButton
                    Margin="0,16"
                    CommandTapped="{Binding CommandSubmitForm, Mode=OneTime}"
                    HorizontalOptions="Center"
                    IsVisible="{Binding CanSubmit}"
                    Text="{x:Static strings:ResStrings.BtnSave}" />

            </draw:SkiaLayout>

        </draw:SkiaLayout>

        <!--  FPS  -->
        <draw:SkiaLabelFps
            Margin="0,0,4,24"
            BackgroundColor="DarkRed"
            ForceRefresh="False"
            HorizontalOptions="End"
            IsVisible="{Binding IsDebug}"
            Rotation="-45"
            TextColor="White"
            VerticalOptions="End" />

    </draw:SkiaLayout>





</draw:SkiaLayout>
