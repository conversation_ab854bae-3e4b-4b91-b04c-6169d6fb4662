using DrawnUi.Controls;
using Newtonsoft.Json.Linq;
using System.Diagnostics;
using Animation = SkiaSharp.Skottie.Animation;

namespace Racebox.Views.Partials;

public class StatusEffect : SkiaLottie
{

    protected bool HasLoaded;
    private Animation _animationFail;
    private Animation _animationOk;

    public override void OnSuperviewShouldRenderChanged(bool state)
    {
        base.OnSuperviewShouldRenderChanged(state);

        IsVisible = false;

    }

    protected override void OnLayoutChanged()
    {
        base.OnLayoutChanged();

        if (!HasLoaded)
        {
            HasLoaded = true;

            _savedOpacity = Opacity;

            //todo refactor this shit

            /*

            Task.Run(async () =>
            {
                ProcessJson = (json) =>
                {
                    JObject lottieJson = JObject.Parse(json);
                    // Find color properties and stroke width properties, store them in separate dictionaries
                    Dictionary<JToken, Color> colorMap = new();
                    Dictionary<Color, int> colorFrequencyMap = new();
                    Dictionary<JToken, float> strokeWidthMap = new();
                    FindProperties(lottieJson, colorMap, colorFrequencyMap, strokeWidthMap);

                    // Modify colors in the colorMap (example: change all colors to red)
                    Dictionary<JToken, string> modifiedColorMap = new();

                    var success = Color.Parse("#65D210");// App.Current.Resources.Get<Color>("ColorSuccess");
                    var alpha = 1.0f;

                    foreach (var entry in colorMap)
                    {
                        modifiedColorMap[entry.Key] = ColorToString(success.WithAlpha(alpha));
                    }
                    // Apply modified colors and stroke widths back to the JSON tree
                    foreach (var entry in modifiedColorMap)
                    {
                        entry.Key.Replace(JToken.Parse(entry.Value));
                    }

                    string modifiedJsonString = lottieJson.ToString();
                    return modifiedJsonString;
                };
                _animationOk = await LoadSource("Lottie/successtick.json");//successtick


                ProcessJson = (json) =>
                {
                    JObject lottieJson = JObject.Parse(json);
                    // Find color properties and stroke width properties, store them in separate dictionaries
                    Dictionary<JToken, Color> colorMap = new();
                    Dictionary<Color, int> colorFrequencyMap = new();
                    Dictionary<JToken, float> strokeWidthMap = new();

                    FindProperties(lottieJson, colorMap, colorFrequencyMap, strokeWidthMap);

                    // Modify colors in the colorMap (example: change all colors to red)
                    Dictionary<JToken, string> modifiedColorMap = new();

                    var success = Color.Parse("#D8281");// App.Current.Resources.Get<Color>("ColorSuccess");
                    var alpha = 1.0f;

                    foreach (var entry in colorMap)
                    {
                        modifiedColorMap[entry.Key] = ColorToString(success.WithAlpha(alpha));
                    }
                    // Apply modified colors and stroke widths back to the JSON tree
                    foreach (var entry in modifiedColorMap)
                    {
                        entry.Key.Replace(JToken.Parse(entry.Value));
                    }

                    string modifiedJsonString = lottieJson.ToString();
                    return modifiedJsonString;
                };
                _animationFail = await LoadSource("Lottie/cross.json");

                ProcessJson = null;
            });

            */
        }
    }

    protected override async void OnFinished()
    {
        Debug.WriteLine("[SkiaLottie] OnFinished");

        base.OnFinished();

        if (finaleStart == finaleCheck)
        {
            try
            {
                var cancel = _cancellationTokenSourceFinale;

                await Task.Delay(350, cancel.Token);

                cancel.Token.ThrowIfCancellationRequested();

                await FadeToAsync(0, 500, Easing.Linear, cancel);

                if (!cancel.IsCancellationRequested)
                {
                    this.IsVisible = false;
                }
            }
            catch (OperationCanceledException)
            {
                Opacity = _savedOpacity;
            }
        }

    }

    CancellationTokenSource _cancellationTokenSourceFinale;

    double _savedOpacity = 1.0;

    long finaleStart, finaleCheck;

    public override void OnTriggerChanged()
    {
        //abort previous running finale if any
        _cancellationTokenSourceFinale?.Cancel();

        Opacity = _savedOpacity;

        if (BindableTrigger is int)
        {
            if ((int)BindableTrigger == 0)
            {
                return;
            }

            if ((int)BindableTrigger == 2 && Animation != _animationFail)
            {
                SetAnimation(_animationFail, false);
                ScaleX = 1.2f;
                ScaleY = 1.2f;
            }
            else
            if ((int)BindableTrigger == 1 && Animation != _animationOk)
            {
                SetAnimation(_animationOk, false);
                ScaleX = 1.0f;
                ScaleY = 1.0f;
            }
        }

        if (Animation == null)
        {
            SetAnimation(_animationFail, false);
        }

        _cancellationTokenSourceFinale = new();
        finaleStart++;
        finaleCheck = finaleStart;

        this.Start(0);

        this.IsVisible = true;
    }
}