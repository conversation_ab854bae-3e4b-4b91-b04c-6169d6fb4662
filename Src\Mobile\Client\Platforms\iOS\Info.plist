<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleIdentifier</key>
	<string>com.raceboxcompanion.app</string>
	<key>CFBundleVersion</key>
	<string>16001</string>
	<key>CFBundleShortVersionString</key>
	<string>160</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIDeviceFamily</key>
	<array>
		<integer>1</integer>
		<integer>2</integer>
	</array>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIStatusBarStyle</key>
	<string>DarkContent</string>
	<key>UIUserInterfaceStyle</key>
	<string>Automatic</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>XSAppIconAssets</key>
	<string>Assets.xcassets/appicon.appiconset</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>Bluetooth is required for this app to function properly</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>Bluetooth is required for this app to function properly</string>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>APFiles</key>
	<dict>
		<key>APFileDescriptionKey</key>
		<string></string>
		<key>APFileDestinationPath</key>
		<string></string>
		<key>APFileName</key>
		<string></string>
		<key>APFileSourcePath</key>
		<string></string>
	</dict>
	<key>CFBundleExecutable</key>
	<string></string>
</dict>
</plist>
