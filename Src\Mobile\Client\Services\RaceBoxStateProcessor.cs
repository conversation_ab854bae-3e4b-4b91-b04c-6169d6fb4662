﻿using Microsoft.Maui.Controls.Internals;
using Racebox.SDK;
using Racebox.Shared.Enums;
using Racebox.Shared.Strings;
using System.Diagnostics;

namespace Racebox.Services;


[Preserve(AllMembers = true)]
public partial class RaceBoxStateProcessor : BindableObject
{
    public FixedSizedQueue<string> InternalLog = new(128);

    static double alphaHz = 0.995;

    #region MOCK




    public void TickRandom()
    {
        this.Speed = GetCloseRandom(Speed, 2, 90, 200);
        if (Speed > MaxSpeed)
            MaxSpeed = Speed;

        SpeedOutput = Speed;

        this.MeasuringIncline = GetCloseRandom(MeasuringIncline, 0.1, 0, 2.5);
        if (MeasuringInclineMax > MeasuringIncline)
            MeasuringInclineMax = MeasuringIncline;

        this.RaceBoxState.Heading = GetCloseRandom(RaceBoxState.Heading.GetValueOrDefault(), 0.5, 90, 200);
        OnPropertyChanged(nameof(Heading));

        this.AltitudeAverage = GetCloseRandom(AltitudeAverage, 1, 200, 1500);

        this.SideAccellerationFiltered = GetCloseRandom(SideAccellerationFiltered, 0.05, -1.5, 1.5);
        this.AccellerationFiltered = (int)(GetCloseRandom(AccellerationFiltered, 0.033, -0.5, 0.5) * 100);


        MeasuringDistanceInMeters += 1 * Speed / 100;
        //Debug.WriteLine($"[MOCK] speed {Speed:0.00} max {MaxSpeed:0.00}");
        RaceBoxState.HDOP = GetCloseRandom(RaceBoxState.HDOP.GetValueOrDefault(), 1, 1, 9); //2.0;
        OnPropertyChanged(nameof(HDOP));

        DataProcessed?.Invoke(this, EventArgs.Empty);
    }




    public static double GetCloseRandom(double value, double spread, double min, double max)
    {
        double precision = 100.0;
        spread *= precision;
        var integer = value * precision;
        var mymax = (int)(max * precision);
        var mymin = (int)(min * precision);

        if (integer < mymin)
        {
            integer = mymin;
        }

        if (integer > mymax)
        {
            integer = mymax;
        }

        var minNext = (int)(integer - spread);

        var maxNext = (int)(integer + spread);

        if (maxNext > mymax)
            maxNext = mymax;
        if (minNext < mymin)
            minNext = mymin;

        try
        {
            var random = RndExtensions.CreateRandom(minNext, maxNext);
            return random / precision;

        }
        catch (Exception e)
        {
            Debug.WriteLine(e);
        }

        return value;
    }

    #endregion

    public event EventHandler<MeasuringState> MeasuringChanged;

    public event EventHandler<MeasuredLogLine> MeasuredFrame;

    public event EventHandler<MeasuredDistance> MeasuredDistance;

    public event EventHandler<MeasuredRange> MeasuredRange;

    public event EventHandler<MeasureResult> MeasureSucces;

    public event EventHandler<MeasureError> MeasureFail;

    public event EventHandler DataProcessed;

    public event EventHandler<RaceBoxExtendedState> ExtDataChanged;



    #region CONSTANTS

    private double lastMs;

    public const double HEADING_ALPHA_16HZ = 0.9;

    public const int delayTime = 500;

    public const double DEG_TO_RAD = 0.017453292519943295769236907684886;

    public const double GRAV_ACCEL = 9.81;

    /// <summary>
    /// Таймаут завершения замера
    /// </summary>
    public const int ACC_TIMEOUT = 120000;

    /// <summary>
    /// Пороговая скорость старта замера
    /// </summary>
    public const int START_SPEED_THRESHOLD = 2;

    /// <summary>
    /// Пороговая скорость готовности к замеру
    /// </summary>
    public const int READY_SPEED_THRESHOLD = 1;

    /// <summary>
    /// Пороговое ускорение завершения замера
    /// </summary>
    public const int DECELERATION_THRESHOLD = -3;

    /// <summary>
    /// Пороговое значение валидности замера по уклону дороги
    /// </summary>
    public const int INCLINE_THRESHOLD = 2;

    /// <summary>
    /// Длина дистанции для замера уклона дороги
    /// </summary>
    public const int INCLINE_SAMPLE_DIST = 30;

    /// <summary>
    /// Калибровочная поправка замера времени
    /// </summary>
    public const int RACELOGIC_CORR = 75;

    /// <summary>
    /// Калибровочная поправка дистанции
    /// </summary>
    public const int DIST_CORR = -15;

    /// <summary>
    /// Калибровочная поправка дистанции с включенным параметром ролл-аут
    /// </summary>
    public const int DIST_CORR_ROLLOUT = 60;

    /// <summary>
    /// Калибровочная поправка замера времени между парой скоростей
    /// </summary>
    public const int RANGE_CORR = 0; //60;

    /// <summary>
    /// Коэффициенты фильтрации ускорения
    /// </summary>
    public const double ACCEL_FAST_ALPHA_16HZ = 0.6;

    /// <summary>
    /// Коэффициенты фильтрации ускорения
    /// </summary>
    public const double ACCEL_ALPHA_16HZ = 0.9;

    /// <summary>
    /// Переводные коэффициенты
    /// </summary>
    public const double KPH_TO_MPH = 0.621;

    /// <summary>
    /// Переводные коэффициенты
    /// </summary>
    public const double MPH_TO_KPH = 1.609;

    /// <summary>
    /// Пороговые значения показателя горизонтальной точности HDOP (* 100), с гестерезисом
    /// </summary>
    public const int HDOP_UT = 400; // = 4.0

    /// <summary>
    /// Пороговые значения показателя горизонтальной точности HDOP (* 100), с гестерезисом
    /// </summary>
    public const int HDOP_LT = 250; // = 2.5

    /// <summary>
    /// Пороговые значения показателя вертикальной точности VDOP (* 100), с гестерезисом
    /// </summary>
    public const int VDOP_UT = 400; // = 4.0

    /// <summary>
    /// Пороговые значения показателя вертикальной точности VDOP (* 100), с гестерезисом
    /// </summary>
    public const int VDOP_LT = 250; // = 2.5

    /// <summary>
    /// Пороговое значение кол-ва спутников
    /// </summary>
    public const int SATS_LT = 5;

    /// <summary>
    /// размер кольцевых буферов для вычисления средних значений
    /// </summary>
    public const int BUFFER_SIZE = 10;


    public const double M_TO_FT = 3.281;


    #endregion



    #region CONVERT CODE

    double HAL_GetTick(DateTime now)
    {
        //    var now = DateTime.UtcNow;
        return now.Subtract(UtcBase).TotalMilliseconds;
    }


    public class Location_t
    {
        public Location_t()
        {

        }
        public Location_t(double lattitude, double longitude)
        {
            lat = lattitude;
            lon = longitude;
        }

        private double _lat;
        public double lat
        {
            get
            {
                return _lat;
            }
            set
            {
                if (_lat != value)
                {
                    _lat = value;
                }
            }
        }
        public double lon { get; set; }
    }




    public class LogFrame_t
    {
        public double speed { get; set; }
        public double AccelG { get; set; }
        public double accelG_raw { get; set; }

        /// <summary>
        /// METERS
        /// </summary>
        public double Distance { get; set; }
        public double Incline { get; set; }
        public double TotalTimeMs { get; set; }
    }


    //private void App.Instance.PlaySoundFile(string filename)
    //{
    //    if (_userManager.User.Options.Sound)
    //    {
    //        //async
    //        Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(10), async () =>
    //        {
    //            await Task.Run(() =>
    //            {
    //                App.Native.PlaySoundFromAssets(filename);
    //            }).ConfigureAwait(false);

    //            return false;
    //        });

    //    }

    //}

    private bool statusVDOP_isOkVDOP = false;

    double SimulateVDOP(double hdop)
    {
        return hdop * 1.0;
    }

    /// <summary>
    /// Функция определения статуса вертикальной точности
    /// </summary>
    /// <returns></returns>
    private bool statusVDOP(RaceBoxState data)
    {
        statusVDOP_isOkVDOP = false;
        if (data.HDOP.HasValue)
        {
            var vdop = SimulateVDOP(data.HDOP.Value);

            // фильтрация показателя вертикальной точности HDOP
            vdop = (vdop == 0) ? vdop : (int)(0.97 * vdop + 0.03 * vdop); // постоянная времени ~5 сек
                                                                          //Debug.WriteLine("VDOP: %s\n", gps.vdop.value());		
            if (vdop >= VDOP_UT)
            {
                statusVDOP_isOkVDOP = false;
            }
            else if (vdop <= VDOP_LT)
            {
                statusVDOP_isOkVDOP = true;
            }
        }
        return statusVDOP_isOkVDOP;
    }





    private bool statusHDOP_isOkHDOP;

    //todo remote static 
    private static double hdop;

    /// <summary>
    /// Функция определения статуса горизонтальной точности
    /// </summary>
    /// <returns></returns>
    private bool statusHDOP(RaceBoxState data)
    {
        statusHDOP_isOkHDOP = false;

        //C++ TO C# CONVERTER NOTE: This static local variable declaration (not allowed in C#) has been moved just prior to the method:
        //	static bool isOkHDOP = false;
        if (data.HDOP.HasValue)
        {
            // фильтрация показателя горизонтальной точности HDOP
            hdop = (hdop == 0) ? data.HDOP.Value : (int)(0.97 * hdop + 0.03 * data.HDOP.Value); // постоянная времени ~5 сек

            // Debug.WriteLine("HDOP: %s\n", gps.hdop.value());		
            if (data.HDOP.Value >= HDOP_UT)
            {
                statusHDOP_isOkHDOP = false;
            }
            else if (hdop <= HDOP_LT && data.SatellitesCount >= SATS_LT)
            {
                statusHDOP_isOkHDOP = true;
            }
        }
        else
        {
            statusHDOP_isOkHDOP = false;
        }
        return statusHDOP_isOkHDOP;
    }



    #endregion

    public RaceBoxStateProcessor(UserManager userManager)
    {
        _userManager = userManager;
        RaceBoxState = new RaceBoxState();

        InitializeMeasuring();

        //test
        //var start = new Location_t(55.9241511, 37.3953701);
        //var here = new Location_t(55.9241516, 37.3953705);

        //var dist = DistanceMeters(start.lat, start.lon, here.lat, here.lon);
        //var dist1 = GetDistanceBetweenPointsInMeters(start.lat, start.lon, here.lat, here.lon);
        //var dist3 = DistanceEarth(start.lat, start.lon, here.lat, here.lon);


        //var stop = true;
    }

    protected UserManager _userManager { get; }
    public void Init(IRaceBoxConnector connector)
    {
        _connector = connector;

        if (!_connector.Initialized)
        {
            _connector.Init(Application.Current.MainPage, ResStrings.VendorTitle);
        }

        Buffer = new(BUFFER_SIZE);

        Subscribe();
    }

    public void Subscribe()
    {
        Unsubscribe();
        _connector.OnDecoded += OnNewDataReceived;
        _connector.OnDecodedExtended += OnNewExtendedDataReceived;
        _connector.OnDecodedSettings += OnNewSettingsDataReceived;
    }


    public void Unsubscribe()
    {
        _connector.OnDecoded -= OnNewDataReceived;
        _connector.OnDecodedExtended -= OnNewExtendedDataReceived;
        _connector.OnDecodedSettings -= OnNewSettingsDataReceived;
    }

    public RaceboxDataBuffer Buffer { get; set; }


    volatile bool lockUpdate;


    private MeasuringState _MeasuringState;
    public MeasuringState MeasuringState
    {
        get { return _MeasuringState; }
        set
        {
            if (_MeasuringState != value)
            {
                _MeasuringState = value;
                OnPropertyChanged();
                MeasuringChanged?.Invoke(this, value);
            }
        }
    }



    /// <summary>
    /// Pressed START or STOP
    /// </summary>
    private bool _IsMeasuring;
    public bool IsMeasuring
    {
        get { return _IsMeasuring; }
        set
        {
            if (_IsMeasuring != value)
            {
                _IsMeasuring = value;
                if (value)
                {
                    MeasuringState = MeasuringState.Monitoring;
                }
                else
                {
                    MeasuringState = MeasuringState.Disabled;
                }
                OnPropertyChanged();
            }
        }
    }


    private DateTime LastReceivedTime { get; set; } = DateTime.MinValue;

    private static long logIndex;

    private double _lastValidFreq;
    public long CycleCount { get; set; }

    private void OnNewSettingsDataReceived(object sender, RaceBoxSettingsState data)
    {
        if (lockUpdate)
            return;

        RaceBoxSettingsState = data;
    }

    private void OnNewExtendedDataReceived(object sender, RaceBoxExtendedState data)
    {
        if (lockUpdate)
            return;

        RaceBoxExtendedState = data;

        ExtDataChanged?.Invoke(this, data);
    }



    #region SETTINGS

    public void ClearDeviceSettings()
    {
        RaceBoxSettingsState = null;
    }

    #endregion


    /// <summary>
    /// Do not call this method directly! Can only do for mocking..
    /// Will drop frames if lagging.
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="data"></param>
    public void OnNewDataReceived(object sender, RaceBoxState data)
    {
        if (lockUpdate)
            return;

        CycleCount++;

        //lockUpdate = true;

        // 1 cycle/second is equal to 1 hertz
        //это частота выполнения на телефоне
        //var now = DateTime.UtcNow;

        //это частота получения данных
        var now = DateTime.UtcNow; //data.TimeStampUtc;
        var pause = (now - LastReceivedTime).TotalMilliseconds;
        LastReceivedTime = now;

        //можно перенести в константы
        var validTimeWindowLowerThan = 2000;//ms

        if (pause < validTimeWindowLowerThan)
        {
            if (pause < 25 && _lastValidFreq > 0)
            {
                data.FrequencyUpdatedHz = 1000 / ((1000 / _lastValidFreq) + pause);
            }
            else
            {
                data.FrequencyUpdatedHz = 1000 / pause;
            }
        }
        else
        {
            data.FrequencyUpdatedHz = 0;
        }

        _lastValidFreq = data.FrequencyUpdatedHz;

        //todo enable disable on prod cuz performance impact
        //InternalLog.Enqueue($"{logIndex++}: now: {now:O}, pause: {pause:R} ms, freq: {data.FrequencyUpdatedHz:0.000} hz");

        Buffer.Enqueue(data);

        RaceBoxState = data;

        //todo process buffer
        BufferFilled = Buffer.Count();

        if (BufferFilled == BUFFER_SIZE)
        {
            ProcessFrame(data).ContinueWith(task =>
            {
                UpdateUi();
            }).ConfigureAwait(false);
        }

        lockUpdate = false;
    }

    private double _FrequencyHz;
    public double FrequencyHz
    {
        get { return _FrequencyHz; }
        set
        {
            if (_FrequencyHz != value)
            {
                _FrequencyHz = value;
                OnPropertyChanged();
            }
        }
    }



    #region CONVERTED

    public double StartTimeMs { get; set; }



    private volatile bool _lastWasMeasuring = false;

    private volatile bool _hadGo = false;
    private volatile bool _hadStart = false;

    private volatile RaceBoxState _lastData;

    SemaphoreSlim semaphore = new SemaphoreSlim(1);

    CancellationTokenSource cancelMeasuring;

    private double _measuredDistance1;
    private double? _measuredLat;
    private double? _measuredLon;

    private double _MeasuringMaxSpeed;
    public double MeasuringMaxSpeed
    {
        get { return _MeasuringMaxSpeed; }
        set
        {
            if (_MeasuringMaxSpeed != value)
            {
                _MeasuringMaxSpeed = value;
                OnPropertyChanged();
            }
        }
    }


    double s_angle180(in double h1, in double h2)
    {
        double diff = h1 - h2;
        if (Math.Abs(diff) <= 180.0)
        {
            return diff;
        }
        else
        {
            return (h2 > h1) ? 360.0 + diff : diff - 360.0;
        }
    }


    private bool _InclineOK;
    public bool InclineOK
    {
        get { return _InclineOK; }
        set
        {
            if (_InclineOK != value)
            {
                _InclineOK = value;
                OnPropertyChanged();
            }
        }
    }


    #region MEASURING INTERNAL

    private Racebox.Services.RaceBoxStateProcessor.MeasuringContext _context;

    private double[] speedBuffer;

    bool log_failed;

    // инициализация переменных для замера
    //время старта замера (системное время в мс)

    public double StartTime
    {
        get
        {
            return _startTime;
        }

        set
        {
            if (_startTime != value)
            {
                _startTime = value;
                OnPropertyChanged();
                if (value == 0)
                {
                    MeasuringStarted = false;
                }
                else
                {
                    MeasuringStarted = true;
                }
            }
        }
    }
    double _startTime;

    /// <summary>
    /// Internal
    /// </summary>
    public bool MeasuringStarted
    {
        get
        {
            return _measuringStarted;
        }

        protected set
        {
            if (_measuringStarted != value)
            {
                _measuringStarted = value;
                OnPropertyChanged();
            }
        }
    }
    bool _measuringStarted;

    // текущая отметка времени (мс)
    private double _MeasuringTimeMs;
    public double MeasuringTimeMs
    {
        get { return _MeasuringTimeMs; }
        set
        {
            if (_MeasuringTimeMs != value)
            {
                _MeasuringTimeMs = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(MeasuringTime));
            }
        }
    }

    private double _Ticks;
    public double Ticks
    {
        get { return _Ticks; }
        set
        {
            if (_Ticks != value)
            {
                _Ticks = value;
                OnPropertyChanged();
            }
        }
    }




    public TimeSpan MeasuringTime
    {
        get
        {
            if (StartTime == 0)
            {
                return TimeSpan.Zero;
            }

            return TimeSpan.FromMilliseconds(MeasuringTimeMs - StartTime);
        }
    }



    // предыдущая отметка времени (мс)
    double prevMs = 0;

    // текущее значение уклона дороги
    private double _MeasuringIncline;
    public double MeasuringIncline
    {
        get { return _MeasuringIncline; }
        set
        {
            if (_MeasuringIncline != value)
            {
                _MeasuringIncline = value;
                OnPropertyChanged();
            }
        }
    }


    private double _measuringInclineMax;
    /// <summary>
    /// максимальное значение уклона дороги
    /// </summary>
    public double MeasuringInclineMax
    {
        get { return _measuringInclineMax; }
        set
        {
            if (_measuringInclineMax != value)
            {
                _measuringInclineMax = value;
                OnPropertyChanged();
            }
        }
    }





    // c
    double avgAlt;

    // предыдущее усреднённое значение высоты над уровнем моря
    double lastAvgAlt;

    // структура местоположения места старта (широта, долгота)
    Location_t startLoc;
    // структура текущего местоположения (широта, долгота)
    Location_t lastLoc;

    // текущая дистанция от места старта
    private double _measuringDistanceInMeters;
    public double MeasuringDistanceInMeters
    {
        get { return _measuringDistanceInMeters; }
        set
        {
            if (_measuringDistanceInMeters != value)
            {
                _measuringDistanceInMeters = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(MeasuringDistanceKm));
            }
        }
    }

    public double MeasuringDistanceKm
    {
        get
        {
            return MeasuringDistanceInMeters / 1000.0;
        }
    }


    private double _MeasuredDistance;
    public double DistanceMeasured
    {
        get { return _MeasuredDistance; }
        set
        {
            if (_MeasuredDistance != value)
            {
                _MeasuredDistance = value;
                OnPropertyChanged();
            }
        }
    }

    private double _MeasuredTimeMs;
    public double MeasuredTimeMs
    {
        get { return _MeasuredTimeMs; }
        set
        {
            if (_MeasuredTimeMs != value)
            {
                _MeasuredTimeMs = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(MeasuredTime));
            }
        }
    }

    public TimeSpan MeasuredTime
    {
        get
        {
            return TimeSpan.FromMilliseconds(MeasuredTimeMs);
        }
    }





    // дистанция от последнего места замера высоты
    double distAlt = 0.0;

    // текущая скорость
    private double _speed;
    public double Speed
    {
        get { return _speed; }
        set
        {
            if (_speed != value)
            {
                _speed = value;
                OnPropertyChanged();
            }
        }
    }

    private double _SpeedOutput;
    public double SpeedOutput
    {
        get
        {
            return _SpeedOutput;
        }
        set
        {
            if (_SpeedOutput != value)
            {
                _SpeedOutput = value;
                OnPropertyChanged();
            }
        }
    }


    private double _speedAverage;
    public double SpeedAverage
    {
        get { return _speedAverage; }
        set
        {
            if (_speedAverage != value)
            {
                _speedAverage = value;
                OnPropertyChanged();
            }
        }
    }






    double prevHeading = 0.0;
    double curHeading = 0.0;
    double angSpeed = 0.0;


    // предыдущая скорость
    double prevSpeed = 0.0;

    // усреднённая скорость
    //double MeasuringSpeedAverage = 0.0;

    // параметры фильтров расчёта ускорения
    double alphaAccel = 0;
    double alphaFastAccel = 0;



    private bool _readyToMeasure;
    /// <summary>
    /// measureReady - флаг готовности к старту замера => скорость упала до ~0
    /// </summary>
    public bool ReadyToMeasure
    {
        get { return _readyToMeasure; }
        set
        {
            if (_readyToMeasure != value)
            {
                _readyToMeasure = value;
                OnPropertyChanged();
            }
        }
    }

    private bool _IsInstant;
    /// <summary>
    /// флаг замера пары скоростей сходу (когда замер начинается не с места, а во время движения,
    /// при достижении граничной скорости speed1 пары скоростей)
    /// </summary>
    public bool IsInstant
    {
        get { return _IsInstant; }
        set
        {
            if (_IsInstant != value)
            {
                _IsInstant = value;
                OnPropertyChanged();
            }
        }
    }


    // флаг завершения замера (при достижении граничного значения замедления)
    bool measureFinished = false;
    // флаг качества определения метоположения GPS (в вертикальной плоскости, для замера уклона дороги) 
    bool vdopOk = false;
    // флаг валидности предыдущей скорости
    bool prevSpeedValid = false;


    // индекс кольцевого буфера скорости и времени
    int buffIndex;


    // текущее ускорение в единицах G * 100 (сильная фильтрация)
    private int _accellerationFiltered;
    public int AccellerationFiltered
    {
        get { return _accellerationFiltered; }
        set
        {
            if (_accellerationFiltered != value)
            {
                _accellerationFiltered = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(MeasuringAcceleration));
            }
        }
    }



    public double MeasuringAcceleration
    {
        get
        {
            return AccellerationFiltered / 100.0;
        }
    }

    //SIDE

    private double _sideAccellerationFiltered;
    public double SideAccellerationFiltered
    {
        get { return _sideAccellerationFiltered; }
        set
        {
            if (_sideAccellerationFiltered != value)
            {
                _sideAccellerationFiltered = value;
                OnPropertyChanged();
                //OnPropertyChanged(nameof(MeasuringSideAcceleration));
            }
        }
    }



    //public double MeasuringSideAcceleration
    //{
    //    get
    //    {
    //        return SideAccellerationFiltered / 100.0;
    //    }
    //}



    // текущее ускорение в единицах G * 100 (слабая фильтрация)
    int fastAccelG;

    // текущее ускорение в единицах G * 100 (без фильтрации)
    int rawAccelG;

    // поправка времени старта с включенным параметром ролл-аут
    uint rolloutTime;

    // переводной коэффициент для расчёта ускорения (magic numbers:) (для метрической и дюмовых систем измерений)
    private int ACCEL_COEFF;
    // константа пороговой скорости начала замера (для км/ч и миль/ч)

    private double START_SPEED_THRESHOLD_UNIT;

    // константа пороговой скорости готовности к замеру (для км/ч и миль/ч)
    private double READY_SPEED_THRESHOLD_UNIT;

    // поправочный коэффициент для поправки времени :)
    private double TIME_LAG_UNIT_COEFF;

    // калибровочная поправка для замера дистанции
    private double distCorr;

    //log
    LogFrame_t frame;




    void SetupMeasuring(RaceBoxState data)
    {
        StartTime = 0;


        startLoc.lat = data.Latitude.GetValueOrDefault();
        startLoc.lon = data.Longitude.GetValueOrDefault();

        lastLoc.lat = startLoc.lat;
        lastLoc.lon = startLoc.lon;

        TmpResult.Lattitude = startLoc.lat;
        TmpResult.Longitude = startLoc.lon;
        TmpResult.Units = _userManager.User.Options.Units;
        TmpResult.StartTime = data.TimeStampUtc;
        TmpResult.CreatedTimeUtc = DateTime.UtcNow;
        if (_userManager.User.SelectedCar != null)
            TmpResult.CarId = _userManager.User.SelectedCar.Id;
    }

    protected MeasureResult TmpResult { get; set; }

    public void Reset()
    {
        CycleCount = 0;
        LastReceivedTime = DateTime.MinValue;
        IsMeasuring = false;
        MeasuringState = MeasuringState.Disabled;
        StartTimeMs = 0;
        RaceBoxState.Reset();
        InitializeMeasuring();

        ClearDeviceSettings();

        UpdateUi();
    }

    public void ClearExtendedData()
    {
        RaceBoxExtendedState = null;
    }

    void InitializeMeasuring()
    {
        EndCause = null;

        _lastWasMeasuring = false;
        _hadGo = false;
        _hadStart = false;

        _context = new Racebox.Services.RaceBoxStateProcessor.MeasuringContext(_userManager);



        buffIndex = 0;

        TmpResult = new()
        {
            AppUserId = _userManager.User.Id
        };

        //если ее сбросить то мы будем думать что стартуем от нуля, что неправда
        //SpeedAverage = 0;

        MeasuredTimeMs = 0;
        StartTimeMs = 0;
        MeasuringInclineMax = 0;
        MaxSpeed = 0;
        MeasuringMaxSpeed = 0;
        MeasuringTimeMs = 0;
        prevSpeedValid = false;
        prevSpeed = 0;

        SideAccellerationFiltered = 0;
        prevHeading = 0.0;
        curHeading = 0.0;
        angSpeed = 0.0;

        prevMs = 0;
        rawAccelG = 0;
        AccellerationFiltered = 0;

        // Кольцевой буфер значений скоростей и системного времени
        speedBuffer = new double[BUFFER_SIZE];
        buffIndex = 0;

        log_failed = false;

        // флаг статуса уклона дороги (при превышении уклона дороги определённого значения, замер считается невалидным)
        InclineOK = true;

        // инициализация переменных для замера
        //время старта замера (системное время в мс)
        StartTime = 0;
        // текущая отметка времени (мс)
        MeasuringTimeMs = 0;

        // предыдущая отметка времени (мс)
        prevMs = 0;

        // текущее значение уклона дороги
        MeasuringIncline = 0;

        // максимальное значение уклона дороги
        MeasuringInclineMax = 0;

        // c
        avgAlt = 0;
        // предыдущее усреднённое значение высоты над уровнем моря
        lastAvgAlt = 0;

        // структура местоположения места старта (широта, долгота)
        startLoc = new Location_t();
        // структура текущего местоположения (широта, долгота)
        lastLoc = new Location_t();

        // текущая дистанция от места старта
        MeasuringDistanceInMeters = 0.0;
        // дистанция от последнего места замера высоты
        distAlt = 0.0;

        // текущая скорость
        Speed = 0.0;

        // предыдущая скорость
        prevSpeed = 0.0;

        // флаг готовности к старту замера
        ReadyToMeasure = false;

        // флаг замера пары скоростей сходу (когда замер начинается не с места, а во время движения, при достижении граничной скорости speed1 пары скоростей)
        IsInstant = false;
        // флаг завершения замера (при достижении граничного значения замедления)
        measureFinished = false;
        // флаг качества определения метоположения GPS (в вертикальной плоскости, для замера уклона дороги) 
        vdopOk = false;
        // флаг валидности предыдущей скорости
        prevSpeedValid = false;

        // индекс кольцевого буфера скорости и времени
        buffIndex = 0;

        // текущее ускорение в единицах G * 100 (сильная фильтрация)
        AccellerationFiltered = 0;

        // текущее ускорение в единицах G * 100 (слабая фильтрация)
        fastAccelG = 0;

        // текущее ускорение в единицах G * 100 (без фильтрации)
        rawAccelG = 0;

        // поправка времени старта с включенным параметром ролл-аут
        rolloutTime = 0;

        // переводной коэффициент для расчёта ускорения (magic numbers:) (для метрической и дюмовых систем измерений)
        ACCEL_COEFF = (int)((_userManager.User.Options.Units == OptionsUnits.EU) ? 2832 : 4558);
        // константа пороговой скорости начала замера (для км/ч и миль/ч)

        START_SPEED_THRESHOLD_UNIT = (_userManager.User.Options.Units == OptionsUnits.EU) ? START_SPEED_THRESHOLD : START_SPEED_THRESHOLD * KPH_TO_MPH;

        // константа пороговой скорости готовности к замеру (для км/ч и миль/ч)
        READY_SPEED_THRESHOLD_UNIT = (_userManager.User.Options.Units == OptionsUnits.EU) ? READY_SPEED_THRESHOLD : READY_SPEED_THRESHOLD * KPH_TO_MPH;

        // поправочный коэффициент для поправки времени :)
        TIME_LAG_UNIT_COEFF = (_userManager.User.Options.Units == OptionsUnits.EU) ? 1.0 : MPH_TO_KPH;

        // калибровочная поправка для замера дистанции
        distCorr = (_userManager.User.Options.Rollout) ? (int)DIST_CORR_ROLLOUT : DIST_CORR;

        //log
        frame = new LogFrame_t();

        // запись заголовка лог-файла
        //todo 

        // параметры фильтров расчёта ускорения
        // инициализация параметров фильтров расчёта ускорения
        alphaAccel = ACCEL_ALPHA_16HZ;
        alphaFastAccel = ACCEL_FAST_ALPHA_16HZ;

        MeasuringState = MeasuringState.Disabled;
    }




    #endregion





    #endregion

    #region CALCULATIONS



    static DateTime UtcBase = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);

    // This function converts decimal degrees to radians
    double deg2rad(double deg)
    {
        return (deg * Math.PI / 180);
    }

    //  This function converts radians to decimal degrees
    double rad2deg(double rad)
    {
        return (rad * 180 / Math.PI);
    }

    double sq(double value)
    {
        return value * value;
    }

    double DistanceEarth(double lat1d, double lon1d, double lat2d, double lon2d)
    {
        var earthRadiusKm = 6371.0088;

        double lat1r;
        double lon1r;
        double lat2r;
        double lon2r;
        double u;
        double v;
        lat1r = deg2rad(lat1d);
        lon1r = deg2rad(lon1d);
        lat2r = deg2rad(lat2d);
        lon2r = deg2rad(lon2d);
        u = Math.Sin((lat2r - lat1r) / 2);
        v = Math.Sin((lon2r - lon1r) / 2);
        var ret = 2.0 * earthRadiusKm * Math.Asin(Math.Sqrt(u * u + Math.Cos(lat1r) * Math.Cos(lat2r) * v * v));

        return ret * 1000; //meters
    }

    public double DistanceMeters(double lat1, double long1, double lat2, double long2)
    {
        var earthRadiusKm = 6371.0088;

        double delta = deg2rad(long1 - long2);
        double sdlong = Math.Sin(delta);
        double cdlong = Math.Cos(delta);
        double slat1 = Math.Sin(deg2rad(lat1));
        double clat1 = Math.Cos(deg2rad(lat1));
        double slat2 = Math.Sin(deg2rad(lat2));
        double clat2 = Math.Cos(deg2rad(lat2));
        delta = (clat1 * slat2) - (slat1 * clat2 * cdlong);
        delta = sq(delta);
        delta += sq(clat2 * sdlong);
        delta = Math.Sqrt(delta);
        double denom = (slat1 * slat2) + (clat1 * clat2 * cdlong);
        delta = Math.Atan2(delta, denom);

        var ret = delta * earthRadiusKm; //km

        return ret * 1000; //meters
    }

    public double GetDistanceBetweenPointsInMeters(double lat1, double long1, double lat2, double long2)
    {
        double ret = 0;

        double dLat = (lat2 - lat1) / 180 * Math.PI;
        double dLong = (long2 - long1) / 180 * Math.PI;

        double a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) + Math.Cos(lat1 / 180 * Math.PI) * Math.Cos(lat2 / 180 * Math.PI) * Math.Sin(dLong / 2) * Math.Sin(dLong / 2);
        double c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));

        //Calculate radius of earth
        // For this you can assume any of the two points.
        double radiusE = 6378135; // Equatorial radius, in metres
        double radiusP = 6356750; // Polar Radius

        //Numerator part of function
        double nr = Math.Pow(radiusE * radiusP * Math.Cos(lat1 / 180 * Math.PI), 2);
        //Denominator part of the function
        double dr = Math.Pow(radiusE * Math.Cos(lat1 / 180 * Math.PI), 2) + Math.Pow(radiusP * Math.Sin(lat1 / 180 * Math.PI), 2);
        double radius = Math.Sqrt(nr / dr);

        ret = radius * c; //meters

        return ret;
    }


    /// <summary>
    /// Функция расчёта средней скорости
    /// </summary>
    /// <param name="speedBuffer"></param>
    /// <returns></returns>
    private double AverageSpeed(double[] speedBuffer)
    {
        double sumSpeed = 0.0;
        int zeroCount = 0;
        for (int i = 0; i < BUFFER_SIZE; i++)
        {
            if (speedBuffer[i] > 0)
            {
                sumSpeed += speedBuffer[i];
            }
            else
            {
                zeroCount++;
            }
        }
        if (zeroCount == BUFFER_SIZE)
        {
            return 0.0;
        }
        return sumSpeed / (BUFFER_SIZE - zeroCount);
    }

    /// <summary>
    /// Функция расчёта средней высоты
    /// </summary>
    /// <param name="altitudeBuffer"></param>
    /// <returns></returns>
    private double BufferAverage(double[] altitudeBuffer)
    {
        double sumAlt = 0;

        int zeroCount = 0;
        for (int i = 0; i < BUFFER_SIZE; i++)
        {
            sumAlt = (altitudeBuffer[i] > 0) ? sumAlt + altitudeBuffer[i] : sumAlt;
            zeroCount = (altitudeBuffer[i] == 0) ? (int)(zeroCount + 1) : zeroCount;
        }
        return (int)(sumAlt / (BUFFER_SIZE - zeroCount));
    }

    /// <summary>
    /// Функция расчёта среднего ускорения
    /// </summary>
    private double AverageAccelerationLegacy(double[] speedBuffer, double[] timeBuffer)
    {
        int buffIndex = BUFFER_SIZE - 1;

        double[] accelBuffer = new double[BUFFER_SIZE];

        for (int i = 0; i < BUFFER_SIZE; i++)
        {
            int prev = (i == 0) ? BUFFER_SIZE - 1 : i - 1;

            if (i == buffIndex || speedBuffer[i] < 0.7)
            {
                accelBuffer[i] = 0;
            }
            else
            {
                var divider = timeBuffer[i] - timeBuffer[prev];
                if (divider != 0.0)
                {
                    accelBuffer[i] = (speedBuffer[i] - speedBuffer[prev]) / divider;
                }
                else
                {
                    accelBuffer[i] = 0;
                }
            }

            //accelBuffer[i] = (i == buffIndex || speedBuffer[i] < 0.7)
            //    ?
            //    0
            //    :
            //    (speedBuffer[i] - speedBuffer[prev]) / (timeBuffer[i] - timeBuffer[prev]);
        }

        int ii = (buffIndex == 0) ? (int)(BUFFER_SIZE - 1) : (int)(buffIndex - 1);

        double sumAccel = 0;
        double countAccel = 0;
        while (accelBuffer[ii] > 0.001)
        {
            sumAccel += accelBuffer[ii];
            countAccel++;
            ii = (ii == 0) ? (int)(BUFFER_SIZE - 1) : (int)(ii - 1);
        }

        if (countAccel != 0)
        {
            return sumAccel / countAccel;
        }

        return 0;
    }

    #endregion

    public RaceBoxState RaceBoxState { get; protected set; }
    public RaceBoxExtendedState RaceBoxExtendedState { get; protected set; }
    public RaceBoxSettingsState RaceBoxSettingsState { get; protected set; }

    bool _lockUpdateUI;

    async void UpdateUi()
    {
        if (RaceBoxState.Speed != null && RaceBoxState.Speed > MaxSpeed)
        {
            MaxSpeed = RaceBoxState.Speed.Value;
        }

        if (!_lockUpdateUI)
        {
            _lockUpdateUI = true;

            MainThread.BeginInvokeOnMainThread(() =>
            {
                try
                {
                    OnPropertyChanged(nameof(MeasuringTime));
                    OnPropertyChanged(nameof(MaxAcceleration));
                    OnPropertyChanged(nameof(Satellites));
                    OnPropertyChanged(nameof(Heading));
                    OnPropertyChanged(nameof(HDOP));
                    OnPropertyChanged(nameof(Speed));
                    //OnPropertyChanged(nameof(MaxSpeed));
                    OnPropertyChanged(nameof(Altitude));
                    OnPropertyChanged(nameof(Latitude));
                    OnPropertyChanged(nameof(Longitude));

                    OnPropertyChanged(nameof(HasBattery));


                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
                finally
                {
                    _lockUpdateUI = false;
                }

            });

        }

        await Task.Delay(1);
    }

    //public RaceBoxState RaceBoxState { get; }

    #region UI       

    private int _BufferFilled;

    private IRaceBoxConnector _connector;

    public int BufferFilled
    {
        get { return _BufferFilled; }
        set
        {
            if (_BufferFilled != value)
            {
                _BufferFilled = value;
                OnPropertyChanged();
            }
        }
    }







    private double _AltitudeAverage;
    /// <summary>
    /// 
    /// </summary>
    public double AltitudeAverage
    {
        get { return _AltitudeAverage; }
        set
        {
            if (_AltitudeAverage != value)
            {
                _AltitudeAverage = value;
                OnPropertyChanged();
            }
        }
    }


    public string ToDo
    {
        get
        {
            return "ToDo";
        }
    }



    public bool HasBattery
    {
        get
        {
            return RaceBoxExtendedState != null;
        }
    }

    public double HDOP
    {
        get
        {
            return RaceBoxState.HDOP.GetValueOrDefault();
        }
    }

    public int Satellites
    {
        get
        {
            return RaceBoxState.SatellitesCount;
        }
    }

    public string Heading
    {
        get
        {
            return RaceBoxState.Heading != null ? $"{RaceBoxState.Heading.GetValueOrDefault():0}" : "-";
        }
    }




    private double _MaxSpeed;
    public double MaxSpeed
    {
        get { return _MaxSpeed; }
        set
        {
            if (_MaxSpeed != value)
            {
                _MaxSpeed = value;
                OnPropertyChanged();
            }
        }
    }




    public double MaxAcceleration
    {
        get
        {
            if (_context != null)
            {
                return _context.MaxAccel / 100.0;
            }

            return 0.0;
        }
    }





    public bool RollOut => _userManager.User.Options.Rollout;

    public static string DataPlaceholder = "-";

    public string Altitude
    {
        get
        {
            return RaceBoxState.Altitude != null ? $"{RaceBoxState.Altitude.GetValueOrDefault():0.0}" : DataPlaceholder;
        }
    }

    public string Latitude
    {
        get
        {
            return RaceBoxState.Latitude != null ? $"{RaceBoxState.Latitude.Value:0.0000000}" : DataPlaceholder;
        }
    }

    public string Longitude
    {
        get
        {
            return RaceBoxState.Longitude != null ? $"{RaceBoxState.Longitude.Value:0.0000000}" : DataPlaceholder;
        }
    }


    #endregion

    private string _EndCause;
    public string EndCause
    {
        get { return _EndCause; }
        set
        {
            if (_EndCause != value)
            {
                _EndCause = value;
                OnPropertyChanged();
                if (!string.IsNullOrEmpty(value))
                {
                    Debug.WriteLine($"[END] {value}");
                }
            }
        }
    }


}

