﻿using AppoMobi.Maui.BLE;
using AppoMobi.Maui.BLE.Connector;
using AppoMobi.Maui.BLE.EventArgs;
using AppoMobi.Specials;
using Microsoft.Maui.Controls.Internals;
using Racebox.SDK.Strings;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Text;
using Trace = System.Diagnostics.Trace;

namespace Racebox.SDK;

[Preserve(AllMembers = true)]
public class RaceBoxConnector : BLEConnector, IRaceBoxConnector
{

    public RaceBoxConnector(IBluetoothLE ble, IServiceProvider services) : base(ble)
    {
        _services = services;
    }


    public static Guid RaceboxService { get; } = Guid.Parse("0000ffe0-0000-1000-8000-00805f9b34fb");

    /// <summary>
    /// Предустановка используемого в данный момент блутус-адаптера
    /// </summary>
    public static string ServiceId { get; set; } = "0000ffe0-0000-1000-8000-00805f9b34fb";

    /// <summary>
    /// Предустановка используемого в данный момент блутус-адаптера
    /// </summary>
    public static string CharacteristicId { get; set; } = "0000ffe1-0000-1000-8000-00805f9b34fb";

    public readonly string CommandReadExtended = "EXT_RQ";

    public readonly string CommandReadSettings = "CONF_READ";

    public readonly string CommandWriteSettings = "CONF_SET";

    public async Task<bool> WriteSettings(RaceBoxSettingsState settings)
    {

        if (WriteTo != null)
        {
            byte[] bytes = Encoding.ASCII.GetBytes(CommandWriteSettings);
            await WriteTo.Info.WriteAsync(bytes);

            var encoded = settings.Encode();
            await WriteTo.Info.WriteAsync(encoded);

            return true;
        }

        return false;
    }

    public async Task<bool> RequestExtendedInfo()
    {
        if (WriteTo != null)
        {
            byte[] bytes = Encoding.ASCII.GetBytes(CommandReadExtended);
            await WriteTo.Info.WriteAsync(bytes);

            return true;
        }

        return false;
    }

    public async Task<bool> RequestSettings(Action<RaceBoxSettingsState> callback = null)
    {
        _settingsCallback = callback;

        if (WriteTo != null)
        {
            byte[] bytes = Encoding.ASCII.GetBytes(CommandReadSettings);
            await WriteTo.Info.WriteAsync(bytes);
            Debug.WriteLine($"RequestSettings OK");
            return true;
        }

        Debug.WriteLine($"RequestSettings FAILED");
        return false;
    }

    public event EventHandler<bool> DeviceConnectionChanged;

    protected override async void OnConnectionChanged()
    {

        base.OnConnectionChanged();

        Debug.WriteLine($"DEVICE connected: {IsConnected}");

        if (!IsConnected)
        {
            StopMonitoring(_subscribed);
            WriteTo = null;
            ConnectedDevice = null;

            DeviceConnectionChanged?.Invoke(this, false);
        }


    }



    /// <summary>
    /// Uses Serial property
    /// </summary>
    /// <param name="needThrow"></param>
    /// <returns></returns>
    public async Task ConnectToSerialDevice(bool needThrow = false)
    {

#if ANDROID || IOS || MACCATALYST || WINDOWS

        //connect
        try
        {
            ConnectionState = ConnectionState.Connecting;

            StopScanning();

            var device = await ConnectDevice(Serial);
            if (device == null)
            {
                throw new Exception($"Устройство {Serial} не найдено.");
            }

            LastName = device.Device.Name;
            LastSerial = device.Id;
            ConnectionState = ConnectionState.Connected;

            var service = device.Services.FirstOrDefault(x => x.Id.Equals(ServiceId, StringComparison.InvariantCultureIgnoreCase));
            if (service == null)
            {
                //service not found
                throw new Exception($"Служба {ServiceId} не найдена.");
            }

            var read = service.Characteristics.FirstOrDefault(x => x.Id.Equals(CharacteristicId, StringComparison.InvariantCultureIgnoreCase));
            if (read == null)
            {
                //characteristic not found
                throw new Exception($"Характеристика {CharacteristicId} не найдена.");
            }

            //RaceBox uses same characteristic read/write
            if (read.Info.CanWrite)
                WriteTo = read; //the actual case
            else
            {
                var write = service.Characteristics.Where(c => c.Info.CanWrite).ToArray();
                foreach (var writable in write)
                {
                    Trace.WriteLine($"[W] {writable.Id} {writable.Info.Id} {writable.Info.Name} {writable.Info.Uuid}");
                }
                //just in case..
                WriteTo = write.FirstOrDefault();
            }

            if (ConnectedDevice == null)
                throw new Exception("Неизвестная ошибка");

            await StartMonitoring(read, true);

            //var firmwareIsV2 = await RequestExtenedInfo();

            DeviceConnectionChanged?.Invoke(this, true);
        }
        catch (Exception e)
        {
            WriteTo = null;
            ConnectedDevice = null;

            Console.WriteLine(e);
            ConnectionState = ConnectionState.Error;
            DeviceConnectionChanged?.Invoke(this, false);

            await Disconnect().ConfigureAwait(false);

            if (needThrow)
            {
                throw e;
            }
        }
#else

        throw new NotImplementedException();

#endif

    }

    BleCharacteristicViewModel WriteTo
    {
        get => _writeTo;
        set => _writeTo = value;
    }


    #region PERMISSIONS

    /// <summary>
    /// Initialize SDK, parameters are for displaying permissions prompts
    /// </summary>
    /// <param name="mainpage">your maui app mainPage to attach permission propts to</param>
    /// <param name="appTitile">the title that will be displayed for permission prompts</param>
    public void Init(Page mainPage, string appTitile)
    {
        AppTitle = appTitile;
        MainPage = mainPage;
        Initialized = true;
    }

    public virtual bool CheckGpsIsAvailable()
    {
        return BluetoothPermissions.CheckGpsIsAvailable();
    }

    public virtual bool CheckBluetoothIsAvailable()
    {
        return Bluetooth.IsAvailable;
    }

    public virtual bool CheckBluetoothIsOn()
    {
        return Bluetooth.IsOn;
    }

    public virtual bool NativeCheckCanConnect()
    {
        return true; //we have no checks provided by native sdk like HasPermissions etc.
    }

    public virtual void OnCanConnect()
    {
        //normally could call some native code to update the internal sdk state etc
    }

    #region CHECK BLE

    protected string AppTitle;

    protected Page MainPage;
    public bool Initialized { get; protected set; }

    void ShowBluetoothOFFError()
    {
        Debug.WriteLine(ResStrings.AlertTurnOnBluetooth);
        MainThread.BeginInvokeOnMainThread(() =>
        {
            MainPage.DisplayAlert(AppTitle, ResStrings.AlertTurnOnBluetooth, ResStrings.BtnOk);
        });
    }
    void ShowGPSPermissionsError()
    {
        Debug.WriteLine(ResStrings.AlertNeedGpsPermissionsForBluetooth);
        MainThread.BeginInvokeOnMainThread(() =>
        {
            MainPage.DisplayAlert(AppTitle, ResStrings.AlertNeedGpsPermissionsForBluetooth, ResStrings.BtnOk);
        });
    }
    void ShowErrorGPSOff()
    {
        Debug.WriteLine(ResStrings.AlertNeedGpsOnForBluetooth);
        MainThread.BeginInvokeOnMainThread(() =>
        {
            MainPage.DisplayAlert(AppTitle, ResStrings.AlertNeedGpsOnForBluetooth, ResStrings.BtnOk);
        });
    }

    void ShowBluetoothNotAvailableError()
    {
        Debug.WriteLine(ResStrings.AlertBluetoothUnsupported);
        MainThread.BeginInvokeOnMainThread(() =>
        {
            MainPage.DisplayAlert(AppTitle, ResStrings.AlertBluetoothUnsupported, ResStrings.BtnOk);
        });
    }

    void ShowBluetoothPermissionsError()
    {
        Debug.WriteLine(ResStrings.AlertBluetoothPermissionsOff);
        MainThread.BeginInvokeOnMainThread(() =>
        {
            MainPage.DisplayAlert(AppTitle, ResStrings.AlertBluetoothPermissionsOff, ResStrings.BtnOk);
        });
    }

    public async Task<bool> CheckCanConnectDisplayErrors()
    {


#if ANDROID

        var status = await BluetoothPermissions.CheckBluetoothStatus();
        if (status != PermissionStatus.Granted)
        {
            Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(150), async () =>
            {
                MainThread.BeginInvokeOnMainThread(async () =>
                {

                    if (BluetoothPermissions.NeedGPS)
                    {
                        await MainPage.DisplayAlert(AppTitle, ResStrings.AlertNeedLocationForBluetooth, ResStrings.BtnOk);
                    }

                    status = await BluetoothPermissions.RequestBluetoothAccess();
                    if (status == PermissionStatus.Granted)
                    {
                        //disabled, using android:usesPermissionFlags="neverForLocation"

                        if (BluetoothPermissions.NeedGPS)
                        {
                            if (!CheckGpsIsAvailable())
                            {
                                ShowErrorGPSOff();
                                return;
                            }
                        }

                        await CheckCanConnectDisplayErrors();
                    }
                    else
                    {
                        ShowBluetoothPermissionsError();
                    }
                });
                return false;
            });

            return false;
        }

        if (BluetoothPermissions.NeedGPS)
        {
            if (!CheckGpsIsAvailable())
            {
                ShowErrorGPSOff();
                return false;
            }
        }



#else
        //Not android

        if (DeviceInfo.DeviceType == DeviceType.Virtual) // simulator
            return true;

#if (IOS || MACCATALYST)

        var create = CheckBluetoothIsAvailable(); //for to show permissions prompt
        if (!NativeCheckCanConnect())
        {
            return false;
        }
        else
        {
            await Task.Delay(200); // loads ble status (on or off)
            OnCanConnect();
        }

#else

        // WINDOWS?..


#endif


#endif


        if (CheckBluetoothIsAvailable())
        {
            if (CheckBluetoothIsOn())
            {
                return true;
            }

            ShowBluetoothOFFError();
            return false;

        }
        else
        {
            ShowBluetoothNotAvailableError();
            return false;
        }


        return true;
    }

    #endregion


    #endregion


    public event EventHandler<RaceBoxState> OnDecoded;
    public event EventHandler<RaceBoxExtendedState> OnDecodedExtended;
    public event EventHandler<RaceBoxSettingsState> OnDecodedSettings;

    private string _LastSerial;
    public string LastSerial
    {
        get { return _LastSerial; }
        set
        {
            if (_LastSerial != value)
            {
                _LastSerial = value;
                OnPropertyChanged();
            }
        }
    }

    private string _LastName;
    public string LastName
    {
        get { return _LastName; }
        set
        {
            if (_LastName != value)
            {
                _LastName = value;
                OnPropertyChanged();
            }
        }
    }


    public void StopScanning()
    {
        if (CancelScan != null)
        {
            CancelScan.Cancel();
        }
    }

    protected CancellationTokenSource CancelScan { get; set; }


    public async Task<bool> ScanForCompatibleDevices()
    {
        if (IsBusy)
        {
            throw new Exception("Already connecting");
        }

        if (await CheckCanConnectDisplayErrors())
        {

            try
            {
                CancelScan = new();

                FilterServiceUuids.Clear();

                await
                    WithScanTimeout(8000)
                        .WithServiceUuid(RaceboxService).ScanAsync(CancelScan.Token);

                return true;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

        }

        return false;

    }





    private string _Serial = "";

    /// <summary>
    /// Уникальный номер устройства 
    /// </summary>
    public string Serial
    {
        get
        {
            return _Serial;
        }
        set
        {
            if (_Serial != value)
            {
                if (value != null)
                {
                    value = value.Trim();
                }
                _Serial = value;
                OnPropertyChanged();
                OnPropertyChanged("SerialIsValid");
            }
        }
    }



    public bool IsMonitoring
    {
        get
        {
            return _subscribed != null;
        }
    }


    private ConnectionState _ConnectionState;
    /// <summary>
    /// Статус Bluetooth соединения
    /// </summary>
    public ConnectionState ConnectionState
    {
        get { return _ConnectionState; }
        set
        {
            if (_ConnectionState != value)
            {
                _ConnectionState = value;
                OnPropertyChanged();
            }
        }
    }

    public bool SerialIsValid
    {
        get
        {
            return Serial != null && Serial.Length == 36;
        }
    }

    public async Task FindDeviceAndConnect()
    {
        if (!SerialIsValid)
            throw new Exception("Неверный серийный номер");

        Preferences.Set("Serial", Serial);

        if (await CheckCanConnectDisplayErrors())
        {
            await ConnectToSerialDevice(true);
        }
    }


    private BleCharacteristicViewModel _subscribed;


    public async Task<bool> StartMonitoring(BleCharacteristicViewModel characteristic, bool needThrow = false)
    {
        if (_subscribed != null)
            StopMonitoring(_subscribed);

        try
        {
            characteristic.Info.ValueUpdated -= OnDataChanged;
            characteristic.Info.ValueUpdated += OnDataChanged;

            await characteristic.Info.StartUpdatesAsync();

            _subscribed = characteristic;

            SetStatus($"Мониторинг данных вкл.");

            var extended = await RequestExtendedInfo();

            SetStatus($"Мониторинг EXTENDED {extended}");

            //silly hack
            var fetcher = _services.GetService<RaceBoxDeviceSettings>();
            fetcher.ObtainSettings(null);

            return true;
        }
        catch (Exception e)
        {
            StopMonitoring(_subscribed);

            Trace.WriteLine(e);

            SetStatus("Мониторинг данных не поддерживается..");

            if (needThrow)
            {
                OnPropertyChanged("IsMonitoring");
                throw e;
            }
        }
        finally
        {
            OnPropertyChanged("IsMonitoring");
        }

        return false;
    }




    private string _LastSentCommand = "none";
    public string LastSentCommand
    {
        get { return _LastSentCommand; }
        set
        {
            if (_LastSentCommand != value)
            {
                _LastSentCommand = value;
                OnPropertyChanged();
            }
        }
    }

    private string _LastSentData;
    public string LastSentData
    {
        get { return _LastSentData; }
        set
        {
            if (_LastSentData != value)
            {
                _LastSentData = value;
                OnPropertyChanged();
            }
        }
    }

    //public RaceBoxState RaceBoxState { get; set; } = new RaceBoxState();

    public static void Log(string message, [CallerMemberName] string caller = null)
    {
        //TODO use ILogger with levels etc

#if WINDOWS
        Trace.WriteLine(message);
#else
        Console.WriteLine(message);
#endif
    }

    public static void Log(Exception e, [CallerMemberName] string caller = null)
    {
        //TODO use ILogger with levels etc

#if WINDOWS
        Trace.WriteLine(e);
#else
        Console.WriteLine(e);
#endif
    }

    protected bool ChannelBusy { get; set; }

    public enum RaceboxDataType
    {
        Default,
        Extended,
        Settings
    }

    void ProcessDataReceived(byte[] bytes)
    {


        var itWas = RaceboxDataType.Default;
        try
        {

            if (bytes.Length == 4 && bytes[0] == 0)
            {
                itWas = RaceboxDataType.Extended;
                OnDecodedExtended?.Invoke(this, RaceBoxExtendedState.FromData(bytes));
            }
            else
            if (bytes.Length == 11)// && bytes[0] == RaceBoxProtocol.CONF_MSG_MARKER)
            {
                Debug.WriteLine($"[DATA] {bytes.Length} => '{bytes[0]}'");
                itWas = RaceboxDataType.Settings;
                var settings = RaceBoxSettingsState.FromData(bytes);
                OnDecodedSettings?.Invoke(this, settings);
                _settingsCallback?.Invoke(settings);
            }
            else
            if (bytes.Length == 20)// && bytes[0] == 134)
            {
                OnDecoded?.Invoke(this, RaceBoxState.FromData(bytes));
            }
        }
        catch (Exception exception)
        {
            Log(exception);
            //if (itWas == RaceboxDataType.Settings)
            //{
            //    //retry
            //    Tasks.StartDelayed(TimeSpan.FromSeconds(1), async () =>
            //    {
            //        await RequestSettings();
            //    });
            //}
        }
        finally
        {
            ChannelBusy = false;
        }
    }

    public void StopMonitoring(BleCharacteristicViewModel characteristic)
    {
        if (characteristic != null)
        {
            try
            {
                characteristic.Info.ValueUpdated -= OnDataChanged;
            }
            catch (Exception e)
            {
                Trace.WriteLine(e);
            }
        }

        _subscribed = null;

        SetStatus("Мониторинг данных выкл.");

        OnPropertyChanged("IsMonitoring");
    }

    public event EventHandler<byte[]> DataReceived;

    /// <summary>
    /// DEBUG only
    /// </summary>
    /// <param name="status"></param>
    [Conditional("DEBUG")]
    void SetStatus(string status)
    {
        Status = status;
        //Trace.WriteLine(status);
    }

    private string _Status;
    public string Status
    {
        get { return _Status; }
        set
        {
            if (_Status != value)
            {
                _Status = value;
                OnPropertyChanged();
                Debug.WriteLine($"Status: {value}");
            }
        }
    }

    private string _DataIn;
    private BleCharacteristicViewModel _writeTo;
    private Action<RaceBoxSettingsState> _settingsCallback;
    private readonly IServiceProvider _services;


    public string DataIn
    {
        get { return _DataIn; }
        set
        {
            if (_DataIn != value)
            {
                _DataIn = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// GPS модуль настроен на 16Гц, прибор должен перекидывать в BLE с той же частотой - в районе 62мс
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="args"></param>
    private void OnDataChanged(object sender, CharacteristicUpdatedEventArgs args)
    {
        int total = 0;
        try
        {
            var bytes = args.Characteristic.Value;
            total = bytes.Length;
            if (total == 0)
                return;

            byte[] clone = new byte[total]; // Create a new array with the same length as the original

            // Perform the copy
            Buffer.BlockCopy(bytes, 0, clone, 0, total);

            //SetStatus($"Получено {total} байт");
            //Debug.WriteLine($"[BLE MONITOR] {Status}");

            ProcessDataReceived(clone);
            DataReceived?.Invoke(this, clone);
        }
        catch (Exception e)
        {
            SetStatus("Ошибка чтения");

            Console.WriteLine(e);
            DataIn = "";//$"Получено {total} байт + Ошибка: {e.ToString()}";
        }
    }


    /// <summary>
    /// Parsing bytes one by one,  ex: "FF56A0" to 0xFF 0x56 0xA0
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    static byte[] HexBytesToBytes(string input)
    {
        var isEven = input.Length / 2 * 2 == input.Length / 2.0;

        if (input.Length % 2 != 0)
        {
            Console.WriteLine($"[HexBytesToBytes] input string length is not even ({input.Length} - {input})!");
            return null;
        }

        var output = new List<byte>();

        try
        {
            for (int i = 0; i < input.Length - 1; i += 2)
            {
                var hexValue = input.Substring(i, 2);

                var value = int.Parse(hexValue, System.Globalization.NumberStyles.HexNumber);

                output.Add(Convert.ToByte(input.Substring(i, 2), 16));
            }

            return output.ToArray();

        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            return null;
        }

    }

    static byte[] HexToBytes(string input)
    {
        byte[] result = new byte[input.Length / 2];
        for (int i = 0; i < result.Length; i++)
        {
            result[i] = Convert.ToByte(input.Substring(2 * i, 2), 16);
        }
        return result;
    }

    static string BytesToHex(byte[] input, string separator = "")
    {
        var bytesDesc = "";
        foreach (var one in input)
        {
            bytesDesc += string.Format("{0:X2}", one) + separator;
        }

        return bytesDesc;
    }


}