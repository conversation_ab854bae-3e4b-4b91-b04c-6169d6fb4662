﻿
namespace Racebox.Helpers.Validation
{
    public class StringValidator : FieldValidator
    {
        public override bool Validate(object value)
        {
            Value = (string)value;

            return base.Validate(value);
        }

        protected string Value { get; set; }
    }

    public class DoubleValidator : FieldValidator
    {
        public override bool Validate(object value)
        {
            Value = $"{value}".ToDouble();

            return base.Validate(value);
        }

        protected double Value { get; set; }
    }

    public class RequiredDoubleValidator : DoubleValidator
    {

        public RequiredDoubleValidator(Func<FieldValidator, double, string> validate)
        {

            ThisValidateFunc = validate;

            ValidateFunc = (validator) =>
            {
                validator.ErrorMessage = ThisValidateFunc(this, Value);
                if (!string.IsNullOrEmpty(ErrorMessage))
                {
                    validator.IsValid = false; ;
                    return false;
                }

                validator.IsValid = true;
                validator.ErrorMessage = null;

                return true;
            };
        }

        Func<FieldValidator, double, string> ThisValidateFunc;
    }

    public class RequiredStringValidator : StringValidator
    {

        public RequiredStringValidator(Func<FieldValidator, string, string> validate)
        {

            ThisValidateFunc = validate;

            ValidateFunc = (validator) =>
            {
                validator.ErrorMessage = ThisValidateFunc(this, Value);
                if (!string.IsNullOrEmpty(ErrorMessage))
                {
                    validator.IsValid = false; ;
                    return false;
                }

                validator.IsValid = true;
                validator.ErrorMessage = null;

                return true;
            };
        }

        Func<FieldValidator, string, string> ThisValidateFunc;
    }
}
