using System.Windows.Input;

namespace Racebox.Views.Partials;

public partial class SkiaCheckBoxView
{
	public SkiaCheckBoxView()
	{
		InitializeComponent();
	}

	public ICommand CommandTapped
	{
		get
		{
			return new Command(async (object context) =>
			{
				if (Command != null)
				{
					Command.Execute(IsToggled);
				}
			});
		}
	}


	public static readonly BindableProperty CommandProperty = BindableProperty.Create(nameof(Command),
		typeof(ICommand), typeof(SkiaCheckBoxView),
		null);
	public ICommand Command
	{
		get { return (ICommand)GetValue(CommandProperty); }
		set { SetValue(CommandProperty, value); }
	}


	//-------------------------------------------------------------
	// IsToggled
	//-------------------------------------------------------------
	private const string nameIsToggled = "IsToggled";
	public static readonly BindableProperty IsToggledProperty = BindableProperty.Create(nameIsToggled,
		typeof(bool), typeof(SkiaCheckBoxView),
		false, BindingMode.TwoWay,
		propertyChanged: UpdateState);
	public bool IsToggled
	{
		get { return (bool)GetValue(IsToggledProperty); }
		set { SetValue(IsToggledProperty, value); }
	}

	private static void UpdateState(BindableObject bindable, object oldvalue, object newvalue)
	{
		if (bindable is SkiaCheckBoxView control)
		{
			//control.MoveThumb(); //todo animate at will
			control.Update();
		}
	}

	public TouchActionEventHandler OnTapped
	{
		get
		{
			return OnTapped_Handler;
		}
	}

	public void OnTapped_Handler(object sender, TouchActionEventArgs args)
	{
		if (sender is DrawnView control)
		{
			IsToggled = !IsToggled;
			//Box.PlayRippleAnimation(control, Colors.White, args.Location.X, args.Location.Y);
		}
	}

}