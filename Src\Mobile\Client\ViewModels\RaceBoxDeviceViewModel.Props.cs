﻿using AppoMobi.Specials.Localization;
using Racebox.SDK;
using Racebox.Shared.Enums;
using Racebox.Shared.Services;
using Racebox.Shared.Strings;

namespace Racebox.ViewModels
{

    public partial class RaceBoxDeviceViewModel : ContentViewModel
    {
        static string _placeholder = "-";

        bool _lockUpdateUI;

        private readonly SemaphoreSlim _updateUiSemaphore = new SemaphoreSlim(1, 1);


        void UpdateUi()
        {
            if (!_lockUpdateUI)
            {
                _lockUpdateUI = true;

                try
                {

                    {
                        if (CanToggleMeasure)
                        {
                            if (Processor.IsMeasuring)
                            {
                                SetVisualStyleForIncline("IsMeasuring");
                            }
                            else
                            {
                                ReplaceVisualStates("Normal");
                                //AddOrRemoveVisualStates(new[] { "Normal" }, new[] { "IsMeasuring", "Disabled", "BadIncline" });
                            }
                        }
                        else
                            ReplaceVisualStates("Disabled");
                    }


                    if (!string.IsNullOrEmpty(HardwareId))
                    {
                        Title = HardwareId;
                    }
                    else
                    {
                        Title = "Racebox";
                    }

                    SetProperty_IsConnecting();
                    SetProperty_SelectedCarTitle();
                    SetProperty_SpeedUnitsDisplay();
                    SetProperty_MainHint();
                    SetProperty_MainStatus();
                    SetProperty_MeasureTitle();
                    SetProperty_MainActionTitle();
                    SetProperty_HardwareId();
                    SetProperty_ServiceStatus();
                    SetProperty_MeasureActionTitle();
                    SetProperty_IsConnected();
                    SetProperty_CanExportLogs();
                    SetProperty_Time();
                    SetProperty_TimeMeasure();
                    SetProperty_DistanceMeasure();
                    SetProperty_DisplayAltitude();
                    SetProperty_DisplayAltitudeMonitor();
                    SetProperty_DisplayIncline();
                    SetProperty_DisplaySpeed();
                    SetProperty_DisplaySpeedMonitor();
                    SetProperty_DisplaySpeedValue();
                    SetProperty_DisplayAccelerationValue();
                    SetProperty_DisplayAccelerationSideValue();
                    SetProperty_DisplayAcceleration();
                    SetProperty_DisplaySideAcceleration();
                    SetProperty_DisplayHeading();
                    SetProperty_DisplayMaxSpeed();
                    SetProperty_SignalStrength();
                    SetProperty_DisplayFrequency();
                    SetProperty_DisplayDevice();
                    SetProperty_DisplayLat();
                    SetProperty_DisplayLon();
                    SetProperty_CanToggleMeasure();
                    SetProperty_GpsStrengthOk();
                    SetProperty_Satellites();
                    SetProperty_Temp();
                    SetProperty_Battery();

                    IsCalibrating = Processor.MeasuringState == MeasuringState.Ready;
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
                finally
                {
                    _lockUpdateUI = false;
                }

            }

        }

        private bool _IsRecording;
        public bool IsCalibrating
        {
            get
            {
                return _IsRecording;
            }
            set
            {
                if (_IsRecording != value)
                {
                    _IsRecording = value;
                    OnPropertyChanged();
                }
            }
        }

        #region REFACTORED

        private double _SignalStrength;
        public double SignalStrength
        {
            get
            {
                return _SignalStrength;
            }
            set
            {
                if (_SignalStrength != value)
                {
                    _SignalStrength = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_DisplayDevice()
        {
            DisplayDevice = IsConnected ? Title : _placeholder;
        }

        private string _DisplayDevice;
        public string DisplayDevice
        {
            get
            {
                return _DisplayDevice;
            }
            set
            {
                if (_DisplayDevice != value)
                {
                    _DisplayDevice = value;
                    OnPropertyChanged();
                }
            }
        }

        void SetProperty_Satellites()
        {
            if (!Connector.IsConnected)
            {
                DisplaySatellites = 0;
            }
            else
                DisplaySatellites = Processor.Satellites;
        }

        public int DisplaySatellites
        {
            get
            {
                return _DisplaySatellites;
            }
            set
            {
                if (value != _DisplaySatellites)
                {
                    _DisplaySatellites = value;
                    OnPropertyChanged();
                }
            }
        }
        private int _DisplaySatellites;

        void SetProperty_SignalStrength()
        {
            if (Processor.HDOP == 0.0 || !Connector.IsConnected)
            {
                SignalStrength = 0;
            }
            else
            if (Processor.HDOP > 5.0)
            {
                SignalStrength = 0.20;
            }
            else
            if (Processor.HDOP < 1.0)
            {
                SignalStrength = 1.0;
            }
            else
            {
                SignalStrength = SignalStrengthConverter.Convert(Processor.HDOP);
            }
        }


        private bool _CanToggleMeasure;
        public bool CanToggleMeasure
        {
            get
            {
                return _CanToggleMeasure;
            }
            set
            {
                if (_CanToggleMeasure != value)
                {
                    _CanToggleMeasure = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_CanToggleMeasure()
        {
            CanToggleMeasure = !(!IsConnected || _connector == null) && (Processor.IsMeasuring || GpsStrengthOk);
        }

        private bool _GpsStrengthOk;
        public bool GpsStrengthOk
        {
            get
            {
                return _GpsStrengthOk;
            }
            set
            {
                if (_GpsStrengthOk != value)
                {
                    _GpsStrengthOk = value;
                    OnPropertyChanged();
                    if (value)
                    {
                        OnGpsStrengthOk();
                    }
                }
            }
        }

        void SetProperty_GpsStrengthOk()
        {
            GpsStrengthOk = IsConnected && (Processor.HDOP < 2.5 && Processor.Satellites > 5);
        }


        private bool _IsConnected;
        public bool IsConnected
        {
            get
            {
                return _IsConnected;
            }
            set
            {
                if (_IsConnected != value)
                {
                    _IsConnected = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_IsConnected()
        {

            if (_connector != null)
            {
                IsConnected = Connector.IsConnected;
            }
            else
            {
                IsConnected = false;
            }
        }

        private string _DisplayHeading;
        public string DisplayHeading
        {
            get
            {
                return _DisplayHeading;
            }
            set
            {
                if (_DisplayHeading != value)
                {
                    _DisplayHeading = value;
                    OnPropertyChanged();
                }
            }
        }

        void SetProperty_DisplayHeading()
        {
            if (IsVisualStateEnabled() && Processor.SpeedOutput > RaceBoxStateProcessor.START_SPEED_THRESHOLD)
            {
                var value = Processor.Heading;
                DisplayHeading = $"{value:0}°";
            }
            else
            {
                DisplayHeading = _placeholder;
            }
        }


        private string _DisplayMaxSpeed;
        public string DisplayMaxSpeed
        {
            get
            {
                return _DisplayMaxSpeed;
            }
            set
            {
                if (_DisplayMaxSpeed != value)
                {
                    _DisplayMaxSpeed = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_DisplayMaxSpeed()
        {
            var units = ResStrings.Kmh;
            if (_userManager.User.Options.Units == OptionsUnits.US)
            {
                units = ResStrings.Mph;
            }
            if (IsVisualStateEnabled())
            {
                var value = Processor.MaxSpeed;
                DisplayMaxSpeed = $"{value:0.0} {units}";
            }
            else
            {
                DisplayMaxSpeed = $"{_placeholder}";
            }
        }


        private string _ExplainRollOut;
        public string ExplainRollOut
        {
            get
            {
                return _ExplainRollOut;
            }
            set
            {
                if (_ExplainRollOut != value)
                {
                    _ExplainRollOut = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_ExplainRollOut()
        {
            ExplainRollOut = LocalizedDisplayProvider.Instance.GetOnOffDisplay(_userManager.User.Options.Rollout);
        }



        private string _DisplaySideAcceleration;
        public string DisplaySideAcceleration
        {
            get
            {
                return _DisplaySideAcceleration;
            }
            set
            {
                if (_DisplaySideAcceleration != value)
                {
                    _DisplaySideAcceleration = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_DisplaySideAcceleration()
        {
            if (IsVisualStateEnabled())
            {
                var value = DisplayAccelerationSideValue;
                DisplaySideAcceleration = $"{value:0.00}G";
            }
            else
            {
                DisplaySideAcceleration = _placeholder;
            }
        }


        private string _DisplayFrequency;
        public string DisplayFrequency
        {
            get
            {
                return _DisplayFrequency;
            }
            set
            {
                if (_DisplayFrequency != value)
                {
                    _DisplayFrequency = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_DisplayFrequency()
        {
            if (IsConnected)
            {
                DisplayFrequency = $"{Processor.FrequencyHz:0.0} {ResStrings.Hz}";
            }
            else
            {
                DisplayFrequency = _placeholder;
            }
        }


        private string _DisplayAcceleration;
        public string DisplayAcceleration
        {
            get
            {
                return _DisplayAcceleration;
            }
            set
            {
                if (_DisplayAcceleration != value)
                {
                    _DisplayAcceleration = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_DisplayAcceleration()
        {
            if (IsVisualStateEnabled())
            {
                var value = DisplayAccelerationValue;
                DisplayAcceleration = $"{value:0.00}G";
            }
            else
            {
                DisplayAcceleration = _placeholder;
            }
        }


        private double _DisplayAccelerationSideValue;
        public double DisplayAccelerationSideValue
        {
            get
            {
                return _DisplayAccelerationSideValue;
            }
            set
            {
                if (_DisplayAccelerationSideValue != value)
                {
                    _DisplayAccelerationSideValue = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_DisplayAccelerationSideValue()
        {
            if (IsVisualStateEnabled() && Processor.SpeedOutput > RaceBoxStateProcessor.START_SPEED_THRESHOLD && Math.Abs(Processor.SideAccellerationFiltered) > 0.04)
            {
                var value = Processor.SideAccellerationFiltered;

                if (DisplayAccelerationSideValueMin > value)
                    DisplayAccelerationSideValueMin = value;
                else if (DisplayAccelerationSideValueMax < value)
                    DisplayAccelerationSideValueMax = value;

                DisplayAccelerationSideValue = value;
            }
            else
            {
                DisplayAccelerationSideValueMin = 0;
                DisplayAccelerationSideValueMax = 0;
                DisplayAccelerationSideValue = 0.0;
            }
        }


        private string _DisplaySpeed;
        public string DisplaySpeed
        {
            get
            {
                return _DisplaySpeed;
            }
            set
            {
                if (_DisplaySpeed != value)
                {
                    _DisplaySpeed = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_DisplaySpeed()
        {
            if (IsVisualStateEnabled())
            {
                var value = Processor.SpeedOutput;
                DisplaySpeed = $"{value:0}";
            }
            else
            {
                DisplaySpeed = _placeholder;
            }
        }


        private string _DisplaySpeedMonitor;
        public string DisplaySpeedMonitor
        {
            get
            {
                return _DisplaySpeedMonitor;
            }
            set
            {
                if (_DisplaySpeedMonitor != value)
                {
                    _DisplaySpeedMonitor = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_DisplaySpeedMonitor()
        {
            if (IsVisualStateEnabled())
            {
                var value = CutDouble1(Processor.SpeedOutput);
                DisplaySpeedMonitor = $"{value:0.0}";
            }
            else
            {
                DisplaySpeedMonitor = _placeholder;
            }
        }


        private string _DisplayLat;
        public string DisplayLat
        {
            get
            {
                return _DisplayLat;
            }
            set
            {
                if (_DisplayLat != value)
                {
                    _DisplayLat = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_DisplayLat()
        {
            if (IsVisualStateEnabled())
            {
                var value = Processor.Latitude;
                DisplayLat = $"{value:0}°";
            }
            else
            {
                DisplayLat = _placeholder;
            }
        }


        private string _DisplayLon;
        public string DisplayLon
        {
            get
            {
                return _DisplayLon;
            }
            set
            {
                if (_DisplayLon != value)
                {
                    _DisplayLon = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_DisplayLon()
        {
            if (IsVisualStateEnabled())
            {
                var value = Processor.Longitude;
                DisplayLon = $"{value:0}°";
            }
            else
            {
                DisplayLon = _placeholder;
            }
        }


        private double _DisplaySpeedValue;
        public double DisplaySpeedValue
        {
            get
            {
                return _DisplaySpeedValue;
            }
            set
            {
                if (_DisplaySpeedValue != value)
                {
                    _DisplaySpeedValue = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_DisplaySpeedValue()
        {
            if (IsVisualStateEnabled())
            {
                DisplaySpeedValue = Processor.SpeedOutput;
            }
            else
            {
                DisplaySpeedValue = 0.0;
            }
        }


        private double _DisplayAccelerationValue;
        public double DisplayAccelerationValue
        {
            get
            {
                return _DisplayAccelerationValue;
            }
            set
            {
                if (_DisplayAccelerationValue != value)
                {
                    _DisplayAccelerationValue = value;
                    OnPropertyChanged();
                }
            }
        }

        void SetProperty_DisplayAccelerationValue()
        {
            if (IsVisualStateEnabled() && Processor.SpeedOutput > RaceBoxStateProcessor.START_SPEED_THRESHOLD && Math.Abs(Processor.MeasuringAcceleration) > 0.04)
            {
                var value = Processor.MeasuringAcceleration;

                if (DisplayAccelerationValueMin > value)
                    DisplayAccelerationValueMin = value;
                else if (DisplayAccelerationValueMax < value)
                    DisplayAccelerationValueMax = value;

                DisplayAccelerationValue = value;
            }
            else
            {
                DisplayAccelerationValueMin = 0;
                DisplayAccelerationValueMax = 0;
                DisplayAccelerationValue = 0.0;
            }
        }

        void SetProperty_Battery()
        {
            if (!IsConnected)
            {
                HasBattery = false;
                return;
            }

            //#if DEBUG
            //            HasBattery = true;
            //            DisplayBateryLevel = 0.75;
            //            return;
            //#endif

            if (Processor.RaceBoxExtendedState != null && Processor.RaceBoxExtendedState.BatteryLevel != null)
            {
                HasBattery = true;
                var state = Processor.RaceBoxExtendedState.BatteryChargingState.GetValueOrDefault();
                if (state == ChargingStateType.NotCharging)
                {
                    DisplayPower = $"{Processor.RaceBoxExtendedState.BatteryLevel.GetValueOrDefault()}%";
                }
                else
                {
                    var localized = LocalizedEnumConverter.ConvertToString(state);
                    DisplayPower = $"{localized}";
                }

                DisplayBateryLevel = Processor.RaceBoxExtendedState.BatteryLevel.GetValueOrDefault() / 100.0;
            }
            else
            {
                DisplayPower = _placeholder;
                HasBattery = false;
            }
        }

        private string _MainStatus;
        public string MainStatus
        {
            get
            {
                return _MainStatus;
            }
            set
            {
                if (_MainStatus != value)
                {
                    _MainStatus = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_MainStatus()
        {
            if (!CanToggleMeasure)
            {
                MainStatus = LocalizedDisplayProvider.Instance.GetMainConnectionStatus(IsBusy, IsConnected, GpsStrengthOk);
            }
            else
            {
                MainStatus = string.Empty;
            }
        }

        private string _TimeMeasure;
        public string TimeMeasure
        {
            get
            {
                return _TimeMeasure;
            }
            set
            {
                if (_TimeMeasure != value)
                {
                    _TimeMeasure = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_TimeMeasure()
        {
            if (!Processor.IsMeasuring)
            {
                if (LastResult != null)
                    TimeMeasure = LocalizedDisplayProvider.Instance.GetTimeDisplay(LastResult.Duration);
                else
                    TimeMeasure = LocalizedDisplayProvider.Instance.GetNullableTimeDisplay(null);
            }
            else
                //идет измерение
                TimeMeasure = LocalizedDisplayProvider.Instance.GetTimeDisplay(Processor.MeasuredTime);
        }


        private string _DistanceMeasure;
        public string DistanceMeasure
        {
            get
            {
                return _DistanceMeasure;
            }
            set
            {
                if (_DistanceMeasure != value)
                {
                    _DistanceMeasure = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_DistanceMeasure()
        {
            if (!Processor.IsMeasuring)
            {
                if (LastResult != null)
                {
                    DistanceMeasure = $"{Processor.MeasuringDistanceInMeters:0}{LocalizedDisplayProvider.Instance.GetDistanceDisplay(_userManager.User.Options.Units)}";
                }
                else
                    DistanceMeasure = $"- {LocalizedDisplayProvider.Instance.GetDistanceDisplay(_userManager.User.Options.Units).Trim()}";
            }
            else
                //идет измерение
                DistanceMeasure = $"{Processor.MeasuringDistanceInMeters:0}{LocalizedDisplayProvider.Instance.GetDistanceDisplay(_userManager.User.Options.Units)}";
        }


        private string _DisplayAltitude;
        public string DisplayAltitude
        {
            get
            {
                return _DisplayAltitude;
            }
            set
            {
                if (_DisplayAltitude != value)
                {
                    _DisplayAltitude = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_DisplayAltitude()
        {
            if (IsVisualStateEnabled())
            {
                var value = Processor.AltitudeAverage;
                if (_userManager.User.Options.Units == OptionsUnits.US)
                {
                    value *= RaceBoxStateProcessor.M_TO_FT;
                }
                DisplayAltitude = $"{value:0}{LocalizedDisplayProvider.Instance.GetDistanceDisplay(_userManager.User.Options.Units)}";
            }
            else
                DisplayAltitude = $"- {LocalizedDisplayProvider.Instance.GetDistanceDisplay(_userManager.User.Options.Units).Trim()}";
        }


        private string _DisplayAltitudeMonitor;
        public string DisplayAltitudeMonitor
        {
            get
            {
                return _DisplayAltitudeMonitor;
            }
            set
            {
                if (_DisplayAltitudeMonitor != value)
                {
                    _DisplayAltitudeMonitor = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_DisplayAltitudeMonitor()
        {
            if (IsVisualStateEnabled())
            {
                var value = Processor.AltitudeAverage;
                if (_userManager.User.Options.Units == OptionsUnits.US)
                {
                    value *= RaceBoxStateProcessor.M_TO_FT;
                }
                DisplayAltitudeMonitor = $"{value:0}{LocalizedDisplayProvider.Instance.GetDistanceDisplay(_userManager.User.Options.Units)}";
            }
            else
                DisplayAltitudeMonitor = _placeholder;
        }


        private string _DisplayIncline;
        public string DisplayIncline
        {
            get
            {
                return _DisplayIncline;
            }
            set
            {
                if (_DisplayIncline != value)
                {
                    _DisplayIncline = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_DisplayIncline()
        {
            if (IsVisualStateEnabled() && Processor.SpeedOutput > RaceBoxStateProcessor.START_SPEED_THRESHOLD)
            {
                var value = Math.Truncate(100 * Processor.MeasuringIncline) / 100;
                DisplayIncline = $"{value:0.0}%";
            }
            else
                DisplayIncline = _placeholder;
        }


        private string _Time;
        [Obsolete]
        public string Time
        {
            get
            {
                return _Time;
            }
            set
            {
                if (_Time != value)
                {
                    _Time = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_Time()
        {
            if (Processor.RaceBoxState.Year == null)
            {
                Time = "неизвестно";
            }
            else
            {
                Time = $"{Processor.RaceBoxState.TimeStampUtc:dd/MM/yy hh':'mm':'ss}";
            }
        }


        private bool _IsConnecting;
        public bool IsConnecting
        {
            get
            {
                return _IsConnecting;
            }
            set
            {
                if (_IsConnecting != value)
                {
                    _IsConnecting = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_IsConnecting()
        {
            IsConnecting = IsBusy && !IsConnected;
        }


        private string _SelectedCarTitle;
        public string SelectedCarTitle
        {
            get
            {
                return _SelectedCarTitle;
            }
            set
            {
                if (_SelectedCarTitle != value)
                {
                    _SelectedCarTitle = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_SelectedCarTitle()
        {
            SelectedCarTitle = _userManager.User.SelectedCar != null ? _userManager.User.SelectedCar.Title : "???";
        }


        private string _SpeedUnitsDisplay;
        public string SpeedUnitsDisplay
        {
            get
            {
                return _SpeedUnitsDisplay;
            }
            set
            {
                if (_SpeedUnitsDisplay != value)
                {
                    _SpeedUnitsDisplay = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_SpeedUnitsDisplay()
        {
            SpeedUnitsDisplay = _userManager.User != null ? LocalizedDisplayProvider.Instance.GetSpeedDisplay(_userManager.User.Options.Units) : string.Empty;
        }


        private string _MeasureActionTitle;
        public string MeasureActionTitle
        {
            get
            {
                return _MeasureActionTitle;
            }
            set
            {
                if (_MeasureActionTitle != value)
                {
                    _MeasureActionTitle = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_MeasureActionTitle()
        {
            if (_connector != null)
            {
                MeasureActionTitle = Connector.IsConnected ? LocalizedDisplayProvider.Instance.GetMeasureActionDisplay(Processor.IsMeasuring) : LocalizedDisplayProvider.Instance.GetMeasureActionDisplay(false);
            }
            else
            {
                MeasureActionTitle = LocalizedDisplayProvider.Instance.GetMeasureActionDisplay(false);
            }
        }


        private string _ServiceStatus;
        public string ServiceStatus
        {
            get
            {
                return _ServiceStatus;
            }
            set
            {
                if (_ServiceStatus != value)
                {
                    _ServiceStatus = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_ServiceStatus()
        {
            if (_connector != null)
            {
                ServiceStatus = Connector.IsConnected ? Connector.Status : "...";
            }
            else
            {
                ServiceStatus = "...";
            }
        }


        private string _HardwareId;
        public string HardwareId
        {
            get
            {
                return _HardwareId;
            }
            set
            {
                if (_HardwareId != value)
                {
                    _HardwareId = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_HardwareId()
        {

            if (_connector != null)
            {
                HardwareId = Connector.IsConnected ? Connector.LastName : "";
            }
            else
            {
                HardwareId = "";
            }
        }


        private bool _CanExportLogs;
        public bool CanExportLogs
        {
            get
            {
                return _CanExportLogs;
            }
            set
            {
                if (_CanExportLogs != value)
                {
                    _CanExportLogs = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_CanExportLogs()
        {
            CanExportLogs = LastResult != null;
        }

        private string _MainHint;
        public string MainHint
        {
            get
            {
                return _MainHint;
            }
            set
            {
                if (_MainHint != value)
                {
                    _MainHint = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_MainHint()
        {
            MainHint = SetMainHint();
        }
        string SetMainHint()
        {

            if (!IsBusy)
            {

                if (IsConnected)
                {

                    switch (Processor.IsMeasuring)
                    {
                    case true when Processor.MeasuringState == MeasuringState.Ready:
                    return ResStrings.HintGo;

                    case true
                        when Processor.MeasuringState == MeasuringState.Monitoring
                             && MeasureResults.Count == 0:
                    return ResStrings.HintMonitoring;

                    case false when MeasuringState == MeasuringState.Error:
                    {
                        //#if DEBUG
                        if (!string.IsNullOrEmpty(Processor.EndCause))
                        {
                            return $"{ResStrings.HintMeasureFailed}: {Processor.EndCause}";
                        }
                        //#endif
                        return ResStrings.HintMeasureFailed;
                    }

                    case false when LastResult == null && MeasureResults.Count == 0:
                    {
                        if (UseMock)
                        {
                            return ResStrings.DemoStartHint;
                        }

                        if (!GpsStrengthOk)
                            return ResStrings.HintGpsLow;

                        return ResStrings.HintStartMeasuring;
                    }

                    }
                }
                else
                {
                    if (MeasureResults.Count == 0)
                        return ResStrings.HintConnectNow;
                }
            }

            return string.Empty;

        }

        private string _MainActionTitle;
        public string MainActionTitle
        {
            get
            {
                return _MainActionTitle;
            }
            set
            {
                if (_MainActionTitle != value)
                {
                    _MainActionTitle = value;
                    OnPropertyChanged();
                }
            }
        }
        void SetProperty_MainActionTitle()
        {

            if (_connector != null)
            {
                MainActionTitle = LocalizedDisplayProvider.Instance.GetMeasureActionDisplay(Connector.IsConnected);
            }
            else
                MainActionTitle = LocalizedDisplayProvider.Instance.GetMeasureActionDisplay(false);
        }


        private string _MeasureTitle;
        public string MeasureTitle
        {
            get
            {
                return _MeasureTitle;
            }
            set
            {
                if (_MeasureTitle != value)
                {
                    _MeasureTitle = value;
                    OnPropertyChanged();
                }
            }
        }

        void SetProperty_MeasureTitle()
        {
            if (CanToggleMeasure)
            {
                MeasureTitle = LocalizedDisplayProvider.Instance.GetMeasureTitle(Processor.IsInstant);
            }
            else
                MeasureTitle = string.Empty;
        }

        void SetProperty_Temp()
        {
            if (!string.IsNullOrEmpty(WeatherIcon))
            {
                if (_userManager.User.Options.Units == OptionsUnits.EU)
                {
                    DisplayTemp = TempC.ToString("+0.0C;-0.0C;0.0C");
                }
                else
                {
                    DisplayTemp = TempF.ToString("+0.0F;-0.0F;0.0F");
                }
            }
            else
            {
                DisplayTemp = _placeholder;
            }
        }

        private string _DisplayTemp;
        public string DisplayTemp
        {
            get
            {
                return _DisplayTemp;
            }
            set
            {
                if (_DisplayTemp != value)
                {
                    _DisplayTemp = value;
                    OnPropertyChanged();
                }
            }
        }

        private decimal _TempC;
        public decimal TempC
        {
            get
            {
                return _TempC;
            }
            set
            {
                if (_TempC != value)
                {
                    _TempC = value;
                    OnPropertyChanged();
                }
            }
        }

        private decimal _TempF;
        public decimal TempF
        {
            get
            {
                return _TempF;
            }
            set
            {
                if (_TempF != value)
                {
                    _TempF = value;
                    OnPropertyChanged();
                }
            }
        }

        private string _WeatherIcon;
        public string WeatherIcon
        {
            get
            {
                return _WeatherIcon;
            }
            set
            {
                if (_WeatherIcon != value)
                {
                    _WeatherIcon = value;
                    OnPropertyChanged();
                }
            }
        }


        private string _DisplayPower;
        public string DisplayPower
        {
            get
            {
                return _DisplayPower;
            }
            set
            {
                if (_DisplayPower != value)
                {
                    _DisplayPower = value;
                    OnPropertyChanged();
                }
            }
        }



        #endregion



    }
}
