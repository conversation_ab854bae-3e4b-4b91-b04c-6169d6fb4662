﻿using Racebox.ApiClient.Dto;
using Racebox.ApiClient.Extensions;
using Racebox.ApiClient.Interfaces;
using Racebox.ApiClient.Models;
using Racebox.Shared.Services;
using System.Net;

namespace Racebox.Shared.WeatherApi
{
    public static class WeatherApiExtensions
    {

        /// <summary>
        /// WeatherService will be added as singleton
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection AddWeatherApi(this IServiceCollection services)
        {
            services.AddSingleton<WeatherService>();
            services.AddApiClient("https://api.weatherapi.com/v1", "weatherApi");
            return services;
        }

        public static string Encode(this string input)
        {
            return WebUtility.UrlEncode(input);
        }

        /// <summary>
        /// Get current weather at location. Pass descriptive address or coords like '48.856079 2.298828'.
        /// </summary>
        public static async Task<IResult<WeatherApiResponse>> GetCurrentWeather(
            this HttpClient client,
            string apiKey, string location)
        {
            try
            {
                var request = $"v1/current.json?key={apiKey}&q={Encode(location)}";

                var result = await client.RequestAsync<WeatherApiResponse>(HttpMethod.Get,
                    request, null);

                return result;
            }
            catch (Exception e)
            {
                return new Result<WeatherApiResponse>(e);
            }
        }
    }
}
