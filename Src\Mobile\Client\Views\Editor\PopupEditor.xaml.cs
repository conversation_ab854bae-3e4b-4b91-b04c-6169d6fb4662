
using FastPopups;
using Mopups.Events;
using Mopups.Services;

namespace Racebox.Views.Popups;

public class MauiPopupContent : Grid, IDisposable
{
    public void Dispose()
    {
        foreach (var child in Children)
        {
            if (child is IDisposable disposable)
            {
                disposable.Dispose();
            }
        }
    }


}

public partial class PopupEditor
{
    private void OnPopped(object sender, PopupNavigationEventArgs e)
    {
        if (e.Page == this)
        {
            MopupService.Instance.Popped -= OnPopped;
            if (this.Content is IDisposable disposable)
            {
                disposable.Dispose();
            }
        }
    }

    private readonly DeviceSettingsViewModel _vm;

    public PopupEditor(DeviceSettingsViewModel vm)
    {
        BindingContext = _vm = vm;

        InitializeComponent();

        MopupService.Instance.Popped += OnPopped;
    }

    bool lockOk;

    private async void OK_Tapped(object sender, ControlTappedEventArgs controlTappedEventArgs)
    {
        if (lockOk)
        {
            return;
        }

        lockOk = true;

        try
        {
            await DismissDialog();
            _vm.EditorOnOk?.Invoke("0");
        }
        catch (Exception exception)
        {
            Super.Log(exception);
        }
        finally
        {
            lockOk = false;
        }
    }

    private async Task DismissDialog()

    {
        try
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                // Update the UI
                try
                {
                    //todo dispose upon MopupService.Instance.Popped
                    await MopupService.Instance.PopAsync(true);
                }
                catch
                {
                }
            });
        }
        catch (Exception e)
        {
            Super.Log(e);
        }
    }

    private void OnClosed(object sender, PopupClosedEventArgs e)
    {
        if (this.Content is IDisposable disposable)
        {
            disposable.Dispose();
        }
    }
}

