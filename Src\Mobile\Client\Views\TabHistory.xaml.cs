using AppoMobi.Framework.Maui.Controls.Navigation.Tabs;
using AppoMobi.Maui.Navigation;

namespace Racebox.Views;

public partial class TabHistory : ILazyTab
{
	private readonly HistoryViewModel _viewModel;

	public bool IsDisposed { get; }

	public TabHistory()
	{
		try
		{
			BindingContext = _viewModel = App.Instance.Services.GetService<HistoryViewModel>();

			InitializeComponent();

		}
		catch (Exception e)
		{
			Designer.DisplayException(this, e);
		}
	}

	public void Dispose()
	{
		//todo
	}

	public void UpdateControls(DeviceRotation orientation)
	{

	}

	public void OnViewAppearing()
	{
		Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(1000), async () =>
		{
			await _viewModel.UpdateState();
			return false;
		});
	}

	public void OnViewDisappearing()
	{

	}

	public void KeyboardResized(double size)
	{

	}



}