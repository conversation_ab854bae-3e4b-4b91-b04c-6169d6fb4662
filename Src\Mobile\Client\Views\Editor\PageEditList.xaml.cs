
using Racebox.Interfaces;

namespace Racebox.Views;



public partial class PageEditList : BasePage, ILazyPage
{
    private readonly ISupportsListEditor _vm;

    public TouchActionEventHandler LongPressingHandler => (sender, args) =>
    {
        args.PreventDefault = true;
    };


    public PageEditList(ISupportsListEditor vm)
    {
        BindingContext = _vm = vm;

        InitializeComponent();
    }

    protected override void OnAppearing()
    {
        base.OnAppearing();

        Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(150), async () =>
        {
            var canUpdate = BindingContext as IUpdateUIState;
            if (canUpdate != null)
            {
                canUpdate.UpdateState(true);
            }
            return false;
        });
    }

    public void OnViewAppearing()
    {
        OnAppearing();
    }

    public void OnViewDisappearing()
    {
        OnDisappearing();
    }

    public void UpdateControls(DeviceRotation orientation)
    {

    }

    //int count = 0;
    //private void OnBtnClicked(object sender, EventArgs e)
    //{
    //    count++;

    //    if (count == 1)
    //        CounterBtn.Text = $"Clicked {count} time";
    //    else
    //        CounterBtn.Text = $"Clicked {count} times";

    //    SemanticScreenReader.Announce(CounterBtn.Text);

    //    MainCanvas.InvalidateChildren();

    //    MainCanvas.PrintDebug();

    //    var check = MainCanvas.ShouldRender();

    //}
}