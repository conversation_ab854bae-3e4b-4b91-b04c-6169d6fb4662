﻿<?xml version="1.0" encoding="utf-8" ?>
<views2:BasePage
    x:Class="Racebox.Views.PageFilterResults"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:partials1="clr-namespace:Racebox.Views.Partials"
    xmlns:strings="clr-namespace:Racebox.Shared.Strings;assembly=Racebox.Shared"
    xmlns:touch="clr-namespace:AppoMobi.Maui.Gestures;assembly=AppoMobi.Maui.Gestures"
    xmlns:viewModels1="clr-namespace:Racebox.ViewModels"
    xmlns:views2="clr-namespace:Racebox.Views"
    xmlns:touch1="clr-namespace:AppoMobi.Framework.Maui.Touch;assembly=AppoMobi.Framework.Maui"
    x:Name="ThisPage"
    Title="PageFilterResults"
    x:DataType="viewModels1:HistoryViewModel">


    <Grid
        HorizontalOptions="FillAndExpand"
        VerticalOptions="FillAndExpand">

        <!--<Grid.Background>
            <LinearGradientBrush EndPoint="0,1">
                <GradientStop Offset="0.0" Color="#343C45" />
                <GradientStop Offset="1.0" Color="#11161D" />
            </LinearGradientBrush>
        </Grid.Background>-->

        <partials1:GradientToBorderView 
            MissingHeight="{Binding NavbarModel.BottomTabsHeightRequest}"
            VerticalOptions="Fill" HorizontalOptions="Fill"/>

        <partials1:EmbossedFrame
            Padding="12,16,12,8"
            VerticalOptions="FillAndExpand">
            <partials1:EmbossedFrame.Content>

                <Grid
                    RowDefinitions="65,*"
                    RowSpacing="0">

                    <!--  NEW NAVBAR  -->
                    <draw:Canvas
                        Gestures="Enabled"
                        HeightRequest="65"
                        HorizontalOptions="Fill"
                        VerticalOptions="Start">
                        <partials1:SkiaNavBar
                            x:Name="NavBar"
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill">

                            <draw:SkiaSvg
                                Margin="16,0,16,0"
                                HeightRequest="16"
                                HorizontalOptions="Start"
                                SvgString="{StaticResource SvgGoBack}"
                                TintColor="#CB6336"
                                VerticalOptions="Center"
                                WidthRequest="16" />

                            <draw:SkiaHotspot
                                TransformView="{x:Reference NavBar}"
                                CommandTapped="{Binding NavbarModel.CommandGoBack, Mode=OneTime}"
                                HorizontalOptions="Start"
                                WidthRequest="44" />

                            <draw:SkiaLabel
                                Margin="48,0,16,0"
                                FontSize="14"
                                HorizontalOptions="Start"
                                LineBreakMode="TailTruncation"
                                MaxLines="1"
                                Style="{StaticResource SkiaLabelDefaultStyle}"
                                Text="{x:Static strings:ResStrings.Filter}"
                                TextColor="#E8E3D7"
                                TranslationY="1"
                                VerticalOptions="Center" />

                            <!--  LINE HORIZONTAL  -->
                            <draw:SkiaShape
                                Margin="-16,0"
                                BackgroundColor="Black"
                                CornerRadius="0"
                                HeightRequest="1"
                                HorizontalOptions="Fill"
                                StrokeWidth="0"
                                VerticalOptions="End">
                                <draw:SkiaShape.FillGradient>

                                    <draw:SkiaGradient
                                        EndXRatio="1"
                                        EndYRatio="0"
                                        StartXRatio="0"
                                        StartYRatio="0"
                                        Type="Linear">
                                        <draw:SkiaGradient.Colors>
                                            <Color>#00E8E3D7</Color>
                                            <Color>#99E8E3D7</Color>
                                            <Color>#00E8E3D7</Color>
                                        </draw:SkiaGradient.Colors>
                                    </draw:SkiaGradient>

                                </draw:SkiaShape.FillGradient>
                            </draw:SkiaShape>

                        </partials1:SkiaNavBar>
                    </draw:Canvas>

                    <ScrollView Grid.Row="1">
                        <StackLayout
                            Padding="16"
                            Spacing="16"
                            VerticalOptions="StartAndExpand">

                            <Label
                                Style="{StaticResource EditorFieldTitle}"
                                Text="{x:Static strings:ResStrings.Car}" />

                            <Label
                                Padding="8,2"
                                touch:TouchEffect.CommandTapped="{Binding CommandSelectFilterCar}"
                                BackgroundColor="#33000000"
                                HorizontalOptions="Fill"
                                Text="{Binding DisplayFilterCars}" />

                            <Label
                                Style="{StaticResource EditorFieldTitle}"
                                Text="{x:Static strings:ResStrings.IsValid}" />

                            <Label
                                Padding="8,2"
                                touch:TouchEffect.CommandTapped="{Binding CommandSelectFilterIsValid}"
                                BackgroundColor="#33000000"
                                HorizontalOptions="Fill"
                                Text="{Binding DisplayIsValid}" />


                            <!--  RESET BTN  -->
                            <partials1:ButtonSmall
                                Margin="0,16,0,0"
                                IsDisabled="{Binding IsBusy}"
                                CommandTapped="{Binding CommandResetFilter, Mode=OneTime}"
                                Text="{x:Static strings:ResStrings.BtnReset}"/>

                            <!--  SAVE BTN  -->
                            <partials1:ButtonSmall
                                Margin="0,16"
                                IsDisabled="{Binding IsBusy}"
                                CommandTapped="{Binding CommandSubmitFilter, Mode=OneTime}"
                                Text="{x:Static strings:ResStrings.BtnApply}"/>


                        </StackLayout>

                    </ScrollView>


                    <touch1:Hotspot
                        Grid.Row="0"
                        CommandTapped="{Binding NavbarModel.CommandGoBack}"
                        HorizontalOptions="Start"
                        WidthRequest="44" />


                </Grid>

            </partials1:EmbossedFrame.Content>
        </partials1:EmbossedFrame>

    </Grid>



</views2:BasePage>