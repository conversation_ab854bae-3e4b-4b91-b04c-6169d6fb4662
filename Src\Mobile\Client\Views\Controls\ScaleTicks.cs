﻿using SkiaSharp;

namespace Racebox.Views.Controls;

public class ScaleTicks : SkiaLayout
{
    private static void NeedUpdate(BindableObject bindable, object oldvalue, object newvalue)
    {
        if (bindable is ScaleTicks control)
        {
            control.Render();
        }
    }


    public void Render()
    {
        _dirty = true;

        Update();
    }

    SkiaLabel CreatePointLabel(double value, double translationX, double translationY)
    {
        var text = $"{value:0.0}".Replace("-", string.Empty).Trim();

        if (ShowSign)
        {
            if (value > 0)
            {
                text = "+" + text;
            }
            else
            if (value < 0)
            {
                text = "-" + text;
            }
        }

        var label = new SkiaLabel()
        {
            FontFamily = "FontText",
            FontSize = 10,
            Text = text,
            TextColor = Color.FromArgb("#82E8E3D7"),
            TranslationX = Orientation == ScrollOrientation.Horizontal ? translationX : translationY - 12.0,
            TranslationY = Orientation == ScrollOrientation.Horizontal ? translationY : translationX
        };

        if (IsMirrored)
        {
            label.HorizontalOptions = Orientation == ScrollOrientation.Horizontal ? LayoutOptions.Center : LayoutOptions.End;
            label.VerticalOptions = Orientation == ScrollOrientation.Horizontal ? LayoutOptions.Start : LayoutOptions.Center;
        }
        else
        {
            label.HorizontalPositionOffsetRatio = Orientation == ScrollOrientation.Horizontal ? -0.5 : 0;
            label.VerticalPositionOffsetRatio = Orientation == ScrollOrientation.Horizontal ? 0 : -0.5;

            label.HorizontalOptions = Orientation == ScrollOrientation.Horizontal ? LayoutOptions.Start : LayoutOptions.End;
            label.VerticalOptions = Orientation == ScrollOrientation.Horizontal ? LayoutOptions.Start : LayoutOptions.Start;
        }
        return label;

    }

    SkiaShape CreateScalePointLine(double translationX,
        double translationY,
        double height)
    {
        var line = new SkiaShape()
        {
            StrokeWidth = 0.0,
            WidthRequest = Orientation == ScrollOrientation.Horizontal ? 1.0 : height,
            HeightRequest = Orientation == ScrollOrientation.Horizontal ? height : 1.0,
            Type = ShapeType.Rectangle,
            BackgroundColor = ScaleColor,
            TranslationX = Orientation == ScrollOrientation.Horizontal ? translationX : translationY,
            TranslationY = Orientation == ScrollOrientation.Horizontal ? translationY : translationX,
        };
        if (IsMirrored)
        {
            line.HorizontalOptions = Orientation == ScrollOrientation.Horizontal ? LayoutOptions.Center : LayoutOptions.End;
            line.VerticalOptions = Orientation == ScrollOrientation.Horizontal ? LayoutOptions.End : LayoutOptions.Center;
        }
        else
        {
            line.HorizontalOptions = Orientation == ScrollOrientation.Horizontal ? LayoutOptions.Start : LayoutOptions.End;
            line.VerticalOptions = Orientation == ScrollOrientation.Horizontal ? LayoutOptions.End : LayoutOptions.Start;
        }
        return line;
    }

    void Build(SKRect destination, double scale)
    {
        var lineLength = 8.0;

        var scaledWidth = Orientation == ScrollOrientation.Horizontal ? destination.Width / scale : destination.Height / scale;

        var points = Points;

        if (IsMirrored)
        {
            points *= 2;
        }

        var totalNumbers = points + 1; // *per side + center



        var stepDistance = (scaledWidth - SideOffset * 2) / (totalNumbers - 1);

        var children = new List<SkiaControl>();

        var y = Min;

        var step = Max / (Points * 1.0);

        if (IsMirrored)
        {

            children.Add(CreatePointLabel(y, 0, 0));
            children.Add(CreateScalePointLine(0, 0, lineLength));

            for (int i = 1; i <= Points; i++)
            {
                var value = Min + step * i;

                if (Invert)
                {
                    value = -value;
                }

                children.Add(CreatePointLabel(-value, stepDistance * i, y));
                children.Add(CreatePointLabel(value, -stepDistance * i, y));

                var offset = stepDistance * i;

                if (ShowHalves)
                    children.Add(CreateScalePointLine(offset - stepDistance / 2.0, 0, lineLength / 2.0));
                children.Add(CreateScalePointLine(offset, 0, lineLength));

                if (ShowHalves)
                    children.Add(CreateScalePointLine(-offset + stepDistance / 2.0, 0, lineLength / 2.0));
                children.Add(CreateScalePointLine(-offset, 0, lineLength));
            }
        }
        else
        {

            if (Orientation == ScrollOrientation.Horizontal)
            {
                children.Add(CreatePointLabel(y, SideOffset, 0));
                children.Add(CreateScalePointLine(SideOffset, 0, lineLength));
            }
            else
            if (Orientation == ScrollOrientation.Vertical)
            {
                children.Add(CreatePointLabel(0, 0, SideOffset));
                children.Add(CreateScalePointLine(0, SideOffset, lineLength));
            }

            for (int i = 1; i <= Points; i++)
            {
                var value = Min + step * i;

                if (Invert)
                {
                    value = -value;
                }

                children.Add(CreatePointLabel(value, SideOffset + stepDistance * i, y));

                var offset = SideOffset + stepDistance * i;

                if (ShowHalves)
                    children.Add(CreateScalePointLine(offset - stepDistance / 2.0, 0, lineLength / 2.0));

                children.Add(CreateScalePointLine(offset, 0, lineLength));

            }
        }

        SetChildren(children);
    }

    private SKRect lastDestination = SKRect.Empty;

    public override void Arrange(SKRect destination, float widthRequest, float heightRequest, float scale)
    {
        base.Arrange(destination, widthRequest, heightRequest, scale);

        if (lastDestination != Destination)
        {
            lastDestination = Destination;
            _dirty = true;
        }

        if (_dirty)
        {
            _dirty = false;
            Build(Destination, scale);
        }
    }

    private bool _dirty = true;

    public static readonly BindableProperty ShowHalvesProperty = BindableProperty.Create(nameof(ShowHalves),
        typeof(bool),
        typeof(ScaleTicks),
        true,
        propertyChanged: NeedUpdate);
    public bool ShowHalves
    {
        get { return (bool)GetValue(ShowHalvesProperty); }
        set { SetValue(ShowHalvesProperty, value); }
    }

    public static readonly BindableProperty InvertProperty = BindableProperty.Create(nameof(Invert),
        typeof(bool),
        typeof(ScaleTicks),
        false,
        propertyChanged: NeedUpdate);
    public bool Invert
    {
        get { return (bool)GetValue(InvertProperty); }
        set { SetValue(InvertProperty, value); }
    }

    public static readonly BindableProperty OrientationProperty = BindableProperty.Create(nameof(Orientation),
        typeof(ScrollOrientation),
        typeof(ScaleTicks),
        ScrollOrientation.Horizontal,
        propertyChanged: NeedUpdate);
    public ScrollOrientation Orientation
    {
        get { return (ScrollOrientation)GetValue(OrientationProperty); }
        set { SetValue(OrientationProperty, value); }
    }

    public static readonly BindableProperty MinProperty = BindableProperty.Create(nameof(Min),
        typeof(double),
        typeof(ScaleTicks),
        0.0, propertyChanged: NeedUpdate);
    public double Min
    {
        get { return (double)GetValue(MinProperty); }
        set { SetValue(MinProperty, value); }
    }

    public static readonly BindableProperty MaxProperty = BindableProperty.Create(nameof(Max),
        typeof(double),
        typeof(ScaleTicks),
        1.0, propertyChanged: NeedUpdate);
    public double Max
    {
        get { return (double)GetValue(MaxProperty); }
        set { SetValue(MaxProperty, value); }
    }

    public static readonly BindableProperty PointsProperty = BindableProperty.Create(nameof(Points),
        typeof(int),
        typeof(ScaleTicks),
        2, propertyChanged: NeedUpdate);
    public int Points
    {
        get { return (int)GetValue(PointsProperty); }
        set { SetValue(PointsProperty, value); }
    }


    public static readonly BindableProperty SideOffsetProperty = BindableProperty.Create(nameof(SideOffset),
        typeof(double),
        typeof(ScaleTicks),
        8.0, propertyChanged: NeedUpdate);
    public double SideOffset
    {
        get { return (double)GetValue(SideOffsetProperty); }
        set { SetValue(SideOffsetProperty, value); }
    }

    public static readonly BindableProperty TextColorProperty = BindableProperty.Create(nameof(TextColor), typeof(Color), typeof(ScaleTicks),
        Colors.Gray,
        propertyChanged: NeedUpdate);
    public Color TextColor
    {
        get { return (Color)GetValue(TextColorProperty); }
        set { SetValue(TextColorProperty, value); }
    }

    public static readonly BindableProperty ScaleColorProperty = BindableProperty.Create(nameof(ScaleColor), typeof(Color), typeof(ScaleTicks),
        Colors.Gray,
        propertyChanged: NeedUpdate);
    public Color ScaleColor
    {
        get { return (Color)GetValue(ScaleColorProperty); }
        set { SetValue(ScaleColorProperty, value); }
    }

    public static readonly BindableProperty AccentColorProperty = BindableProperty.Create(nameof(AccentColor),
        typeof(Color), typeof(ScaleTicks),
        Colors.Red,
        propertyChanged: NeedUpdate);
    public Color AccentColor
    {
        get { return (Color)GetValue(AccentColorProperty); }
        set { SetValue(AccentColorProperty, value); }
    }

    public static readonly BindableProperty ShowSignProperty =
        BindableProperty.Create(nameof(ShowSign),
            typeof(bool),
            typeof(ScaleTicks),
            false,
            propertyChanged: NeedUpdate);
    public bool ShowSign
    {
        get { return (bool)GetValue(ShowSignProperty); }
        set { SetValue(ShowSignProperty, value); }
    }

    public static readonly BindableProperty IsMirroredProperty =
        BindableProperty.Create(nameof(IsMirrored),
            typeof(bool),
            typeof(ScaleTicks),
            false,
            propertyChanged: NeedUpdate);
    public bool IsMirrored
    {
        get { return (bool)GetValue(IsMirroredProperty); }
        set { SetValue(IsMirroredProperty, value); }
    }


}