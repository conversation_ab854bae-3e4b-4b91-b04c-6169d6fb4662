namespace Racebox.Views.Drawn;

public partial class DrawnFrame
{
    public DrawnFrame()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty CornerRadiusProperty = BindableProperty.Create(
        nameof(CornerRadius),
        typeof(double), typeof(DrawnFrame),
        16.0);
    public double CornerRadius
    {
        get { return (double)GetValue(CornerRadiusProperty); }
        set { SetValue(CornerRadiusProperty, value); }
    }
}