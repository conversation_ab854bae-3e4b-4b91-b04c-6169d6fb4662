﻿namespace Racebox.ApiClient;

public class HttpHandler : DelegatingHandler
{

    public HttpHandler()
    {
    }

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request,
        CancellationToken cancellationToken)
    {


        //if (request.Headers.Authorization?.Scheme != "Bearer")
        //{
        //    var savedToken = await localStorage.GetItemAsync<string>("authToken");

        //    if (!string.IsNullOrWhiteSpace(savedToken))
        //        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", savedToken);
        //}

        return await base.SendAsync(request, cancellationToken);
    }
}