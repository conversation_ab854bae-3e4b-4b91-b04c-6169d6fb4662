using DrawnUi;
using SkiaSharp;

namespace Racebox.Views.Partials;
using DrawnUi.Extensions;

public class SkiaSignalContainer : SkiaLayout
{
    public SkiaSignalContainer()
    {
        IsClippedToBounds = true;
    }


    public override SKPath CreateClip(object arguments, bool usePosition, SKPath path = null)
    {
        path ??= new SKPath();

        var scale = RenderingScale;
        var cornerRadius = (float)(22.0 * scale);
        var area = DrawingRect.Clone();
        var bordersize = (float)(2 * scale);
        area.Inflate(-bordersize, -bordersize);

        path.AddRoundRect(area,
            cornerRadius,
            cornerRadius);

        return path;
    }

}