using Microsoft.VisualStudio.TestTools.UnitTesting;
using TestApi.Services;
using TestApi.Models;
using Newtonsoft.Json;
using System.Text;

namespace TestApi.Tests;

/// <summary>
/// Integration tests that use real test data and may call the actual API
/// These tests are more comprehensive but may fail due to external dependencies
/// </summary>
[TestClass]
public class ChartApiIntegrationTests
{
    private IChartApiService _chartService = null!;
    private ICsvMetadataGenerator _metadataGenerator = null!;
    private string _testOutputDirectory = null!;

    [TestInitialize]
    public void Setup()
    {
        _chartService = new ChartApiService();
        _metadataGenerator = new CsvMetadataGenerator();
        _testOutputDirectory = Path.Combine(Path.GetTempPath(), "RaceboxIntegrationTests", Guid.NewGuid().ToString());
        Directory.CreateDirectory(_testOutputDirectory);
    }

    [TestCleanup]
    public void Cleanup()
    {
        _chartService?.Dispose();
        
        if (Directory.Exists(_testOutputDirectory))
        {
            try
            {
                Directory.Delete(_testOutputDirectory, true);
            }
            catch
            {
                // Ignore cleanup failures
            }
        }
    }

    [TestMethod, Timeout(15000)] // 15 second timeout
    public async Task EndToEnd_CsvToChart_WithRealTestData_Success()
    {
        // Arrange - Load real test data if available
        var testDataPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "TestData");
        var csvPath = Path.Combine(testDataPath, "racebox_log.csv");

        byte[] csvData;
        if (File.Exists(csvPath))
        {
            csvData = await File.ReadAllBytesAsync(csvPath);
        }
        else
        {
            // Fallback to inline test data
            csvData = GetFallbackCsvData();
        }

        // Generate metadata from CSV data
        var csvText = Encoding.UTF8.GetString(csvData);
        var metadata = _metadataGenerator.GenerateFromCsv(csvText, csvData.Length);
        var metadataJson = JsonConvert.SerializeObject(metadata, Formatting.Indented);

        // Act
        var result = await _chartService.GenerateChartAsync(csvData, metadataJson, _testOutputDirectory);

        // Assert
        if (result.IsSuccess)
        {
            // Full end-to-end success
            Assert.IsNotNull(result.FilePath);
            Assert.IsNotNull(result.Filename);
            Assert.IsTrue(result.FileSize > 0);
            Assert.IsNotNull(result.ResponseTime);
            Assert.IsTrue(File.Exists(result.FilePath));
            
            // Verify the image file was created and has reasonable size
            var fileInfo = new FileInfo(result.FilePath);
            Assert.IsTrue(fileInfo.Length > 10000); // At least 10KB for a chart image
            Assert.IsTrue(result.Filename!.EndsWith(".png"));
            
            Console.WriteLine($"✅ Chart generated successfully!");
            Console.WriteLine($"📁 File: {result.Filename}");
            Console.WriteLine($"🖼️  Size: {result.FileSize:N0} bytes");
            Console.WriteLine($"⏱️  Time: {result.ResponseTime?.TotalSeconds:F2}s");
        }
        else
        {
            // Test the error handling behavior
            Assert.IsNotNull(result.ErrorMessage);
            Assert.IsFalse(string.IsNullOrWhiteSpace(result.ErrorMessage));
            
            Console.WriteLine($"❌ Chart generation failed (expected for network issues):");
            Console.WriteLine($"🔍 Error: {result.ErrorMessage}");
            
            // Still validate that common error types are handled correctly
            Assert.IsTrue(
                result.ErrorMessage.Contains("Network error") ||
                result.ErrorMessage.Contains("Request timed out") ||
                result.ErrorMessage.Contains("API Error") ||
                result.ErrorMessage.Contains("Request was cancelled"));
        }
    }

    [TestMethod]
    public void MetadataGeneration_WithRealCsvData_GeneratesValidJson()
    {
        // Arrange
        var testDataPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "TestData");
        var csvPath = Path.Combine(testDataPath, "racebox_log.csv");

        string csvText;
        long fileSize;

        if (File.Exists(csvPath))
        {
            csvText = File.ReadAllText(csvPath);
            fileSize = new FileInfo(csvPath).Length;
        }
        else
        {
            var fallbackData = GetFallbackCsvData();
            csvText = Encoding.UTF8.GetString(fallbackData);
            fileSize = fallbackData.Length;
        }

        // Act
        var metadata = _metadataGenerator.GenerateFromCsv(csvText, fileSize);
        var json = JsonConvert.SerializeObject(metadata, Formatting.Indented);

        // Assert
        Assert.IsNotNull(metadata);
        Assert.AreEqual("app", metadata.DataSource);
        Assert.AreEqual("accel", metadata.Type);
        Assert.AreEqual(fileSize, metadata.Size);
        
        // Verify JSON is valid and contains expected fields
        var deserializedMetadata = JsonConvert.DeserializeObject<ChartMetadata>(json);
        Assert.IsNotNull(deserializedMetadata);
        Assert.AreEqual("app", deserializedMetadata.DataSource);
        
        // Performance metrics should be calculated if data is valid
        if (metadata.Spd.Count > 0)
        {
            Assert.IsTrue(metadata.Spd.ContainsKey("0-60") || metadata.Spd.ContainsKey("0-100"));
        }
        
        Console.WriteLine($"📊 Generated metadata JSON ({json.Length} chars):");
        Console.WriteLine($"🚗 Speed metrics: {metadata.Spd.Count}");
        Console.WriteLine($"📏 Distance metrics: {metadata.Dst.Count}");
        Console.WriteLine($"📈 Range metrics: {metadata.Rng.Count}");
    }

    [TestMethod]
    public void ServiceConfiguration_CrossPlatformCompatibility_ValidatesCorrectly()
    {
        // This test validates that our services are configured for cross-platform use
        
        // Arrange & Act
        using var service = new ChartApiService();
        var generator = new CsvMetadataGenerator();

        // Assert - Services can be instantiated without platform-specific dependencies
        Assert.IsNotNull(service);
        Assert.IsNotNull(generator);
        
        // Verify interfaces are properly implemented
        Assert.IsInstanceOfType(service, typeof(IChartApiService));
        Assert.IsInstanceOfType(generator, typeof(ICsvMetadataGenerator));
        
        // Test basic functionality without network calls
        var testData = GetFallbackCsvData();
        var csvText = Encoding.UTF8.GetString(testData);
        var metadata = generator.GenerateFromCsv(csvText, testData.Length);
        
        Assert.IsNotNull(metadata);
        Assert.AreEqual("app", metadata.DataSource);
        
        Console.WriteLine("✅ Cross-platform service configuration validated");
        Console.WriteLine($"📱 Data source: {metadata.DataSource}");
        Console.WriteLine($"🔧 Service type: {service.GetType().Name}");
    }

    private static byte[] GetFallbackCsvData()
    {
        var csvContent = """
            Total Time (s);Lat;Lon;Speed (km/h);LonAccel (G);Distance (m);Alt (m);Incline (%);Course (deg);HDOP;Sats
            0.18;56.7916267;60.5735093;2.5;0.06;1.9;268.8;0.0;296;0.7;15
            1.02;56.7916367;60.5735193;15.2;0.12;5.8;268.9;0.1;298;0.6;15
            2.45;56.7916467;60.5735293;35.8;0.18;12.4;269.0;0.2;302;0.7;14
            3.34;56.7916567;60.5735393;60.5;0.22;18.5;269.1;0.2;305;0.6;15
            4.89;56.7916667;60.5735493;78.3;0.19;28.7;269.2;0.1;308;0.7;15
            7.25;56.7916767;60.5735593;100.2;0.15;50.1;269.3;0.0;310;0.6;15
            10.44;56.7916867;60.5735693;125.7;0.12;78.9;269.4;-0.1;312;0.7;14
            15.93;56.7916967;60.5735793;150.8;0.08;120.3;269.5;-0.2;315;0.6;15
            22.87;56.7917067;60.5735893;175.4;0.05;168.7;269.6;-0.1;318;0.7;15
            38.31;56.7917167;60.5735993;200.5;0.02;280.3;269.7;0.0;320;0.6;15
            """;
        return Encoding.UTF8.GetBytes(csvContent);
    }
}