﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Racebox.SDK.Strings {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ResStrings {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResStrings() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Racebox.SDK.Strings.ResStrings", typeof(ResStrings).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No permissions to use Bluetooth..
        /// </summary>
        public static string AlertBluetoothPermissionsOff {
            get {
                return ResourceManager.GetString("AlertBluetoothPermissionsOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your device does not support Bluetooth..
        /// </summary>
        public static string AlertBluetoothUnsupported {
            get {
                return ResourceManager.GetString("AlertBluetoothUnsupported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bluetooth will not be available..
        /// </summary>
        public static string AlertBluetoothWillNotBeAvailable {
            get {
                return ResourceManager.GetString("AlertBluetoothWillNotBeAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enable GPS (Android system requirement to use Bluetooth)..
        /// </summary>
        public static string AlertNeedGpsOnForBluetooth {
            get {
                return ResourceManager.GetString("AlertNeedGpsOnForBluetooth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please grant location permissions (Android system requirement to use Bluetooth)..
        /// </summary>
        public static string AlertNeedGpsPermissionsForBluetooth {
            get {
                return ResourceManager.GetString("AlertNeedGpsPermissionsForBluetooth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We would need you to provide permissions to use your GPS location (Android system requirement to use Bluetooth)..
        /// </summary>
        public static string AlertNeedLocationForBluetooth {
            get {
                return ResourceManager.GetString("AlertNeedLocationForBluetooth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please turn on Bluetooth..
        /// </summary>
        public static string AlertTurnOnBluetooth {
            get {
                return ResourceManager.GetString("AlertTurnOnBluetooth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        public static string BtnNo {
            get {
                return ResourceManager.GetString("BtnNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OK.
        /// </summary>
        public static string BtnOk {
            get {
                return ResourceManager.GetString("BtnOk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        public static string BtnYes {
            get {
                return ResourceManager.GetString("BtnYes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Charging.
        /// </summary>
        public static string ChargingStateType_FastCharging {
            get {
                return ResourceManager.GetString("ChargingStateType_FastCharging", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not charging.
        /// </summary>
        public static string ChargingStateType_NotCharging {
            get {
                return ResourceManager.GetString("ChargingStateType_NotCharging", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Charging.
        /// </summary>
        public static string ChargingStateType_Precharge {
            get {
                return ResourceManager.GetString("ChargingStateType_Precharge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Charged.
        /// </summary>
        public static string ChargingStateType_TrickleCharging {
            get {
                return ResourceManager.GetString("ChargingStateType_TrickleCharging", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Every 10 secs.
        /// </summary>
        public static string DeviceLograteType_Every10Sec {
            get {
                return ResourceManager.GetString("DeviceLograteType_Every10Sec", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Every second.
        /// </summary>
        public static string DeviceLograteType_Every1Sec {
            get {
                return ResourceManager.GetString("DeviceLograteType_Every1Sec", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Every minute.
        /// </summary>
        public static string DeviceLograteType_Every60Sec {
            get {
                return ResourceManager.GetString("DeviceLograteType_Every60Sec", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max.
        /// </summary>
        public static string DeviceLograteType_Max {
            get {
                return ResourceManager.GetString("DeviceLograteType_Max", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1000m.
        /// </summary>
        public static string DeviceSettingsMinShowDistance_M1000 {
            get {
                return ResourceManager.GetString("DeviceSettingsMinShowDistance_M1000", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1609m.
        /// </summary>
        public static string DeviceSettingsMinShowDistance_M1609 {
            get {
                return ResourceManager.GetString("DeviceSettingsMinShowDistance_M1609", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 18m.
        /// </summary>
        public static string DeviceSettingsMinShowDistance_M18 {
            get {
                return ResourceManager.GetString("DeviceSettingsMinShowDistance_M18", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 201m.
        /// </summary>
        public static string DeviceSettingsMinShowDistance_M201 {
            get {
                return ResourceManager.GetString("DeviceSettingsMinShowDistance_M201", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 402m.
        /// </summary>
        public static string DeviceSettingsMinShowDistance_M402 {
            get {
                return ResourceManager.GetString("DeviceSettingsMinShowDistance_M402", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Automatic.
        /// </summary>
        public static string DeviceSettingsTransmission_Auto {
            get {
                return ResourceManager.GetString("DeviceSettingsTransmission_Auto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manual.
        /// </summary>
        public static string DeviceSettingsTransmission_Manual {
            get {
                return ResourceManager.GetString("DeviceSettingsTransmission_Manual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Robot.
        /// </summary>
        public static string DeviceSettingsTransmission_Robot {
            get {
                return ResourceManager.GetString("DeviceSettingsTransmission_Robot", resourceCulture);
            }
        }
    }
}
