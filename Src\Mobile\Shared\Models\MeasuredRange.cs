﻿using Racebox.Shared.Enums;
using Racebox.Shared.Interfaces;
using Racebox.Shared.Services;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics;

namespace Racebox.Shared.Models;

[DebuggerDisplay("{Start:0.00}-{End:0.00} time {Time:HH':'mm':'ss':'ff}")]
public class MeasuredRange : ValuesRange, IHasDetailedDisplay, IHasSpeech
{
    #region FK
    public int MeasureResultId { get; set; }

    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]

    public MeasureResult MeasureResult { get; set; }

    #endregion

    [NotMapped]
    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]
    public bool WasShown { get; set; }

    public bool IsUserDefined { get; set; }
    public static MeasuredRange FromRange(ValuesRange range)
    {
        return new MeasuredRange(range.Start, range.End);
    }
    public TimeSpan? Time { get; set; }
    public double StartedAtMs { get; set; }

    public MeasuredRange(double start, double end) : base(start, end)
    {
    }
    public MeasuredRange()
    {
    }

    public OptionsUnits Units { get; set; }

    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]
    public string SpeedDisplay
    {
        get
        {
            return LocalizedDisplayProvider.Instance.GetAddSpeedDisplay(Units);
        }
    }

    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]
    public string TimeDisplay
    {
        get
        {
            return LocalizedDisplayProvider.Instance.GetNullableTimeDisplay(Time);
        }
    }

    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]

    public string Display
    {
        get
        {
            return $"{Start:0}-{End:0}{SpeedDisplay}: {TimeDisplay}";
        }
    }


    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]

    public string Display1
    {
        get
        {
            return $"{Start:0}-{End:0}{SpeedDisplay}";
        }
    }

    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]

    public string Display2
    {
        get
        {
            return $"{TimeDisplay}";
        }
    }

    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]

    public string Say
    {
        get
        {
            return $"{End:0}";
            //return $"{End:0}{SpeedDisplay} - {LocalizedDisplayProvider.Instance.GetNullableTimeDisplay(Time, false)}";
        }
    }


}