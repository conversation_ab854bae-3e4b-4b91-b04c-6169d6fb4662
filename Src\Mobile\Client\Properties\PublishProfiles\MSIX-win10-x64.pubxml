﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PublishDir>bin\Release\net8.0-ios\iossimulator-x64\publish\</PublishDir>
    <PublishProtocol>FileSystem</PublishProtocol>
    <RuntimeIdentifier>win10-x64</RuntimeIdentifier>
    <Platform>Any CPU</Platform>
    <Configuration>Release</Configuration>
    <TargetFramework>net8.0-windows10.0.19041.0</TargetFramework>
    <PublishSingleFile>false</PublishSingleFile>
    <PublishReadyToRun>false</PublishReadyToRun>
    <SelfContained>True</SelfContained>
    <PublishAppxPackage>true</PublishAppxPackage>
    <AppxPackageDir>bin\Release\net8.0-windows10.0.19041.0\win10-x64\AppPackages\</AppxPackageDir>
  </PropertyGroup>
</Project>