# Racebox Companion App


## UI/UIX Design

Initial:
https://www.figma.com/file/rSmH2csR1w178KQMW5JxhX/Racebox-App-(Copy)?node-id=0%3A1&t=3LJwlEwjqeywzMyd-0

Latest:
https://www.figma.com/file/DUsv9rwyh49vwKTe2AzTj3/Racebox-App?node-id=0%3A1&t=b4nPh23obM6WYaUF-0

## Development Notes

### Known Bugs

* _WIndows_ connector cannot connect again after being disconnected.

### Publish for iPhone:

```bash
dotnet publish -f net8.0-ios -c Release -p:ArchiveOnBuild=true -p:RuntimeIdentifier=ios-arm64 -p:CodesignKey="Apple Distribution: <PERSON> (YUWVSRRN2T)" -p:CodesignProvision="AppStore Nick Racebox"
```

### Publish for Windows:

```bash
dotnet publish -f net8.0-windows10.0.19041.0 -c Release /p:RuntimeIdentifierOverride=win10-x64 /p:PublishReadyToRunShowWarnings=true
```
 
### If you change DB structure

Inside Visual Studio for Windows open Package Management Console:  
1. set startup project to `Racebox.Migrator`
2. set default project to `Racebox.Shared`
3. type and submit, replacing `MigrationName` with your own random name:
`add-migration MigrationName -Context Racebox.Shared.Services.LocalDatabase -Verbose`  
 
