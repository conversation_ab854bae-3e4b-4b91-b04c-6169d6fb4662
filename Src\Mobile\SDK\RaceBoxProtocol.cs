﻿using System.Collections;
using System.Diagnostics;
using System.Runtime.CompilerServices;

namespace Racebox.SDK;

public static class RaceBoxProtocol
{

    static RaceBoxProtocol()
    {

    }


    //public static string Test()
    //{


    //    var payload = "81649c66 0fc86595 e0514b21 d7125a16 38014f1a";

    //    var bytes = HexBytesToBytes(payload);

    //    var state = ToSystemState(bytes);

    //    var check = state.Month;

    //    return check.ToString();

    //}

    public const byte CONF_MSG_MARKER = 0x20;

    public static void DecodeTo(this byte[] bytes, RaceBoxState target)
    {
        void Crash()
        {
            throw new ApplicationException($"Data is corrupt");
        }

        if (bytes.Length != 20)
        {
            throw new ApplicationException($"Wrong size of data, excpecting 20 but received {bytes.Length}");
        }
        //Debug.WriteLine("**************** DecodeTo:");
        //Debug.WriteLine(Convert.ToBase64String(bytes));

        //bytes 0-1
        var wordStart = (ushort)bytes.BytesToUint(0);

        //year
        var bitsYear = wordStart.WordBitsNumbersToInteger(0, 5);
        target.Year = bitsYear == 0b11111 ? null : 2018 + bitsYear;

        //month
        var bitsMonth = wordStart.WordBitsNumbersToInteger(5, 4);
        target.Month = bitsMonth == 0b1111 ? null : bitsMonth;

        //hdop
        var bitsHdop = wordStart.WordBitsNumbersToInteger(9, 7);
        target.HDOP = bitsHdop == 0b1111111 ? null : bitsHdop / 10.0; ;

        //bytes 2-3
        var wordTime = (ushort)bytes.BytesToUint(2);

        //day
        var bitsDay = wordTime.WordBitsNumbersToInteger(0, 5);
        target.Day = bitsDay == 0b11111 ? null : bitsDay;

        //hours
        var bitsHour = wordTime.WordBitsNumbersToInteger(5, 5);
        target.Hours = bitsHour == 0b11111 ? null : bitsHour;

        //mins
        var bitsMinute = wordTime.WordBitsNumbersToInteger(10, 6);
        target.Minutes = bitsMinute == 0b111111 ? null : bitsMinute;

        //bytes 4-5
        var wordSecs = (ushort)bytes.BytesToUint(4);

        //secs
        var bitsSecs = wordSecs.WordBitsNumbersToInteger(0, 6);
        target.Seconds = bitsSecs == 0b111111 ? null : bitsSecs;

        //ms
        var bitsMs = wordSecs.WordBitsNumbersToInteger(6, 10);
        target.Milliseconds = bitsMs == 0b1111111111 ? null : bitsMs;

        //bytes 6-7
        var wordNavi = (ushort)bytes.BytesToUint(6);

        //satellites
        var bitsGnss = wordNavi.WordBitsNumbersToInteger(0, 4);
        target.SatellitesCount = bitsGnss;

        //heading
        var bitsHeading = wordNavi.WordBitsNumbersToInteger(4, 12);
        target.Heading = bitsHeading == 4095 ? null : bitsHeading / 10.0;

        var doubleWord = new byte[4];

        //lattitude
        Array.Copy(bytes, 8, doubleWord, 0, 4);
        var lattitude = BitConverter.ToInt32(doubleWord, 0);
        target.Latitude = lattitude == -1 ? null : lattitude / 10000000.0;

        //lattitude
        Array.Copy(bytes, 12, doubleWord, 0, 4);

        var longitude = BitConverter.ToInt32(doubleWord, 0);
        target.Longitude = longitude == -1 ? null : longitude / 10000000.0;

        var singleWord = new byte[2];

        //speed
        Array.Copy(bytes, 16, singleWord, 0, 2);
        var speed = (ushort)BitConverter.ToInt16(singleWord, 0);
        //System.Diagnostics.Debug.WriteLine($"SPEED: {speed.ToBinary()}");
        target.Speed = speed == 0xFFFF ? null : speed / 100.0;

        //altitude
        Array.Copy(bytes, 18, singleWord, 0, 2);
        var altitude = (ushort)BitConverter.ToInt16(singleWord, 0);
        target.Altitude = altitude == 0xFFFF ? null : altitude / 10.0 - 500;


        // if (target.Year == null || target.Month == null || target.Day == null)
        //     Crash();

        try
        {
            target.TimeStampUtc = new DateTime(
                target.Year.GetValueOrDefault(),
                target.Month.GetValueOrDefault(),
                target.Day.GetValueOrDefault(),
                target.Hours.GetValueOrDefault(),
                target.Minutes.GetValueOrDefault(),
                target.Seconds.GetValueOrDefault(),
                target.Milliseconds.GetValueOrDefault(),
                DateTimeKind.Utc);
        }
        catch (Exception e)
        {
            target.TimeStampUtc = DateTime.UtcNow;
        }


    }

    public static void DecodeTo(this byte[] bytes, RaceBoxExtendedState target)
    {
        //Debug.WriteLine("**************** MORE DecodeTo:");
        //Debug.WriteLine(Convert.ToBase64String(bytes));

        if (bytes[0] == 0)
        {
            var firstByte = (ushort)bytes.BytesToUint(1);

            target.BatteryChargingState = (ChargingStateType)firstByte.WordBitsNumbersToInteger(0, 2);

            var battery = firstByte.WordBitsNumbersToInteger(2, 5);
            if (battery != 0x1F)
            {
                target.BatteryLevel = battery * 5;
            }

            //bytes 1-2
            var word1 = (ushort)bytes.BytesToUint(1);

            target.DeviceType = (ModelType)word1.WordBitsNumbersToInteger(7, 2);

            target.VersionMajor = word1.WordBitsNumbersToInteger(9, 4); ;

            //bytes 2-3
            var word2 = (ushort)bytes.BytesToUint(2);

            var v1 = word2.WordBitsNumbersToInteger(5, 4); ;
            var v2 = word2.WordBitsNumbersToInteger(9, 4); ;

            target.VersionMinor = v1 * 10 + v2;
        }
        else
        {
            throw new ApplicationException("Data is not extended state");
        }
    }

    public static void DecodeTo(this byte[] bytes, RaceBoxSettingsState target)
    {
        Debug.WriteLine("**************** SETTS Decoded from:");
        Debug.WriteLine(Convert.ToBase64String(bytes));

        if (bytes[0] == CONF_MSG_MARKER)
        {
            //1-2
            var _word = (ushort)bytes.BytesToUint(1);

            target.Led = _word.WordBitsNumbersToInteger(0, 2) + 1;
            target.Tone = _word.WordBitsNumbersToInteger(2, 1) == 1;
            target.Transmission = (DeviceSettingsTransmission)_word.WordBitsNumbersToInteger(3, 2);
            target.Utc = _word.WordBitsNumbersToInteger(5, 5) - 12;

            //2-3
            _word = (ushort)bytes.BytesToUint(2);

            target.Speed1 = _word.WordBitsNumbersToInteger(2, 9);

            // 3-4
            _word = (ushort)bytes.BytesToUint(3);

            target.Speed2 = _word.WordBitsNumbersToInteger(3, 9);

            target.LogFormat = (DeviceSettingsLogFormat)_word.WordBitsNumbersToInteger(12, 2);

            target.LogRate = (DeviceLograteType)_word.WordBitsNumbersToInteger(14, 2);

            // 5-6
            _word = (ushort)bytes.BytesToUint(5);

            target.Language = (DeviceSettingsLanguage)_word.WordBitsNumbersToInteger(0, 1);
            target.Maps = (DeviceSettingsMapsType)_word.WordBitsNumbersToInteger(1, 1);
            target.Rollout = _word.WordBitsNumbersToInteger(2, 1) == 1;
            target.Prediction = _word.WordBitsNumbersToInteger(3, 1) == 1;
            target.RandomStart = _word.WordBitsNumbersToInteger(4, 1) == 1;
            target.Units = (DeviceSettingsMetricsType)_word.WordBitsNumbersToInteger(5, 1);
            target.MinDistance = (DeviceSettingsMinShowDistance)_word.WordBitsNumbersToInteger(6, 3);
            target.Weight = _word.WordBitsNumbersToInteger(9, 7) * 50;

            // 7-8
            _word = (ushort)bytes.BytesToUint(7);

            target.Drag = _word.WordBitsNumbersToInteger(0, 6) / 100.0;

            target.Frontal = _word.WordBitsNumbersToInteger(6, 6) / 10.0;

            target.ObdLog = _word.WordBitsNumbersToInteger(12, 1) == 1;
            target.ShiftTime = _word.WordBitsNumbersToInteger(13, 1) == 1;
            target.ObdPidAuto = _word.WordBitsNumbersToInteger(14, 1) == 1;

            // 8-9
            _word = (ushort)bytes.BytesToUint(8);

            target.ObdPids = _word.WordBitsNumbersToInteger(7, 6);
            target.ObdProtocol = (DeviceSettingsObdProtocolType)_word.WordBitsNumbersToInteger(13, 3);

            // 10
            var lastByte = (ushort)bytes.BytesToUint(10);

            target.GnssSecondarySource = lastByte.WordBitsNumbersToInteger(0, 1);
            target.GnssUartOut = lastByte.WordBitsNumbersToInteger(1, 1) == 1;

            target.GnssColdStart = false;// lastByte.WordBitsNumbersToInteger(2, 1) == 1;
            target.GnssReconfig = false;//lastByte.WordBitsNumbersToInteger(3, 1) == 1;

            target.ResetConfiguration = false;// lastByte.WordBitsNumbersToInteger(4, 1) == 1;
            target.ResetDevice = false;// lastByte.WordBitsNumbersToInteger(5, 1) == 1;
        }
        else
        {
            throw new ApplicationException("Data is not settings");
        }

    }

    public static byte[] Encode(this RaceBoxSettingsState config)
    {
        byte[] encoded = new byte[11];// { 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF };

        //0
        encoded[0] = CONF_MSG_MARKER;

        //1
        byte led_mask = (byte)((byte)(config.Led - 1) & 0b11);
        byte tone_mask = (byte)((byte)(config.Tone ? 1 : 0) << 2);
        byte trans_mask = (byte)(((int)config.Transmission & 0b11) << 3);
        byte utc_mask_1 = (byte)(((config.Utc + 12) & 0b111) << 5);
        encoded[1] = (byte)(led_mask | tone_mask | trans_mask | utc_mask_1);

        //2
        byte utc_mask_2 = (byte)(((config.Utc + 12) & 0b11000) >> 3);
        byte speed1_mask_1 = (byte)((config.Speed1 & 0b111111) << 2);
        encoded[2] = (byte)(utc_mask_2 | speed1_mask_1);

        //3
        byte speed1_mask_2 = (byte)((config.Speed1 & 0b111000000) >> 6);
        byte speed2_mask_1 = (byte)((config.Speed2 & 0b11111) << 3);
        encoded[3] = (byte)(speed1_mask_2 | speed2_mask_1);

        //4
        byte speed2_mask_2 = (byte)((config.Speed2 & 0b111100000) >> 5);
        byte log_fmt_mask = (byte)(((int)config.LogFormat & 0b11) << 4);
        byte log_rate_mask = (byte)(((int)config.LogRate & 0b11) << 6);
        encoded[4] = (byte)(speed2_mask_2 | log_fmt_mask | log_rate_mask);

        //5
        byte rep_lang_mask = (byte)((int)config.Language & 0b1);
        byte maps_mask = (byte)(((int)config.Maps & 0b1) << 1);
        byte rollout_mask = (byte)(((config.Rollout ? 1 : 0) & 0b1) << 2);
        byte predict_mask = (byte)(((config.Prediction ? 1 : 0) & 0b1) << 3);
        byte random_mask = (byte)(((config.RandomStart ? 1 : 0) & 0b1) << 4);
        byte units_mask = (byte)(((int)config.Units & 0b1) << 5);
        byte dist_mask_1 = (byte)(((int)config.MinDistance & 0b11) << 6);
        encoded[5] = (byte)(rep_lang_mask | maps_mask | rollout_mask | predict_mask | random_mask | units_mask | dist_mask_1);

        //6
        byte dist_mask_2 = (byte)(((int)config.MinDistance & 0b100) >> 2);
        byte weight_mask = (byte)(config.Weight / 50 << 1);
        encoded[6] = (byte)(dist_mask_2 | weight_mask);

        //7
        byte drag_mask = (byte)((int)(config.Drag * 100.0) & 0b111111);
        byte front_mask_1 = (byte)(((int)(config.Frontal * 10.0) & 0b11) << 6);
        encoded[7] = (byte)(drag_mask | front_mask_1);

        //8
        byte front_mask_2 = (byte)(((int)(config.Frontal * 10.0) & 0b111100) >> 2);
        byte obd_log_mask = (byte)(((config.ObdLog ? 1 : 0) & 0b1) << 4);
        byte shift_mask = (byte)(((config.ShiftTime ? 1 : 0) & 0b1) << 5);
        byte obd_pid_det_mask = (byte)(((config.ObdPidAuto ? 1 : 0) & 0b1) << 6);
        byte obd_pid_mask_1 = (byte)((config.ObdPids & 0b1) << 7);
        encoded[8] = (byte)(front_mask_2 | obd_log_mask | shift_mask | obd_pid_det_mask | obd_pid_mask_1);

        //9
        byte obd_pid_mask_2 = (byte)((config.ObdPids & 0b111110) >> 1);
        byte obd_prot_mask = (byte)(((int)config.ObdProtocol & 0b111) << 5);
        encoded[9] = (byte)(obd_pid_mask_2 | obd_prot_mask);

        //10
        byte gnss_src_mask = (byte)(config.GnssSecondarySource & 0b1);
        byte gnss_uart_mask = (byte)(((config.GnssUartOut ? 1 : 0) & 0b1) << 1);

        byte cmd_gnss_cold_mask = (byte)((config.GnssColdStart ? 1 : 0) << 2);
        byte cmd_defaults_mask = (byte)((config.ResetConfiguration ? 1 : 0) << 3);
        byte cmd_reset_mask = (byte)((config.ResetDevice ? 1 : 0) << 4);
        //byte cmd_gnss_reconfig_mask = (byte)((config.GnssReconfig ? 1 : 0) << 5);

        encoded[10] = (byte)(gnss_src_mask | gnss_uart_mask | cmd_gnss_cold_mask | cmd_defaults_mask | cmd_reset_mask);//|  cmd_gnss_reconfig_mask

        Debug.WriteLine("**************** SETTS Encoded to:");
        Debug.WriteLine(Convert.ToBase64String(encoded));

        return encoded;
    }

    #region HELPERS

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static void SetBits(ref ushort data, int startBit, int len, int value)
    {
        int mask = (1 << len) - 1;
        data = (ushort)((data & ~(mask << startBit)) | ((value & mask) << startBit));
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static byte[] ToBytes(ushort value)
    {
        byte[] bytes = new byte[2];
        bytes[0] = (byte)(value >> 8);
        bytes[1] = (byte)(value & 0xFF);
        return bytes;
    }

    public static ushort SetWordBitsNumbersFromInteger(this ushort value, int integer, int number, int len)
    {
        int start = WordBitNbToPos(number) - len + 1;
        int mask = (1 << len) - 1;
        value = (ushort)((value & ~(mask << start)) | ((integer & mask) << start));
        return value;
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static int BitsToInteger(this byte value, int number, int len)
    {
        var start = WordBitNbToPos(number) - len;
        return WordBitsToInteger(value, start, len);
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static bool IsBitSet(byte b, int pos)
    {
        return (b & (1 << pos)) != 0;
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static bool IsBitSet(ushort value, int pos)
    {
        return (value & (1 << pos)) != 0;
    }

    /// <summary>
    /// Parsing bytes one by one,  ex: "FF56A0" to 0xFF 0x56 0xA0
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public static byte[] HexBytesToBytes(string input)
    {
        if (string.IsNullOrEmpty(input))
            return null;

        input = input.Replace(" ", string.Empty);

        var isEven = ((int)input.Length / 2) * 2 == input.Length / 2.0;

        if (input.Length % 2 != 0)
            return null;

        var output = new List<byte>();

        try
        {
            for (int i = 0; i < input.Length - 1; i += 2)
            {
                var hexValue = input.Substring(i, 2);

                var value = int.Parse(hexValue, System.Globalization.NumberStyles.HexNumber);

                output.Add(Convert.ToByte(input.Substring(i, 2), 16));
            }

            return output.ToArray();

        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            return null;
        }

    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static int WordBitNbToPos(int number)
    {
        return 15 - number;
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static int WordBitsNumbersToInteger(this ushort v, int number, int len)
    {
        var start = WordBitNbToPos(number) - len;
        return WordBitsToInteger(v, start, len);
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static int WordBitsToInteger(this ushort v, int start, int len)
    {
        //System.Diagnostics.Debug.WriteLine($"WordBitsToInteger: {v.ToBinary()}");

        var shift = 16 - (start + 1 + len);
        var max = (int)Math.Pow(2, len) - 1;
        var ret = (v >> shift) & max;

        return ret;
    }

    public static string ToBinary(this int myValue)
    {
        string binVal = Convert.ToString(myValue, 2);
        int bits = 0;
        int bitblock = 4;

        for (int i = 0; i < binVal.Length; i = i + bitblock)
        { bits += bitblock; }

        return binVal.PadLeft(bits, '0');
    }

    public static string ToBinary(this ushort myValue)
    {
        string binVal = Convert.ToString(myValue, 2);
        int bits = 0;
        int bitblock = 4;

        for (int i = 0; i < binVal.Length; i = i + bitblock)
        { bits += bitblock; }

        return binVal.PadLeft(bits, '0');
    }

    public static string ToBinary(this byte myValue)
    {
        string binVal = Convert.ToString(myValue, 2);
        int bits = 0;
        int bitblock = 4;

        for (int i = 0; i < binVal.Length; i = i + bitblock)
        { bits += bitblock; }

        return binVal.PadLeft(bits, '0');
    }

    public static string ToBinaryString(this int num)
    {
        return Convert.ToString((uint)num, 2).PadLeft(32, '0');
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="bytes"></param>
    /// <param name="start"></param>
    /// <returns></returns>
    public static int BytesToUintBE(this byte[] bytes, int start)
    {
        var hi = bytes[start];
        var lo = bytes[start + 1];

        var ret = hi * 256 + lo;

        return ret;
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static int BytesToUint(this byte[] bytes, int start)
    {
        byte hi = 0;

        if (bytes.Length > start + 1)
            hi = bytes[start + 1];

        var lo = bytes[start];

        var ret = hi * 256 + lo;

        return ret;
    }



    public static RaceBoxState ToSystemState(this byte[] bytes)
    {
        var infoSystem = new RaceBoxState();
        bytes.DecodeTo(infoSystem);
        return infoSystem;
    }


    #endregion

    public class RaceBoxCommand
    {
        public string Name { get; set; }
        public string Code { get; set; }
        public string Parameters { get; set; }

        public bool HasInput
        {
            get
            {
                if (Code != null && Code.Contains("XX"))
                    return true;
                return false;
            }
        }
    }

}
