﻿using AppoMobi.Maui.BLE.Connector;
using AppoMobi.Maui.BLE.EventArgs;
using System.Collections.ObjectModel;

namespace Racebox.SDK;

public interface IRaceBoxConnector
{
    bool Initialized { get; }

    bool IsConnected { get; }

    bool IsBusy { get; }

    public string Status { get; }

    /// <summary>
    /// Last connected device name
    /// </summary>
    public string LastName { get; }

    /// <summary>
    /// Last connected device serial
    /// </summary>
    string LastSerial { get; }

    /// <summary>
    /// Used to connect to the device
    /// </summary>
    string Serial { get; set; }

    /// <summary>
    /// Uses Serial property
    /// </summary>
    /// <param name="needThrow"></param>
    /// <returns></returns>
    Task ConnectToSerialDevice(bool needThrow = false);

    Task Disconnect();

    Task<bool> ScanForCompatibleDevices();

    void Init(Page mainPage, string appTitile);

    public event EventHandler<BluetoothStateChangedArgs> StateChanged;

    public event EventHandler<bool> DeviceConnectionChanged;

    //public event EventHandler ConnectionChanged;

    public event EventHandler<RaceBoxState> OnDecoded;

    public event EventHandler<RaceBoxExtendedState> OnDecodedExtended;

    public event EventHandler<RaceBoxSettingsState> OnDecodedSettings;

    Task<bool> WriteSettings(RaceBoxSettingsState settings);

    Task<bool> RequestExtendedInfo();

    Task<bool> RequestSettings(Action<RaceBoxSettingsState> callback = null);

    public ObservableCollection<BleDeviceViewModel> FoundDevices { get; }
}