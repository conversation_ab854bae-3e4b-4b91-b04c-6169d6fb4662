﻿
namespace Racebox.Extensions
{
    public static class AppExtensions
    {

        public static string GenerateId(this Guid This, int cut = 6, string pfx = "id")
        {
            return pfx + This.ToString().Replace("-", "").Left(cut);
        }

        public static DateTime SetKindUtc(this DateTime dateTime)
        {
            if (dateTime.Kind == DateTimeKind.Utc) { return dateTime; }
            return DateTime.SpecifyKind(dateTime, DateTimeKind.Utc);
        }

        public static async Task<T> RunTask<T>(Task<T> task, int timeout = 0, CancellationToken cancellationToken = default)
        {
            await RunTask((Task)task, timeout, cancellationToken);
            return await task;
        }

        public static async Task RunTask(Task task, int timeout = 0, CancellationToken cancellationToken = default)
        {
            if (timeout == 0) timeout = -1;

            var timeoutTask = Task.Delay(timeout, cancellationToken);
            await Task.WhenAny(task, timeoutTask);

            if (cancellationToken.IsCancellationRequested || timeoutTask.IsCompleted)
            {
                throw new TimeoutException();
            };

            if (!task.IsCanceled && !task.IsCompleted)
                await task;
        }





    }



}
