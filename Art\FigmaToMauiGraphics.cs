
// View:     frameView
// NodeName: Setting Screen
// NodeType: FRAME
// NodeId:   9:734
canvas.SaveState();
canvas.RestoreState();


// View:     frameView1
// NodeName: iPhone 11 Pro
// NodeType: COMPONENT
// NodeId:   9:658
canvas.SaveState();
canvas.RestoreState();


// View:     frameView2
// NodeName: iPhone 11 Pro
// NodeType: INSTANCE
// NodeId:   1:6
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(52, 60, 69, 255)) ,new PaintGradientStop(1, new Color(17, 22, 29, 255)) ,}}, new RectF(1594f, -458f, 375f, 812f));
canvas.FillRoundedRectangle(1594f, -458f, 375f, 812f, 55f);
canvas.RestoreState();


// View:     rectangleView
// NodeName: Results screen
// NodeType: RECTANGLE
// NodeId:   9:672
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(35, 35, 35, 255)) ,new PaintGradientStop(1, new Color(0, 0, 0, 255)) ,}}, new RectF(1620f, -398f, 324f, 647f));
canvas.FillRoundedRectangle(1620f, -398f, 324f, 647f, 25f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(21, 25, 30, 255)) ,new PaintGradientStop(1, new Color(66, 70, 75, 255)) ,}}, new RectF(1620f, -398f, 324f, 647f));
canvas.StrokeSize  = 2;
canvas.DrawRoundedRectangle(1620f, -398f, 324f, 647f, 25f);
canvas.RestoreState();


// View:     frameView3
// NodeName: Group 17
// NodeType: GROUP
// NodeId:   9:721
canvas.SaveState();
canvas.RestoreState();


// View:     textView
// NodeName: Настройки Racebox
// NodeType: TEXT
// NodeId:   9:722
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Настройки Racebox", 1640f, 78f, 245f, 25f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     lineView
// NodeName: Line 1
// NodeType: LINE
// NodeId:   9:724
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(1639.999, 111.46001), new Point(1922.726, 111.46001));
canvas.RestoreState();


// View:     frameView4
// NodeName: navigate_next_FILL0_wght400_GRAD0_opsz48 1
// NodeType: FRAME
// NodeId:   16:887
canvas.SaveState();
canvas.RestoreState();


// View:     imageView
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   16:888
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(1907.4125f, 82.18125f);
var vector0Builder = new PathBuilder();
var vector0path = vector0Builder.BuildPath("M1.47812 16.5688L0 15.0906L6.80625 8.28438L0 1.47813L1.47812 0L9.7625 8.28438L1.47812 16.5688Z");
canvas.FillPath(vector0path);
canvas.RestoreState();


// View:     frameView5
// NodeName: Group 16
// NodeType: GROUP
// NodeId:   9:717
canvas.SaveState();
canvas.RestoreState();


// View:     textView1
// NodeName: Пользователь
// NodeType: TEXT
// NodeId:   9:718
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Пользователь", 1640f, 35f, 202f, 25f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView2
// NodeName: Дмитрий
// NodeType: TEXT
// NodeId:   9:719
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Дмитрий", 1718f, 35f, 202f, 25f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     lineView1
// NodeName: Line 1
// NodeType: LINE
// NodeId:   9:720
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(1639.999, 68.46001), new Point(1922.726, 68.46001));
canvas.RestoreState();


// View:     frameView6
// NodeName: Group 18
// NodeType: GROUP
// NodeId:   10:810
canvas.SaveState();
canvas.RestoreState();


// View:     textView3
// NodeName: Версия
// NodeType: TEXT
// NodeId:   10:811
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Версия", 1640f, 121f, 202f, 25f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView4
// NodeName: 0.0.1
// NodeType: TEXT
// NodeId:   10:812
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"0.0.1", 1718f, 121f, 202f, 25f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     lineView2
// NodeName: Line 1
// NodeType: LINE
// NodeId:   10:813
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(1639.999, 154.46), new Point(1922.726, 154.46));
canvas.RestoreState();


// View:     frameView7
// NodeName: Group 14
// NodeType: GROUP
// NodeId:   9:709
canvas.SaveState();
canvas.RestoreState();


// View:     textView5
// NodeName: Автомобиль
// NodeType: TEXT
// NodeId:   9:710
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Автомобиль", 1640f, -6f, 202f, 25f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView6
// NodeName: Ford Focus 2
// NodeType: TEXT
// NodeId:   9:711
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Ford Focus 2", 1718f, -6f, 202f, 25f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     lineView3
// NodeName: Line 1
// NodeType: LINE
// NodeId:   9:712
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(1639.999, 27.460007), new Point(1922.726, 27.460007));
canvas.RestoreState();


// View:     frameView8
// NodeName: User Speed
// NodeType: GROUP
// NodeId:   9:732
canvas.SaveState();
canvas.RestoreState();


// View:     lineView4
// NodeName: Line 1
// NodeType: LINE
// NodeId:   9:730
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(1639.999, -13), new Point(1922.726, -13));
canvas.RestoreState();


// View:     textView7
// NodeName: 0 - 120 км/ч
// NodeType: TEXT
// NodeId:   9:706
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"0 - 120 км/ч", 1655f, -89f, 108f, 25f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView9
// NodeName: Group 23
// NodeType: GROUP
// NodeId:   10:809
canvas.SaveState();
canvas.RestoreState();


// View:     textView8
// NodeName: Добавить
// NodeType: TEXT
// NodeId:   9:729
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Добавить", 1653f, -54f, 86.90625f, 25f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     rectangleView1
// NodeName: Rectangle 13
// NodeType: RECTANGLE
// NodeId:   10:808
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(1641f, -59f, 110f, 34f, 35f);
canvas.StrokeColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.StrokeSize  = 1;
canvas.DrawRoundedRectangle(1641f, -59f, 110f, 34f, 35f);
canvas.RestoreState();


// View:     textView9
// NodeName: 100 - 200 км/ч
// NodeType: TEXT
// NodeId:   9:697
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"100 - 200 км/ч", 1655f, -157f, 131f, 25f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView10
// NodeName: 80 - 120 км/ч
// NodeType: TEXT
// NodeId:   9:694
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"80 - 120 км/ч", 1655f, -191f, 120f, 25f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView11
// NodeName: 0 - 40 км/ч
// NodeType: TEXT
// NodeId:   9:704
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"0 - 40 км/ч", 1655f, -123f, 108f, 25f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView10
// NodeName: Group 10
// NodeType: GROUP
// NodeId:   9:700
canvas.SaveState();
canvas.RestoreState();


// View:     textView12
// NodeName: Пользовательские метрики:
// NodeType: TEXT
// NodeId:   9:701
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Пользовательские метрики:", 1640f, -225f, 282f, 25f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView11
// NodeName: delete_FILL0_wght400_GRAD0_opsz48 1
// NodeType: FRAME
// NodeId:   16:889
canvas.SaveState();
canvas.RestoreState();


// View:     imageView1
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   16:890
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(1901f, -187f);
var vector1Builder = new PathBuilder();
var vector1path = vector1Builder.BuildPath("M2.31458 16.5C1.93264 16.5 1.60799 16.3663 1.34063 16.099C1.07326 15.8316 0.939583 15.5069 0.939583 15.125L0.939583 2.0625L0 2.0625L0 0.6875L4.30833 0.6875L4.30833 0L10.3583 0L10.3583 0.6875L14.6667 0.6875L14.6667 2.0625L13.7271 2.0625L13.7271 15.125C13.7271 15.4917 13.5896 15.8125 13.3146 16.0875C13.0396 16.3625 12.7188 16.5 12.3521 16.5L2.31458 16.5ZM12.3521 2.0625L2.31458 2.0625L2.31458 15.125L12.3521 15.125L12.3521 2.0625ZM4.74375 13.1542L6.11875 13.1542L6.11875 4.01042L4.74375 4.01042L4.74375 13.1542ZM8.54792 13.1542L9.92292 13.1542L9.92292 4.01042L8.54792 4.01042L8.54792 13.1542Z");
canvas.FillPath(vector1path);
canvas.RestoreState();


// View:     frameView12
// NodeName: delete_FILL0_wght400_GRAD0_opsz48 2
// NodeType: FRAME
// NodeId:   16:891
canvas.SaveState();
canvas.RestoreState();


// View:     imageView2
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   16:892
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(1900.6666f, -153.25f);
var vector2Builder = new PathBuilder();
var vector2path = vector2Builder.BuildPath("M2.31458 16.5C1.93264 16.5 1.60799 16.3663 1.34063 16.099C1.07326 15.8316 0.939583 15.5069 0.939583 15.125L0.939583 2.0625L0 2.0625L0 0.6875L4.30833 0.6875L4.30833 0L10.3583 0L10.3583 0.6875L14.6667 0.6875L14.6667 2.0625L13.7271 2.0625L13.7271 15.125C13.7271 15.4917 13.5896 15.8125 13.3146 16.0875C13.0396 16.3625 12.7188 16.5 12.3521 16.5L2.31458 16.5ZM12.3521 2.0625L2.31458 2.0625L2.31458 15.125L12.3521 15.125L12.3521 2.0625ZM4.74375 13.1542L6.11875 13.1542L6.11875 4.01042L4.74375 4.01042L4.74375 13.1542ZM8.54792 13.1542L9.92292 13.1542L9.92292 4.01042L8.54792 4.01042L8.54792 13.1542ZM2.31458 2.0625L2.31458 15.125L2.31458 2.0625Z");
canvas.FillPath(vector2path);
canvas.RestoreState();


// View:     frameView13
// NodeName: delete_FILL0_wght400_GRAD0_opsz48 3
// NodeType: FRAME
// NodeId:   16:893
canvas.SaveState();
canvas.RestoreState();


// View:     imageView3
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   16:894
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(1900.6666f, -119.25f);
var vector3Builder = new PathBuilder();
var vector3path = vector3Builder.BuildPath("M2.31458 16.5C1.93264 16.5 1.60799 16.3663 1.34063 16.099C1.07326 15.8316 0.939583 15.5069 0.939583 15.125L0.939583 2.0625L0 2.0625L0 0.6875L4.30833 0.6875L4.30833 0L10.3583 0L10.3583 0.6875L14.6667 0.6875L14.6667 2.0625L13.7271 2.0625L13.7271 15.125C13.7271 15.4917 13.5896 15.8125 13.3146 16.0875C13.0396 16.3625 12.7188 16.5 12.3521 16.5L2.31458 16.5ZM12.3521 2.0625L2.31458 2.0625L2.31458 15.125L12.3521 15.125L12.3521 2.0625ZM4.74375 13.1542L6.11875 13.1542L6.11875 4.01042L4.74375 4.01042L4.74375 13.1542ZM8.54792 13.1542L9.92292 13.1542L9.92292 4.01042L8.54792 4.01042L8.54792 13.1542ZM2.31458 2.0625L2.31458 15.125L2.31458 2.0625Z");
canvas.FillPath(vector3path);
canvas.RestoreState();


// View:     frameView14
// NodeName: delete_FILL0_wght400_GRAD0_opsz48 4
// NodeType: FRAME
// NodeId:   16:895
canvas.SaveState();
canvas.RestoreState();


// View:     imageView4
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   16:896
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(1900.6666f, -85.25f);
var vector4Builder = new PathBuilder();
var vector4path = vector4Builder.BuildPath("M2.31458 16.5C1.93264 16.5 1.60799 16.3663 1.34063 16.099C1.07326 15.8316 0.939583 15.5069 0.939583 15.125L0.939583 2.0625L0 2.0625L0 0.6875L4.30833 0.6875L4.30833 0L10.3583 0L10.3583 0.6875L14.6667 0.6875L14.6667 2.0625L13.7271 2.0625L13.7271 15.125C13.7271 15.4917 13.5896 15.8125 13.3146 16.0875C13.0396 16.3625 12.7188 16.5 12.3521 16.5L2.31458 16.5ZM12.3521 2.0625L2.31458 2.0625L2.31458 15.125L12.3521 15.125L12.3521 2.0625ZM4.74375 13.1542L6.11875 13.1542L6.11875 4.01042L4.74375 4.01042L4.74375 13.1542ZM8.54792 13.1542L9.92292 13.1542L9.92292 4.01042L8.54792 4.01042L8.54792 13.1542ZM2.31458 2.0625L2.31458 15.125L2.31458 2.0625Z");
canvas.FillPath(vector4path);
canvas.RestoreState();


// View:     frameView15
// NodeName: Group 5
// NodeType: GROUP
// NodeId:   9:683
canvas.SaveState();
canvas.RestoreState();


// View:     textView13
// NodeName: Ед. измерения
// NodeType: TEXT
// NodeId:   9:684
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Ед. измерения", 1640f, -304f, 202f, 25f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView14
// NodeName: Метрические
// NodeType: TEXT
// NodeId:   9:688
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Метрические", 1718f, -304f, 202f, 25f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     lineView5
// NodeName: Line 1
// NodeType: LINE
// NodeId:   9:687
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(1639.999, -270.53998), new Point(1922.726, -270.53998));
canvas.RestoreState();


// View:     frameView16
// NodeName: Group 19
// NodeType: GROUP
// NodeId:   15:883
canvas.SaveState();
canvas.RestoreState();


// View:     textView15
// NodeName: Формат лога
// NodeType: TEXT
// NodeId:   15:884
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Формат лога", 1640f, -264f, 202f, 25f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView16
// NodeName: CSV
// NodeType: TEXT
// NodeId:   15:885
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"CSV", 1718f, -264f, 202f, 25f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     lineView6
// NodeName: Line 1
// NodeType: LINE
// NodeId:   15:886
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(1639.999, -230.54), new Point(1922.726, -230.54));
canvas.RestoreState();


// View:     frameView17
// NodeName: Group 4
// NodeType: GROUP
// NodeId:   9:673
canvas.SaveState();
canvas.RestoreState();


// View:     textView17
// NodeName: Ролл-аут
// NodeType: TEXT
// NodeId:   9:674
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Ролл-аут", 1641f, -382f, 202f, 25f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView18
// NodeName: check_box_FILL0_wght400_GRAD0_opsz48 1
// NodeType: FRAME
// NodeId:   9:681
canvas.SaveState();
canvas.RestoreState();


// View:     imageView5
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   9:682
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(1896.75f, -381.25f);
var vector5Builder = new PathBuilder();
var vector5path = vector5Builder.BuildPath("M9.34375 16.2188L18.375 7.1875L17.0312 5.84375L9.34375 13.5312L5.625 9.8125L4.28125 11.1562L9.34375 16.2188ZM1.875 22.5C1.375 22.5 0.9375 22.3125 0.5625 21.9375C0.1875 21.5625 0 21.125 0 20.625L0 1.875C0 1.375 0.1875 0.9375 0.5625 0.5625C0.9375 0.1875 1.375 0 1.875 0L20.625 0C21.125 0 21.5625 0.1875 21.9375 0.5625C22.3125 0.9375 22.5 1.375 22.5 1.875L22.5 20.625C22.5 21.125 22.3125 21.5625 21.9375 21.9375C21.5625 22.3125 21.125 22.5 20.625 22.5L1.875 22.5ZM1.875 20.625L20.625 20.625L20.625 1.875L1.875 1.875L1.875 20.625Z");
canvas.FillPath(vector5path);
canvas.RestoreState();


// View:     lineView7
// NodeName: Line 1
// NodeType: LINE
// NodeId:   9:676
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(1640.999, -348.53998), new Point(1923.726, -348.53998));
canvas.RestoreState();


// View:     frameView19
// NodeName: Group 20
// NodeType: GROUP
// NodeId:   31:11
canvas.SaveState();
canvas.RestoreState();


// View:     textView18
// NodeName: Звук
// NodeType: TEXT
// NodeId:   31:12
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Звук", 1641f, -341f, 202f, 25f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView20
// NodeName: check_box_FILL0_wght400_GRAD0_opsz48 1
// NodeType: FRAME
// NodeId:   31:13
canvas.SaveState();
canvas.RestoreState();


// View:     imageView6
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   31:14
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(1896.75f, -340.25f);
var vector6Builder = new PathBuilder();
var vector6path = vector6Builder.BuildPath("M9.34375 16.2188L18.375 7.1875L17.0312 5.84375L9.34375 13.5312L5.625 9.8125L4.28125 11.1562L9.34375 16.2188ZM1.875 22.5C1.375 22.5 0.9375 22.3125 0.5625 21.9375C0.1875 21.5625 0 21.125 0 20.625L0 1.875C0 1.375 0.1875 0.9375 0.5625 0.5625C0.9375 0.1875 1.375 0 1.875 0L20.625 0C21.125 0 21.5625 0.1875 21.9375 0.5625C22.3125 0.9375 22.5 1.375 22.5 1.875L22.5 20.625C22.5 21.125 22.3125 21.5625 21.9375 21.9375C21.5625 22.3125 21.125 22.5 20.625 22.5L1.875 22.5ZM1.875 20.625L20.625 20.625L20.625 1.875L1.875 1.875L1.875 20.625Z");
canvas.FillPath(vector6path);
canvas.RestoreState();


// View:     lineView8
// NodeName: Line 1
// NodeType: LINE
// NodeId:   31:15
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(1640.999, -307.53998), new Point(1923.726, -307.53998));
canvas.RestoreState();


// View:     frameView21
// NodeName: Menu
// NodeType: FRAME
// NodeId:   10:775
canvas.SaveState();
canvas.RestoreState();


// View:     frameView22
// NodeName: Measure
// NodeType: GROUP
// NodeId:   10:776
canvas.SaveState();
canvas.RestoreState();


// View:     textView19
// NodeName: Разгон
// NodeType: TEXT
// NodeId:   10:777
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Разгон", 1619f, 302f, 53f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView7
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   10:778
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(1628f, 274f);
var vector7Builder = new PathBuilder();
var vector7path = vector7Builder.BuildPath("M21.35 25.375C24.4417 25.375 27.0521 24.2448 29.1812 21.9844C31.3104 19.724 32.375 17.0333 32.375 13.9125C32.375 10.7042 31.2302 8.02083 28.9406 5.8625C26.651 3.70417 23.9021 2.625 20.6938 2.625C19.0604 2.625 17.5292 2.82188 16.1 3.21563C14.6708 3.60938 13.1687 4.22917 11.5938 5.075L16.8875 7.04375C18.375 7.59792 19.3885 8.39271 19.9281 9.42812C20.4677 10.4635 20.7375 11.6375 20.7375 12.95C20.7375 14.7 20.1542 16.1656 18.9875 17.3469C17.8208 18.5281 16.3917 19.1187 14.7 19.1187L2.8875 19.1187C2.74167 19.7896 2.66146 20.6865 2.64688 21.8094C2.63229 22.9323 2.625 24.1208 2.625 25.375L21.35 25.375ZM3.19375 16.4937L14.4375 16.4937C15.5167 16.4937 16.399 16.151 17.0844 15.4656C17.7698 14.7802 18.1125 13.9417 18.1125 12.95C18.1125 12.1042 17.9156 11.3896 17.5219 10.8063C17.1281 10.2229 16.5521 9.8 15.7937 9.5375L9.05625 6.7375C7.59792 8.02083 6.3875 9.46458 5.425 11.0687C4.4625 12.6729 3.71875 14.4813 3.19375 16.4937L3.19375 16.4937ZM21.35 28L2.625 28C1.925 28 1.3125 27.7375 0.7875 27.2125C0.2625 26.6875 0 26.075 0 25.375L0 22.0938C0 18.9438 0.525 16.0198 1.575 13.3219C2.625 10.624 4.07604 8.29063 5.92812 6.32188C7.78021 4.35313 9.96771 2.80729 12.4906 1.68438C15.0135 0.561459 17.7479 0 20.6938 0C22.6479 0 24.4927 0.357292 26.2281 1.07188C27.9635 1.78646 29.4802 2.77083 30.7781 4.025C32.076 5.27917 33.1042 6.75208 33.8625 8.44375C34.6208 10.1354 35 11.9583 35 13.9125C35 15.8375 34.6427 17.6604 33.9281 19.3813C33.2135 21.1021 32.2365 22.5969 30.9969 23.8656C29.7573 25.1344 28.3135 26.1406 26.6656 26.8844C25.0177 27.6281 23.2458 28 21.35 28Z");
canvas.FillPath(vector7path);
canvas.RestoreState();


// View:     frameView23
// NodeName: Gauges
// NodeType: GROUP
// NodeId:   10:779
canvas.SaveState();
canvas.RestoreState();


// View:     textView20
// NodeName: Приборы
// NodeType: TEXT
// NodeId:   10:780
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Приборы", 1699f, 302f, 61f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView8
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   10:781
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(1712f, 274f);
var vector8Builder = new PathBuilder();
var vector8path = vector8Builder.BuildPath("M14.3917 20.77C15.0665 21.4418 16.0421 21.7559 17.3185 21.7121C18.5948 21.6682 19.5264 21.1935 20.1132 20.288L29.6196 5.43349L14.8758 15.0297C13.9956 15.614 13.5261 16.5488 13.4675 17.8341C13.4088 19.1195 13.7169 20.0981 14.3917 20.77L14.3917 20.77ZM17.5165 0C19.1889 0 20.9347 0.270214 22.7539 0.810642C24.573 1.35107 26.3188 2.27856 27.9912 3.59311L25.7026 5.2144C24.3823 4.33803 22.9666 3.68805 21.4555 3.26448C19.9445 2.8409 18.6315 2.62911 17.5165 2.62911C13.4088 2.62911 9.90255 4.08972 6.9978 7.01095C4.09305 9.93218 2.64068 13.4815 2.64068 17.6588C2.64068 18.9734 2.82406 20.3026 3.19082 21.6463C3.55758 22.9901 4.07838 24.2316 4.75322 25.3709L30.2358 25.3709C30.8813 24.3192 31.3947 23.0923 31.7762 21.6901C32.1576 20.2879 32.3483 18.915 32.3483 17.5712C32.3483 16.3443 32.1649 15.0224 31.7982 13.6056C31.4314 12.1888 30.7786 10.8816 29.8397 9.68388L31.5561 7.40532C32.6711 9.04121 33.5073 10.6844 34.0648 12.3349C34.6222 13.9854 34.9303 15.6432 34.989 17.3083C35.0477 19.061 34.8716 20.7115 34.4609 22.2598C34.0501 23.808 33.4486 25.2394 32.6564 26.554C32.3043 27.2259 31.9302 27.6348 31.5341 27.7809C31.138 27.927 30.6465 28 30.0597 28L4.92927 28C4.43047 28 3.93901 27.8758 3.45489 27.6275C2.97076 27.3792 2.61134 27.0214 2.37661 26.554C1.61375 25.1518 1.02693 23.7277 0.616158 22.2817C0.205386 20.8357 0 19.2947 0 17.6588C0 15.2342 0.462119 12.9484 1.38636 10.8013C2.31059 8.65415 3.56492 6.78456 5.14932 5.19249C6.73373 3.60042 8.58954 2.33698 10.7168 1.40219C12.844 0.467396 15.1106 -1.55674e-15 17.5165 0L17.5165 0Z");
canvas.FillPath(vector8path);
canvas.RestoreState();


// View:     frameView24
// NodeName: History
// NodeType: GROUP
// NodeId:   10:782
canvas.SaveState();
canvas.RestoreState();


// View:     textView21
// NodeName: История
// NodeType: TEXT
// NodeId:   10:783
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"История", 1787f, 302f, 57f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView9
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   10:784
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(1799f, 274f);
var vector9Builder = new PathBuilder();
var vector9path = vector9Builder.BuildPath("M2.40833 28C1.74722 28 1.18056 27.7744 0.708333 27.3233C0.236111 26.8722 8.38835e-16 26.3356 0 25.7133C0 25.06 0.236111 24.5 0.708333 24.0333C1.18056 23.5667 1.74722 23.3333 2.40833 23.3333C3.03796 23.3333 3.58102 23.5667 4.0375 24.0333C4.49398 24.5 4.72222 25.06 4.72222 25.7133C4.72222 26.3356 4.49398 26.8722 4.0375 27.3233C3.58102 27.7744 3.03796 28 2.40833 28ZM9.44444 27.0667L9.44444 24.2667L34 24.2667L34 27.0667L9.44444 27.0667ZM2.40833 16.3333C1.74722 16.3333 1.18056 16.1078 0.708333 15.6567C0.236111 15.2056 8.38835e-16 14.6533 0 14C0 13.3467 0.236111 12.7944 0.708333 12.3433C1.18056 11.8922 1.74722 11.6667 2.40833 11.6667C3.03796 11.6667 3.58102 11.9 4.0375 12.3667C4.49398 12.8333 4.72222 13.3778 4.72222 14C4.72222 14.6222 4.49398 15.1667 4.0375 15.6333C3.58102 16.1 3.03796 16.3333 2.40833 16.3333ZM9.44444 15.4L9.44444 12.6L34 12.6L34 15.4L9.44444 15.4ZM2.36111 4.66667C1.7 4.66667 1.1412 4.44111 0.684722 3.99C0.228241 3.53889 0 2.98667 0 2.33333C0 1.68 0.228241 1.12778 0.684722 0.676667C1.1412 0.225556 1.7 0 2.36111 0C3.02222 0 3.58102 0.225556 4.0375 0.676667C4.49398 1.12778 4.72222 1.68 4.72222 2.33333C4.72222 2.98667 4.49398 3.53889 4.0375 3.99C3.58102 4.44111 3.02222 4.66667 2.36111 4.66667ZM9.44444 3.73333L9.44444 0.933333L34 0.933333L34 3.73333L9.44444 3.73333Z");
canvas.FillPath(vector9path);
canvas.RestoreState();


// View:     frameView25
// NodeName: Settings
// NodeType: GROUP
// NodeId:   10:785
canvas.SaveState();
canvas.RestoreState();


// View:     textView22
// NodeName: Настройки
// NodeType: TEXT
// NodeId:   10:786
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Настройки", 1871f, 302f, 70f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView10
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   10:787
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(1892f, 274f);
var vector10Builder = new PathBuilder();
var vector10path = vector10Builder.BuildPath("M10.78 28L10.08 23.59C9.63667 23.4267 9.17 23.205 8.68 22.925C8.19 22.645 7.75833 22.3533 7.385 22.05L3.255 23.94L0 18.2L3.78 15.435C3.73333 15.225 3.70417 14.9858 3.6925 14.7175C3.68083 14.4492 3.675 14.21 3.675 14C3.675 13.79 3.68083 13.5508 3.6925 13.2825C3.70417 13.0142 3.73333 12.775 3.78 12.565L0 9.8L3.255 4.06L7.385 5.95C7.75833 5.64667 8.19 5.355 8.68 5.075C9.17 4.795 9.63667 4.585 10.08 4.445L10.78 0L17.22 0L17.92 4.41C18.3633 4.57333 18.8358 4.78917 19.3375 5.0575C19.8392 5.32583 20.265 5.62333 20.615 5.95L24.745 4.06L28 9.8L24.22 12.495C24.2667 12.7283 24.2958 12.9792 24.3075 13.2475C24.3192 13.5158 24.325 13.7667 24.325 14C24.325 14.2333 24.3192 14.4783 24.3075 14.735C24.2958 14.9917 24.2667 15.2367 24.22 15.47L28 18.2L24.745 23.94L20.615 22.05C20.2417 22.3533 19.8158 22.6508 19.3375 22.9425C18.8592 23.2342 18.3867 23.45 17.92 23.59L17.22 28L10.78 28ZM14 18.55C15.26 18.55 16.3333 18.1067 17.22 17.22C18.1067 16.3333 18.55 15.26 18.55 14C18.55 12.74 18.1067 11.6667 17.22 10.78C16.3333 9.89333 15.26 9.45 14 9.45C12.74 9.45 11.6667 9.89333 10.78 10.78C9.89333 11.6667 9.45 12.74 9.45 14C9.45 15.26 9.89333 16.3333 10.78 17.22C11.6667 18.1067 12.74 18.55 14 18.55ZM14 16.45C13.3233 16.45 12.7458 16.2108 12.2675 15.7325C11.7892 15.2542 11.55 14.6767 11.55 14C11.55 13.3233 11.7892 12.7458 12.2675 12.2675C12.7458 11.7892 13.3233 11.55 14 11.55C14.6767 11.55 15.2542 11.7892 15.7325 12.2675C16.2108 12.7458 16.45 13.3233 16.45 14C16.45 14.6767 16.2108 15.2542 15.7325 15.7325C15.2542 16.2108 14.6767 16.45 14 16.45ZM12.46 25.9L15.54 25.9L16.03 21.98C16.8 21.7933 17.5292 21.5017 18.2175 21.105C18.9058 20.7083 19.53 20.23 20.09 19.67L23.8 21.28L25.2 18.76L21.91 16.345C22.0033 15.9483 22.0792 15.5575 22.1375 15.1725C22.1958 14.7875 22.225 14.3967 22.225 14C22.225 13.6033 22.2017 13.2125 22.155 12.8275C22.1083 12.4425 22.0267 12.0517 21.91 11.655L25.2 9.24L23.8 6.72L20.09 8.33C19.5533 7.72333 18.9467 7.21583 18.27 6.8075C17.5933 6.39917 16.8467 6.13667 16.03 6.02L15.54 2.1L12.46 2.1L11.97 6.02C11.1767 6.18333 10.4358 6.46333 9.7475 6.86C9.05917 7.25667 8.44667 7.74667 7.91 8.33L4.2 6.72L2.8 9.24L6.09 11.655C5.99667 12.0517 5.92083 12.4425 5.8625 12.8275C5.80417 13.2125 5.775 13.6033 5.775 14C5.775 14.3967 5.80417 14.7875 5.8625 15.1725C5.92083 15.5575 5.99667 15.9483 6.09 16.345L2.8 18.76L4.2 21.28L7.91 19.67C8.47 20.23 9.09417 20.7083 9.7825 21.105C10.4708 21.5017 11.2 21.7933 11.97 21.98L12.46 25.9Z");
canvas.FillPath(vector10path);
canvas.RestoreState();


// View:     frameView26
// NodeName: Add user
// NodeType: FRAME
// NodeId:   51:560
canvas.SaveState();
canvas.RestoreState();


// View:     frameView27
// NodeName: iPhone 11 Pro
// NodeType: INSTANCE
// NodeId:   51:561
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(52, 60, 69, 255)) ,new PaintGradientStop(1, new Color(17, 22, 29, 255)) ,}}, new RectF(1594f, 452f, 375f, 812f));
canvas.FillRoundedRectangle(1594f, 452f, 375f, 812f, 55f);
canvas.RestoreState();


// View:     rectangleView2
// NodeName: Results screen
// NodeType: RECTANGLE
// NodeId:   51:562
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(35, 35, 35, 255)) ,new PaintGradientStop(1, new Color(0, 0, 0, 255)) ,}}, new RectF(1620f, 512f, 324f, 647f));
canvas.FillRoundedRectangle(1620f, 512f, 324f, 647f, 25f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(21, 25, 30, 255)) ,new PaintGradientStop(1, new Color(66, 70, 75, 255)) ,}}, new RectF(1620f, 512f, 324f, 647f));
canvas.StrokeSize  = 2;
canvas.DrawRoundedRectangle(1620f, 512f, 324f, 647f, 25f);
canvas.RestoreState();


// View:     frameView28
// NodeName: User Speed
// NodeType: GROUP
// NodeId:   51:580
canvas.SaveState();
canvas.RestoreState();


// View:     lineView9
// NodeName: Line 1
// NodeType: LINE
// NodeId:   51:581
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(1639.999, 566.334), new Point(1922.726, 566.334));
canvas.RestoreState();


// View:     textView23
// NodeName: Subaru Forester
// NodeType: TEXT
// NodeId:   51:582
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Subaru Forester", 1655f, 709.0216f, 144f, 33.276f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView24
// NodeName: Lada Granta
// NodeType: TEXT
// NodeId:   51:586
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Lada Granta", 1655f, 618.5108f, 131f, 33.276062f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView25
// NodeName: Ford Focus 2
// NodeType: TEXT
// NodeId:   51:587
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Ford Focus 2", 1655f, 573.2554f, 120f, 33.276f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView26
// NodeName: BMW M3
// NodeType: TEXT
// NodeId:   51:588
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"BMW M3", 1655f, 663.76624f, 108f, 33.276f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView29
// NodeName: Group 10
// NodeType: GROUP
// NodeId:   51:589
canvas.SaveState();
canvas.RestoreState();


// View:     textView27
// NodeName: Выбор автомобиля
// NodeType: TEXT
// NodeId:   51:590
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Выбор автомобиля", 1640f, 528f, 282f, 33.276f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView30
// NodeName: add_FILL0_wght400_GRAD0_opsz48 1
// NodeType: FRAME
// NodeId:   51:630
canvas.SaveState();
canvas.RestoreState();


// View:     imageView11
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:631
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(1899.25f, 536.25f);
var vector11Builder = new PathBuilder();
var vector11path = vector11Builder.BuildPath("M7.8125 17.5L7.8125 9.6875L0 9.6875L0 7.8125L7.8125 7.8125L7.8125 0L9.6875 0L9.6875 7.8125L17.5 7.8125L17.5 9.6875L9.6875 9.6875L9.6875 17.5L7.8125 17.5Z");
canvas.FillPath(vector11path);
canvas.RestoreState();


// View:     frameView31
// NodeName: delete_FILL0_wght400_GRAD0_opsz48 1
// NodeType: FRAME
// NodeId:   51:591
canvas.SaveState();
canvas.RestoreState();


// View:     imageView12
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:592
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(1901f, 578.5795f);
var vector12Builder = new PathBuilder();
var vector12path = vector12Builder.BuildPath("M2.31458 21.9622C1.93264 21.9622 1.60799 21.7842 1.34063 21.4284C1.07326 21.0725 0.939583 20.6404 0.939583 20.132L0.939583 2.74527L0 2.74527L0 0.915091L4.30833 0.915091L4.30833 0L10.3583 0L10.3583 0.915091L14.6667 0.915091L14.6667 2.74527L13.7271 2.74527L13.7271 20.132C13.7271 20.62 13.5896 21.0471 13.3146 21.4131C13.0396 21.7792 12.7188 21.9622 12.3521 21.9622L2.31458 21.9622ZM12.3521 2.74527L2.31458 2.74527L2.31458 20.132L12.3521 20.132L12.3521 2.74527ZM4.74375 17.5087L6.11875 17.5087L6.11875 5.33803L4.74375 5.33803L4.74375 17.5087ZM8.54792 17.5087L9.92292 17.5087L9.92292 5.33803L8.54792 5.33803L8.54792 17.5087Z");
canvas.FillPath(vector12path);
canvas.RestoreState();


// View:     frameView32
// NodeName: delete_FILL0_wght400_GRAD0_opsz48 2
// NodeType: FRAME
// NodeId:   51:593
canvas.SaveState();
canvas.RestoreState();


// View:     imageView13
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:594
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(1900.6666f, 623.5022f);
var vector13Builder = new PathBuilder();
var vector13path = vector13Builder.BuildPath("M2.31458 21.9622C1.93264 21.9622 1.60799 21.7842 1.34063 21.4284C1.07326 21.0725 0.939583 20.6404 0.939583 20.132L0.939583 2.74527L0 2.74527L0 0.915091L4.30833 0.915091L4.30833 0L10.3583 0L10.3583 0.915091L14.6667 0.915091L14.6667 2.74527L13.7271 2.74527L13.7271 20.132C13.7271 20.62 13.5896 21.0471 13.3146 21.4131C13.0396 21.7792 12.7188 21.9622 12.3521 21.9622L2.31458 21.9622ZM12.3521 2.74527L2.31458 2.74527L2.31458 20.132L12.3521 20.132L12.3521 2.74527ZM4.74375 17.5087L6.11875 17.5087L6.11875 5.33803L4.74375 5.33803L4.74375 17.5087ZM8.54792 17.5087L9.92292 17.5087L9.92292 5.33803L8.54792 5.33803L8.54792 17.5087ZM2.31458 2.74527L2.31458 20.132L2.31458 2.74527Z");
canvas.FillPath(vector13path);
canvas.RestoreState();


// View:     frameView33
// NodeName: delete_FILL0_wght400_GRAD0_opsz48 3
// NodeType: FRAME
// NodeId:   51:595
canvas.SaveState();
canvas.RestoreState();


// View:     imageView14
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:596
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(1900.6666f, 668.75757f);
var vector14Builder = new PathBuilder();
var vector14path = vector14Builder.BuildPath("M2.31458 21.9622C1.93264 21.9622 1.60799 21.7842 1.34063 21.4284C1.07326 21.0725 0.939583 20.6404 0.939583 20.132L0.939583 2.74527L0 2.74527L0 0.915091L4.30833 0.915091L4.30833 0L10.3583 0L10.3583 0.915091L14.6667 0.915091L14.6667 2.74527L13.7271 2.74527L13.7271 20.132C13.7271 20.62 13.5896 21.0471 13.3146 21.4131C13.0396 21.7792 12.7188 21.9622 12.3521 21.9622L2.31458 21.9622ZM12.3521 2.74527L2.31458 2.74527L2.31458 20.132L12.3521 20.132L12.3521 2.74527ZM4.74375 17.5087L6.11875 17.5087L6.11875 5.33803L4.74375 5.33803L4.74375 17.5087ZM8.54792 17.5087L9.92292 17.5087L9.92292 5.33803L8.54792 5.33803L8.54792 17.5087ZM2.31458 2.74527L2.31458 20.132L2.31458 2.74527Z");
canvas.FillPath(vector14path);
canvas.RestoreState();


// View:     frameView34
// NodeName: delete_FILL0_wght400_GRAD0_opsz48 4
// NodeType: FRAME
// NodeId:   51:597
canvas.SaveState();
canvas.RestoreState();


// View:     imageView15
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:598
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(1900.6666f, 714.013f);
var vector15Builder = new PathBuilder();
var vector15path = vector15Builder.BuildPath("M2.31458 21.9622C1.93264 21.9622 1.60799 21.7842 1.34063 21.4284C1.07326 21.0725 0.939583 20.6404 0.939583 20.132L0.939583 2.74527L0 2.74527L0 0.915091L4.30833 0.915091L4.30833 0L10.3583 0L10.3583 0.915091L14.6667 0.915091L14.6667 2.74527L13.7271 2.74527L13.7271 20.132C13.7271 20.62 13.5896 21.0471 13.3146 21.4131C13.0396 21.7792 12.7188 21.9622 12.3521 21.9622L2.31458 21.9622ZM12.3521 2.74527L2.31458 2.74527L2.31458 20.132L12.3521 20.132L12.3521 2.74527ZM4.74375 17.5087L6.11875 17.5087L6.11875 5.33803L4.74375 5.33803L4.74375 17.5087ZM8.54792 17.5087L9.92292 17.5087L9.92292 5.33803L8.54792 5.33803L8.54792 17.5087ZM2.31458 2.74527L2.31458 20.132L2.31458 2.74527Z");
canvas.FillPath(vector15path);
canvas.RestoreState();


// View:     frameView35
// NodeName: Menu
// NodeType: FRAME
// NodeId:   51:617
canvas.SaveState();
canvas.RestoreState();


// View:     frameView36
// NodeName: Measure
// NodeType: GROUP
// NodeId:   51:618
canvas.SaveState();
canvas.RestoreState();


// View:     textView28
// NodeName: Разгон
// NodeType: TEXT
// NodeId:   51:619
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Разгон", 1619f, 1212f, 53f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView16
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:620
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(1628f, 1184f);
var vector16Builder = new PathBuilder();
var vector16path = vector16Builder.BuildPath("M21.35 25.375C24.4417 25.375 27.0521 24.2448 29.1812 21.9844C31.3104 19.724 32.375 17.0333 32.375 13.9125C32.375 10.7042 31.2302 8.02083 28.9406 5.8625C26.651 3.70417 23.9021 2.625 20.6938 2.625C19.0604 2.625 17.5292 2.82188 16.1 3.21563C14.6708 3.60938 13.1687 4.22917 11.5938 5.075L16.8875 7.04375C18.375 7.59792 19.3885 8.39271 19.9281 9.42812C20.4677 10.4635 20.7375 11.6375 20.7375 12.95C20.7375 14.7 20.1542 16.1656 18.9875 17.3469C17.8208 18.5281 16.3917 19.1187 14.7 19.1187L2.8875 19.1187C2.74167 19.7896 2.66146 20.6865 2.64688 21.8094C2.63229 22.9323 2.625 24.1208 2.625 25.375L21.35 25.375ZM3.19375 16.4937L14.4375 16.4937C15.5167 16.4937 16.399 16.151 17.0844 15.4656C17.7698 14.7802 18.1125 13.9417 18.1125 12.95C18.1125 12.1042 17.9156 11.3896 17.5219 10.8063C17.1281 10.2229 16.5521 9.8 15.7937 9.5375L9.05625 6.7375C7.59792 8.02083 6.3875 9.46458 5.425 11.0687C4.4625 12.6729 3.71875 14.4813 3.19375 16.4937L3.19375 16.4937ZM21.35 28L2.625 28C1.925 28 1.3125 27.7375 0.7875 27.2125C0.2625 26.6875 0 26.075 0 25.375L0 22.0938C0 18.9438 0.525 16.0198 1.575 13.3219C2.625 10.624 4.07604 8.29063 5.92812 6.32188C7.78021 4.35313 9.96771 2.80729 12.4906 1.68438C15.0135 0.561459 17.7479 0 20.6938 0C22.6479 0 24.4927 0.357292 26.2281 1.07188C27.9635 1.78646 29.4802 2.77083 30.7781 4.025C32.076 5.27917 33.1042 6.75208 33.8625 8.44375C34.6208 10.1354 35 11.9583 35 13.9125C35 15.8375 34.6427 17.6604 33.9281 19.3813C33.2135 21.1021 32.2365 22.5969 30.9969 23.8656C29.7573 25.1344 28.3135 26.1406 26.6656 26.8844C25.0177 27.6281 23.2458 28 21.35 28Z");
canvas.FillPath(vector16path);
canvas.RestoreState();


// View:     frameView37
// NodeName: Gauges
// NodeType: GROUP
// NodeId:   51:621
canvas.SaveState();
canvas.RestoreState();


// View:     textView29
// NodeName: Приборы
// NodeType: TEXT
// NodeId:   51:622
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Приборы", 1699f, 1212f, 61f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView17
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:623
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(1712f, 1184f);
var vector17Builder = new PathBuilder();
var vector17path = vector17Builder.BuildPath("M14.3917 20.77C15.0665 21.4418 16.0421 21.7559 17.3185 21.7121C18.5948 21.6682 19.5264 21.1935 20.1132 20.288L29.6196 5.43349L14.8758 15.0297C13.9956 15.614 13.5261 16.5488 13.4675 17.8341C13.4088 19.1195 13.7169 20.0981 14.3917 20.77L14.3917 20.77ZM17.5165 0C19.1889 0 20.9347 0.270214 22.7539 0.810642C24.573 1.35107 26.3188 2.27856 27.9912 3.59311L25.7026 5.2144C24.3823 4.33803 22.9666 3.68805 21.4555 3.26448C19.9445 2.8409 18.6315 2.62911 17.5165 2.62911C13.4088 2.62911 9.90255 4.08972 6.9978 7.01095C4.09305 9.93218 2.64068 13.4815 2.64068 17.6588C2.64068 18.9734 2.82406 20.3026 3.19082 21.6463C3.55758 22.9901 4.07838 24.2316 4.75322 25.3709L30.2358 25.3709C30.8813 24.3192 31.3947 23.0923 31.7762 21.6901C32.1576 20.2879 32.3483 18.915 32.3483 17.5712C32.3483 16.3443 32.1649 15.0224 31.7982 13.6056C31.4314 12.1888 30.7786 10.8816 29.8397 9.68388L31.5561 7.40532C32.6711 9.04121 33.5073 10.6844 34.0648 12.3349C34.6222 13.9854 34.9303 15.6432 34.989 17.3083C35.0477 19.061 34.8716 20.7115 34.4609 22.2598C34.0501 23.808 33.4486 25.2394 32.6564 26.554C32.3043 27.2259 31.9302 27.6348 31.5341 27.7809C31.138 27.927 30.6465 28 30.0597 28L4.92927 28C4.43047 28 3.93901 27.8758 3.45489 27.6275C2.97076 27.3792 2.61134 27.0214 2.37661 26.554C1.61375 25.1518 1.02693 23.7277 0.616158 22.2817C0.205386 20.8357 0 19.2947 0 17.6588C0 15.2342 0.462119 12.9484 1.38636 10.8013C2.31059 8.65415 3.56492 6.78456 5.14932 5.19249C6.73373 3.60042 8.58954 2.33698 10.7168 1.40219C12.844 0.467396 15.1106 -1.55674e-15 17.5165 0L17.5165 0Z");
canvas.FillPath(vector17path);
canvas.RestoreState();


// View:     frameView38
// NodeName: History
// NodeType: GROUP
// NodeId:   51:624
canvas.SaveState();
canvas.RestoreState();


// View:     textView30
// NodeName: История
// NodeType: TEXT
// NodeId:   51:625
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"История", 1787f, 1212f, 57f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView18
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:626
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(1799f, 1184f);
var vector18Builder = new PathBuilder();
var vector18path = vector18Builder.BuildPath("M2.40833 28C1.74722 28 1.18056 27.7744 0.708333 27.3233C0.236111 26.8722 8.38835e-16 26.3356 0 25.7133C0 25.06 0.236111 24.5 0.708333 24.0333C1.18056 23.5667 1.74722 23.3333 2.40833 23.3333C3.03796 23.3333 3.58102 23.5667 4.0375 24.0333C4.49398 24.5 4.72222 25.06 4.72222 25.7133C4.72222 26.3356 4.49398 26.8722 4.0375 27.3233C3.58102 27.7744 3.03796 28 2.40833 28ZM9.44444 27.0667L9.44444 24.2667L34 24.2667L34 27.0667L9.44444 27.0667ZM2.40833 16.3333C1.74722 16.3333 1.18056 16.1078 0.708333 15.6567C0.236111 15.2056 8.38835e-16 14.6533 0 14C0 13.3467 0.236111 12.7944 0.708333 12.3433C1.18056 11.8922 1.74722 11.6667 2.40833 11.6667C3.03796 11.6667 3.58102 11.9 4.0375 12.3667C4.49398 12.8333 4.72222 13.3778 4.72222 14C4.72222 14.6222 4.49398 15.1667 4.0375 15.6333C3.58102 16.1 3.03796 16.3333 2.40833 16.3333ZM9.44444 15.4L9.44444 12.6L34 12.6L34 15.4L9.44444 15.4ZM2.36111 4.66667C1.7 4.66667 1.1412 4.44111 0.684722 3.99C0.228241 3.53889 0 2.98667 0 2.33333C0 1.68 0.228241 1.12778 0.684722 0.676667C1.1412 0.225556 1.7 0 2.36111 0C3.02222 0 3.58102 0.225556 4.0375 0.676667C4.49398 1.12778 4.72222 1.68 4.72222 2.33333C4.72222 2.98667 4.49398 3.53889 4.0375 3.99C3.58102 4.44111 3.02222 4.66667 2.36111 4.66667ZM9.44444 3.73333L9.44444 0.933333L34 0.933333L34 3.73333L9.44444 3.73333Z");
canvas.FillPath(vector18path);
canvas.RestoreState();


// View:     frameView39
// NodeName: Settings
// NodeType: GROUP
// NodeId:   51:627
canvas.SaveState();
canvas.RestoreState();


// View:     textView31
// NodeName: Настройки
// NodeType: TEXT
// NodeId:   51:628
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Настройки", 1871f, 1212f, 70f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView19
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:629
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(1892f, 1184f);
var vector19Builder = new PathBuilder();
var vector19path = vector19Builder.BuildPath("M10.78 28L10.08 23.59C9.63667 23.4267 9.17 23.205 8.68 22.925C8.19 22.645 7.75833 22.3533 7.385 22.05L3.255 23.94L0 18.2L3.78 15.435C3.73333 15.225 3.70417 14.9858 3.6925 14.7175C3.68083 14.4492 3.675 14.21 3.675 14C3.675 13.79 3.68083 13.5508 3.6925 13.2825C3.70417 13.0142 3.73333 12.775 3.78 12.565L0 9.8L3.255 4.06L7.385 5.95C7.75833 5.64667 8.19 5.355 8.68 5.075C9.17 4.795 9.63667 4.585 10.08 4.445L10.78 0L17.22 0L17.92 4.41C18.3633 4.57333 18.8358 4.78917 19.3375 5.0575C19.8392 5.32583 20.265 5.62333 20.615 5.95L24.745 4.06L28 9.8L24.22 12.495C24.2667 12.7283 24.2958 12.9792 24.3075 13.2475C24.3192 13.5158 24.325 13.7667 24.325 14C24.325 14.2333 24.3192 14.4783 24.3075 14.735C24.2958 14.9917 24.2667 15.2367 24.22 15.47L28 18.2L24.745 23.94L20.615 22.05C20.2417 22.3533 19.8158 22.6508 19.3375 22.9425C18.8592 23.2342 18.3867 23.45 17.92 23.59L17.22 28L10.78 28ZM14 18.55C15.26 18.55 16.3333 18.1067 17.22 17.22C18.1067 16.3333 18.55 15.26 18.55 14C18.55 12.74 18.1067 11.6667 17.22 10.78C16.3333 9.89333 15.26 9.45 14 9.45C12.74 9.45 11.6667 9.89333 10.78 10.78C9.89333 11.6667 9.45 12.74 9.45 14C9.45 15.26 9.89333 16.3333 10.78 17.22C11.6667 18.1067 12.74 18.55 14 18.55ZM14 16.45C13.3233 16.45 12.7458 16.2108 12.2675 15.7325C11.7892 15.2542 11.55 14.6767 11.55 14C11.55 13.3233 11.7892 12.7458 12.2675 12.2675C12.7458 11.7892 13.3233 11.55 14 11.55C14.6767 11.55 15.2542 11.7892 15.7325 12.2675C16.2108 12.7458 16.45 13.3233 16.45 14C16.45 14.6767 16.2108 15.2542 15.7325 15.7325C15.2542 16.2108 14.6767 16.45 14 16.45ZM12.46 25.9L15.54 25.9L16.03 21.98C16.8 21.7933 17.5292 21.5017 18.2175 21.105C18.9058 20.7083 19.53 20.23 20.09 19.67L23.8 21.28L25.2 18.76L21.91 16.345C22.0033 15.9483 22.0792 15.5575 22.1375 15.1725C22.1958 14.7875 22.225 14.3967 22.225 14C22.225 13.6033 22.2017 13.2125 22.155 12.8275C22.1083 12.4425 22.0267 12.0517 21.91 11.655L25.2 9.24L23.8 6.72L20.09 8.33C19.5533 7.72333 18.9467 7.21583 18.27 6.8075C17.5933 6.39917 16.8467 6.13667 16.03 6.02L15.54 2.1L12.46 2.1L11.97 6.02C11.1767 6.18333 10.4358 6.46333 9.7475 6.86C9.05917 7.25667 8.44667 7.74667 7.91 8.33L4.2 6.72L2.8 9.24L6.09 11.655C5.99667 12.0517 5.92083 12.4425 5.8625 12.8275C5.80417 13.2125 5.775 13.6033 5.775 14C5.775 14.3967 5.80417 14.7875 5.8625 15.1725C5.92083 15.5575 5.99667 15.9483 6.09 16.345L2.8 18.76L4.2 21.28L7.91 19.67C8.47 20.23 9.09417 20.7083 9.7825 21.105C10.4708 21.5017 11.2 21.7933 11.97 21.98L12.46 25.9Z");
canvas.FillPath(vector19path);
canvas.RestoreState();


// View:     frameView40
// NodeName: Add vehicle
// NodeType: FRAME
// NodeId:   51:632
canvas.SaveState();
canvas.RestoreState();


// View:     frameView41
// NodeName: iPhone 11 Pro
// NodeType: INSTANCE
// NodeId:   51:633
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(52, 60, 69, 255)) ,new PaintGradientStop(1, new Color(17, 22, 29, 255)) ,}}, new RectF(2009f, 452f, 375f, 812f));
canvas.FillRoundedRectangle(2009f, 452f, 375f, 812f, 55f);
canvas.RestoreState();


// View:     rectangleView3
// NodeName: Results screen
// NodeType: RECTANGLE
// NodeId:   51:634
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(35, 35, 35, 255)) ,new PaintGradientStop(1, new Color(0, 0, 0, 255)) ,}}, new RectF(2035f, 512f, 324f, 647f));
canvas.FillRoundedRectangle(2035f, 512f, 324f, 647f, 25f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(21, 25, 30, 255)) ,new PaintGradientStop(1, new Color(66, 70, 75, 255)) ,}}, new RectF(2035f, 512f, 324f, 647f));
canvas.StrokeSize  = 2;
canvas.DrawRoundedRectangle(2035f, 512f, 324f, 647f, 25f);
canvas.RestoreState();


// View:     frameView42
// NodeName: User Speed
// NodeType: GROUP
// NodeId:   51:635
canvas.SaveState();
canvas.RestoreState();


// View:     lineView10
// NodeName: Line 1
// NodeType: LINE
// NodeId:   51:636
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(2054.999, 566.334), new Point(2337.726, 566.334));
canvas.RestoreState();


// View:     textView32
// NodeName: Николай
// NodeType: TEXT
// NodeId:   51:637
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Николай", 2070f, 709.0216f, 144f, 33.276f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView33
// NodeName: Сергей
// NodeType: TEXT
// NodeId:   51:638
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Сергей", 2070f, 618.5108f, 131f, 33.276062f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView34
// NodeName: Дмитрий
// NodeType: TEXT
// NodeId:   51:639
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Дмитрий", 2070f, 573.2554f, 120f, 33.276f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView35
// NodeName: Иван
// NodeType: TEXT
// NodeId:   51:640
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Иван", 2070f, 663.76624f, 108f, 33.276f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView43
// NodeName: Group 10
// NodeType: GROUP
// NodeId:   51:641
canvas.SaveState();
canvas.RestoreState();


// View:     textView36
// NodeName: Выбор пользователя
// NodeType: TEXT
// NodeId:   51:642
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Выбор пользователя", 2055f, 528f, 282f, 33.276f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView44
// NodeName: add_FILL0_wght400_GRAD0_opsz48 1
// NodeType: FRAME
// NodeId:   51:643
canvas.SaveState();
canvas.RestoreState();


// View:     imageView20
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:644
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(2314.25f, 536.25f);
var vector20Builder = new PathBuilder();
var vector20path = vector20Builder.BuildPath("M7.8125 17.5L7.8125 9.6875L0 9.6875L0 7.8125L7.8125 7.8125L7.8125 0L9.6875 0L9.6875 7.8125L17.5 7.8125L17.5 9.6875L9.6875 9.6875L9.6875 17.5L7.8125 17.5Z");
canvas.FillPath(vector20path);
canvas.RestoreState();


// View:     frameView45
// NodeName: delete_FILL0_wght400_GRAD0_opsz48 1
// NodeType: FRAME
// NodeId:   51:645
canvas.SaveState();
canvas.RestoreState();


// View:     imageView21
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:646
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(2316f, 578.5795f);
var vector21Builder = new PathBuilder();
var vector21path = vector21Builder.BuildPath("M2.31458 21.9622C1.93264 21.9622 1.60799 21.7842 1.34063 21.4284C1.07326 21.0725 0.939583 20.6404 0.939583 20.132L0.939583 2.74527L0 2.74527L0 0.915091L4.30833 0.915091L4.30833 0L10.3583 0L10.3583 0.915091L14.6667 0.915091L14.6667 2.74527L13.7271 2.74527L13.7271 20.132C13.7271 20.62 13.5896 21.0471 13.3146 21.4131C13.0396 21.7792 12.7188 21.9622 12.3521 21.9622L2.31458 21.9622ZM12.3521 2.74527L2.31458 2.74527L2.31458 20.132L12.3521 20.132L12.3521 2.74527ZM4.74375 17.5087L6.11875 17.5087L6.11875 5.33803L4.74375 5.33803L4.74375 17.5087ZM8.54792 17.5087L9.92292 17.5087L9.92292 5.33803L8.54792 5.33803L8.54792 17.5087Z");
canvas.FillPath(vector21path);
canvas.RestoreState();


// View:     frameView46
// NodeName: delete_FILL0_wght400_GRAD0_opsz48 2
// NodeType: FRAME
// NodeId:   51:647
canvas.SaveState();
canvas.RestoreState();


// View:     imageView22
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:648
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(2315.6667f, 623.5022f);
var vector22Builder = new PathBuilder();
var vector22path = vector22Builder.BuildPath("M2.31458 21.9622C1.93264 21.9622 1.60799 21.7842 1.34063 21.4284C1.07326 21.0725 0.939583 20.6404 0.939583 20.132L0.939583 2.74527L0 2.74527L0 0.915091L4.30833 0.915091L4.30833 0L10.3583 0L10.3583 0.915091L14.6667 0.915091L14.6667 2.74527L13.7271 2.74527L13.7271 20.132C13.7271 20.62 13.5896 21.0471 13.3146 21.4131C13.0396 21.7792 12.7188 21.9622 12.3521 21.9622L2.31458 21.9622ZM12.3521 2.74527L2.31458 2.74527L2.31458 20.132L12.3521 20.132L12.3521 2.74527ZM4.74375 17.5087L6.11875 17.5087L6.11875 5.33803L4.74375 5.33803L4.74375 17.5087ZM8.54792 17.5087L9.92292 17.5087L9.92292 5.33803L8.54792 5.33803L8.54792 17.5087ZM2.31458 2.74527L2.31458 20.132L2.31458 2.74527Z");
canvas.FillPath(vector22path);
canvas.RestoreState();


// View:     frameView47
// NodeName: delete_FILL0_wght400_GRAD0_opsz48 3
// NodeType: FRAME
// NodeId:   51:649
canvas.SaveState();
canvas.RestoreState();


// View:     imageView23
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:650
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(2315.6667f, 668.75757f);
var vector23Builder = new PathBuilder();
var vector23path = vector23Builder.BuildPath("M2.31458 21.9622C1.93264 21.9622 1.60799 21.7842 1.34063 21.4284C1.07326 21.0725 0.939583 20.6404 0.939583 20.132L0.939583 2.74527L0 2.74527L0 0.915091L4.30833 0.915091L4.30833 0L10.3583 0L10.3583 0.915091L14.6667 0.915091L14.6667 2.74527L13.7271 2.74527L13.7271 20.132C13.7271 20.62 13.5896 21.0471 13.3146 21.4131C13.0396 21.7792 12.7188 21.9622 12.3521 21.9622L2.31458 21.9622ZM12.3521 2.74527L2.31458 2.74527L2.31458 20.132L12.3521 20.132L12.3521 2.74527ZM4.74375 17.5087L6.11875 17.5087L6.11875 5.33803L4.74375 5.33803L4.74375 17.5087ZM8.54792 17.5087L9.92292 17.5087L9.92292 5.33803L8.54792 5.33803L8.54792 17.5087ZM2.31458 2.74527L2.31458 20.132L2.31458 2.74527Z");
canvas.FillPath(vector23path);
canvas.RestoreState();


// View:     frameView48
// NodeName: delete_FILL0_wght400_GRAD0_opsz48 4
// NodeType: FRAME
// NodeId:   51:651
canvas.SaveState();
canvas.RestoreState();


// View:     imageView24
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:652
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(2315.6667f, 714.013f);
var vector24Builder = new PathBuilder();
var vector24path = vector24Builder.BuildPath("M2.31458 21.9622C1.93264 21.9622 1.60799 21.7842 1.34063 21.4284C1.07326 21.0725 0.939583 20.6404 0.939583 20.132L0.939583 2.74527L0 2.74527L0 0.915091L4.30833 0.915091L4.30833 0L10.3583 0L10.3583 0.915091L14.6667 0.915091L14.6667 2.74527L13.7271 2.74527L13.7271 20.132C13.7271 20.62 13.5896 21.0471 13.3146 21.4131C13.0396 21.7792 12.7188 21.9622 12.3521 21.9622L2.31458 21.9622ZM12.3521 2.74527L2.31458 2.74527L2.31458 20.132L12.3521 20.132L12.3521 2.74527ZM4.74375 17.5087L6.11875 17.5087L6.11875 5.33803L4.74375 5.33803L4.74375 17.5087ZM8.54792 17.5087L9.92292 17.5087L9.92292 5.33803L8.54792 5.33803L8.54792 17.5087ZM2.31458 2.74527L2.31458 20.132L2.31458 2.74527Z");
canvas.FillPath(vector24path);
canvas.RestoreState();


// View:     frameView49
// NodeName: Menu
// NodeType: FRAME
// NodeId:   51:653
canvas.SaveState();
canvas.RestoreState();


// View:     frameView50
// NodeName: Measure
// NodeType: GROUP
// NodeId:   51:654
canvas.SaveState();
canvas.RestoreState();


// View:     textView37
// NodeName: Разгон
// NodeType: TEXT
// NodeId:   51:655
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Разгон", 2034f, 1212f, 53f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView25
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:656
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(2043f, 1184f);
var vector25Builder = new PathBuilder();
var vector25path = vector25Builder.BuildPath("M21.35 25.375C24.4417 25.375 27.0521 24.2448 29.1812 21.9844C31.3104 19.724 32.375 17.0333 32.375 13.9125C32.375 10.7042 31.2302 8.02083 28.9406 5.8625C26.651 3.70417 23.9021 2.625 20.6938 2.625C19.0604 2.625 17.5292 2.82188 16.1 3.21563C14.6708 3.60938 13.1687 4.22917 11.5938 5.075L16.8875 7.04375C18.375 7.59792 19.3885 8.39271 19.9281 9.42812C20.4677 10.4635 20.7375 11.6375 20.7375 12.95C20.7375 14.7 20.1542 16.1656 18.9875 17.3469C17.8208 18.5281 16.3917 19.1187 14.7 19.1187L2.8875 19.1187C2.74167 19.7896 2.66146 20.6865 2.64688 21.8094C2.63229 22.9323 2.625 24.1208 2.625 25.375L21.35 25.375ZM3.19375 16.4937L14.4375 16.4937C15.5167 16.4937 16.399 16.151 17.0844 15.4656C17.7698 14.7802 18.1125 13.9417 18.1125 12.95C18.1125 12.1042 17.9156 11.3896 17.5219 10.8063C17.1281 10.2229 16.5521 9.8 15.7937 9.5375L9.05625 6.7375C7.59792 8.02083 6.3875 9.46458 5.425 11.0687C4.4625 12.6729 3.71875 14.4813 3.19375 16.4937L3.19375 16.4937ZM21.35 28L2.625 28C1.925 28 1.3125 27.7375 0.7875 27.2125C0.2625 26.6875 0 26.075 0 25.375L0 22.0938C0 18.9438 0.525 16.0198 1.575 13.3219C2.625 10.624 4.07604 8.29063 5.92812 6.32188C7.78021 4.35313 9.96771 2.80729 12.4906 1.68438C15.0135 0.561459 17.7479 0 20.6938 0C22.6479 0 24.4927 0.357292 26.2281 1.07188C27.9635 1.78646 29.4802 2.77083 30.7781 4.025C32.076 5.27917 33.1042 6.75208 33.8625 8.44375C34.6208 10.1354 35 11.9583 35 13.9125C35 15.8375 34.6427 17.6604 33.9281 19.3813C33.2135 21.1021 32.2365 22.5969 30.9969 23.8656C29.7573 25.1344 28.3135 26.1406 26.6656 26.8844C25.0177 27.6281 23.2458 28 21.35 28Z");
canvas.FillPath(vector25path);
canvas.RestoreState();


// View:     frameView51
// NodeName: Gauges
// NodeType: GROUP
// NodeId:   51:657
canvas.SaveState();
canvas.RestoreState();


// View:     textView38
// NodeName: Приборы
// NodeType: TEXT
// NodeId:   51:658
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Приборы", 2114f, 1212f, 61f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView26
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:659
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(2127f, 1184f);
var vector26Builder = new PathBuilder();
var vector26path = vector26Builder.BuildPath("M14.3917 20.77C15.0665 21.4418 16.0421 21.7559 17.3185 21.7121C18.5948 21.6682 19.5264 21.1935 20.1132 20.288L29.6196 5.43349L14.8758 15.0297C13.9956 15.614 13.5261 16.5488 13.4675 17.8341C13.4088 19.1195 13.7169 20.0981 14.3917 20.77L14.3917 20.77ZM17.5165 0C19.1889 0 20.9347 0.270214 22.7539 0.810642C24.573 1.35107 26.3188 2.27856 27.9912 3.59311L25.7026 5.2144C24.3823 4.33803 22.9666 3.68805 21.4555 3.26448C19.9445 2.8409 18.6315 2.62911 17.5165 2.62911C13.4088 2.62911 9.90255 4.08972 6.9978 7.01095C4.09305 9.93218 2.64068 13.4815 2.64068 17.6588C2.64068 18.9734 2.82406 20.3026 3.19082 21.6463C3.55758 22.9901 4.07838 24.2316 4.75322 25.3709L30.2358 25.3709C30.8813 24.3192 31.3947 23.0923 31.7762 21.6901C32.1576 20.2879 32.3483 18.915 32.3483 17.5712C32.3483 16.3443 32.1649 15.0224 31.7982 13.6056C31.4314 12.1888 30.7786 10.8816 29.8397 9.68388L31.5561 7.40532C32.6711 9.04121 33.5073 10.6844 34.0648 12.3349C34.6222 13.9854 34.9303 15.6432 34.989 17.3083C35.0477 19.061 34.8716 20.7115 34.4609 22.2598C34.0501 23.808 33.4486 25.2394 32.6564 26.554C32.3043 27.2259 31.9302 27.6348 31.5341 27.7809C31.138 27.927 30.6465 28 30.0597 28L4.92927 28C4.43047 28 3.93901 27.8758 3.45489 27.6275C2.97076 27.3792 2.61134 27.0214 2.37661 26.554C1.61375 25.1518 1.02693 23.7277 0.616158 22.2817C0.205386 20.8357 0 19.2947 0 17.6588C0 15.2342 0.462119 12.9484 1.38636 10.8013C2.31059 8.65415 3.56492 6.78456 5.14932 5.19249C6.73373 3.60042 8.58954 2.33698 10.7168 1.40219C12.844 0.467396 15.1106 -1.55674e-15 17.5165 0L17.5165 0Z");
canvas.FillPath(vector26path);
canvas.RestoreState();


// View:     frameView52
// NodeName: History
// NodeType: GROUP
// NodeId:   51:660
canvas.SaveState();
canvas.RestoreState();


// View:     textView39
// NodeName: История
// NodeType: TEXT
// NodeId:   51:661
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"История", 2202f, 1212f, 57f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView27
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:662
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(2214f, 1184f);
var vector27Builder = new PathBuilder();
var vector27path = vector27Builder.BuildPath("M2.40833 28C1.74722 28 1.18056 27.7744 0.708333 27.3233C0.236111 26.8722 8.38835e-16 26.3356 0 25.7133C0 25.06 0.236111 24.5 0.708333 24.0333C1.18056 23.5667 1.74722 23.3333 2.40833 23.3333C3.03796 23.3333 3.58102 23.5667 4.0375 24.0333C4.49398 24.5 4.72222 25.06 4.72222 25.7133C4.72222 26.3356 4.49398 26.8722 4.0375 27.3233C3.58102 27.7744 3.03796 28 2.40833 28ZM9.44444 27.0667L9.44444 24.2667L34 24.2667L34 27.0667L9.44444 27.0667ZM2.40833 16.3333C1.74722 16.3333 1.18056 16.1078 0.708333 15.6567C0.236111 15.2056 8.38835e-16 14.6533 0 14C0 13.3467 0.236111 12.7944 0.708333 12.3433C1.18056 11.8922 1.74722 11.6667 2.40833 11.6667C3.03796 11.6667 3.58102 11.9 4.0375 12.3667C4.49398 12.8333 4.72222 13.3778 4.72222 14C4.72222 14.6222 4.49398 15.1667 4.0375 15.6333C3.58102 16.1 3.03796 16.3333 2.40833 16.3333ZM9.44444 15.4L9.44444 12.6L34 12.6L34 15.4L9.44444 15.4ZM2.36111 4.66667C1.7 4.66667 1.1412 4.44111 0.684722 3.99C0.228241 3.53889 0 2.98667 0 2.33333C0 1.68 0.228241 1.12778 0.684722 0.676667C1.1412 0.225556 1.7 0 2.36111 0C3.02222 0 3.58102 0.225556 4.0375 0.676667C4.49398 1.12778 4.72222 1.68 4.72222 2.33333C4.72222 2.98667 4.49398 3.53889 4.0375 3.99C3.58102 4.44111 3.02222 4.66667 2.36111 4.66667ZM9.44444 3.73333L9.44444 0.933333L34 0.933333L34 3.73333L9.44444 3.73333Z");
canvas.FillPath(vector27path);
canvas.RestoreState();


// View:     frameView53
// NodeName: Settings
// NodeType: GROUP
// NodeId:   51:663
canvas.SaveState();
canvas.RestoreState();


// View:     textView40
// NodeName: Настройки
// NodeType: TEXT
// NodeId:   51:664
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Настройки", 2286f, 1212f, 70f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView28
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:665
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(2307f, 1184f);
var vector28Builder = new PathBuilder();
var vector28path = vector28Builder.BuildPath("M10.78 28L10.08 23.59C9.63667 23.4267 9.17 23.205 8.68 22.925C8.19 22.645 7.75833 22.3533 7.385 22.05L3.255 23.94L0 18.2L3.78 15.435C3.73333 15.225 3.70417 14.9858 3.6925 14.7175C3.68083 14.4492 3.675 14.21 3.675 14C3.675 13.79 3.68083 13.5508 3.6925 13.2825C3.70417 13.0142 3.73333 12.775 3.78 12.565L0 9.8L3.255 4.06L7.385 5.95C7.75833 5.64667 8.19 5.355 8.68 5.075C9.17 4.795 9.63667 4.585 10.08 4.445L10.78 0L17.22 0L17.92 4.41C18.3633 4.57333 18.8358 4.78917 19.3375 5.0575C19.8392 5.32583 20.265 5.62333 20.615 5.95L24.745 4.06L28 9.8L24.22 12.495C24.2667 12.7283 24.2958 12.9792 24.3075 13.2475C24.3192 13.5158 24.325 13.7667 24.325 14C24.325 14.2333 24.3192 14.4783 24.3075 14.735C24.2958 14.9917 24.2667 15.2367 24.22 15.47L28 18.2L24.745 23.94L20.615 22.05C20.2417 22.3533 19.8158 22.6508 19.3375 22.9425C18.8592 23.2342 18.3867 23.45 17.92 23.59L17.22 28L10.78 28ZM14 18.55C15.26 18.55 16.3333 18.1067 17.22 17.22C18.1067 16.3333 18.55 15.26 18.55 14C18.55 12.74 18.1067 11.6667 17.22 10.78C16.3333 9.89333 15.26 9.45 14 9.45C12.74 9.45 11.6667 9.89333 10.78 10.78C9.89333 11.6667 9.45 12.74 9.45 14C9.45 15.26 9.89333 16.3333 10.78 17.22C11.6667 18.1067 12.74 18.55 14 18.55ZM14 16.45C13.3233 16.45 12.7458 16.2108 12.2675 15.7325C11.7892 15.2542 11.55 14.6767 11.55 14C11.55 13.3233 11.7892 12.7458 12.2675 12.2675C12.7458 11.7892 13.3233 11.55 14 11.55C14.6767 11.55 15.2542 11.7892 15.7325 12.2675C16.2108 12.7458 16.45 13.3233 16.45 14C16.45 14.6767 16.2108 15.2542 15.7325 15.7325C15.2542 16.2108 14.6767 16.45 14 16.45ZM12.46 25.9L15.54 25.9L16.03 21.98C16.8 21.7933 17.5292 21.5017 18.2175 21.105C18.9058 20.7083 19.53 20.23 20.09 19.67L23.8 21.28L25.2 18.76L21.91 16.345C22.0033 15.9483 22.0792 15.5575 22.1375 15.1725C22.1958 14.7875 22.225 14.3967 22.225 14C22.225 13.6033 22.2017 13.2125 22.155 12.8275C22.1083 12.4425 22.0267 12.0517 21.91 11.655L25.2 9.24L23.8 6.72L20.09 8.33C19.5533 7.72333 18.9467 7.21583 18.27 6.8075C17.5933 6.39917 16.8467 6.13667 16.03 6.02L15.54 2.1L12.46 2.1L11.97 6.02C11.1767 6.18333 10.4358 6.46333 9.7475 6.86C9.05917 7.25667 8.44667 7.74667 7.91 8.33L4.2 6.72L2.8 9.24L6.09 11.655C5.99667 12.0517 5.92083 12.4425 5.8625 12.8275C5.80417 13.2125 5.775 13.6033 5.775 14C5.775 14.3967 5.80417 14.7875 5.8625 15.1725C5.92083 15.5575 5.99667 15.9483 6.09 16.345L2.8 18.76L4.2 21.28L7.91 19.67C8.47 20.23 9.09417 20.7083 9.7825 21.105C10.4708 21.5017 11.2 21.7933 11.97 21.98L12.46 25.9Z");
canvas.FillPath(vector28path);
canvas.RestoreState();


// View:     frameView54
// NodeName: Record Screen
// NodeType: FRAME
// NodeId:   9:656
canvas.SaveState();
canvas.RestoreState();


// View:     frameView55
// NodeName: iPhone 11 Pro
// NodeType: COMPONENT
// NodeId:   8:538
canvas.SaveState();
canvas.RestoreState();


// View:     frameView56
// NodeName: iPhone 11 Pro
// NodeType: INSTANCE
// NodeId:   9:657
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(52, 60, 69, 255)) ,new PaintGradientStop(1, new Color(17, 22, 29, 255)) ,}}, new RectF(1143f, -458f, 375f, 812f));
canvas.FillRoundedRectangle(1143f, -458f, 375f, 812f, 55f);
canvas.RestoreState();


// View:     rectangleView4
// NodeName: Results screen
// NodeType: RECTANGLE
// NodeId:   8:552
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(35, 35, 35, 255)) ,new PaintGradientStop(1, new Color(0, 0, 0, 255)) ,}}, new RectF(1169f, -398f, 324f, 647f));
canvas.FillRoundedRectangle(1169f, -398f, 324f, 647f, 25f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(21, 25, 30, 255)) ,new PaintGradientStop(1, new Color(66, 70, 75, 255)) ,}}, new RectF(1169f, -398f, 324f, 647f));
canvas.StrokeSize  = 2;
canvas.DrawRoundedRectangle(1169f, -398f, 324f, 647f, 25f);
canvas.RestoreState();


// View:     frameView57
// NodeName: Map
// NodeType: GROUP
// NodeId:   9:655
canvas.SaveState();
canvas.RestoreState();


// View:     rectangleView5
// NodeName: Map
// NodeType: RECTANGLE
// NodeId:   9:644
canvas.SaveState();
canvas.FillColor  = Colors.White;
canvas.FillRoundedRectangle(1190f, 70f, 285f, 175.99998f, 18f);
canvas.RestoreState();


// View:     frameView58
// NodeName: Other Results
// NodeType: GROUP
// NodeId:   10:804
canvas.SaveState();
canvas.RestoreState();


// View:     lineView11
// NodeName: Line 2
// NodeType: LINE
// NodeId:   10:802
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(1189.9991, -19.4915), new Point(1472.7272, -19.4915));
canvas.RestoreState();


// View:     imageView29
// NodeName: Line 16
// NodeType: VECTOR
// NodeId:   10:801

// View:     frameView59
// NodeName: Group 17
// NodeType: GROUP
// NodeId:   10:789
canvas.SaveState();
canvas.RestoreState();


// View:     textView41
// NodeName: 164.5 км/ч
// NodeType: TEXT
// NodeId:   10:790
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 24f;
canvas.DrawString(@"164.5 км/ч", 1190f, -79f, 136f, 22.554043f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView42
// NodeName: Макс. скор.
// NodeType: TEXT
// NodeId:   10:791
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Макс. скор.", 1195f, -46.166466f, 126f, 13.999062f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView60
// NodeName: Group 19
// NodeType: GROUP
// NodeId:   10:795
canvas.SaveState();
canvas.RestoreState();


// View:     textView43
// NodeName: 2.5%
// NodeType: TEXT
// NodeId:   10:796
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 24f;
canvas.DrawString(@"2.5%", 1190f, -10f, 136f, 22.554043f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView44
// NodeName: Уклон дороги
// NodeType: TEXT
// NodeId:   10:797
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Уклон дороги", 1195f, 22.833534f, 126f, 13.999062f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView61
// NodeName: Group 18
// NodeType: GROUP
// NodeId:   10:792
canvas.SaveState();
canvas.RestoreState();


// View:     textView45
// NodeName: 0.73G
// NodeType: TEXT
// NodeId:   10:793
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 24f;
canvas.DrawString(@"0.73G", 1337f, -79f, 136f, 22.554043f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView46
// NodeName: Макс. ускор.
// NodeType: TEXT
// NodeId:   10:794
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Макс. ускор.", 1342f, -46.166466f, 126f, 13.999062f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView62
// NodeName: Group 20
// NodeType: GROUP
// NodeId:   10:798
canvas.SaveState();
canvas.RestoreState();


// View:     textView47
// NodeName: Выкл
// NodeType: TEXT
// NodeId:   10:799
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 24f;
canvas.DrawString(@"Выкл", 1337f, -10f, 136f, 22.554043f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView48
// NodeName: Ролл-аут
// NodeType: TEXT
// NodeId:   10:800
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Ролл-аут", 1342f, 22.833534f, 126f, 13.999062f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     lineView12
// NodeName: Line 17
// NodeType: LINE
// NodeId:   10:803
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(1331, -84), new Point(1331, 45));
canvas.RestoreState();


// View:     frameView63
// NodeName: Time Results
// NodeType: GROUP
// NodeId:   9:592
canvas.SaveState();
canvas.RestoreState();


// View:     frameView64
// NodeName: Group 8
// NodeType: GROUP
// NodeId:   9:609
canvas.SaveState();
canvas.RestoreState();


// View:     textView49
// NodeName: 402 м
// NodeType: TEXT
// NodeId:   9:610
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"402 м", 1190f, -122f, 122.789795f, 24f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView65
// NodeName: Group 20
// NodeType: GROUP
// NodeId:   9:642
canvas.SaveState();
canvas.RestoreState();


// View:     textView50
// NodeName: 17.42 с @ 162 км/ч
// NodeType: TEXT
// NodeId:   9:611
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"17.42 с @ 162 км/ч", 1313f, -122f, 160f, 24f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView66
// NodeName: Group 6
// NodeType: GROUP
// NodeId:   9:601
canvas.SaveState();
canvas.RestoreState();


// View:     textView51
// NodeName: 201 м
// NodeType: TEXT
// NodeId:   9:602
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"201 м", 1190.0009f, -152f, 122.789795f, 24f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView52
// NodeName: 10.71 с @ 106 км/ч
// NodeType: TEXT
// NodeId:   9:603
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"10.71 с @ 106 км/ч", 1313f, -152f, 160f, 24f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     lineView13
// NodeName: Line 1
// NodeType: LINE
// NodeId:   9:604
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(1190, -124.49147), new Point(1472.728, -124.49147));
canvas.RestoreState();


// View:     frameView67
// NodeName: Group 9
// NodeType: GROUP
// NodeId:   9:613
canvas.SaveState();
canvas.RestoreState();


// View:     textView53
// NodeName: 0-200 км/ч
// NodeType: TEXT
// NodeId:   9:614
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"0-200 км/ч", 1190.0009f, -183f, 122.789795f, 24f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView54
// NodeName: 23.56 с
// NodeType: TEXT
// NodeId:   9:615
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"23.56 с", 1349.937f, -183f, 122.789795f, 24f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     lineView14
// NodeName: Line 1
// NodeType: LINE
// NodeId:   9:612
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(1190, -154.49147), new Point(1472.728, -154.49147));
canvas.RestoreState();


// View:     frameView68
// NodeName: Group 7
// NodeType: GROUP
// NodeId:   9:605
canvas.SaveState();
canvas.RestoreState();


// View:     textView55
// NodeName: 0-150 км/ч
// NodeType: TEXT
// NodeId:   9:606
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"0-150 км/ч", 1190f, -213f, 122.789795f, 24f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView56
// NodeName: 15.89 с
// NodeType: TEXT
// NodeId:   9:607
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"15.89 с", 1349.9363f, -213f, 122.789795f, 24f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     lineView15
// NodeName: Line 1
// NodeType: LINE
// NodeId:   9:608
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(1189.9991, -185.49147), new Point(1472.7272, -185.49147));
canvas.RestoreState();


// View:     frameView69
// NodeName: Group 5
// NodeType: GROUP
// NodeId:   9:597
canvas.SaveState();
canvas.RestoreState();


// View:     textView57
// NodeName: 0-100 км/ч
// NodeType: TEXT
// NodeId:   9:598
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"0-100 км/ч", 1190f, -243f, 122.789795f, 24f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView58
// NodeName: 9.64 с
// NodeType: TEXT
// NodeId:   9:599
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"9.64 с", 1349.9363f, -243f, 122.789795f, 24f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     lineView16
// NodeName: Line 1
// NodeType: LINE
// NodeId:   9:600
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(1189.9991, -215.49147), new Point(1472.7272, -215.49147));
canvas.RestoreState();


// View:     frameView70
// NodeName: Group 4
// NodeType: GROUP
// NodeId:   9:593
canvas.SaveState();
canvas.RestoreState();


// View:     textView59
// NodeName: 0-60 км/ч
// NodeType: TEXT
// NodeId:   9:594
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"0-60 км/ч", 1190f, -273f, 122.789795f, 24f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView60
// NodeName: 4.63 с
// NodeType: TEXT
// NodeId:   9:595
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"4.63 с", 1349.9363f, -273f, 122.789795f, 24f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     lineView17
// NodeName: Line 1
// NodeType: LINE
// NodeId:   9:596
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(1189.9991, -245.49147), new Point(1472.7272, -245.49147));
canvas.RestoreState();


// View:     imageView30
// NodeName: Line 16
// NodeType: VECTOR
// NodeId:   10:788

// View:     frameView71
// NodeName: Result Header
// NodeType: GROUP
// NodeId:   9:654
canvas.SaveState();
canvas.RestoreState();


// View:     frameView72
// NodeName: Group 8
// NodeType: GROUP
// NodeId:   9:635
canvas.SaveState();
canvas.RestoreState();


// View:     textView61
// NodeName: Статус замера
// NodeType: TEXT
// NodeId:   9:636
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Статус замера", 1326f, -314f, 124f, 24f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView62
// NodeName: ✅
// NodeType: TEXT
// NodeId:   9:637
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"✅", 1453f, -313f, 20f, 24f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView63
// NodeName: Замер #147
// NodeType: TEXT
// NodeId:   8:553
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"Замер #147", 1355f, -386f, 118f, 28f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView64
// NodeName: 16/09/2022 22:35:17
// NodeType: TEXT
// NodeId:   8:554
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"16/09/2022  22:35:17", 1306f, -358f, 167f, 22f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView65
// NodeName: Ford Focus 2
// NodeType: TEXT
// NodeId:   9:643
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Ford Focus 2", 1372f, -336f, 101f, 22f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView73
// NodeName: Share Button
// NodeType: GROUP
// NodeId:   9:647
canvas.SaveState();
canvas.RestoreState();


// View:     rectangleView6
// NodeName: Rectangle 1
// NodeType: RECTANGLE
// NodeId:   9:648
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(146, 67, 33, 255)) ,new PaintGradientStop(1, new Color(228, 127, 83, 255)) ,}}, new RectF(1206f, -356f, 45f, 45f));
canvas.FillRoundedRectangle(1206f, -356f, 45f, 45f, 31f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(229, 120, 73, 255)) ,new PaintGradientStop(1, new Color(184, 79, 33, 255)) ,}}, new RectF(1206f, -356f, 45f, 45f));
canvas.StrokeSize  = 2;
canvas.DrawRoundedRectangle(1206f, -356f, 45f, 45f, 31f);
canvas.RestoreState();


// View:     frameView74
// NodeName: ios_share_FILL0_wght400_GRAD0_opsz48 1
// NodeType: FRAME
// NodeId:   9:650
canvas.SaveState();
canvas.RestoreState();


// View:     imageView31
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   9:651
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(1220.817f, -345.1616f);
var vector29Builder = new PathBuilder();
var vector29path = vector29Builder.BuildPath("M1.44055 21.128C1.0564 21.128 0.720274 20.984 0.432164 20.6959C0.144055 20.4078 0 20.0716 0 19.6875L0 7.46684C0 7.0827 0.144055 6.74657 0.432164 6.45846C0.720274 6.17035 1.0564 6.0263 1.44055 6.0263L5.49809 6.0263L5.49809 7.46684L1.44055 7.46684L1.44055 19.6875L13.9253 19.6875L13.9253 7.46684L9.81974 7.46684L9.81974 6.0263L13.9253 6.0263C14.3095 6.0263 14.6456 6.17035 14.9337 6.45846C15.2218 6.74657 15.3659 7.0827 15.3659 7.46684L15.3659 19.6875C15.3659 20.0716 15.2218 20.4078 14.9337 20.6959C14.6456 20.984 14.3095 21.128 13.9253 21.128L1.44055 21.128ZM6.93864 13.7572L6.93864 2.78506L4.82584 4.89787L3.79344 3.86547L7.65892 0L11.5244 3.86547L10.492 4.89787L8.37919 2.78506L8.37919 13.7572L6.93864 13.7572Z");
canvas.FillPath(vector29path);
canvas.RestoreState();


// View:     imageView32
// NodeName: Line 16
// NodeType: VECTOR
// NodeId:   8:556

// View:     frameView75
// NodeName: Menu
// NodeType: FRAME
// NodeId:   10:762
canvas.SaveState();
canvas.RestoreState();


// View:     frameView76
// NodeName: Measure
// NodeType: GROUP
// NodeId:   10:763
canvas.SaveState();
canvas.RestoreState();


// View:     textView66
// NodeName: Разгон
// NodeType: TEXT
// NodeId:   10:764
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Разгон", 1168f, 302f, 53f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView33
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   10:765
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(1177f, 274f);
var vector30Builder = new PathBuilder();
var vector30path = vector30Builder.BuildPath("M21.35 25.375C24.4417 25.375 27.0521 24.2448 29.1812 21.9844C31.3104 19.724 32.375 17.0333 32.375 13.9125C32.375 10.7042 31.2302 8.02083 28.9406 5.8625C26.651 3.70417 23.9021 2.625 20.6938 2.625C19.0604 2.625 17.5292 2.82188 16.1 3.21563C14.6708 3.60938 13.1687 4.22917 11.5938 5.075L16.8875 7.04375C18.375 7.59792 19.3885 8.39271 19.9281 9.42812C20.4677 10.4635 20.7375 11.6375 20.7375 12.95C20.7375 14.7 20.1542 16.1656 18.9875 17.3469C17.8208 18.5281 16.3917 19.1187 14.7 19.1187L2.8875 19.1187C2.74167 19.7896 2.66146 20.6865 2.64688 21.8094C2.63229 22.9323 2.625 24.1208 2.625 25.375L21.35 25.375ZM3.19375 16.4937L14.4375 16.4937C15.5167 16.4937 16.399 16.151 17.0844 15.4656C17.7698 14.7802 18.1125 13.9417 18.1125 12.95C18.1125 12.1042 17.9156 11.3896 17.5219 10.8063C17.1281 10.2229 16.5521 9.8 15.7937 9.5375L9.05625 6.7375C7.59792 8.02083 6.3875 9.46458 5.425 11.0687C4.4625 12.6729 3.71875 14.4813 3.19375 16.4937L3.19375 16.4937ZM21.35 28L2.625 28C1.925 28 1.3125 27.7375 0.7875 27.2125C0.2625 26.6875 0 26.075 0 25.375L0 22.0938C0 18.9438 0.525 16.0198 1.575 13.3219C2.625 10.624 4.07604 8.29063 5.92812 6.32188C7.78021 4.35313 9.96771 2.80729 12.4906 1.68438C15.0135 0.561459 17.7479 0 20.6938 0C22.6479 0 24.4927 0.357292 26.2281 1.07188C27.9635 1.78646 29.4802 2.77083 30.7781 4.025C32.076 5.27917 33.1042 6.75208 33.8625 8.44375C34.6208 10.1354 35 11.9583 35 13.9125C35 15.8375 34.6427 17.6604 33.9281 19.3813C33.2135 21.1021 32.2365 22.5969 30.9969 23.8656C29.7573 25.1344 28.3135 26.1406 26.6656 26.8844C25.0177 27.6281 23.2458 28 21.35 28Z");
canvas.FillPath(vector30path);
canvas.RestoreState();


// View:     frameView77
// NodeName: Gauges
// NodeType: GROUP
// NodeId:   10:766
canvas.SaveState();
canvas.RestoreState();


// View:     textView67
// NodeName: Приборы
// NodeType: TEXT
// NodeId:   10:767
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Приборы", 1248f, 302f, 61f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView34
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   10:768
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(1261f, 274f);
var vector31Builder = new PathBuilder();
var vector31path = vector31Builder.BuildPath("M14.3917 20.77C15.0665 21.4418 16.0421 21.7559 17.3185 21.7121C18.5948 21.6682 19.5264 21.1935 20.1132 20.288L29.6196 5.43349L14.8758 15.0297C13.9956 15.614 13.5261 16.5488 13.4675 17.8341C13.4088 19.1195 13.7169 20.0981 14.3917 20.77L14.3917 20.77ZM17.5165 0C19.1889 0 20.9347 0.270214 22.7539 0.810642C24.573 1.35107 26.3188 2.27856 27.9912 3.59311L25.7026 5.2144C24.3823 4.33803 22.9666 3.68805 21.4555 3.26448C19.9445 2.8409 18.6315 2.62911 17.5165 2.62911C13.4088 2.62911 9.90255 4.08972 6.9978 7.01095C4.09305 9.93218 2.64068 13.4815 2.64068 17.6588C2.64068 18.9734 2.82406 20.3026 3.19082 21.6463C3.55758 22.9901 4.07838 24.2316 4.75322 25.3709L30.2358 25.3709C30.8813 24.3192 31.3947 23.0923 31.7762 21.6901C32.1576 20.2879 32.3483 18.915 32.3483 17.5712C32.3483 16.3443 32.1649 15.0224 31.7982 13.6056C31.4314 12.1888 30.7786 10.8816 29.8397 9.68388L31.5561 7.40532C32.6711 9.04121 33.5073 10.6844 34.0648 12.3349C34.6222 13.9854 34.9303 15.6432 34.989 17.3083C35.0477 19.061 34.8716 20.7115 34.4609 22.2598C34.0501 23.808 33.4486 25.2394 32.6564 26.554C32.3043 27.2259 31.9302 27.6348 31.5341 27.7809C31.138 27.927 30.6465 28 30.0597 28L4.92927 28C4.43047 28 3.93901 27.8758 3.45489 27.6275C2.97076 27.3792 2.61134 27.0214 2.37661 26.554C1.61375 25.1518 1.02693 23.7277 0.616158 22.2817C0.205386 20.8357 0 19.2947 0 17.6588C0 15.2342 0.462119 12.9484 1.38636 10.8013C2.31059 8.65415 3.56492 6.78456 5.14932 5.19249C6.73373 3.60042 8.58954 2.33698 10.7168 1.40219C12.844 0.467396 15.1106 -1.55674e-15 17.5165 0L17.5165 0Z");
canvas.FillPath(vector31path);
canvas.RestoreState();


// View:     frameView78
// NodeName: History
// NodeType: GROUP
// NodeId:   10:769
canvas.SaveState();
canvas.RestoreState();


// View:     textView68
// NodeName: История
// NodeType: TEXT
// NodeId:   10:770
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"История", 1336f, 302f, 57f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView35
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   10:771
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(1348f, 274f);
var vector32Builder = new PathBuilder();
var vector32path = vector32Builder.BuildPath("M2.40833 28C1.74722 28 1.18056 27.7744 0.708333 27.3233C0.236111 26.8722 8.38835e-16 26.3356 0 25.7133C0 25.06 0.236111 24.5 0.708333 24.0333C1.18056 23.5667 1.74722 23.3333 2.40833 23.3333C3.03796 23.3333 3.58102 23.5667 4.0375 24.0333C4.49398 24.5 4.72222 25.06 4.72222 25.7133C4.72222 26.3356 4.49398 26.8722 4.0375 27.3233C3.58102 27.7744 3.03796 28 2.40833 28ZM9.44444 27.0667L9.44444 24.2667L34 24.2667L34 27.0667L9.44444 27.0667ZM2.40833 16.3333C1.74722 16.3333 1.18056 16.1078 0.708333 15.6567C0.236111 15.2056 8.38835e-16 14.6533 0 14C0 13.3467 0.236111 12.7944 0.708333 12.3433C1.18056 11.8922 1.74722 11.6667 2.40833 11.6667C3.03796 11.6667 3.58102 11.9 4.0375 12.3667C4.49398 12.8333 4.72222 13.3778 4.72222 14C4.72222 14.6222 4.49398 15.1667 4.0375 15.6333C3.58102 16.1 3.03796 16.3333 2.40833 16.3333ZM9.44444 15.4L9.44444 12.6L34 12.6L34 15.4L9.44444 15.4ZM2.36111 4.66667C1.7 4.66667 1.1412 4.44111 0.684722 3.99C0.228241 3.53889 0 2.98667 0 2.33333C0 1.68 0.228241 1.12778 0.684722 0.676667C1.1412 0.225556 1.7 0 2.36111 0C3.02222 0 3.58102 0.225556 4.0375 0.676667C4.49398 1.12778 4.72222 1.68 4.72222 2.33333C4.72222 2.98667 4.49398 3.53889 4.0375 3.99C3.58102 4.44111 3.02222 4.66667 2.36111 4.66667ZM9.44444 3.73333L9.44444 0.933333L34 0.933333L34 3.73333L9.44444 3.73333Z");
canvas.FillPath(vector32path);
canvas.RestoreState();


// View:     frameView79
// NodeName: Settings
// NodeType: GROUP
// NodeId:   10:772
canvas.SaveState();
canvas.RestoreState();


// View:     textView69
// NodeName: Настройки
// NodeType: TEXT
// NodeId:   10:773
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Настройки", 1420f, 302f, 70f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView36
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   10:774
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(1441f, 274f);
var vector33Builder = new PathBuilder();
var vector33path = vector33Builder.BuildPath("M10.78 28L10.08 23.59C9.63667 23.4267 9.17 23.205 8.68 22.925C8.19 22.645 7.75833 22.3533 7.385 22.05L3.255 23.94L0 18.2L3.78 15.435C3.73333 15.225 3.70417 14.9858 3.6925 14.7175C3.68083 14.4492 3.675 14.21 3.675 14C3.675 13.79 3.68083 13.5508 3.6925 13.2825C3.70417 13.0142 3.73333 12.775 3.78 12.565L0 9.8L3.255 4.06L7.385 5.95C7.75833 5.64667 8.19 5.355 8.68 5.075C9.17 4.795 9.63667 4.585 10.08 4.445L10.78 0L17.22 0L17.92 4.41C18.3633 4.57333 18.8358 4.78917 19.3375 5.0575C19.8392 5.32583 20.265 5.62333 20.615 5.95L24.745 4.06L28 9.8L24.22 12.495C24.2667 12.7283 24.2958 12.9792 24.3075 13.2475C24.3192 13.5158 24.325 13.7667 24.325 14C24.325 14.2333 24.3192 14.4783 24.3075 14.735C24.2958 14.9917 24.2667 15.2367 24.22 15.47L28 18.2L24.745 23.94L20.615 22.05C20.2417 22.3533 19.8158 22.6508 19.3375 22.9425C18.8592 23.2342 18.3867 23.45 17.92 23.59L17.22 28L10.78 28ZM14 18.55C15.26 18.55 16.3333 18.1067 17.22 17.22C18.1067 16.3333 18.55 15.26 18.55 14C18.55 12.74 18.1067 11.6667 17.22 10.78C16.3333 9.89333 15.26 9.45 14 9.45C12.74 9.45 11.6667 9.89333 10.78 10.78C9.89333 11.6667 9.45 12.74 9.45 14C9.45 15.26 9.89333 16.3333 10.78 17.22C11.6667 18.1067 12.74 18.55 14 18.55ZM14 16.45C13.3233 16.45 12.7458 16.2108 12.2675 15.7325C11.7892 15.2542 11.55 14.6767 11.55 14C11.55 13.3233 11.7892 12.7458 12.2675 12.2675C12.7458 11.7892 13.3233 11.55 14 11.55C14.6767 11.55 15.2542 11.7892 15.7325 12.2675C16.2108 12.7458 16.45 13.3233 16.45 14C16.45 14.6767 16.2108 15.2542 15.7325 15.7325C15.2542 16.2108 14.6767 16.45 14 16.45ZM12.46 25.9L15.54 25.9L16.03 21.98C16.8 21.7933 17.5292 21.5017 18.2175 21.105C18.9058 20.7083 19.53 20.23 20.09 19.67L23.8 21.28L25.2 18.76L21.91 16.345C22.0033 15.9483 22.0792 15.5575 22.1375 15.1725C22.1958 14.7875 22.225 14.3967 22.225 14C22.225 13.6033 22.2017 13.2125 22.155 12.8275C22.1083 12.4425 22.0267 12.0517 21.91 11.655L25.2 9.24L23.8 6.72L20.09 8.33C19.5533 7.72333 18.9467 7.21583 18.27 6.8075C17.5933 6.39917 16.8467 6.13667 16.03 6.02L15.54 2.1L12.46 2.1L11.97 6.02C11.1767 6.18333 10.4358 6.46333 9.7475 6.86C9.05917 7.25667 8.44667 7.74667 7.91 8.33L4.2 6.72L2.8 9.24L6.09 11.655C5.99667 12.0517 5.92083 12.4425 5.8625 12.8275C5.80417 13.2125 5.775 13.6033 5.775 14C5.775 14.3967 5.80417 14.7875 5.8625 15.1725C5.92083 15.5575 5.99667 15.9483 6.09 16.345L2.8 18.76L4.2 21.28L7.91 19.67C8.47 20.23 9.09417 20.7083 9.7825 21.105C10.4708 21.5017 11.2 21.7933 11.97 21.98L12.46 25.9Z");
canvas.FillPath(vector33path);
canvas.RestoreState();


// View:     frameView80
// NodeName: History Screen
// NodeType: FRAME
// NodeId:   8:536
canvas.SaveState();
canvas.RestoreState();


// View:     frameView81
// NodeName: iPhone 11 Pro
// NodeType: COMPONENT
// NodeId:   8:455
canvas.SaveState();
canvas.RestoreState();


// View:     frameView82
// NodeName: iPhone 11 Pro
// NodeType: INSTANCE
// NodeId:   8:537
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(52, 60, 69, 255)) ,new PaintGradientStop(1, new Color(17, 22, 29, 255)) ,}}, new RectF(692f, -458f, 375f, 812f));
canvas.FillRoundedRectangle(692f, -458f, 375f, 812f, 55f);
canvas.RestoreState();


// View:     frameView83
// NodeName: Results
// NodeType: GROUP
// NodeId:   31:10
canvas.SaveState();
canvas.RestoreState();


// View:     rectangleView7
// NodeName: Results screen
// NodeType: RECTANGLE
// NodeId:   8:469
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(35, 35, 35, 255)) ,new PaintGradientStop(1, new Color(0, 0, 0, 255)) ,}}, new RectF(718f, -398f, 324f, 647f));
canvas.FillRoundedRectangle(718f, -398f, 324f, 647f, 25f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(21, 25, 30, 255)) ,new PaintGradientStop(1, new Color(66, 70, 75, 255)) ,}}, new RectF(718f, -398f, 324f, 647f));
canvas.StrokeSize  = 2;
canvas.DrawRoundedRectangle(718f, -398f, 324f, 647f, 25f);
canvas.RestoreState();


// View:     frameView84
// NodeName: Frame 1
// NodeType: FRAME
// NodeId:   8:471
canvas.SaveState();
canvas.RestoreState();


// View:     imageView37
// NodeName: Line 16
// NodeType: VECTOR
// NodeId:   8:472

// View:     textView70
// NodeName: #147
// NodeType: TEXT
// NodeId:   8:473
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"#147", 973f, -385f, 47f, 28f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView71
// NodeName: 22:35:17
// NodeType: TEXT
// NodeId:   8:474
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"22:35:17", 964f, -313f, 56f, 18f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView72
// NodeName: 16/09/2022
// NodeType: TEXT
// NodeId:   8:475
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"16/09/2022", 935f, -332f, 85f, 17f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView73
// NodeName: Ford Focus 2
// NodeType: TEXT
// NodeId:   51:666
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"Ford Focus 2", 931f, -352f, 89f, 18f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView74
// NodeName: 0-60 км/ч: 4.63 с
// NodeType: TEXT
// NodeId:   8:476
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"0-60 км/ч: 4.63 с", 738f, -381f, 124f, 20f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView75
// NodeName: 0-100 км/ч: 9.64 с
// NodeType: TEXT
// NodeId:   8:477
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"0-100 км/ч: 9.64 с", 738f, -359f, 130f, 20f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView76
// NodeName: 201 м: 10.71 с
// NodeType: TEXT
// NodeId:   8:478
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"201 м: 10.71 с", 738f, -337f, 88f, 20f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView77
// NodeName: 0-150 км/ч: 15.89 с
// NodeType: TEXT
// NodeId:   8:479
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"0-150 км/ч: 15.89 с", 738f, -315f, 135f, 20f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView85
// NodeName: Frame 4
// NodeType: FRAME
// NodeId:   8:509
canvas.SaveState();
canvas.RestoreState();


// View:     imageView38
// NodeName: Line 17
// NodeType: VECTOR
// NodeId:   51:677

// View:     textView78
// NodeName: #144
// NodeType: TEXT
// NodeId:   51:678
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"#144", 970f, -43f, 50f, 28f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView79
// NodeName: 17:54:55
// NodeType: TEXT
// NodeId:   51:679
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"17:54:55", 949f, 29f, 71f, 18f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView80
// NodeName: 10/09/2022
// NodeType: TEXT
// NodeId:   51:680
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"10/09/2022", 935f, 10f, 85f, 17f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView81
// NodeName: Subaru Forester
// NodeType: TEXT
// NodeId:   51:681
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"Subaru Forester", 898f, -10f, 122f, 18f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView82
// NodeName: 0-60 км/ч: 4.63 с
// NodeType: TEXT
// NodeId:   51:682
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"0-60 км/ч: 4.63 с", 738f, -39f, 124f, 20f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView83
// NodeName: 0-100 км/ч: 9.64 с
// NodeType: TEXT
// NodeId:   51:683
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"0-100 км/ч: 9.64 с", 738f, -17f, 130f, 20f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView84
// NodeName: 201 м: 10.71 с
// NodeType: TEXT
// NodeId:   51:684
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"201 м: 10.71 с", 738f, 5f, 88f, 20f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView85
// NodeName: 0-150 км/ч: 15.89 с
// NodeType: TEXT
// NodeId:   51:685
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"0-150 км/ч: 15.89 с", 738f, 27f, 135f, 20f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView86
// NodeName: Frame 5
// NodeType: FRAME
// NodeId:   8:527
canvas.SaveState();
canvas.RestoreState();


// View:     imageView39
// NodeName: Line 17
// NodeType: VECTOR
// NodeId:   51:686

// View:     textView86
// NodeName: #143
// NodeType: TEXT
// NodeId:   51:687
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"#143", 971f, 72f, 49f, 28f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView87
// NodeName: 14:01:54
// NodeType: TEXT
// NodeId:   51:688
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"14:01:54", 953f, 144f, 67f, 18f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView88
// NodeName: 03/09/2022
// NodeType: TEXT
// NodeId:   51:689
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"03/09/2022", 922f, 125f, 98f, 17f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView89
// NodeName: Неизвестный
// NodeType: TEXT
// NodeId:   51:690
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"Неизвестный", 903f, 105f, 117f, 18f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView90
// NodeName: 0-60 км/ч: 4.63 с
// NodeType: TEXT
// NodeId:   51:691
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"0-60 км/ч: 4.63 с", 738f, 76f, 124f, 20f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView91
// NodeName: 0-100 км/ч: 9.64 с
// NodeType: TEXT
// NodeId:   51:692
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"0-100 км/ч: 9.64 с", 738f, 98f, 130f, 20f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView92
// NodeName: 201 м: 10.71 с
// NodeType: TEXT
// NodeId:   51:693
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"201 м: 10.71 с", 738f, 120f, 88f, 20f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView93
// NodeName: 0-150 км/ч: 15.89 с
// NodeType: TEXT
// NodeId:   51:694
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"0-150 км/ч: 15.89 с", 738f, 142f, 135f, 20f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView87
// NodeName: Frame 3
// NodeType: FRAME
// NodeId:   8:500
canvas.SaveState();
canvas.RestoreState();


// View:     imageView40
// NodeName: Line 17
// NodeType: VECTOR
// NodeId:   51:695

// View:     textView94
// NodeName: #145
// NodeType: TEXT
// NodeId:   51:696
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"#145", 971f, -157f, 49f, 28f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView95
// NodeName: 09:45:57
// NodeType: TEXT
// NodeId:   51:697
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"09:45:57", 948f, -85f, 72f, 18f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView96
// NodeName: 15/09/2022
// NodeType: TEXT
// NodeId:   51:698
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"15/09/2022", 935f, -104f, 85f, 17f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView97
// NodeName: Ford Focus 2
// NodeType: TEXT
// NodeId:   51:699
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"Ford Focus 2", 931f, -124f, 89f, 18f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView98
// NodeName: 0-60 км/ч: 4.63 с
// NodeType: TEXT
// NodeId:   51:700
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"0-60 км/ч: 4.63 с", 738f, -153f, 124f, 20f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView99
// NodeName: 0-100 км/ч: 9.64 с
// NodeType: TEXT
// NodeId:   51:701
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"0-100 км/ч: 9.64 с", 738f, -131f, 130f, 20f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView100
// NodeName: 201 м: 10.71 с
// NodeType: TEXT
// NodeId:   51:702
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"201 м: 10.71 с", 738f, -109f, 88f, 20f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView101
// NodeName: 0-150 км/ч: 15.89 с
// NodeType: TEXT
// NodeId:   51:703
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"0-150 км/ч: 15.89 с", 738f, -87f, 135f, 20f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView88
// NodeName: Frame 2
// NodeType: FRAME
// NodeId:   8:491
canvas.SaveState();
canvas.RestoreState();


// View:     imageView41
// NodeName: Line 16
// NodeType: VECTOR
// NodeId:   51:704

// View:     textView102
// NodeName: #146
// NodeType: TEXT
// NodeId:   51:705
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"#146", 969f, -271f, 51f, 28f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView103
// NodeName: 12:32:09
// NodeType: TEXT
// NodeId:   51:706
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"12:32:09", 949f, -199f, 71f, 18f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView104
// NodeName: 15/09/2022
// NodeType: TEXT
// NodeId:   51:707
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"15/09/2022", 935f, -218f, 85f, 17f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView105
// NodeName: Lada Granta
// NodeType: TEXT
// NodeId:   51:708
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"Lada  Granta", 911f, -238f, 109f, 18f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView106
// NodeName: 0-60 км/ч: 4.63 с
// NodeType: TEXT
// NodeId:   51:709
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"0-60 км/ч: 4.63 с", 738f, -267f, 124f, 20f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView107
// NodeName: 0-100 км/ч: 9.64 с
// NodeType: TEXT
// NodeId:   51:710
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"0-100 км/ч: 9.64 с", 738f, -245f, 130f, 20f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView108
// NodeName: 201 м: 10.71 с
// NodeType: TEXT
// NodeId:   51:711
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"201 м: 10.71 с", 738f, -223f, 88f, 20f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView109
// NodeName: 0-150 км/ч: 15.89 с
// NodeType: TEXT
// NodeId:   51:712
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"0-150 км/ч: 15.89 с", 738f, -201f, 135f, 20f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView89
// NodeName: Group 26
// NodeType: GROUP
// NodeId:   31:9
canvas.SaveState();
canvas.RestoreState();


// View:     frameView90
// NodeName: filter_alt_FILL0_wght400_GRAD0_opsz48 1
// NodeType: FRAME
// NodeId:   31:5
canvas.SaveState();
canvas.RestoreState();


// View:     imageView42
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   31:6
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(890.64166f, 195f);
var vector34Builder = new PathBuilder();
var vector34path = vector34Builder.BuildPath("M14.3583 32C13.7917 32 13.3167 31.8083 12.9333 31.425C12.55 31.0417 12.3583 30.5667 12.3583 30L12.3583 18L0.408334 2.75C-0.0583332 2.18333 -0.125 1.58333 0.208333 0.95C0.541667 0.316666 1.05833 0 1.75833 0L30.9583 0C31.6583 0 32.175 0.316666 32.5083 0.95C32.8417 1.58333 32.775 2.18333 32.3083 2.75L20.3583 18L20.3583 30C20.3583 30.5667 20.1667 31.0417 19.7833 31.425C19.4 31.8083 18.925 32 18.3583 32L14.3583 32ZM16.3583 18.2L28.3583 3L4.35833 3L16.3583 18.2Z");
canvas.FillPath(vector34path);
canvas.RestoreState();


// View:     frameView91
// NodeName: sort_FILL0_wght400_GRAD0_opsz48 1
// NodeType: FRAME
// NodeId:   31:7
canvas.SaveState();
canvas.RestoreState();


// View:     imageView43
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   31:8
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(835f, 199f);
var vector35Builder = new PathBuilder();
var vector35path = vector35Builder.BuildPath("M0 24L0 21L12 21L12 24L0 24ZM0 13.5L0 10.5L24 10.5L24 13.5L0 13.5ZM0 3L0 0L36 0L36 3L0 3Z");
canvas.FillPath(vector35path);
canvas.RestoreState();


// View:     frameView92
// NodeName: Menu
// NodeType: FRAME
// NodeId:   10:749
canvas.SaveState();
canvas.RestoreState();


// View:     frameView93
// NodeName: Measure
// NodeType: GROUP
// NodeId:   10:750
canvas.SaveState();
canvas.RestoreState();


// View:     textView110
// NodeName: Разгон
// NodeType: TEXT
// NodeId:   10:751
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Разгон", 717f, 302f, 53f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView44
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   10:752
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(726f, 274f);
var vector36Builder = new PathBuilder();
var vector36path = vector36Builder.BuildPath("M21.35 25.375C24.4417 25.375 27.0521 24.2448 29.1812 21.9844C31.3104 19.724 32.375 17.0333 32.375 13.9125C32.375 10.7042 31.2302 8.02083 28.9406 5.8625C26.651 3.70417 23.9021 2.625 20.6938 2.625C19.0604 2.625 17.5292 2.82188 16.1 3.21563C14.6708 3.60938 13.1687 4.22917 11.5938 5.075L16.8875 7.04375C18.375 7.59792 19.3885 8.39271 19.9281 9.42812C20.4677 10.4635 20.7375 11.6375 20.7375 12.95C20.7375 14.7 20.1542 16.1656 18.9875 17.3469C17.8208 18.5281 16.3917 19.1187 14.7 19.1187L2.8875 19.1187C2.74167 19.7896 2.66146 20.6865 2.64688 21.8094C2.63229 22.9323 2.625 24.1208 2.625 25.375L21.35 25.375ZM3.19375 16.4937L14.4375 16.4937C15.5167 16.4937 16.399 16.151 17.0844 15.4656C17.7698 14.7802 18.1125 13.9417 18.1125 12.95C18.1125 12.1042 17.9156 11.3896 17.5219 10.8063C17.1281 10.2229 16.5521 9.8 15.7937 9.5375L9.05625 6.7375C7.59792 8.02083 6.3875 9.46458 5.425 11.0687C4.4625 12.6729 3.71875 14.4813 3.19375 16.4937L3.19375 16.4937ZM21.35 28L2.625 28C1.925 28 1.3125 27.7375 0.7875 27.2125C0.2625 26.6875 0 26.075 0 25.375L0 22.0938C0 18.9438 0.525 16.0198 1.575 13.3219C2.625 10.624 4.07604 8.29063 5.92812 6.32188C7.78021 4.35313 9.96771 2.80729 12.4906 1.68438C15.0135 0.561459 17.7479 0 20.6938 0C22.6479 0 24.4927 0.357292 26.2281 1.07188C27.9635 1.78646 29.4802 2.77083 30.7781 4.025C32.076 5.27917 33.1042 6.75208 33.8625 8.44375C34.6208 10.1354 35 11.9583 35 13.9125C35 15.8375 34.6427 17.6604 33.9281 19.3813C33.2135 21.1021 32.2365 22.5969 30.9969 23.8656C29.7573 25.1344 28.3135 26.1406 26.6656 26.8844C25.0177 27.6281 23.2458 28 21.35 28Z");
canvas.FillPath(vector36path);
canvas.RestoreState();


// View:     frameView94
// NodeName: Gauges
// NodeType: GROUP
// NodeId:   10:753
canvas.SaveState();
canvas.RestoreState();


// View:     textView111
// NodeName: Приборы
// NodeType: TEXT
// NodeId:   10:754
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Приборы", 797f, 302f, 61f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView45
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   10:755
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(810f, 274f);
var vector37Builder = new PathBuilder();
var vector37path = vector37Builder.BuildPath("M14.3917 20.77C15.0665 21.4418 16.0421 21.7559 17.3185 21.7121C18.5948 21.6682 19.5264 21.1935 20.1132 20.288L29.6196 5.43349L14.8758 15.0297C13.9956 15.614 13.5261 16.5488 13.4675 17.8341C13.4088 19.1195 13.7169 20.0981 14.3917 20.77L14.3917 20.77ZM17.5165 0C19.1889 0 20.9347 0.270214 22.7539 0.810642C24.573 1.35107 26.3188 2.27856 27.9912 3.59311L25.7026 5.2144C24.3823 4.33803 22.9666 3.68805 21.4555 3.26448C19.9445 2.8409 18.6315 2.62911 17.5165 2.62911C13.4088 2.62911 9.90255 4.08972 6.9978 7.01095C4.09305 9.93218 2.64068 13.4815 2.64068 17.6588C2.64068 18.9734 2.82406 20.3026 3.19082 21.6463C3.55758 22.9901 4.07838 24.2316 4.75322 25.3709L30.2358 25.3709C30.8813 24.3192 31.3947 23.0923 31.7762 21.6901C32.1576 20.2879 32.3483 18.915 32.3483 17.5712C32.3483 16.3443 32.1649 15.0224 31.7982 13.6056C31.4314 12.1888 30.7786 10.8816 29.8397 9.68388L31.5561 7.40532C32.6711 9.04121 33.5073 10.6844 34.0648 12.3349C34.6222 13.9854 34.9303 15.6432 34.989 17.3083C35.0477 19.061 34.8716 20.7115 34.4609 22.2598C34.0501 23.808 33.4486 25.2394 32.6564 26.554C32.3043 27.2259 31.9302 27.6348 31.5341 27.7809C31.138 27.927 30.6465 28 30.0597 28L4.92927 28C4.43047 28 3.93901 27.8758 3.45489 27.6275C2.97076 27.3792 2.61134 27.0214 2.37661 26.554C1.61375 25.1518 1.02693 23.7277 0.616158 22.2817C0.205386 20.8357 0 19.2947 0 17.6588C0 15.2342 0.462119 12.9484 1.38636 10.8013C2.31059 8.65415 3.56492 6.78456 5.14932 5.19249C6.73373 3.60042 8.58954 2.33698 10.7168 1.40219C12.844 0.467396 15.1106 -1.55674e-15 17.5165 0L17.5165 0Z");
canvas.FillPath(vector37path);
canvas.RestoreState();


// View:     frameView95
// NodeName: History
// NodeType: GROUP
// NodeId:   10:756
canvas.SaveState();
canvas.RestoreState();


// View:     textView112
// NodeName: История
// NodeType: TEXT
// NodeId:   10:757
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"История", 885f, 302f, 57f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView46
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   10:758
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(897f, 274f);
var vector38Builder = new PathBuilder();
var vector38path = vector38Builder.BuildPath("M2.40833 28C1.74722 28 1.18056 27.7744 0.708333 27.3233C0.236111 26.8722 8.38835e-16 26.3356 0 25.7133C0 25.06 0.236111 24.5 0.708333 24.0333C1.18056 23.5667 1.74722 23.3333 2.40833 23.3333C3.03796 23.3333 3.58102 23.5667 4.0375 24.0333C4.49398 24.5 4.72222 25.06 4.72222 25.7133C4.72222 26.3356 4.49398 26.8722 4.0375 27.3233C3.58102 27.7744 3.03796 28 2.40833 28ZM9.44444 27.0667L9.44444 24.2667L34 24.2667L34 27.0667L9.44444 27.0667ZM2.40833 16.3333C1.74722 16.3333 1.18056 16.1078 0.708333 15.6567C0.236111 15.2056 8.38835e-16 14.6533 0 14C0 13.3467 0.236111 12.7944 0.708333 12.3433C1.18056 11.8922 1.74722 11.6667 2.40833 11.6667C3.03796 11.6667 3.58102 11.9 4.0375 12.3667C4.49398 12.8333 4.72222 13.3778 4.72222 14C4.72222 14.6222 4.49398 15.1667 4.0375 15.6333C3.58102 16.1 3.03796 16.3333 2.40833 16.3333ZM9.44444 15.4L9.44444 12.6L34 12.6L34 15.4L9.44444 15.4ZM2.36111 4.66667C1.7 4.66667 1.1412 4.44111 0.684722 3.99C0.228241 3.53889 0 2.98667 0 2.33333C0 1.68 0.228241 1.12778 0.684722 0.676667C1.1412 0.225556 1.7 0 2.36111 0C3.02222 0 3.58102 0.225556 4.0375 0.676667C4.49398 1.12778 4.72222 1.68 4.72222 2.33333C4.72222 2.98667 4.49398 3.53889 4.0375 3.99C3.58102 4.44111 3.02222 4.66667 2.36111 4.66667ZM9.44444 3.73333L9.44444 0.933333L34 0.933333L34 3.73333L9.44444 3.73333Z");
canvas.FillPath(vector38path);
canvas.RestoreState();


// View:     frameView96
// NodeName: Settings
// NodeType: GROUP
// NodeId:   10:759
canvas.SaveState();
canvas.RestoreState();


// View:     textView113
// NodeName: Настройки
// NodeType: TEXT
// NodeId:   10:760
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Настройки", 969f, 302f, 70f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView47
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   10:761
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(990f, 274f);
var vector39Builder = new PathBuilder();
var vector39path = vector39Builder.BuildPath("M10.78 28L10.08 23.59C9.63667 23.4267 9.17 23.205 8.68 22.925C8.19 22.645 7.75833 22.3533 7.385 22.05L3.255 23.94L0 18.2L3.78 15.435C3.73333 15.225 3.70417 14.9858 3.6925 14.7175C3.68083 14.4492 3.675 14.21 3.675 14C3.675 13.79 3.68083 13.5508 3.6925 13.2825C3.70417 13.0142 3.73333 12.775 3.78 12.565L0 9.8L3.255 4.06L7.385 5.95C7.75833 5.64667 8.19 5.355 8.68 5.075C9.17 4.795 9.63667 4.585 10.08 4.445L10.78 0L17.22 0L17.92 4.41C18.3633 4.57333 18.8358 4.78917 19.3375 5.0575C19.8392 5.32583 20.265 5.62333 20.615 5.95L24.745 4.06L28 9.8L24.22 12.495C24.2667 12.7283 24.2958 12.9792 24.3075 13.2475C24.3192 13.5158 24.325 13.7667 24.325 14C24.325 14.2333 24.3192 14.4783 24.3075 14.735C24.2958 14.9917 24.2667 15.2367 24.22 15.47L28 18.2L24.745 23.94L20.615 22.05C20.2417 22.3533 19.8158 22.6508 19.3375 22.9425C18.8592 23.2342 18.3867 23.45 17.92 23.59L17.22 28L10.78 28ZM14 18.55C15.26 18.55 16.3333 18.1067 17.22 17.22C18.1067 16.3333 18.55 15.26 18.55 14C18.55 12.74 18.1067 11.6667 17.22 10.78C16.3333 9.89333 15.26 9.45 14 9.45C12.74 9.45 11.6667 9.89333 10.78 10.78C9.89333 11.6667 9.45 12.74 9.45 14C9.45 15.26 9.89333 16.3333 10.78 17.22C11.6667 18.1067 12.74 18.55 14 18.55ZM14 16.45C13.3233 16.45 12.7458 16.2108 12.2675 15.7325C11.7892 15.2542 11.55 14.6767 11.55 14C11.55 13.3233 11.7892 12.7458 12.2675 12.2675C12.7458 11.7892 13.3233 11.55 14 11.55C14.6767 11.55 15.2542 11.7892 15.7325 12.2675C16.2108 12.7458 16.45 13.3233 16.45 14C16.45 14.6767 16.2108 15.2542 15.7325 15.7325C15.2542 16.2108 14.6767 16.45 14 16.45ZM12.46 25.9L15.54 25.9L16.03 21.98C16.8 21.7933 17.5292 21.5017 18.2175 21.105C18.9058 20.7083 19.53 20.23 20.09 19.67L23.8 21.28L25.2 18.76L21.91 16.345C22.0033 15.9483 22.0792 15.5575 22.1375 15.1725C22.1958 14.7875 22.225 14.3967 22.225 14C22.225 13.6033 22.2017 13.2125 22.155 12.8275C22.1083 12.4425 22.0267 12.0517 21.91 11.655L25.2 9.24L23.8 6.72L20.09 8.33C19.5533 7.72333 18.9467 7.21583 18.27 6.8075C17.5933 6.39917 16.8467 6.13667 16.03 6.02L15.54 2.1L12.46 2.1L11.97 6.02C11.1767 6.18333 10.4358 6.46333 9.7475 6.86C9.05917 7.25667 8.44667 7.74667 7.91 8.33L4.2 6.72L2.8 9.24L6.09 11.655C5.99667 12.0517 5.92083 12.4425 5.8625 12.8275C5.80417 13.2125 5.775 13.6033 5.775 14C5.775 14.3967 5.80417 14.7875 5.8625 15.1725C5.92083 15.5575 5.99667 15.9483 6.09 16.345L2.8 18.76L4.2 21.28L7.91 19.67C8.47 20.23 9.09417 20.7083 9.7825 21.105C10.4708 21.5017 11.2 21.7933 11.97 21.98L12.46 25.9Z");
canvas.FillPath(vector39path);
canvas.RestoreState();


// View:     frameView97
// NodeName: Gauges Screen
// NodeType: FRAME
// NodeId:   5:157
canvas.SaveState();
canvas.RestoreState();


// View:     frameView98
// NodeName: iPhone 11 Pro
// NodeType: COMPONENT
// NodeId:   8:159
canvas.SaveState();
canvas.RestoreState();


// View:     frameView99
// NodeName: iPhone 11 Pro
// NodeType: INSTANCE
// NodeId:   8:427
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(52, 60, 69, 255)) ,new PaintGradientStop(1, new Color(17, 22, 29, 255)) ,}}, new RectF(241f, -458f, 375f, 812f));
canvas.FillRoundedRectangle(241f, -458f, 375f, 812f, 55f);
canvas.RestoreState();


// View:     frameView100
// NodeName: Gauges
// NodeType: GROUP
// NodeId:   8:160
canvas.SaveState();
canvas.RestoreState();


// View:     frameView101
// NodeName: Group 21
// NodeType: GROUP
// NodeId:   10:748
canvas.SaveState();
canvas.RestoreState();


// View:     rectangleView8
// NodeName: Results screen
// NodeType: RECTANGLE
// NodeId:   8:161
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(35, 35, 35, 255)) ,new PaintGradientStop(1, new Color(0, 0, 0, 255)) ,}}, new RectF(267f, -80f, 324f, 331f));
canvas.FillRoundedRectangle(267f, -80f, 324f, 331f, 25f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(21, 25, 30, 255)) ,new PaintGradientStop(1, new Color(66, 70, 75, 255)) ,}}, new RectF(267f, -80f, 324f, 331f));
canvas.StrokeSize  = 2;
canvas.DrawRoundedRectangle(267f, -80f, 324f, 331f, 25f);
canvas.RestoreState();


// View:     frameView102
// NodeName: Group 12
// NodeType: GROUP
// NodeId:   8:283
canvas.SaveState();
canvas.RestoreState();


// View:     textView114
// NodeName: 1955 м
// NodeType: TEXT
// NodeId:   8:273
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 32f;
canvas.DrawString(@"1955 м", 286f, -56.22189f, 126f, 22.554043f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView115
// NodeName: Высота
// NodeType: TEXT
// NodeId:   8:274
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Высота", 286f, -23.388344f, 126f, 13.99906f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView103
// NodeName: Group 18
// NodeType: GROUP
// NodeId:   8:304
canvas.SaveState();
canvas.RestoreState();


// View:     textView116
// NodeName: 67°
// NodeType: TEXT
// NodeId:   8:305
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 32f;
canvas.DrawString(@"67°", 446f, -56.357143f, 126f, 22.554043f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView117
// NodeName: Курс
// NodeType: TEXT
// NodeId:   8:306
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Курс", 446f, -23.523602f, 126f, 13.99906f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView104
// NodeName: Group 14
// NodeType: GROUP
// NodeId:   8:287
canvas.SaveState();
canvas.RestoreState();


// View:     textView118
// NodeName: 35.987654
// NodeType: TEXT
// NodeId:   8:288
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"35.987654", 280f, 24.661568f, 138f, 22.554043f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView119
// NodeName: Широта
// NodeType: TEXT
// NodeId:   8:289
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Широта", 286f, 57.495113f, 126f, 13.999058f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView105
// NodeName: Group 15
// NodeType: GROUP
// NodeId:   8:290
canvas.SaveState();
canvas.RestoreState();


// View:     textView120
// NodeName: 172.233004
// NodeType: TEXT
// NodeId:   8:291
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"172.233004", 440f, 24.661568f, 138f, 22.554043f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView121
// NodeName: Долгота
// NodeType: TEXT
// NodeId:   8:292
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Долгота", 446f, 57.495113f, 126f, 13.999058f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView106
// NodeName: Group 13
// NodeType: GROUP
// NodeId:   8:284
canvas.SaveState();
canvas.RestoreState();


// View:     textView122
// NodeName: 2.7%
// NodeType: TEXT
// NodeId:   8:285
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 32f;
canvas.DrawString(@"2.7%", 446f, 107.08696f, 126f, 22.554047f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView123
// NodeName: Уклон
// NodeType: TEXT
// NodeId:   8:286
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Уклон", 446f, 139.9205f, 126f, 13.999054f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView107
// NodeName: Group 16
// NodeType: GROUP
// NodeId:   8:293
canvas.SaveState();
canvas.RestoreState();


// View:     textView124
// NodeName: 16.2 Гц
// NodeType: TEXT
// NodeId:   8:294
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 32f;
canvas.DrawString(@"16.2 Гц", 446f, 188.29504f, 126f, 22.554047f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView125
// NodeName: Частота
// NodeType: TEXT
// NodeId:   8:295
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Частота", 446f, 221.12859f, 126f, 13.999054f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView108
// NodeName: Group 19
// NodeType: GROUP
// NodeId:   8:307
canvas.SaveState();
canvas.RestoreState();


// View:     textView126
// NodeName: Racebox#532D
// NodeType: TEXT
// NodeId:   8:308
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Racebox#532D", 286f, 188.29504f, 126f, 22.554047f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView127
// NodeName: Прибор
// NodeType: TEXT
// NodeId:   8:309
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Прибор", 286f, 221.12859f, 126f, 13.999054f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView109
// NodeName: Group 17
// NodeType: GROUP
// NodeId:   8:296
canvas.SaveState();
canvas.RestoreState();


// View:     textView128
// NodeName: 164.5 км/ч
// NodeType: TEXT
// NodeId:   8:297
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 24f;
canvas.DrawString(@"164.5 км/ч", 281f, 106.322754f, 136f, 22.554047f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView129
// NodeName: Макс. скор.
// NodeType: TEXT
// NodeId:   8:298
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Макс. скор.", 286f, 139.15628f, 126f, 13.999054f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     rectangleView9
// NodeName: Rectangle 5
// NodeType: RECTANGLE
// NodeId:   8:277
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(274f, -71.7764f, 150f, 74.01242f, 0f);
canvas.StrokeColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.StrokeSize  = 1;
canvas.DrawRoundedRectangle(274f, -71.7764f, 150f, 74.01242f, 0f);
canvas.RestoreState();


// View:     rectangleView10
// NodeName: Rectangle 6
// NodeType: RECTANGLE
// NodeId:   8:278
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(274f, 9.431679f, 150f, 74.01242f, 0f);
canvas.StrokeColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.StrokeSize  = 1;
canvas.DrawRoundedRectangle(274f, 9.431679f, 150f, 74.01242f, 0f);
canvas.RestoreState();


// View:     rectangleView11
// NodeName: Rectangle 7
// NodeType: RECTANGLE
// NodeId:   8:279
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(274f, 90.639755f, 150f, 74.01242f, 0f);
canvas.StrokeColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.StrokeSize  = 1;
canvas.DrawRoundedRectangle(274f, 90.639755f, 150f, 74.01242f, 0f);
canvas.RestoreState();


// View:     rectangleView12
// NodeName: Rectangle 11
// NodeType: RECTANGLE
// NodeId:   8:302
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(274f, 171.84782f, 150f, 74.01242f, 0f);
canvas.StrokeColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.StrokeSize  = 1;
canvas.DrawRoundedRectangle(274f, 171.84782f, 150f, 74.01242f, 0f);
canvas.RestoreState();


// View:     rectangleView13
// NodeName: Rectangle 12
// NodeType: RECTANGLE
// NodeId:   8:303
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(434f, 171.84782f, 150f, 74.01242f, 0f);
canvas.StrokeColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.StrokeSize  = 1;
canvas.DrawRoundedRectangle(434f, 171.84782f, 150f, 74.01242f, 0f);
canvas.RestoreState();


// View:     rectangleView14
// NodeName: Rectangle 8
// NodeType: RECTANGLE
// NodeId:   8:280
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(434f, -71.7764f, 150f, 74.01242f, 0f);
canvas.StrokeColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.StrokeSize  = 1;
canvas.DrawRoundedRectangle(434f, -71.7764f, 150f, 74.01242f, 0f);
canvas.RestoreState();


// View:     rectangleView15
// NodeName: Rectangle 9
// NodeType: RECTANGLE
// NodeId:   8:281
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(434f, 9.431679f, 150f, 74.01242f, 0f);
canvas.StrokeColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.StrokeSize  = 1;
canvas.DrawRoundedRectangle(434f, 9.431679f, 150f, 74.01242f, 0f);
canvas.RestoreState();


// View:     rectangleView16
// NodeName: Rectangle 10
// NodeType: RECTANGLE
// NodeId:   8:282
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(434f, 90.639755f, 150f, 74.01242f, 0f);
canvas.StrokeColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.StrokeSize  = 1;
canvas.DrawRoundedRectangle(434f, 90.639755f, 150f, 74.01242f, 0f);
canvas.RestoreState();


// View:     lineView18
// NodeName: Line 13
// NodeType: LINE
// NodeId:   8:299
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(429, -76), new Point(429, 246));
canvas.RestoreState();


// View:     lineView19
// NodeName: Line 14
// NodeType: LINE
// NodeId:   8:300
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(275, 6.347824), new Point(584, 6.347824));
canvas.RestoreState();


// View:     lineView20
// NodeName: Line 15
// NodeType: LINE
// NodeId:   8:301
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(274, 87.55591), new Point(583, 87.55591));
canvas.RestoreState();


// View:     lineView21
// NodeName: Line 16
// NodeType: LINE
// NodeId:   8:310
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(274, 168.76398), new Point(583, 168.76398));
canvas.RestoreState();


// View:     frameView110
// NodeName: Speed block
// NodeType: GROUP
// NodeId:   8:242
canvas.SaveState();
canvas.RestoreState();


// View:     frameView111
// NodeName: Speedometer
// NodeType: GROUP
// NodeId:   5:150
canvas.SaveState();
canvas.RestoreState();


// View:     elipseView
// NodeName: Ellipse 1
// NodeType: ELLIPSE
// NodeId:   5:151
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(35, 35, 35, 255)) ,new PaintGradientStop(1, new Color(0, 0, 0, 255)) ,}}, new RectF(315f, -327f, 168f, 168f));
canvas.FillEllipse(315f, -327f, 168f, 168f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(21, 25, 30, 255)) ,new PaintGradientStop(1, new Color(66, 70, 75, 255)) ,}}, new RectF(315f, -327f, 168f, 168f));
canvas.StrokeSize = 2;
canvas.DrawEllipse(315f, -327f, 168f, 168f);
canvas.RestoreState();


// View:     textView130
// NodeName: 156.3
// NodeType: TEXT
// NodeId:   5:152
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 40f;
canvas.DrawString(@"156.3", 332.64f, -293.4f, 133.56f, 100.8f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView131
// NodeName: км/ч
// NodeType: TEXT
// NodeId:   5:153
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"км/ч", 368.76f, -218.92f, 61.320007f, 21.839996f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     elipseView1
// NodeName: Ellipse 2
// NodeType: ELLIPSE
// NodeId:   5:154
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillEllipse(321.72f, -320.28f, 155.4f, 155.4f);
canvas.StrokeSize = 8;
canvas.DrawEllipse(321.72f, -320.28f, 155.4f, 155.4f);
canvas.RestoreState();


// View:     frameView112
// NodeName: Accel gauge
// NodeType: GROUP
// NodeId:   8:213
canvas.SaveState();
canvas.RestoreState();


// View:     rectangleView17
// NodeName: Rectangle 3
// NodeType: RECTANGLE
// NodeId:   8:188
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(540f, -323f, 6f, 157f, 9f);
canvas.RestoreState();


// View:     frameView113
// NodeName: G-scale
// NodeType: GROUP
// NodeId:   8:209
canvas.SaveState();
canvas.RestoreState();


// View:     imageView48
// NodeName: Line 2
// NodeType: VECTOR
// NodeId:   8:197

// View:     imageView49
// NodeName: Line 7
// NodeType: VECTOR
// NodeId:   8:203

// View:     imageView50
// NodeName: Line 3
// NodeType: VECTOR
// NodeId:   8:199

// View:     imageView51
// NodeName: Line 4
// NodeType: VECTOR
// NodeId:   8:200

// View:     imageView52
// NodeName: Line 9
// NodeType: VECTOR
// NodeId:   8:207

// View:     imageView53
// NodeName: Line 5
// NodeType: VECTOR
// NodeId:   8:201

// View:     imageView54
// NodeName: Line 10
// NodeType: VECTOR
// NodeId:   8:208

// View:     imageView55
// NodeName: Line 6
// NodeType: VECTOR
// NodeId:   8:202

// View:     imageView56
// NodeName: Line 8
// NodeType: VECTOR
// NodeId:   8:206

// View:     frameView114
// NodeName: G-slider
// NodeType: GROUP
// NodeId:   8:191
canvas.SaveState();
canvas.RestoreState();


// View:     rectangleView18
// NodeName: Rectangle 1
// NodeType: RECTANGLE
// NodeId:   8:192
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(137, 132, 120, 255)) ,new PaintGradientStop(1, new Color(255, 250, 237, 255)) ,}}, new RectF(520f, -278f, 46f, 24f));
canvas.FillRoundedRectangle(520f, -278f, 46f, 24f, 31f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(255, 249, 234, 255)) ,new PaintGradientStop(1, new Color(126, 122, 110, 255)) ,}}, new RectF(520f, -278f, 46f, 24f));
canvas.StrokeSize  = 2;
canvas.DrawRoundedRectangle(520f, -278f, 46f, 24f, 31f);
canvas.RestoreState();


// View:     textView132
// NodeName: -0.34
// NodeType: TEXT
// NodeId:   8:193
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Medium", 500, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"-0.34", 526f, -273.82608f, 33f, 15.652161f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView115
// NodeName: Group 10
// NodeType: GROUP
// NodeId:   8:212
canvas.SaveState();
canvas.RestoreState();


// View:     textView133
// NodeName: -0.8
// NodeType: TEXT
// NodeId:   8:194
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"-0.8", 497f, -321f, 27f, 11f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView134
// NodeName: -0.4
// NodeType: TEXT
// NodeId:   8:237
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"-0.4", 497f, -284f, 27f, 11f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView135
// NodeName: +0.8
// NodeType: TEXT
// NodeId:   8:196
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"+0.8", 492f, -181f, 32f, 12f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView136
// NodeName: +0.4
// NodeType: TEXT
// NodeId:   8:210
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"+0.4", 492f, -218f, 32f, 14f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView137
// NodeName: 0
// NodeType: TEXT
// NodeId:   8:195
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"0", 507f, -249f, 17f, 12f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView116
// NodeName: Lat gauge
// NodeType: GROUP
// NodeId:   8:240
canvas.SaveState();
canvas.RestoreState();


// View:     frameView117
// NodeName: G-scale
// NodeType: GROUP
// NodeId:   8:239
canvas.SaveState();
canvas.RestoreState();


// View:     frameView118
// NodeName: Group 11
// NodeType: GROUP
// NodeId:   8:238
canvas.SaveState();
canvas.RestoreState();


// View:     textView138
// NodeName: 1.2
// NodeType: TEXT
// NodeId:   8:228
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"1.2", 302f, -145f, 33f, 14f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView139
// NodeName: 0.6
// NodeType: TEXT
// NodeId:   8:233
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"0.6", 344f, -145f, 33f, 14f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView140
// NodeName: 0.6
// NodeType: TEXT
// NodeId:   8:235
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"0.6", 423f, -145f, 33f, 14f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView141
// NodeName: 1.2
// NodeType: TEXT
// NodeId:   8:236
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"1.2", 466f, -145f, 33f, 14f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView142
// NodeName: 0
// NodeType: TEXT
// NodeId:   8:234
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"0", 381f, -145f, 33f, 14f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView119
// NodeName: G-scale
// NodeType: GROUP
// NodeId:   8:217
canvas.SaveState();
canvas.RestoreState();


// View:     imageView57
// NodeName: Line 2
// NodeType: VECTOR
// NodeId:   8:218

// View:     imageView58
// NodeName: Line 7
// NodeType: VECTOR
// NodeId:   8:219

// View:     imageView59
// NodeName: Line 3
// NodeType: VECTOR
// NodeId:   8:220

// View:     imageView60
// NodeName: Line 4
// NodeType: VECTOR
// NodeId:   8:221

// View:     imageView61
// NodeName: Line 9
// NodeType: VECTOR
// NodeId:   8:222

// View:     imageView62
// NodeName: Line 5
// NodeType: VECTOR
// NodeId:   8:223

// View:     imageView63
// NodeName: Line 10
// NodeType: VECTOR
// NodeId:   8:224

// View:     imageView64
// NodeName: Line 6
// NodeType: VECTOR
// NodeId:   8:225

// View:     imageView65
// NodeName: Line 8
// NodeType: VECTOR
// NodeId:   8:226

// View:     rectangleView19
// NodeName: Rectangle 2
// NodeType: RECTANGLE
// NodeId:   8:187
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(314f, -116f, 176f, 6f, 9f);
canvas.RestoreState();


// View:     frameView120
// NodeName: G-slider
// NodeType: GROUP
// NodeId:   8:214
canvas.SaveState();
canvas.RestoreState();


// View:     rectangleView20
// NodeName: Rectangle 1
// NodeType: RECTANGLE
// NodeId:   8:215
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(137, 132, 120, 255)) ,new PaintGradientStop(1, new Color(255, 250, 237, 255)) ,}}, new RectF(407.07407f, -125f, 46.5679f, 24f));
canvas.FillRoundedRectangle(407.07407f, -125f, 46.5679f, 24f, 31f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(255, 249, 234, 255)) ,new PaintGradientStop(1, new Color(126, 122, 110, 255)) ,}}, new RectF(407.07407f, -125f, 46.5679f, 24f));
canvas.StrokeSize  = 2;
canvas.DrawRoundedRectangle(407.07407f, -125f, 46.5679f, 24f, 31f);
canvas.RestoreState();


// View:     textView143
// NodeName: 0.52
// NodeType: TEXT
// NodeId:   8:216
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Medium", 500, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"0.52", 413.14813f, -120.82608f, 33.40741f, 15.652176f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView144
// NodeName: G
// NodeType: TEXT
// NodeId:   8:241
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 32f;
canvas.DrawString(@"G", 515f, -137f, 37f, 32f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView121
// NodeName: Status block
// NodeType: GROUP
// NodeId:   8:243
canvas.SaveState();
canvas.RestoreState();


// View:     rectangleView21
// NodeName: Results screen
// NodeType: RECTANGLE
// NodeId:   8:244
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(35, 35, 35, 255)) ,new PaintGradientStop(1, new Color(0, 0, 0, 255)) ,}}, new RectF(267f, -398f, 324f, 50f));
canvas.FillRoundedRectangle(267f, -398f, 324f, 50f, 25f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(21, 25, 30, 255)) ,new PaintGradientStop(1, new Color(66, 70, 75, 255)) ,}}, new RectF(267f, -398f, 324f, 50f));
canvas.StrokeSize  = 2;
canvas.DrawRoundedRectangle(267f, -398f, 324f, 50f, 25f);
canvas.RestoreState();


// View:     frameView122
// NodeName: Sats
// NodeType: GROUP
// NodeId:   8:245
canvas.SaveState();
canvas.RestoreState();


// View:     textView145
// NodeName: 13
// NodeType: TEXT
// NodeId:   8:246
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"13", 305f, -390f, 49f, 34f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView66
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   8:247
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(280f, -389f);
var vector40Builder = new PathBuilder();
var vector40path = vector40Builder.BuildPath("M18.3621 30L18.3621 28.0583C21.056 28.0583 23.3459 27.1143 25.2317 25.2265C27.1175 23.3387 28.0603 21.0464 28.0603 18.3495L30 18.3495C30 19.9676 29.6929 21.4833 29.0787 22.8964C28.4644 24.3096 27.6347 25.5394 26.5894 26.5858C25.5442 27.6321 24.3157 28.4628 22.9041 29.0777C21.4925 29.6926 19.9784 30 18.3621 30ZM18.3621 26.1165L18.3621 24.1748C19.8491 24.1748 21.1907 23.5761 22.3869 22.3786C23.583 21.1812 24.181 19.8382 24.181 18.3495L26.1207 18.3495C26.1207 20.3991 25.3394 22.206 23.7769 23.7702C22.2144 25.3344 20.4095 26.1165 18.3621 26.1165ZM7.43534 29.4498C7.17672 29.4498 6.92349 29.4013 6.67565 29.3042C6.4278 29.2071 6.21767 29.0723 6.04526 28.8997L0.549569 23.3981C0.377155 23.2255 0.242457 23.0151 0.145474 22.767C0.0484914 22.5189 0 22.2654 0 22.0065C0 21.7476 0.0484914 21.4995 0.145474 21.2621C0.242457 21.0248 0.377155 20.8198 0.549569 20.6472L5.43103 15.7605C5.79741 15.3937 6.25539 15.2104 6.80496 15.2104C7.35453 15.2104 7.8125 15.3937 8.17888 15.7605L10.2478 17.8317L11.444 16.6343L9.375 14.5631C9.00862 14.1963 8.82543 13.7433 8.82543 13.2039C8.82543 12.6645 9.00862 12.2114 9.375 11.8447L11.7996 9.41748C12.1659 9.0507 12.6239 8.86731 13.1735 8.86731C13.7231 8.86731 14.181 9.0507 14.5474 9.41748L16.6164 11.4887L17.8125 10.2913L15.7435 8.22006C15.3772 7.85329 15.194 7.39482 15.194 6.84466C15.194 6.2945 15.3772 5.83603 15.7435 5.46926L20.625 0.582524C20.819 0.388349 21.0345 0.242718 21.2716 0.145631C21.5086 0.0485437 21.7565 0 22.0151 0C22.2737 0 22.5216 0.0431499 22.7586 0.12945C22.9957 0.21575 23.2112 0.355987 23.4052 0.550162L28.9009 6.05178C29.0948 6.24595 29.2349 6.4617 29.3211 6.69903C29.4073 6.93635 29.4504 7.18447 29.4504 7.44337C29.4504 7.70227 29.3966 7.95577 29.2888 8.20388C29.181 8.452 29.0409 8.66235 28.8685 8.83495L23.9871 13.7217C23.4914 14.2179 23.0334 14.466 22.6131 14.466C22.1929 14.466 21.7349 14.2179 21.2392 13.7217L19.1703 11.6505L17.9741 12.8479L20.0431 14.9191C20.4095 15.2859 20.5873 15.7443 20.5765 16.2945C20.5657 16.8447 20.3772 17.3031 20.0108 17.6699L17.6185 20.0647C17.2522 20.4315 16.7942 20.6149 16.2446 20.6149C15.695 20.6149 15.2371 20.4315 14.8707 20.0647L12.8017 17.9935L11.6056 19.1909L13.6746 21.2621C14.0409 21.6289 14.2241 22.0874 14.2241 22.6375C14.2241 23.1877 14.0409 23.6462 13.6746 24.0129L8.7931 28.8997C8.62069 29.0723 8.41595 29.2071 8.17888 29.3042C7.94181 29.4013 7.69397 29.4498 7.43534 29.4498L7.43534 29.4498ZM7.43534 27.5405L9.18103 25.7929L3.68534 20.2913L1.93966 22.0388L7.43534 27.5405ZM10.5711 24.4013L12.3168 22.6537L6.82112 17.1521L5.07543 18.8997L10.5711 24.4013ZM16.2608 18.7055L18.653 16.3107L13.1573 10.8091L10.7651 13.2039L16.2608 18.7055ZM22.6293 12.3301L24.375 10.5825L18.8793 5.08091L17.1336 6.82848L22.6293 12.3301ZM25.7651 9.19094L27.5108 7.44337L22.0151 1.94175L20.2694 3.68932L25.7651 9.19094Z");
canvas.FillPath(vector40path);
canvas.RestoreState();


// View:     lineView22
// NodeName: Line 11
// NodeType: LINE
// NodeId:   8:248
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(351, -390), new Point(351, -356));
canvas.RestoreState();


// View:     lineView23
// NodeName: Line 12
// NodeType: LINE
// NodeId:   8:250
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(543, -390), new Point(543, -356));
canvas.RestoreState();


// View:     imageView67
// NodeName: BLE
// NodeType: VECTOR
// NodeId:   8:249
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(54, 167, 203);
canvas.Alpha  = 1;
canvas.Translate(555f, -388f);
var vector41Builder = new PathBuilder();
var vector41path = vector41Builder.BuildPath("M8.35833 30L8.35833 17.7979L1.4875 25.3368L0 23.7047L7.93333 15L0 6.29534L1.4875 4.66321L8.35833 12.2021L8.35833 0L9.42083 0L17 8.31606L10.9083 15L17 21.6839L9.42083 30L8.35833 30ZM10.4833 12.2021L14.025 8.31606L10.4833 4.50777L10.4833 12.2021ZM10.4833 25.4922L14.025 21.6839L10.4833 17.7979L10.4833 25.4922Z");
canvas.FillPath(vector41path);
canvas.RestoreState();


// View:     frameView123
// NodeName: Signal
// NodeType: GROUP
// NodeId:   8:255
canvas.SaveState();
canvas.RestoreState();


// View:     textView146
// NodeName: Уровень сигнала
// NodeType: TEXT
// NodeId:   8:253
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"Уровень сигнала", 390f, -389f, 115f, 16f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView68
// NodeName: Rectangle 3
// NodeType: VECTOR
// NodeId:   8:251
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(216, 40, 29, 255)) ,new PaintGradientStop(0, new Color(240, 231, 11, 255)) ,new PaintGradientStop(1, new Color(101, 210, 16, 255)) ,}}, new RectF(364f, -368f, 167f, 6f));
canvas.Translate(364f, -368f);
var vector42Builder = new PathBuilder();
var vector42path = vector42Builder.BuildPath("M0 3C0 1.34315 1.34315 0 3 0L164 0C165.657 0 167 1.34315 167 3C167 4.65685 165.657 6 164 6L3 6C1.34314 6 0 4.65685 0 3Z");
canvas.FillPath(vector42path);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(216, 40, 29, 255)) ,new PaintGradientStop(0, new Color(240, 231, 11, 255)) ,new PaintGradientStop(1, new Color(101, 210, 16, 255)) ,}}, new RectF(364f, -368f, 167f, 6f));
canvas.StrokeSize  = 1;
canvas.Translate(364f, -368f);
var vector43Builder = new PathBuilder();
var vector43path = vector43Builder.BuildPath("M0 3C0 1.34315 1.34315 0 3 0L164 0C165.657 0 167 1.34315 167 3C167 4.65685 165.657 6 164 6L3 6C1.34314 6 0 4.65685 0 3Z");
canvas.DrawPath(vector43path);
canvas.RestoreState();


// View:     elipseView2
// NodeName: Ellipse 3
// NodeType: ELLIPSE
// NodeId:   8:254
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(137, 132, 120, 255)) ,new PaintGradientStop(1, new Color(255, 250, 237, 255)) ,}}, new RectF(476f, -373f, 16f, 16f));
canvas.FillEllipse(476f, -373f, 16f, 16f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(255, 249, 234, 255)) ,new PaintGradientStop(1, new Color(126, 122, 110, 255)) ,}}, new RectF(476f, -373f, 16f, 16f));
canvas.StrokeSize = 2;
canvas.DrawEllipse(476f, -373f, 16f, 16f);
canvas.RestoreState();


// View:     frameView124
// NodeName: Menu
// NodeType: FRAME
// NodeId:   10:735
canvas.SaveState();
canvas.RestoreState();


// View:     frameView125
// NodeName: Measure
// NodeType: GROUP
// NodeId:   10:736
canvas.SaveState();
canvas.RestoreState();


// View:     textView147
// NodeName: Разгон
// NodeType: TEXT
// NodeId:   10:737
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Разгон", 266f, 302f, 53f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView69
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   10:738
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(275f, 274f);
var vector44Builder = new PathBuilder();
var vector44path = vector44Builder.BuildPath("M21.35 25.375C24.4417 25.375 27.0521 24.2448 29.1812 21.9844C31.3104 19.724 32.375 17.0333 32.375 13.9125C32.375 10.7042 31.2302 8.02083 28.9406 5.8625C26.651 3.70417 23.9021 2.625 20.6938 2.625C19.0604 2.625 17.5292 2.82188 16.1 3.21563C14.6708 3.60938 13.1687 4.22917 11.5938 5.075L16.8875 7.04375C18.375 7.59792 19.3885 8.39271 19.9281 9.42812C20.4677 10.4635 20.7375 11.6375 20.7375 12.95C20.7375 14.7 20.1542 16.1656 18.9875 17.3469C17.8208 18.5281 16.3917 19.1187 14.7 19.1187L2.8875 19.1187C2.74167 19.7896 2.66146 20.6865 2.64688 21.8094C2.63229 22.9323 2.625 24.1208 2.625 25.375L21.35 25.375ZM3.19375 16.4937L14.4375 16.4937C15.5167 16.4937 16.399 16.151 17.0844 15.4656C17.7698 14.7802 18.1125 13.9417 18.1125 12.95C18.1125 12.1042 17.9156 11.3896 17.5219 10.8063C17.1281 10.2229 16.5521 9.8 15.7937 9.5375L9.05625 6.7375C7.59792 8.02083 6.3875 9.46458 5.425 11.0687C4.4625 12.6729 3.71875 14.4813 3.19375 16.4937L3.19375 16.4937ZM21.35 28L2.625 28C1.925 28 1.3125 27.7375 0.7875 27.2125C0.2625 26.6875 0 26.075 0 25.375L0 22.0938C0 18.9438 0.525 16.0198 1.575 13.3219C2.625 10.624 4.07604 8.29063 5.92812 6.32188C7.78021 4.35313 9.96771 2.80729 12.4906 1.68438C15.0135 0.561459 17.7479 0 20.6938 0C22.6479 0 24.4927 0.357292 26.2281 1.07188C27.9635 1.78646 29.4802 2.77083 30.7781 4.025C32.076 5.27917 33.1042 6.75208 33.8625 8.44375C34.6208 10.1354 35 11.9583 35 13.9125C35 15.8375 34.6427 17.6604 33.9281 19.3813C33.2135 21.1021 32.2365 22.5969 30.9969 23.8656C29.7573 25.1344 28.3135 26.1406 26.6656 26.8844C25.0177 27.6281 23.2458 28 21.35 28Z");
canvas.FillPath(vector44path);
canvas.RestoreState();


// View:     frameView126
// NodeName: Gauges
// NodeType: GROUP
// NodeId:   10:739
canvas.SaveState();
canvas.RestoreState();


// View:     textView148
// NodeName: Приборы
// NodeType: TEXT
// NodeId:   10:740
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Приборы", 346f, 302f, 61f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView70
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   10:741
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(359f, 274f);
var vector45Builder = new PathBuilder();
var vector45path = vector45Builder.BuildPath("M14.3917 20.77C15.0665 21.4418 16.0421 21.7559 17.3185 21.7121C18.5948 21.6682 19.5264 21.1935 20.1132 20.288L29.6196 5.43349L14.8758 15.0297C13.9956 15.614 13.5261 16.5488 13.4675 17.8341C13.4088 19.1195 13.7169 20.0981 14.3917 20.77L14.3917 20.77ZM17.5165 0C19.1889 0 20.9347 0.270214 22.7539 0.810642C24.573 1.35107 26.3188 2.27856 27.9912 3.59311L25.7026 5.2144C24.3823 4.33803 22.9666 3.68805 21.4555 3.26448C19.9445 2.8409 18.6315 2.62911 17.5165 2.62911C13.4088 2.62911 9.90255 4.08972 6.9978 7.01095C4.09305 9.93218 2.64068 13.4815 2.64068 17.6588C2.64068 18.9734 2.82406 20.3026 3.19082 21.6463C3.55758 22.9901 4.07838 24.2316 4.75322 25.3709L30.2358 25.3709C30.8813 24.3192 31.3947 23.0923 31.7762 21.6901C32.1576 20.2879 32.3483 18.915 32.3483 17.5712C32.3483 16.3443 32.1649 15.0224 31.7982 13.6056C31.4314 12.1888 30.7786 10.8816 29.8397 9.68388L31.5561 7.40532C32.6711 9.04121 33.5073 10.6844 34.0648 12.3349C34.6222 13.9854 34.9303 15.6432 34.989 17.3083C35.0477 19.061 34.8716 20.7115 34.4609 22.2598C34.0501 23.808 33.4486 25.2394 32.6564 26.554C32.3043 27.2259 31.9302 27.6348 31.5341 27.7809C31.138 27.927 30.6465 28 30.0597 28L4.92927 28C4.43047 28 3.93901 27.8758 3.45489 27.6275C2.97076 27.3792 2.61134 27.0214 2.37661 26.554C1.61375 25.1518 1.02693 23.7277 0.616158 22.2817C0.205386 20.8357 0 19.2947 0 17.6588C0 15.2342 0.462119 12.9484 1.38636 10.8013C2.31059 8.65415 3.56492 6.78456 5.14932 5.19249C6.73373 3.60042 8.58954 2.33698 10.7168 1.40219C12.844 0.467396 15.1106 -1.55674e-15 17.5165 0L17.5165 0Z");
canvas.FillPath(vector45path);
canvas.RestoreState();


// View:     frameView127
// NodeName: History
// NodeType: GROUP
// NodeId:   10:742
canvas.SaveState();
canvas.RestoreState();


// View:     textView149
// NodeName: История
// NodeType: TEXT
// NodeId:   10:743
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"История", 434f, 302f, 57f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView71
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   10:744
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(446f, 274f);
var vector46Builder = new PathBuilder();
var vector46path = vector46Builder.BuildPath("M2.40833 28C1.74722 28 1.18056 27.7744 0.708333 27.3233C0.236111 26.8722 8.38835e-16 26.3356 0 25.7133C0 25.06 0.236111 24.5 0.708333 24.0333C1.18056 23.5667 1.74722 23.3333 2.40833 23.3333C3.03796 23.3333 3.58102 23.5667 4.0375 24.0333C4.49398 24.5 4.72222 25.06 4.72222 25.7133C4.72222 26.3356 4.49398 26.8722 4.0375 27.3233C3.58102 27.7744 3.03796 28 2.40833 28ZM9.44444 27.0667L9.44444 24.2667L34 24.2667L34 27.0667L9.44444 27.0667ZM2.40833 16.3333C1.74722 16.3333 1.18056 16.1078 0.708333 15.6567C0.236111 15.2056 8.38835e-16 14.6533 0 14C0 13.3467 0.236111 12.7944 0.708333 12.3433C1.18056 11.8922 1.74722 11.6667 2.40833 11.6667C3.03796 11.6667 3.58102 11.9 4.0375 12.3667C4.49398 12.8333 4.72222 13.3778 4.72222 14C4.72222 14.6222 4.49398 15.1667 4.0375 15.6333C3.58102 16.1 3.03796 16.3333 2.40833 16.3333ZM9.44444 15.4L9.44444 12.6L34 12.6L34 15.4L9.44444 15.4ZM2.36111 4.66667C1.7 4.66667 1.1412 4.44111 0.684722 3.99C0.228241 3.53889 0 2.98667 0 2.33333C0 1.68 0.228241 1.12778 0.684722 0.676667C1.1412 0.225556 1.7 0 2.36111 0C3.02222 0 3.58102 0.225556 4.0375 0.676667C4.49398 1.12778 4.72222 1.68 4.72222 2.33333C4.72222 2.98667 4.49398 3.53889 4.0375 3.99C3.58102 4.44111 3.02222 4.66667 2.36111 4.66667ZM9.44444 3.73333L9.44444 0.933333L34 0.933333L34 3.73333L9.44444 3.73333Z");
canvas.FillPath(vector46path);
canvas.RestoreState();


// View:     frameView128
// NodeName: Settings
// NodeType: GROUP
// NodeId:   10:745
canvas.SaveState();
canvas.RestoreState();


// View:     textView150
// NodeName: Настройки
// NodeType: TEXT
// NodeId:   10:746
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Настройки", 518f, 302f, 70f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView72
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   10:747
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(539f, 274f);
var vector47Builder = new PathBuilder();
var vector47path = vector47Builder.BuildPath("M10.78 28L10.08 23.59C9.63667 23.4267 9.17 23.205 8.68 22.925C8.19 22.645 7.75833 22.3533 7.385 22.05L3.255 23.94L0 18.2L3.78 15.435C3.73333 15.225 3.70417 14.9858 3.6925 14.7175C3.68083 14.4492 3.675 14.21 3.675 14C3.675 13.79 3.68083 13.5508 3.6925 13.2825C3.70417 13.0142 3.73333 12.775 3.78 12.565L0 9.8L3.255 4.06L7.385 5.95C7.75833 5.64667 8.19 5.355 8.68 5.075C9.17 4.795 9.63667 4.585 10.08 4.445L10.78 0L17.22 0L17.92 4.41C18.3633 4.57333 18.8358 4.78917 19.3375 5.0575C19.8392 5.32583 20.265 5.62333 20.615 5.95L24.745 4.06L28 9.8L24.22 12.495C24.2667 12.7283 24.2958 12.9792 24.3075 13.2475C24.3192 13.5158 24.325 13.7667 24.325 14C24.325 14.2333 24.3192 14.4783 24.3075 14.735C24.2958 14.9917 24.2667 15.2367 24.22 15.47L28 18.2L24.745 23.94L20.615 22.05C20.2417 22.3533 19.8158 22.6508 19.3375 22.9425C18.8592 23.2342 18.3867 23.45 17.92 23.59L17.22 28L10.78 28ZM14 18.55C15.26 18.55 16.3333 18.1067 17.22 17.22C18.1067 16.3333 18.55 15.26 18.55 14C18.55 12.74 18.1067 11.6667 17.22 10.78C16.3333 9.89333 15.26 9.45 14 9.45C12.74 9.45 11.6667 9.89333 10.78 10.78C9.89333 11.6667 9.45 12.74 9.45 14C9.45 15.26 9.89333 16.3333 10.78 17.22C11.6667 18.1067 12.74 18.55 14 18.55ZM14 16.45C13.3233 16.45 12.7458 16.2108 12.2675 15.7325C11.7892 15.2542 11.55 14.6767 11.55 14C11.55 13.3233 11.7892 12.7458 12.2675 12.2675C12.7458 11.7892 13.3233 11.55 14 11.55C14.6767 11.55 15.2542 11.7892 15.7325 12.2675C16.2108 12.7458 16.45 13.3233 16.45 14C16.45 14.6767 16.2108 15.2542 15.7325 15.7325C15.2542 16.2108 14.6767 16.45 14 16.45ZM12.46 25.9L15.54 25.9L16.03 21.98C16.8 21.7933 17.5292 21.5017 18.2175 21.105C18.9058 20.7083 19.53 20.23 20.09 19.67L23.8 21.28L25.2 18.76L21.91 16.345C22.0033 15.9483 22.0792 15.5575 22.1375 15.1725C22.1958 14.7875 22.225 14.3967 22.225 14C22.225 13.6033 22.2017 13.2125 22.155 12.8275C22.1083 12.4425 22.0267 12.0517 21.91 11.655L25.2 9.24L23.8 6.72L20.09 8.33C19.5533 7.72333 18.9467 7.21583 18.27 6.8075C17.5933 6.39917 16.8467 6.13667 16.03 6.02L15.54 2.1L12.46 2.1L11.97 6.02C11.1767 6.18333 10.4358 6.46333 9.7475 6.86C9.05917 7.25667 8.44667 7.74667 7.91 8.33L4.2 6.72L2.8 9.24L6.09 11.655C5.99667 12.0517 5.92083 12.4425 5.8625 12.8275C5.80417 13.2125 5.775 13.6033 5.775 14C5.775 14.3967 5.80417 14.7875 5.8625 15.1725C5.92083 15.5575 5.99667 15.9483 6.09 16.345L2.8 18.76L4.2 21.28L7.91 19.67C8.47 20.23 9.09417 20.7083 9.7825 21.105C10.4708 21.5017 11.2 21.7933 11.97 21.98L12.46 25.9Z");
canvas.FillPath(vector47path);
canvas.RestoreState();


// View:     frameView129
// NodeName: Gauges Init Screen
// NodeType: FRAME
// NodeId:   51:256
canvas.SaveState();
canvas.RestoreState();


// View:     frameView130
// NodeName: iPhone 11 Pro
// NodeType: FRAME
// NodeId:   51:373
canvas.SaveState();
canvas.RestoreState();


// View:     frameView131
// NodeName: iPhone 11 Pro
// NodeType: INSTANCE
// NodeId:   51:374
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(52, 60, 69, 255)) ,new PaintGradientStop(1, new Color(17, 22, 29, 255)) ,}}, new RectF(241f, -1366f, 375f, 812f));
canvas.FillRoundedRectangle(241f, -1366f, 375f, 812f, 55f);
canvas.RestoreState();


// View:     frameView132
// NodeName: Gauges
// NodeType: GROUP
// NodeId:   51:375
canvas.SaveState();
canvas.RestoreState();


// View:     frameView133
// NodeName: Group 21
// NodeType: GROUP
// NodeId:   51:376
canvas.SaveState();
canvas.RestoreState();


// View:     rectangleView22
// NodeName: Results screen
// NodeType: RECTANGLE
// NodeId:   51:377
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(35, 35, 35, 255)) ,new PaintGradientStop(1, new Color(0, 0, 0, 255)) ,}}, new RectF(267f, -988f, 324f, 331f));
canvas.FillRoundedRectangle(267f, -988f, 324f, 331f, 25f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(21, 25, 30, 255)) ,new PaintGradientStop(1, new Color(66, 70, 75, 255)) ,}}, new RectF(267f, -988f, 324f, 331f));
canvas.StrokeSize  = 2;
canvas.DrawRoundedRectangle(267f, -988f, 324f, 331f, 25f);
canvas.RestoreState();


// View:     frameView134
// NodeName: Group 12
// NodeType: GROUP
// NodeId:   51:378
canvas.SaveState();
canvas.RestoreState();


// View:     textView151
// NodeName: -
// NodeType: TEXT
// NodeId:   51:379
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 32f;
canvas.DrawString(@"-", 286f, -964.22186f, 126f, 22.554016f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView152
// NodeName: Высота
// NodeType: TEXT
// NodeId:   51:380
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Высота", 286f, -931.3883f, 126f, 13.999084f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView135
// NodeName: Group 18
// NodeType: GROUP
// NodeId:   51:381
canvas.SaveState();
canvas.RestoreState();


// View:     textView153
// NodeName: -
// NodeType: TEXT
// NodeId:   51:382
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 32f;
canvas.DrawString(@"-", 446f, -964.3571f, 126f, 22.554016f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView154
// NodeName: Курс
// NodeType: TEXT
// NodeId:   51:383
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Курс", 446f, -931.52356f, 126f, 13.999084f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView136
// NodeName: Group 14
// NodeType: GROUP
// NodeId:   51:384
canvas.SaveState();
canvas.RestoreState();


// View:     textView155
// NodeName: -
// NodeType: TEXT
// NodeId:   51:385
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 32f;
canvas.DrawString(@"-", 280f, -883.33844f, 138f, 22.554016f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView156
// NodeName: Широта
// NodeType: TEXT
// NodeId:   51:386
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Широта", 286f, -850.5049f, 126f, 13.999084f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView137
// NodeName: Group 15
// NodeType: GROUP
// NodeId:   51:387
canvas.SaveState();
canvas.RestoreState();


// View:     textView157
// NodeName: -
// NodeType: TEXT
// NodeId:   51:388
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 32f;
canvas.DrawString(@"-", 440f, -883.33844f, 138f, 22.554016f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView158
// NodeName: Долгота
// NodeType: TEXT
// NodeId:   51:389
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Долгота", 446f, -850.5049f, 126f, 13.999084f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView138
// NodeName: Group 13
// NodeType: GROUP
// NodeId:   51:390
canvas.SaveState();
canvas.RestoreState();


// View:     textView159
// NodeName: -
// NodeType: TEXT
// NodeId:   51:391
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 32f;
canvas.DrawString(@"-", 446f, -800.913f, 126f, 22.554016f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView160
// NodeName: Уклон
// NodeType: TEXT
// NodeId:   51:392
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Уклон", 446f, -768.07947f, 126f, 13.999084f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView139
// NodeName: Group 16
// NodeType: GROUP
// NodeId:   51:393
canvas.SaveState();
canvas.RestoreState();


// View:     textView161
// NodeName: -
// NodeType: TEXT
// NodeId:   51:394
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 32f;
canvas.DrawString(@"-", 446f, -719.70496f, 126f, 22.554016f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView162
// NodeName: Частота
// NodeType: TEXT
// NodeId:   51:395
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Частота", 446f, -686.8714f, 126f, 13.999084f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView140
// NodeName: Group 19
// NodeType: GROUP
// NodeId:   51:396
canvas.SaveState();
canvas.RestoreState();


// View:     textView163
// NodeName: -
// NodeType: TEXT
// NodeId:   51:397
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 32f;
canvas.DrawString(@"-", 286f, -719.70496f, 126f, 22.554016f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView164
// NodeName: Прибор
// NodeType: TEXT
// NodeId:   51:398
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Прибор", 286f, -686.8714f, 126f, 13.999084f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView141
// NodeName: Group 17
// NodeType: GROUP
// NodeId:   51:399
canvas.SaveState();
canvas.RestoreState();


// View:     textView165
// NodeName: -
// NodeType: TEXT
// NodeId:   51:400
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 32f;
canvas.DrawString(@"-", 281f, -801.67725f, 136f, 22.554016f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView166
// NodeName: Макс. скор.
// NodeType: TEXT
// NodeId:   51:401
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 16f;
canvas.DrawString(@"Макс. скор.", 286f, -768.8437f, 126f, 13.999084f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     rectangleView23
// NodeName: Rectangle 5
// NodeType: RECTANGLE
// NodeId:   51:402
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(274f, -979.77637f, 150f, 74.01245f, 0f);
canvas.StrokeColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.StrokeSize  = 1;
canvas.DrawRoundedRectangle(274f, -979.77637f, 150f, 74.01245f, 0f);
canvas.RestoreState();


// View:     rectangleView24
// NodeName: Rectangle 6
// NodeType: RECTANGLE
// NodeId:   51:403
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(274f, -898.5683f, 150f, 74.01239f, 0f);
canvas.StrokeColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.StrokeSize  = 1;
canvas.DrawRoundedRectangle(274f, -898.5683f, 150f, 74.01239f, 0f);
canvas.RestoreState();


// View:     rectangleView25
// NodeName: Rectangle 7
// NodeType: RECTANGLE
// NodeId:   51:404
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(274f, -817.3602f, 150f, 74.01245f, 0f);
canvas.StrokeColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.StrokeSize  = 1;
canvas.DrawRoundedRectangle(274f, -817.3602f, 150f, 74.01245f, 0f);
canvas.RestoreState();


// View:     rectangleView26
// NodeName: Rectangle 11
// NodeType: RECTANGLE
// NodeId:   51:405
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(274f, -736.15216f, 150f, 74.01239f, 0f);
canvas.StrokeColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.StrokeSize  = 1;
canvas.DrawRoundedRectangle(274f, -736.15216f, 150f, 74.01239f, 0f);
canvas.RestoreState();


// View:     rectangleView27
// NodeName: Rectangle 12
// NodeType: RECTANGLE
// NodeId:   51:406
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(434f, -736.15216f, 150f, 74.01239f, 0f);
canvas.StrokeColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.StrokeSize  = 1;
canvas.DrawRoundedRectangle(434f, -736.15216f, 150f, 74.01239f, 0f);
canvas.RestoreState();


// View:     rectangleView28
// NodeName: Rectangle 8
// NodeType: RECTANGLE
// NodeId:   51:407
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(434f, -979.77637f, 150f, 74.01245f, 0f);
canvas.StrokeColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.StrokeSize  = 1;
canvas.DrawRoundedRectangle(434f, -979.77637f, 150f, 74.01245f, 0f);
canvas.RestoreState();


// View:     rectangleView29
// NodeName: Rectangle 9
// NodeType: RECTANGLE
// NodeId:   51:408
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(434f, -898.5683f, 150f, 74.01239f, 0f);
canvas.StrokeColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.StrokeSize  = 1;
canvas.DrawRoundedRectangle(434f, -898.5683f, 150f, 74.01239f, 0f);
canvas.RestoreState();


// View:     rectangleView30
// NodeName: Rectangle 10
// NodeType: RECTANGLE
// NodeId:   51:409
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(434f, -817.3602f, 150f, 74.01245f, 0f);
canvas.StrokeColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.StrokeSize  = 1;
canvas.DrawRoundedRectangle(434f, -817.3602f, 150f, 74.01245f, 0f);
canvas.RestoreState();


// View:     lineView24
// NodeName: Line 13
// NodeType: LINE
// NodeId:   51:410
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(429, -984), new Point(429, -662));
canvas.RestoreState();


// View:     lineView25
// NodeName: Line 14
// NodeType: LINE
// NodeId:   51:411
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(275, -901.65216), new Point(584, -901.65216));
canvas.RestoreState();


// View:     lineView26
// NodeName: Line 15
// NodeType: LINE
// NodeId:   51:412
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(274, -820.4441), new Point(583, -820.4441));
canvas.RestoreState();


// View:     lineView27
// NodeName: Line 16
// NodeType: LINE
// NodeId:   51:413
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(274, -739.236), new Point(583, -739.236));
canvas.RestoreState();


// View:     frameView142
// NodeName: Speed block
// NodeType: GROUP
// NodeId:   51:414
canvas.SaveState();
canvas.RestoreState();


// View:     frameView143
// NodeName: Speedometer
// NodeType: GROUP
// NodeId:   51:415
canvas.SaveState();
canvas.RestoreState();


// View:     elipseView3
// NodeName: Ellipse 1
// NodeType: ELLIPSE
// NodeId:   51:416
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(35, 35, 35, 255)) ,new PaintGradientStop(1, new Color(0, 0, 0, 255)) ,}}, new RectF(315f, -1235f, 168f, 168f));
canvas.FillEllipse(315f, -1235f, 168f, 168f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(21, 25, 30, 255)) ,new PaintGradientStop(1, new Color(66, 70, 75, 255)) ,}}, new RectF(315f, -1235f, 168f, 168f));
canvas.StrokeSize = 2;
canvas.DrawEllipse(315f, -1235f, 168f, 168f);
canvas.RestoreState();


// View:     textView167
// NodeName: -
// NodeType: TEXT
// NodeId:   51:417
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 40f;
canvas.DrawString(@"-", 332.64f, -1201.4f, 133.56f, 100.80005f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView168
// NodeName: км/ч
// NodeType: TEXT
// NodeId:   51:418
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"км/ч", 368.76f, -1126.92f, 61.320007f, 21.839966f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     elipseView4
// NodeName: Ellipse 2
// NodeType: ELLIPSE
// NodeId:   51:419
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillEllipse(321.72f, -1228.28f, 155.4f, 155.40002f);
canvas.StrokeSize = 8;
canvas.DrawEllipse(321.72f, -1228.28f, 155.4f, 155.40002f);
canvas.RestoreState();


// View:     frameView144
// NodeName: Accel gauge
// NodeType: GROUP
// NodeId:   51:420
canvas.SaveState();
canvas.RestoreState();


// View:     rectangleView31
// NodeName: Rectangle 3
// NodeType: RECTANGLE
// NodeId:   51:421
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(540f, -1231f, 6f, 157f, 9f);
canvas.RestoreState();


// View:     frameView145
// NodeName: G-scale
// NodeType: GROUP
// NodeId:   51:422
canvas.SaveState();
canvas.RestoreState();


// View:     imageView73
// NodeName: Line 2
// NodeType: VECTOR
// NodeId:   51:423

// View:     imageView74
// NodeName: Line 7
// NodeType: VECTOR
// NodeId:   51:424

// View:     imageView75
// NodeName: Line 3
// NodeType: VECTOR
// NodeId:   51:425

// View:     imageView76
// NodeName: Line 4
// NodeType: VECTOR
// NodeId:   51:426

// View:     imageView77
// NodeName: Line 9
// NodeType: VECTOR
// NodeId:   51:427

// View:     imageView78
// NodeName: Line 5
// NodeType: VECTOR
// NodeId:   51:428

// View:     imageView79
// NodeName: Line 10
// NodeType: VECTOR
// NodeId:   51:429

// View:     imageView80
// NodeName: Line 6
// NodeType: VECTOR
// NodeId:   51:430

// View:     imageView81
// NodeName: Line 8
// NodeType: VECTOR
// NodeId:   51:431

// View:     frameView146
// NodeName: Group 10
// NodeType: GROUP
// NodeId:   51:435
canvas.SaveState();
canvas.RestoreState();


// View:     textView169
// NodeName: -0.8
// NodeType: TEXT
// NodeId:   51:436
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"-0.8", 497f, -1229f, 27f, 11f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView170
// NodeName: -0.4
// NodeType: TEXT
// NodeId:   51:437
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"-0.4", 497f, -1192f, 27f, 11f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView171
// NodeName: +0.8
// NodeType: TEXT
// NodeId:   51:438
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"+0.8", 492f, -1089f, 32f, 12f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView172
// NodeName: +0.4
// NodeType: TEXT
// NodeId:   51:439
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"+0.4", 492f, -1126f, 32f, 14f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView173
// NodeName: 0
// NodeType: TEXT
// NodeId:   51:440
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"0", 507f, -1157f, 17f, 12f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView147
// NodeName: Lat gauge
// NodeType: GROUP
// NodeId:   51:441
canvas.SaveState();
canvas.RestoreState();


// View:     frameView148
// NodeName: G-scale
// NodeType: GROUP
// NodeId:   51:442
canvas.SaveState();
canvas.RestoreState();


// View:     frameView149
// NodeName: Group 11
// NodeType: GROUP
// NodeId:   51:443
canvas.SaveState();
canvas.RestoreState();


// View:     textView174
// NodeName: 1.2
// NodeType: TEXT
// NodeId:   51:444
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"1.2", 302f, -1053f, 33f, 14f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView175
// NodeName: 0.6
// NodeType: TEXT
// NodeId:   51:445
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"0.6", 344f, -1053f, 33f, 14f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView176
// NodeName: 0.6
// NodeType: TEXT
// NodeId:   51:446
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"0.6", 423f, -1053f, 33f, 14f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView177
// NodeName: 1.2
// NodeType: TEXT
// NodeId:   51:447
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"1.2", 466f, -1053f, 33f, 14f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView178
// NodeName: 0
// NodeType: TEXT
// NodeId:   51:448
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"0", 381f, -1053f, 33f, 14f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView150
// NodeName: G-scale
// NodeType: GROUP
// NodeId:   51:449
canvas.SaveState();
canvas.RestoreState();


// View:     imageView82
// NodeName: Line 2
// NodeType: VECTOR
// NodeId:   51:450

// View:     imageView83
// NodeName: Line 7
// NodeType: VECTOR
// NodeId:   51:451

// View:     imageView84
// NodeName: Line 3
// NodeType: VECTOR
// NodeId:   51:452

// View:     imageView85
// NodeName: Line 4
// NodeType: VECTOR
// NodeId:   51:453

// View:     imageView86
// NodeName: Line 9
// NodeType: VECTOR
// NodeId:   51:454

// View:     imageView87
// NodeName: Line 5
// NodeType: VECTOR
// NodeId:   51:455

// View:     imageView88
// NodeName: Line 10
// NodeType: VECTOR
// NodeId:   51:456

// View:     imageView89
// NodeName: Line 6
// NodeType: VECTOR
// NodeId:   51:457

// View:     imageView90
// NodeName: Line 8
// NodeType: VECTOR
// NodeId:   51:458

// View:     rectangleView32
// NodeName: Rectangle 2
// NodeType: RECTANGLE
// NodeId:   51:459
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(0, 0, 0);
canvas.Alpha  = 1;
canvas.FillRoundedRectangle(314f, -1024f, 176f, 6f, 9f);
canvas.RestoreState();


// View:     textView179
// NodeName: G
// NodeType: TEXT
// NodeId:   51:463
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 32f;
canvas.DrawString(@"G", 515f, -1045f, 37f, 32f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView151
// NodeName: Status block
// NodeType: GROUP
// NodeId:   51:464
canvas.SaveState();
canvas.RestoreState();


// View:     rectangleView33
// NodeName: Results screen
// NodeType: RECTANGLE
// NodeId:   51:465
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(35, 35, 35, 255)) ,new PaintGradientStop(1, new Color(0, 0, 0, 255)) ,}}, new RectF(267f, -1306f, 324f, 50f));
canvas.FillRoundedRectangle(267f, -1306f, 324f, 50f, 25f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(21, 25, 30, 255)) ,new PaintGradientStop(1, new Color(66, 70, 75, 255)) ,}}, new RectF(267f, -1306f, 324f, 50f));
canvas.StrokeSize  = 2;
canvas.DrawRoundedRectangle(267f, -1306f, 324f, 50f, 25f);
canvas.RestoreState();


// View:     frameView152
// NodeName: Sats
// NodeType: GROUP
// NodeId:   51:466
canvas.SaveState();
canvas.RestoreState();


// View:     textView180
// NodeName: 0
// NodeType: TEXT
// NodeId:   51:467
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"0", 305f, -1298f, 49f, 34f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView91
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:468
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(280f, -1297f);
var vector48Builder = new PathBuilder();
var vector48path = vector48Builder.BuildPath("M18.3621 30L18.3621 28.0583C21.056 28.0583 23.3459 27.1143 25.2317 25.2265C27.1175 23.3387 28.0603 21.0464 28.0603 18.3495L30 18.3495C30 19.9676 29.6929 21.4833 29.0787 22.8964C28.4644 24.3096 27.6347 25.5394 26.5894 26.5858C25.5442 27.6321 24.3157 28.4628 22.9041 29.0777C21.4925 29.6926 19.9784 30 18.3621 30ZM18.3621 26.1165L18.3621 24.1748C19.8491 24.1748 21.1907 23.5761 22.3869 22.3786C23.583 21.1812 24.181 19.8382 24.181 18.3495L26.1207 18.3495C26.1207 20.3991 25.3394 22.206 23.7769 23.7702C22.2144 25.3344 20.4095 26.1165 18.3621 26.1165ZM7.43534 29.4498C7.17672 29.4498 6.92349 29.4013 6.67565 29.3042C6.4278 29.2071 6.21767 29.0723 6.04526 28.8997L0.549569 23.3981C0.377155 23.2255 0.242457 23.0151 0.145474 22.767C0.0484914 22.5189 0 22.2654 0 22.0065C0 21.7476 0.0484914 21.4995 0.145474 21.2621C0.242457 21.0248 0.377155 20.8198 0.549569 20.6472L5.43103 15.7605C5.79741 15.3937 6.25539 15.2104 6.80496 15.2104C7.35453 15.2104 7.8125 15.3937 8.17888 15.7605L10.2478 17.8317L11.444 16.6343L9.375 14.5631C9.00862 14.1963 8.82543 13.7433 8.82543 13.2039C8.82543 12.6645 9.00862 12.2114 9.375 11.8447L11.7996 9.41748C12.1659 9.0507 12.6239 8.86731 13.1735 8.86731C13.7231 8.86731 14.181 9.0507 14.5474 9.41748L16.6164 11.4887L17.8125 10.2913L15.7435 8.22006C15.3772 7.85329 15.194 7.39482 15.194 6.84466C15.194 6.2945 15.3772 5.83603 15.7435 5.46926L20.625 0.582524C20.819 0.388349 21.0345 0.242718 21.2716 0.145631C21.5086 0.0485437 21.7565 0 22.0151 0C22.2737 0 22.5216 0.0431499 22.7586 0.12945C22.9957 0.21575 23.2112 0.355987 23.4052 0.550162L28.9009 6.05178C29.0948 6.24595 29.2349 6.4617 29.3211 6.69903C29.4073 6.93635 29.4504 7.18447 29.4504 7.44337C29.4504 7.70227 29.3966 7.95577 29.2888 8.20388C29.181 8.452 29.0409 8.66235 28.8685 8.83495L23.9871 13.7217C23.4914 14.2179 23.0334 14.466 22.6131 14.466C22.1929 14.466 21.7349 14.2179 21.2392 13.7217L19.1703 11.6505L17.9741 12.8479L20.0431 14.9191C20.4095 15.2859 20.5873 15.7443 20.5765 16.2945C20.5657 16.8447 20.3772 17.3031 20.0108 17.6699L17.6185 20.0647C17.2522 20.4315 16.7942 20.6149 16.2446 20.6149C15.695 20.6149 15.2371 20.4315 14.8707 20.0647L12.8017 17.9935L11.6056 19.1909L13.6746 21.2621C14.0409 21.6289 14.2241 22.0874 14.2241 22.6375C14.2241 23.1877 14.0409 23.6462 13.6746 24.0129L8.7931 28.8997C8.62069 29.0723 8.41595 29.2071 8.17888 29.3042C7.94181 29.4013 7.69397 29.4498 7.43534 29.4498L7.43534 29.4498ZM7.43534 27.5405L9.18103 25.7929L3.68534 20.2913L1.93966 22.0388L7.43534 27.5405ZM10.5711 24.4013L12.3168 22.6537L6.82112 17.1521L5.07543 18.8997L10.5711 24.4013ZM16.2608 18.7055L18.653 16.3107L13.1573 10.8091L10.7651 13.2039L16.2608 18.7055ZM22.6293 12.3301L24.375 10.5825L18.8793 5.08091L17.1336 6.82848L22.6293 12.3301ZM25.7651 9.19094L27.5108 7.44337L22.0151 1.94175L20.2694 3.68932L25.7651 9.19094Z");
canvas.FillPath(vector48path);
canvas.RestoreState();


// View:     lineView28
// NodeName: Line 11
// NodeType: LINE
// NodeId:   51:469
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(351, -1298), new Point(351, -1264));
canvas.RestoreState();


// View:     lineView29
// NodeName: Line 12
// NodeType: LINE
// NodeId:   51:470
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(543, -1298), new Point(543, -1264));
canvas.RestoreState();


// View:     imageView92
// NodeName: BLE
// NodeType: VECTOR
// NodeId:   51:471
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(555f, -1296f);
var vector49Builder = new PathBuilder();
var vector49path = vector49Builder.BuildPath("M8.35833 30L8.35833 17.7979L1.4875 25.3368L0 23.7047L7.93333 15L0 6.29534L1.4875 4.66321L8.35833 12.2021L8.35833 0L9.42083 0L17 8.31606L10.9083 15L17 21.6839L9.42083 30L8.35833 30ZM10.4833 12.2021L14.025 8.31606L10.4833 4.50777L10.4833 12.2021ZM10.4833 25.4922L14.025 21.6839L10.4833 17.7979L10.4833 25.4922Z");
canvas.FillPath(vector49path);
canvas.RestoreState();


// View:     frameView153
// NodeName: Signal
// NodeType: GROUP
// NodeId:   51:472
canvas.SaveState();
canvas.RestoreState();


// View:     textView181
// NodeName: Уровень сигнала
// NodeType: TEXT
// NodeId:   51:473
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"Уровень сигнала", 390f, -1297f, 115f, 16f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView93
// NodeName: Rectangle 3
// NodeType: VECTOR
// NodeId:   51:474
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(216, 40, 29, 255)) ,new PaintGradientStop(0, new Color(240, 231, 11, 255)) ,new PaintGradientStop(1, new Color(101, 210, 16, 255)) ,}}, new RectF(364f, -1276f, 167f, 6f));
canvas.Translate(364f, -1276f);
var vector50Builder = new PathBuilder();
var vector50path = vector50Builder.BuildPath("M0 3C0 1.34315 1.34315 0 3 0L164 0C165.657 0 167 1.34315 167 3L167 3C167 4.65685 165.657 6 164 6L3 6C1.34314 6 0 4.65685 0 3L0 3Z");
canvas.FillPath(vector50path);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(216, 40, 29, 255)) ,new PaintGradientStop(0, new Color(240, 231, 11, 255)) ,new PaintGradientStop(1, new Color(101, 210, 16, 255)) ,}}, new RectF(364f, -1276f, 167f, 6f));
canvas.StrokeSize  = 1;
canvas.Translate(364f, -1276f);
var vector51Builder = new PathBuilder();
var vector51path = vector51Builder.BuildPath("M0 3C0 1.34315 1.34315 0 3 0L164 0C165.657 0 167 1.34315 167 3L167 3C167 4.65685 165.657 6 164 6L3 6C1.34314 6 0 4.65685 0 3L0 3Z");
canvas.DrawPath(vector51path);
canvas.RestoreState();


// View:     elipseView5
// NodeName: Ellipse 3
// NodeType: ELLIPSE
// NodeId:   51:475
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(137, 132, 120, 255)) ,new PaintGradientStop(1, new Color(255, 250, 237, 255)) ,}}, new RectF(365f, -1281f, 16f, 16f));
canvas.FillEllipse(365f, -1281f, 16f, 16f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(255, 249, 234, 255)) ,new PaintGradientStop(1, new Color(126, 122, 110, 255)) ,}}, new RectF(365f, -1281f, 16f, 16f));
canvas.StrokeSize = 2;
canvas.DrawEllipse(365f, -1281f, 16f, 16f);
canvas.RestoreState();


// View:     frameView154
// NodeName: Menu
// NodeType: FRAME
// NodeId:   51:476
canvas.SaveState();
canvas.RestoreState();


// View:     frameView155
// NodeName: Measure
// NodeType: GROUP
// NodeId:   51:477
canvas.SaveState();
canvas.RestoreState();


// View:     textView182
// NodeName: Разгон
// NodeType: TEXT
// NodeId:   51:478
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Разгон", 266f, -606f, 53f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView94
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:479
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(275f, -634f);
var vector52Builder = new PathBuilder();
var vector52path = vector52Builder.BuildPath("M21.35 25.375C24.4417 25.375 27.0521 24.2448 29.1812 21.9844C31.3104 19.724 32.375 17.0333 32.375 13.9125C32.375 10.7042 31.2302 8.02083 28.9406 5.8625C26.651 3.70417 23.9021 2.625 20.6938 2.625C19.0604 2.625 17.5292 2.82188 16.1 3.21563C14.6708 3.60938 13.1687 4.22917 11.5938 5.075L16.8875 7.04375C18.375 7.59792 19.3885 8.39271 19.9281 9.42812C20.4677 10.4635 20.7375 11.6375 20.7375 12.95C20.7375 14.7 20.1542 16.1656 18.9875 17.3469C17.8208 18.5281 16.3917 19.1187 14.7 19.1187L2.8875 19.1187C2.74167 19.7896 2.66146 20.6865 2.64688 21.8094C2.63229 22.9323 2.625 24.1208 2.625 25.375L21.35 25.375ZM3.19375 16.4937L14.4375 16.4937C15.5167 16.4937 16.399 16.151 17.0844 15.4656C17.7698 14.7802 18.1125 13.9417 18.1125 12.95C18.1125 12.1042 17.9156 11.3896 17.5219 10.8063C17.1281 10.2229 16.5521 9.8 15.7937 9.5375L9.05625 6.7375C7.59792 8.02083 6.3875 9.46458 5.425 11.0687C4.4625 12.6729 3.71875 14.4813 3.19375 16.4937L3.19375 16.4937ZM21.35 28L2.625 28C1.925 28 1.3125 27.7375 0.7875 27.2125C0.2625 26.6875 0 26.075 0 25.375L0 22.0938C0 18.9438 0.525 16.0198 1.575 13.3219C2.625 10.624 4.07604 8.29063 5.92812 6.32188C7.78021 4.35313 9.96771 2.80729 12.4906 1.68438C15.0135 0.561459 17.7479 0 20.6938 0C22.6479 0 24.4927 0.357292 26.2281 1.07188C27.9635 1.78646 29.4802 2.77083 30.7781 4.025C32.076 5.27917 33.1042 6.75208 33.8625 8.44375C34.6208 10.1354 35 11.9583 35 13.9125C35 15.8375 34.6427 17.6604 33.9281 19.3813C33.2135 21.1021 32.2365 22.5969 30.9969 23.8656C29.7573 25.1344 28.3135 26.1406 26.6656 26.8844C25.0177 27.6281 23.2458 28 21.35 28Z");
canvas.FillPath(vector52path);
canvas.RestoreState();


// View:     frameView156
// NodeName: Gauges
// NodeType: GROUP
// NodeId:   51:480
canvas.SaveState();
canvas.RestoreState();


// View:     textView183
// NodeName: Приборы
// NodeType: TEXT
// NodeId:   51:481
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Приборы", 346f, -606f, 61f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView95
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:482
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(359f, -634f);
var vector53Builder = new PathBuilder();
var vector53path = vector53Builder.BuildPath("M14.3917 20.77C15.0665 21.4418 16.0421 21.7559 17.3185 21.7121C18.5948 21.6682 19.5264 21.1935 20.1132 20.288L29.6196 5.43349L14.8758 15.0297C13.9956 15.614 13.5261 16.5488 13.4675 17.8341C13.4088 19.1195 13.7169 20.0981 14.3917 20.77L14.3917 20.77ZM17.5165 0C19.1889 0 20.9347 0.270214 22.7539 0.810642C24.573 1.35107 26.3188 2.27856 27.9912 3.59311L25.7026 5.2144C24.3823 4.33803 22.9666 3.68805 21.4555 3.26448C19.9445 2.8409 18.6315 2.62911 17.5165 2.62911C13.4088 2.62911 9.90255 4.08972 6.9978 7.01095C4.09305 9.93218 2.64068 13.4815 2.64068 17.6588C2.64068 18.9734 2.82406 20.3026 3.19082 21.6463C3.55758 22.9901 4.07838 24.2316 4.75322 25.3709L30.2358 25.3709C30.8813 24.3192 31.3947 23.0923 31.7762 21.6901C32.1576 20.2879 32.3483 18.915 32.3483 17.5712C32.3483 16.3443 32.1649 15.0224 31.7982 13.6056C31.4314 12.1888 30.7786 10.8816 29.8397 9.68388L31.5561 7.40532C32.6711 9.04121 33.5073 10.6844 34.0648 12.3349C34.6222 13.9854 34.9303 15.6432 34.989 17.3083C35.0477 19.061 34.8716 20.7115 34.4609 22.2598C34.0501 23.808 33.4486 25.2394 32.6564 26.554C32.3043 27.2259 31.9302 27.6348 31.5341 27.7809C31.138 27.927 30.6465 28 30.0597 28L4.92927 28C4.43047 28 3.93901 27.8758 3.45489 27.6275C2.97076 27.3792 2.61134 27.0214 2.37661 26.554C1.61375 25.1518 1.02693 23.7277 0.616158 22.2817C0.205386 20.8357 0 19.2947 0 17.6588C0 15.2342 0.462119 12.9484 1.38636 10.8013C2.31059 8.65415 3.56492 6.78456 5.14932 5.19249C6.73373 3.60042 8.58954 2.33698 10.7168 1.40219C12.844 0.467396 15.1106 -1.55674e-15 17.5165 0L17.5165 0Z");
canvas.FillPath(vector53path);
canvas.RestoreState();


// View:     frameView157
// NodeName: History
// NodeType: GROUP
// NodeId:   51:483
canvas.SaveState();
canvas.RestoreState();


// View:     textView184
// NodeName: История
// NodeType: TEXT
// NodeId:   51:484
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"История", 434f, -606f, 57f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView96
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:485
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(446f, -634f);
var vector54Builder = new PathBuilder();
var vector54path = vector54Builder.BuildPath("M2.40833 28C1.74722 28 1.18056 27.7744 0.708333 27.3233C0.236111 26.8722 8.38835e-16 26.3356 0 25.7133C0 25.06 0.236111 24.5 0.708333 24.0333C1.18056 23.5667 1.74722 23.3333 2.40833 23.3333C3.03796 23.3333 3.58102 23.5667 4.0375 24.0333C4.49398 24.5 4.72222 25.06 4.72222 25.7133C4.72222 26.3356 4.49398 26.8722 4.0375 27.3233C3.58102 27.7744 3.03796 28 2.40833 28ZM9.44444 27.0667L9.44444 24.2667L34 24.2667L34 27.0667L9.44444 27.0667ZM2.40833 16.3333C1.74722 16.3333 1.18056 16.1078 0.708333 15.6567C0.236111 15.2056 8.38835e-16 14.6533 0 14C0 13.3467 0.236111 12.7944 0.708333 12.3433C1.18056 11.8922 1.74722 11.6667 2.40833 11.6667C3.03796 11.6667 3.58102 11.9 4.0375 12.3667C4.49398 12.8333 4.72222 13.3778 4.72222 14C4.72222 14.6222 4.49398 15.1667 4.0375 15.6333C3.58102 16.1 3.03796 16.3333 2.40833 16.3333ZM9.44444 15.4L9.44444 12.6L34 12.6L34 15.4L9.44444 15.4ZM2.36111 4.66667C1.7 4.66667 1.1412 4.44111 0.684722 3.99C0.228241 3.53889 0 2.98667 0 2.33333C0 1.68 0.228241 1.12778 0.684722 0.676667C1.1412 0.225556 1.7 0 2.36111 0C3.02222 0 3.58102 0.225556 4.0375 0.676667C4.49398 1.12778 4.72222 1.68 4.72222 2.33333C4.72222 2.98667 4.49398 3.53889 4.0375 3.99C3.58102 4.44111 3.02222 4.66667 2.36111 4.66667ZM9.44444 3.73333L9.44444 0.933333L34 0.933333L34 3.73333L9.44444 3.73333Z");
canvas.FillPath(vector54path);
canvas.RestoreState();


// View:     frameView158
// NodeName: Settings
// NodeType: GROUP
// NodeId:   51:486
canvas.SaveState();
canvas.RestoreState();


// View:     textView185
// NodeName: Настройки
// NodeType: TEXT
// NodeId:   51:487
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Настройки", 518f, -606f, 70f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView97
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:488
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(539f, -634f);
var vector55Builder = new PathBuilder();
var vector55path = vector55Builder.BuildPath("M10.78 28L10.08 23.59C9.63667 23.4267 9.17 23.205 8.68 22.925C8.19 22.645 7.75833 22.3533 7.385 22.05L3.255 23.94L0 18.2L3.78 15.435C3.73333 15.225 3.70417 14.9858 3.6925 14.7175C3.68083 14.4492 3.675 14.21 3.675 14C3.675 13.79 3.68083 13.5508 3.6925 13.2825C3.70417 13.0142 3.73333 12.775 3.78 12.565L0 9.8L3.255 4.06L7.385 5.95C7.75833 5.64667 8.19 5.355 8.68 5.075C9.17 4.795 9.63667 4.585 10.08 4.445L10.78 0L17.22 0L17.92 4.41C18.3633 4.57333 18.8358 4.78917 19.3375 5.0575C19.8392 5.32583 20.265 5.62333 20.615 5.95L24.745 4.06L28 9.8L24.22 12.495C24.2667 12.7283 24.2958 12.9792 24.3075 13.2475C24.3192 13.5158 24.325 13.7667 24.325 14C24.325 14.2333 24.3192 14.4783 24.3075 14.735C24.2958 14.9917 24.2667 15.2367 24.22 15.47L28 18.2L24.745 23.94L20.615 22.05C20.2417 22.3533 19.8158 22.6508 19.3375 22.9425C18.8592 23.2342 18.3867 23.45 17.92 23.59L17.22 28L10.78 28ZM14 18.55C15.26 18.55 16.3333 18.1067 17.22 17.22C18.1067 16.3333 18.55 15.26 18.55 14C18.55 12.74 18.1067 11.6667 17.22 10.78C16.3333 9.89333 15.26 9.45 14 9.45C12.74 9.45 11.6667 9.89333 10.78 10.78C9.89333 11.6667 9.45 12.74 9.45 14C9.45 15.26 9.89333 16.3333 10.78 17.22C11.6667 18.1067 12.74 18.55 14 18.55ZM14 16.45C13.3233 16.45 12.7458 16.2108 12.2675 15.7325C11.7892 15.2542 11.55 14.6767 11.55 14C11.55 13.3233 11.7892 12.7458 12.2675 12.2675C12.7458 11.7892 13.3233 11.55 14 11.55C14.6767 11.55 15.2542 11.7892 15.7325 12.2675C16.2108 12.7458 16.45 13.3233 16.45 14C16.45 14.6767 16.2108 15.2542 15.7325 15.7325C15.2542 16.2108 14.6767 16.45 14 16.45ZM12.46 25.9L15.54 25.9L16.03 21.98C16.8 21.7933 17.5292 21.5017 18.2175 21.105C18.9058 20.7083 19.53 20.23 20.09 19.67L23.8 21.28L25.2 18.76L21.91 16.345C22.0033 15.9483 22.0792 15.5575 22.1375 15.1725C22.1958 14.7875 22.225 14.3967 22.225 14C22.225 13.6033 22.2017 13.2125 22.155 12.8275C22.1083 12.4425 22.0267 12.0517 21.91 11.655L25.2 9.24L23.8 6.72L20.09 8.33C19.5533 7.72333 18.9467 7.21583 18.27 6.8075C17.5933 6.39917 16.8467 6.13667 16.03 6.02L15.54 2.1L12.46 2.1L11.97 6.02C11.1767 6.18333 10.4358 6.46333 9.7475 6.86C9.05917 7.25667 8.44667 7.74667 7.91 8.33L4.2 6.72L2.8 9.24L6.09 11.655C5.99667 12.0517 5.92083 12.4425 5.8625 12.8275C5.80417 13.2125 5.775 13.6033 5.775 14C5.775 14.3967 5.80417 14.7875 5.8625 15.1725C5.92083 15.5575 5.99667 15.9483 6.09 16.345L2.8 18.76L4.2 21.28L7.91 19.67C8.47 20.23 9.09417 20.7083 9.7825 21.105C10.4708 21.5017 11.2 21.7933 11.97 21.98L12.46 25.9Z");
canvas.FillPath(vector55path);
canvas.RestoreState();


// View:     frameView159
// NodeName: Measure Screen
// NodeType: FRAME
// NodeId:   5:156
canvas.SaveState();
canvas.RestoreState();


// View:     frameView160
// NodeName: iPhone 11 Pro
// NodeType: COMPONENT
// NodeId:   5:158
canvas.SaveState();
canvas.RestoreState();


// View:     frameView161
// NodeName: iPhone 11 Pro
// NodeType: INSTANCE
// NodeId:   5:136
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(52, 60, 69, 255)) ,new PaintGradientStop(1, new Color(17, 22, 29, 255)) ,}}, new RectF(-210f, -458f, 375f, 812f));
canvas.FillRoundedRectangle(-210f, -458f, 375f, 812f, 55f);
canvas.RestoreState();


// View:     frameView162
// NodeName: Speedometer
// NodeType: GROUP
// NodeId:   5:132
canvas.SaveState();
canvas.RestoreState();


// View:     elipseView6
// NodeName: Ellipse 1
// NodeType: ELLIPSE
// NodeId:   3:3
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(35, 35, 35, 255)) ,new PaintGradientStop(1, new Color(0, 0, 0, 255)) ,}}, new RectF(-123f, -316f, 200f, 200f));
canvas.FillEllipse(-123f, -316f, 200f, 200f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(21, 25, 30, 255)) ,new PaintGradientStop(1, new Color(66, 70, 75, 255)) ,}}, new RectF(-123f, -316f, 200f, 200f));
canvas.StrokeSize = 2;
canvas.DrawEllipse(-123f, -316f, 200f, 200f);
canvas.RestoreState();


// View:     textView186
// NodeName: 156
// NodeType: TEXT
// NodeId:   3:4
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 70f;
canvas.DrawString(@"156", -102f, -276f, 159f, 120f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView187
// NodeName: км/ч
// NodeType: TEXT
// NodeId:   3:7
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 24f;
canvas.DrawString(@"км/ч", -59f, -179f, 73f, 26f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     elipseView7
// NodeName: Ellipse 2
// NodeType: ELLIPSE
// NodeId:   3:5
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillEllipse(-115f, -308f, 185f, 185f);
canvas.StrokeSize = 8;
canvas.DrawEllipse(-115f, -308f, 185f, 185f);
canvas.RestoreState();


// View:     textView188
// NodeName: Ford Focus 2
// NodeType: TEXT
// NodeId:   26:4
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"Ford Focus 2", -68f, -280f, 90f, 26f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView163
// NodeName: Results
// NodeType: GROUP
// NodeId:   5:134
canvas.SaveState();
canvas.RestoreState();


// View:     rectangleView34
// NodeName: Results screen
// NodeType: RECTANGLE
// NodeId:   3:10
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(35, 35, 35, 255)) ,new PaintGradientStop(1, new Color(0, 0, 0, 255)) ,}}, new RectF(-185f, -1f, 324f, 253f));
canvas.FillRoundedRectangle(-185f, -1f, 324f, 253f, 25f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(21, 25, 30, 255)) ,new PaintGradientStop(1, new Color(66, 70, 75, 255)) ,}}, new RectF(-185f, -1f, 324f, 253f));
canvas.StrokeSize  = 2;
canvas.DrawRoundedRectangle(-185f, -1f, 324f, 253f, 25f);
canvas.RestoreState();


// View:     frameView164
// NodeName: Results
// NodeType: GROUP
// NodeId:   5:130
canvas.SaveState();
canvas.RestoreState();


// View:     frameView165
// NodeName: Group 4
// NodeType: GROUP
// NodeId:   5:35
canvas.SaveState();
canvas.RestoreState();


// View:     textView189
// NodeName: 0-60 км/ч
// NodeType: TEXT
// NodeId:   5:32
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"0-60 км/ч", -166.42671f, 10.452675f, 122.789825f, 26.396599f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView190
// NodeName: 4.63 с
// NodeType: TEXT
// NodeId:   5:33
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"4.63 с", -6.490494f, 10.452675f, 122.789825f, 26.396599f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     lineView30
// NodeName: Line 1
// NodeType: LINE
// NodeId:   5:34
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(-166.42783, 45.088863), new Point(116.3002, 45.088863));
canvas.RestoreState();


// View:     frameView166
// NodeName: Group 5
// NodeType: GROUP
// NodeId:   5:36
canvas.SaveState();
canvas.RestoreState();


// View:     textView191
// NodeName: 0-100 км/ч
// NodeType: TEXT
// NodeId:   5:37
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"0-100 км/ч", -166.42671f, 51.147434f, 122.789825f, 26.396603f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView192
// NodeName: 9.64 с
// NodeType: TEXT
// NodeId:   5:38
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"9.64 с", -6.490494f, 51.147434f, 122.789825f, 26.396603f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     lineView31
// NodeName: Line 1
// NodeType: LINE
// NodeId:   5:39
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(-166.42783, 85.78363), new Point(116.3002, 85.78363));
canvas.RestoreState();


// View:     frameView167
// NodeName: Group 6
// NodeType: GROUP
// NodeId:   5:40
canvas.SaveState();
canvas.RestoreState();


// View:     textView193
// NodeName: 201 м
// NodeType: TEXT
// NodeId:   5:41
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"201 м", -166.42671f, 91.84219f, 122.789825f, 26.396599f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView194
// NodeName: 10.71 с
// NodeType: TEXT
// NodeId:   5:42
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"10.71 с", -6.490494f, 91.84219f, 122.789825f, 26.396599f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     lineView32
// NodeName: Line 1
// NodeType: LINE
// NodeId:   5:43
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(-166.42783, 126.47838), new Point(116.3002, 126.47838));
canvas.RestoreState();


// View:     frameView168
// NodeName: Group 7
// NodeType: GROUP
// NodeId:   5:44
canvas.SaveState();
canvas.RestoreState();


// View:     textView195
// NodeName: 0-150 км/ч
// NodeType: TEXT
// NodeId:   5:45
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"0-150 км/ч", -166.42671f, 132.53697f, 122.789825f, 26.396606f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView196
// NodeName: 15.89 с
// NodeType: TEXT
// NodeId:   5:46
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"15.89 с", -6.490494f, 132.53697f, 122.789825f, 26.396606f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     lineView33
// NodeName: Line 1
// NodeType: LINE
// NodeId:   5:47
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(-166.42783, 167.17316), new Point(116.3002, 167.17316));
canvas.RestoreState();


// View:     frameView169
// NodeName: Group 8
// NodeType: GROUP
// NodeId:   5:48
canvas.SaveState();
canvas.RestoreState();


// View:     textView197
// NodeName: 402 м
// NodeType: TEXT
// NodeId:   5:49
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"402 м", -166.42671f, 173.23172f, 122.789825f, 26.396606f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView198
// NodeName: 17.42 с
// NodeType: TEXT
// NodeId:   5:50
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"17.42 с", -6.490494f, 173.23172f, 122.789825f, 26.396606f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     lineView34
// NodeName: Line 1
// NodeType: LINE
// NodeId:   5:51
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(-166.42783, 207.8679), new Point(116.3002, 207.8679));
canvas.RestoreState();


// View:     frameView170
// NodeName: Group 9
// NodeType: GROUP
// NodeId:   5:52
canvas.SaveState();
canvas.RestoreState();


// View:     textView199
// NodeName: 0-200 км/ч
// NodeType: TEXT
// NodeId:   5:53
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"0-200 км/ч", -166.42671f, 213.92645f, 122.789825f, 26.396606f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView200
// NodeName: 23.56 с
// NodeType: TEXT
// NodeId:   5:54
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"23.56 с", -6.490494f, 213.92645f, 122.789825f, 26.396606f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView171
// NodeName: Group 4
// NodeType: GROUP
// NodeId:   51:83
canvas.SaveState();
canvas.RestoreState();


// View:     textView201
// NodeName: 0-60 км/ч
// NodeType: TEXT
// NodeId:   51:84
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"0-60 км/ч", -166.42671f, 10.452667f, 122.789825f, 26.396599f, HorizontalAlignment.Left, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView202
// NodeName: 4.63 с
// NodeType: TEXT
// NodeId:   51:85
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"4.63 с", -6.490494f, 10.452667f, 122.789825f, 26.396599f, HorizontalAlignment.Right, VerticalAlignment.Center);
canvas.RestoreState();


// View:     lineView35
// NodeName: Line 1
// NodeType: LINE
// NodeId:   51:86
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(-166.42783, 45.088856), new Point(116.3002, 45.088856));
canvas.RestoreState();


// View:     frameView172
// NodeName: Menu
// NodeType: FRAME
// NodeId:   5:133
canvas.SaveState();
canvas.RestoreState();


// View:     frameView173
// NodeName: Measure
// NodeType: GROUP
// NodeId:   5:70
canvas.SaveState();
canvas.RestoreState();


// View:     textView203
// NodeName: Разгон
// NodeType: TEXT
// NodeId:   5:60
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Разгон", -185f, 302f, 53f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView98
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   5:59
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(-176f, 274f);
var vector56Builder = new PathBuilder();
var vector56path = vector56Builder.BuildPath("M21.35 25.375C24.4417 25.375 27.0521 24.2448 29.1812 21.9844C31.3104 19.724 32.375 17.0333 32.375 13.9125C32.375 10.7042 31.2302 8.02083 28.9406 5.8625C26.651 3.70417 23.9021 2.625 20.6938 2.625C19.0604 2.625 17.5292 2.82188 16.1 3.21563C14.6708 3.60938 13.1687 4.22917 11.5938 5.075L16.8875 7.04375C18.375 7.59792 19.3885 8.39271 19.9281 9.42812C20.4677 10.4635 20.7375 11.6375 20.7375 12.95C20.7375 14.7 20.1542 16.1656 18.9875 17.3469C17.8208 18.5281 16.3917 19.1187 14.7 19.1187L2.8875 19.1187C2.74167 19.7896 2.66146 20.6865 2.64688 21.8094C2.63229 22.9323 2.625 24.1208 2.625 25.375L21.35 25.375ZM3.19375 16.4937L14.4375 16.4937C15.5167 16.4937 16.399 16.151 17.0844 15.4656C17.7698 14.7802 18.1125 13.9417 18.1125 12.95C18.1125 12.1042 17.9156 11.3896 17.5219 10.8063C17.1281 10.2229 16.5521 9.8 15.7937 9.5375L9.05625 6.7375C7.59792 8.02083 6.3875 9.46458 5.425 11.0687C4.4625 12.6729 3.71875 14.4813 3.19375 16.4937L3.19375 16.4937ZM21.35 28L2.625 28C1.925 28 1.3125 27.7375 0.7875 27.2125C0.2625 26.6875 0 26.075 0 25.375L0 22.0938C0 18.9438 0.525 16.0198 1.575 13.3219C2.625 10.624 4.07604 8.29063 5.92812 6.32188C7.78021 4.35313 9.96771 2.80729 12.4906 1.68438C15.0135 0.561459 17.7479 0 20.6938 0C22.6479 0 24.4927 0.357292 26.2281 1.07188C27.9635 1.78646 29.4802 2.77083 30.7781 4.025C32.076 5.27917 33.1042 6.75208 33.8625 8.44375C34.6208 10.1354 35 11.9583 35 13.9125C35 15.8375 34.6427 17.6604 33.9281 19.3813C33.2135 21.1021 32.2365 22.5969 30.9969 23.8656C29.7573 25.1344 28.3135 26.1406 26.6656 26.8844C25.0177 27.6281 23.2458 28 21.35 28Z");
canvas.FillPath(vector56path);
canvas.RestoreState();


// View:     frameView174
// NodeName: Gauges
// NodeType: GROUP
// NodeId:   5:71
canvas.SaveState();
canvas.RestoreState();


// View:     textView204
// NodeName: Приборы
// NodeType: TEXT
// NodeId:   5:63
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Приборы", -105f, 302f, 61f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView99
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   5:62
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(-92f, 274f);
var vector57Builder = new PathBuilder();
var vector57path = vector57Builder.BuildPath("M14.3917 20.77C15.0665 21.4418 16.0421 21.7559 17.3185 21.7121C18.5948 21.6682 19.5264 21.1935 20.1132 20.288L29.6196 5.43349L14.8758 15.0297C13.9956 15.614 13.5261 16.5488 13.4675 17.8341C13.4088 19.1195 13.7169 20.0981 14.3917 20.77L14.3917 20.77ZM17.5165 0C19.1889 0 20.9347 0.270214 22.7539 0.810642C24.573 1.35107 26.3188 2.27856 27.9912 3.59311L25.7026 5.2144C24.3823 4.33803 22.9666 3.68805 21.4555 3.26448C19.9445 2.8409 18.6315 2.62911 17.5165 2.62911C13.4088 2.62911 9.90255 4.08972 6.9978 7.01095C4.09305 9.93218 2.64068 13.4815 2.64068 17.6588C2.64068 18.9734 2.82406 20.3026 3.19082 21.6463C3.55758 22.9901 4.07838 24.2316 4.75322 25.3709L30.2358 25.3709C30.8813 24.3192 31.3947 23.0923 31.7762 21.6901C32.1576 20.2879 32.3483 18.915 32.3483 17.5712C32.3483 16.3443 32.1649 15.0224 31.7982 13.6056C31.4314 12.1888 30.7786 10.8816 29.8397 9.68388L31.5561 7.40532C32.6711 9.04121 33.5073 10.6844 34.0648 12.3349C34.6222 13.9854 34.9303 15.6432 34.989 17.3083C35.0477 19.061 34.8716 20.7115 34.4609 22.2598C34.0501 23.808 33.4486 25.2394 32.6564 26.554C32.3043 27.2259 31.9302 27.6348 31.5341 27.7809C31.138 27.927 30.6465 28 30.0597 28L4.92927 28C4.43047 28 3.93901 27.8758 3.45489 27.6275C2.97076 27.3792 2.61134 27.0214 2.37661 26.554C1.61375 25.1518 1.02693 23.7277 0.616158 22.2817C0.205386 20.8357 0 19.2947 0 17.6588C0 15.2342 0.462119 12.9484 1.38636 10.8013C2.31059 8.65415 3.56492 6.78456 5.14932 5.19249C6.73373 3.60042 8.58954 2.33698 10.7168 1.40219C12.844 0.467396 15.1106 -1.55674e-15 17.5165 0L17.5165 0Z");
canvas.FillPath(vector57path);
canvas.RestoreState();


// View:     frameView175
// NodeName: History
// NodeType: GROUP
// NodeId:   5:72
canvas.SaveState();
canvas.RestoreState();


// View:     textView205
// NodeName: История
// NodeType: TEXT
// NodeId:   5:66
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"История", -17f, 302f, 57f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView100
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   5:65
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(-5f, 274f);
var vector58Builder = new PathBuilder();
var vector58path = vector58Builder.BuildPath("M2.40833 28C1.74722 28 1.18056 27.7744 0.708333 27.3233C0.236111 26.8722 8.38835e-16 26.3356 0 25.7133C0 25.06 0.236111 24.5 0.708333 24.0333C1.18056 23.5667 1.74722 23.3333 2.40833 23.3333C3.03796 23.3333 3.58102 23.5667 4.0375 24.0333C4.49398 24.5 4.72222 25.06 4.72222 25.7133C4.72222 26.3356 4.49398 26.8722 4.0375 27.3233C3.58102 27.7744 3.03796 28 2.40833 28ZM9.44444 27.0667L9.44444 24.2667L34 24.2667L34 27.0667L9.44444 27.0667ZM2.40833 16.3333C1.74722 16.3333 1.18056 16.1078 0.708333 15.6567C0.236111 15.2056 8.38835e-16 14.6533 0 14C0 13.3467 0.236111 12.7944 0.708333 12.3433C1.18056 11.8922 1.74722 11.6667 2.40833 11.6667C3.03796 11.6667 3.58102 11.9 4.0375 12.3667C4.49398 12.8333 4.72222 13.3778 4.72222 14C4.72222 14.6222 4.49398 15.1667 4.0375 15.6333C3.58102 16.1 3.03796 16.3333 2.40833 16.3333ZM9.44444 15.4L9.44444 12.6L34 12.6L34 15.4L9.44444 15.4ZM2.36111 4.66667C1.7 4.66667 1.1412 4.44111 0.684722 3.99C0.228241 3.53889 0 2.98667 0 2.33333C0 1.68 0.228241 1.12778 0.684722 0.676667C1.1412 0.225556 1.7 0 2.36111 0C3.02222 0 3.58102 0.225556 4.0375 0.676667C4.49398 1.12778 4.72222 1.68 4.72222 2.33333C4.72222 2.98667 4.49398 3.53889 4.0375 3.99C3.58102 4.44111 3.02222 4.66667 2.36111 4.66667ZM9.44444 3.73333L9.44444 0.933333L34 0.933333L34 3.73333L9.44444 3.73333Z");
canvas.FillPath(vector58path);
canvas.RestoreState();


// View:     frameView176
// NodeName: Settings
// NodeType: GROUP
// NodeId:   5:73
canvas.SaveState();
canvas.RestoreState();


// View:     textView206
// NodeName: Настройки
// NodeType: TEXT
// NodeId:   5:69
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Настройки", 67f, 302f, 70f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView101
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   5:68
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(88f, 274f);
var vector59Builder = new PathBuilder();
var vector59path = vector59Builder.BuildPath("M10.78 28L10.08 23.59C9.63667 23.4267 9.17 23.205 8.68 22.925C8.19 22.645 7.75833 22.3533 7.385 22.05L3.255 23.94L0 18.2L3.78 15.435C3.73333 15.225 3.70417 14.9858 3.6925 14.7175C3.68083 14.4492 3.675 14.21 3.675 14C3.675 13.79 3.68083 13.5508 3.6925 13.2825C3.70417 13.0142 3.73333 12.775 3.78 12.565L0 9.8L3.255 4.06L7.385 5.95C7.75833 5.64667 8.19 5.355 8.68 5.075C9.17 4.795 9.63667 4.585 10.08 4.445L10.78 0L17.22 0L17.92 4.41C18.3633 4.57333 18.8358 4.78917 19.3375 5.0575C19.8392 5.32583 20.265 5.62333 20.615 5.95L24.745 4.06L28 9.8L24.22 12.495C24.2667 12.7283 24.2958 12.9792 24.3075 13.2475C24.3192 13.5158 24.325 13.7667 24.325 14C24.325 14.2333 24.3192 14.4783 24.3075 14.735C24.2958 14.9917 24.2667 15.2367 24.22 15.47L28 18.2L24.745 23.94L20.615 22.05C20.2417 22.3533 19.8158 22.6508 19.3375 22.9425C18.8592 23.2342 18.3867 23.45 17.92 23.59L17.22 28L10.78 28ZM14 18.55C15.26 18.55 16.3333 18.1067 17.22 17.22C18.1067 16.3333 18.55 15.26 18.55 14C18.55 12.74 18.1067 11.6667 17.22 10.78C16.3333 9.89333 15.26 9.45 14 9.45C12.74 9.45 11.6667 9.89333 10.78 10.78C9.89333 11.6667 9.45 12.74 9.45 14C9.45 15.26 9.89333 16.3333 10.78 17.22C11.6667 18.1067 12.74 18.55 14 18.55ZM14 16.45C13.3233 16.45 12.7458 16.2108 12.2675 15.7325C11.7892 15.2542 11.55 14.6767 11.55 14C11.55 13.3233 11.7892 12.7458 12.2675 12.2675C12.7458 11.7892 13.3233 11.55 14 11.55C14.6767 11.55 15.2542 11.7892 15.7325 12.2675C16.2108 12.7458 16.45 13.3233 16.45 14C16.45 14.6767 16.2108 15.2542 15.7325 15.7325C15.2542 16.2108 14.6767 16.45 14 16.45ZM12.46 25.9L15.54 25.9L16.03 21.98C16.8 21.7933 17.5292 21.5017 18.2175 21.105C18.9058 20.7083 19.53 20.23 20.09 19.67L23.8 21.28L25.2 18.76L21.91 16.345C22.0033 15.9483 22.0792 15.5575 22.1375 15.1725C22.1958 14.7875 22.225 14.3967 22.225 14C22.225 13.6033 22.2017 13.2125 22.155 12.8275C22.1083 12.4425 22.0267 12.0517 21.91 11.655L25.2 9.24L23.8 6.72L20.09 8.33C19.5533 7.72333 18.9467 7.21583 18.27 6.8075C17.5933 6.39917 16.8467 6.13667 16.03 6.02L15.54 2.1L12.46 2.1L11.97 6.02C11.1767 6.18333 10.4358 6.46333 9.7475 6.86C9.05917 7.25667 8.44667 7.74667 7.91 8.33L4.2 6.72L2.8 9.24L6.09 11.655C5.99667 12.0517 5.92083 12.4425 5.8625 12.8275C5.80417 13.2125 5.775 13.6033 5.775 14C5.775 14.3967 5.80417 14.7875 5.8625 15.1725C5.92083 15.5575 5.99667 15.9483 6.09 16.345L2.8 18.76L4.2 21.28L7.91 19.67C8.47 20.23 9.09417 20.7083 9.7825 21.105C10.4708 21.5017 11.2 21.7933 11.97 21.98L12.46 25.9Z");
canvas.FillPath(vector59path);
canvas.RestoreState();


// View:     frameView177
// NodeName: Button
// NodeType: GROUP
// NodeId:   5:131
canvas.SaveState();
canvas.RestoreState();


// View:     rectangleView35
// NodeName: Rectangle 1
// NodeType: RECTANGLE
// NodeId:   3:8
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(146, 67, 33, 255)) ,new PaintGradientStop(1, new Color(228, 127, 83, 255)) ,}}, new RectF(-98f, -83f, 150f, 49f));
canvas.FillRoundedRectangle(-98f, -83f, 150f, 49f, 31f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(229, 120, 73, 255)) ,new PaintGradientStop(1, new Color(184, 79, 33, 255)) ,}}, new RectF(-98f, -83f, 150f, 49f));
canvas.StrokeSize  = 2;
canvas.DrawRoundedRectangle(-98f, -83f, 150f, 49f, 31f);
canvas.RestoreState();


// View:     textView207
// NodeName: Старт
// NodeType: TEXT
// NodeId:   3:9
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 24f;
canvas.DrawString(@"Старт", -65.59259f, -71f, 85.18518f, 24f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView178
// NodeName: Add metric
// NodeType: GROUP
// NodeId:   31:16
canvas.SaveState();
canvas.RestoreState();


// View:     rectangleView36
// NodeName: Rectangle 1
// NodeType: RECTANGLE
// NodeId:   31:17
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(146, 67, 33, 255)) ,new PaintGradientStop(1, new Color(228, 127, 83, 255)) ,}}, new RectF(75f, -83f, 49f, 49f));
canvas.FillRoundedRectangle(75f, -83f, 49f, 49f, 31f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(229, 120, 73, 255)) ,new PaintGradientStop(1, new Color(184, 79, 33, 255)) ,}}, new RectF(75f, -83f, 49f, 49f));
canvas.StrokeSize  = 2;
canvas.DrawRoundedRectangle(75f, -83f, 49f, 49f, 31f);
canvas.RestoreState();


// View:     imageView102
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   31:29
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(87.66666f, -72.66666f);
var vector60Builder = new PathBuilder();
var vector60path = vector60Builder.BuildPath("M12 26.6667C10.3333 26.6667 8.77222 26.35 7.31667 25.7167C5.86111 25.0833 4.59444 24.2278 3.51667 23.15C2.43889 22.0722 1.58333 20.8056 0.95 19.35C0.316667 17.8944 0 16.3333 0 14.6667C0 13 0.316667 11.4389 0.95 9.98333C1.58333 8.52778 2.44444 7.26111 3.53333 6.18333C4.62222 5.10556 5.89444 4.25 7.35 3.61667C8.80556 2.98333 10.3667 2.66667 12.0333 2.66667C12.6111 2.66667 13.1944 2.71111 13.7833 2.8C14.3722 2.88889 14.9778 3.02222 15.6 3.2L15.6 5.33333C15.0222 5.11111 14.4333 4.94444 13.8333 4.83333C13.2333 4.72222 12.6333 4.66667 12.0333 4.66667C9.25556 4.66667 6.88889 5.63889 4.93333 7.58333C2.97778 9.52778 2 11.8889 2 14.6667C2 17.4444 2.97222 19.8056 4.91667 21.75C6.86111 23.6944 9.22222 24.6667 12 24.6667C14.7778 24.6667 17.1389 23.6944 19.0833 21.75C21.0278 19.8056 22 17.4444 22 14.6667C22 14.3333 21.9833 13.9833 21.95 13.6167C21.9167 13.25 21.8556 12.8333 21.7667 12.3667L23.8 12.3667C23.8667 12.7444 23.9167 13.1278 23.95 13.5167C23.9833 13.9056 24 14.2889 24 14.6667C24 16.3333 23.6833 17.8944 23.05 19.35C22.4167 20.8056 21.5611 22.0722 20.4833 23.15C19.4056 24.2278 18.1389 25.0833 16.6833 25.7167C15.2278 26.35 13.6667 26.6667 12 26.6667ZM15.8667 20.2333L10.8 15.1667L10.8 8L12.8 8L12.8 14.3667L17.2667 18.8333L15.8667 20.2333ZM21.8 10.3667L21.8 6.2L17.6 6.2L17.6 4.2L21.8 4.2L21.8 0L23.8 0L23.8 4.2L28 4.2L28 6.2L23.8 6.2L23.8 10.3667L21.8 10.3667Z");
canvas.FillPath(vector60path);
canvas.RestoreState();


// View:     frameView179
// NodeName: Choose vehicle
// NodeType: GROUP
// NodeId:   31:19
canvas.SaveState();
canvas.RestoreState();


// View:     rectangleView37
// NodeName: Rectangle 1
// NodeType: RECTANGLE
// NodeId:   31:20
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(146, 67, 33, 255)) ,new PaintGradientStop(1, new Color(228, 127, 83, 255)) ,}}, new RectF(-166f, -83f, 49f, 49f));
canvas.FillRoundedRectangle(-166f, -83f, 49f, 49f, 31f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(229, 120, 73, 255)) ,new PaintGradientStop(1, new Color(184, 79, 33, 255)) ,}}, new RectF(-166f, -83f, 49f, 49f));
canvas.StrokeSize  = 2;
canvas.DrawRoundedRectangle(-166f, -83f, 49f, 49f, 31f);
canvas.RestoreState();


// View:     imageView103
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   31:27
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(-155f, -70f);
var vector61Builder = new PathBuilder();
var vector61path = vector61Builder.BuildPath("M2.88889 19.9812L2.88889 21.9219C2.88889 22.2333 2.78657 22.4909 2.58194 22.6945C2.37731 22.8982 2.11852 23 1.80556 23L1.08333 23C0.77037 23 0.511574 22.8982 0.306945 22.6945C0.102315 22.4909 0 22.2333 0 21.9219L0 10.2781L3.06944 1.07812C3.18981 0.742708 3.38843 0.479166 3.66528 0.2875C3.94213 0.095833 4.26111 0 4.62222 0L21.3778 0C21.7389 0 22.0579 0.095833 22.3347 0.2875C22.6116 0.479166 22.8102 0.742708 22.9306 1.07812L26 10.2781L26 21.9219C26 22.2333 25.8977 22.4909 25.6931 22.6945C25.4884 22.8982 25.2296 23 24.9167 23L24.1583 23C23.8454 23 23.5926 22.8982 23.4 22.6945C23.2074 22.4909 23.1111 22.2333 23.1111 21.9219L23.1111 19.9812L2.88889 19.9812ZM2.99722 8.12187L23.0028 8.12187L21.0167 2.15625L4.98333 2.15625L2.99722 8.12187ZM2.16667 10.2781L2.16667 17.825L2.16667 10.2781ZM5.99444 16.0281C6.54815 16.0281 7.01157 15.8365 7.38472 15.4531C7.75787 15.0698 7.94444 14.6146 7.94444 14.0875C7.94444 13.5365 7.75787 13.0633 7.38472 12.668C7.01157 12.2727 6.54815 12.075 5.99444 12.075C5.44074 12.075 4.96528 12.2727 4.56806 12.668C4.17083 13.0633 3.97222 13.5365 3.97222 14.0875C3.97222 14.6385 4.17083 15.0997 4.56806 15.4711C4.96528 15.8424 5.44074 16.0281 5.99444 16.0281ZM20.0417 16.0281C20.5954 16.0281 21.0708 15.8365 21.4681 15.4531C21.8653 15.0698 22.0639 14.6146 22.0639 14.0875C22.0639 13.5365 21.8653 13.0633 21.4681 12.668C21.0708 12.2727 20.5954 12.075 20.0417 12.075C19.488 12.075 19.0245 12.2727 18.6514 12.668C18.2782 13.0633 18.0917 13.5365 18.0917 14.0875C18.0917 14.6385 18.2843 15.0997 18.6694 15.4711C19.0546 15.8424 19.512 16.0281 20.0417 16.0281ZM2.16667 17.825L23.8333 17.825L23.8333 10.2781L2.16667 10.2781L2.16667 17.825Z");
canvas.FillPath(vector61path);
canvas.RestoreState();


// View:     frameView180
// NodeName: Status block
// NodeType: GROUP
// NodeId:   8:256
canvas.SaveState();
canvas.RestoreState();


// View:     rectangleView38
// NodeName: Results screen
// NodeType: RECTANGLE
// NodeId:   8:257
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(35, 35, 35, 255)) ,new PaintGradientStop(1, new Color(0, 0, 0, 255)) ,}}, new RectF(-184f, -398f, 324f, 50f));
canvas.FillRoundedRectangle(-184f, -398f, 324f, 50f, 25f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(21, 25, 30, 255)) ,new PaintGradientStop(1, new Color(66, 70, 75, 255)) ,}}, new RectF(-184f, -398f, 324f, 50f));
canvas.StrokeSize  = 2;
canvas.DrawRoundedRectangle(-184f, -398f, 324f, 50f, 25f);
canvas.RestoreState();


// View:     frameView181
// NodeName: Sats
// NodeType: GROUP
// NodeId:   8:258
canvas.SaveState();
canvas.RestoreState();


// View:     textView208
// NodeName: 13
// NodeType: TEXT
// NodeId:   8:259
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"13", -146f, -390f, 49f, 34f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView104
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   8:260
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(-171f, -389f);
var vector62Builder = new PathBuilder();
var vector62path = vector62Builder.BuildPath("M18.3621 30L18.3621 28.0583C21.056 28.0583 23.3459 27.1143 25.2317 25.2265C27.1175 23.3387 28.0603 21.0464 28.0603 18.3495L30 18.3495C30 19.9676 29.6929 21.4833 29.0787 22.8964C28.4644 24.3096 27.6347 25.5394 26.5894 26.5858C25.5442 27.6321 24.3157 28.4628 22.9041 29.0777C21.4925 29.6926 19.9784 30 18.3621 30ZM18.3621 26.1165L18.3621 24.1748C19.8491 24.1748 21.1907 23.5761 22.3869 22.3786C23.583 21.1812 24.181 19.8382 24.181 18.3495L26.1207 18.3495C26.1207 20.3991 25.3394 22.206 23.7769 23.7702C22.2144 25.3344 20.4095 26.1165 18.3621 26.1165ZM7.43534 29.4498C7.17672 29.4498 6.92349 29.4013 6.67565 29.3042C6.4278 29.2071 6.21767 29.0723 6.04526 28.8997L0.549569 23.3981C0.377155 23.2255 0.242457 23.0151 0.145474 22.767C0.0484914 22.5189 0 22.2654 0 22.0065C0 21.7476 0.0484914 21.4995 0.145474 21.2621C0.242457 21.0248 0.377155 20.8198 0.549569 20.6472L5.43103 15.7605C5.79741 15.3937 6.25539 15.2104 6.80496 15.2104C7.35453 15.2104 7.8125 15.3937 8.17888 15.7605L10.2478 17.8317L11.444 16.6343L9.375 14.5631C9.00862 14.1963 8.82543 13.7433 8.82543 13.2039C8.82543 12.6645 9.00862 12.2114 9.375 11.8447L11.7996 9.41748C12.1659 9.0507 12.6239 8.86731 13.1735 8.86731C13.7231 8.86731 14.181 9.0507 14.5474 9.41748L16.6164 11.4887L17.8125 10.2913L15.7435 8.22006C15.3772 7.85329 15.194 7.39482 15.194 6.84466C15.194 6.2945 15.3772 5.83603 15.7435 5.46926L20.625 0.582524C20.819 0.388349 21.0345 0.242718 21.2716 0.145631C21.5086 0.0485437 21.7565 0 22.0151 0C22.2737 0 22.5216 0.0431499 22.7586 0.12945C22.9957 0.21575 23.2112 0.355987 23.4052 0.550162L28.9009 6.05178C29.0948 6.24595 29.2349 6.4617 29.3211 6.69903C29.4073 6.93635 29.4504 7.18447 29.4504 7.44337C29.4504 7.70227 29.3966 7.95577 29.2888 8.20388C29.181 8.452 29.0409 8.66235 28.8685 8.83495L23.9871 13.7217C23.4914 14.2179 23.0334 14.466 22.6131 14.466C22.1929 14.466 21.7349 14.2179 21.2392 13.7217L19.1703 11.6505L17.9741 12.8479L20.0431 14.9191C20.4095 15.2859 20.5873 15.7443 20.5765 16.2945C20.5657 16.8447 20.3772 17.3031 20.0108 17.6699L17.6185 20.0647C17.2522 20.4315 16.7942 20.6149 16.2446 20.6149C15.695 20.6149 15.2371 20.4315 14.8707 20.0647L12.8017 17.9935L11.6056 19.1909L13.6746 21.2621C14.0409 21.6289 14.2241 22.0874 14.2241 22.6375C14.2241 23.1877 14.0409 23.6462 13.6746 24.0129L8.7931 28.8997C8.62069 29.0723 8.41595 29.2071 8.17888 29.3042C7.94181 29.4013 7.69397 29.4498 7.43534 29.4498L7.43534 29.4498ZM7.43534 27.5405L9.18103 25.7929L3.68534 20.2913L1.93966 22.0388L7.43534 27.5405ZM10.5711 24.4013L12.3168 22.6537L6.82112 17.1521L5.07543 18.8997L10.5711 24.4013ZM16.2608 18.7055L18.653 16.3107L13.1573 10.8091L10.7651 13.2039L16.2608 18.7055ZM22.6293 12.3301L24.375 10.5825L18.8793 5.08091L17.1336 6.82848L22.6293 12.3301ZM25.7651 9.19094L27.5108 7.44337L22.0151 1.94175L20.2694 3.68932L25.7651 9.19094Z");
canvas.FillPath(vector62path);
canvas.RestoreState();


// View:     lineView36
// NodeName: Line 11
// NodeType: LINE
// NodeId:   8:261
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(-100, -390), new Point(-100, -356));
canvas.RestoreState();


// View:     lineView37
// NodeName: Line 12
// NodeType: LINE
// NodeId:   8:262
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(92, -390), new Point(92, -356));
canvas.RestoreState();


// View:     imageView105
// NodeName: BLE
// NodeType: VECTOR
// NodeId:   8:263
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(54, 167, 203);
canvas.Alpha  = 1;
canvas.Translate(104f, -388f);
var vector63Builder = new PathBuilder();
var vector63path = vector63Builder.BuildPath("M8.35833 30L8.35833 17.7979L1.4875 25.3368L0 23.7047L7.93333 15L0 6.29534L1.4875 4.66321L8.35833 12.2021L8.35833 0L9.42083 0L17 8.31606L10.9083 15L17 21.6839L9.42083 30L8.35833 30ZM10.4833 12.2021L14.025 8.31606L10.4833 4.50777L10.4833 12.2021ZM10.4833 25.4922L14.025 21.6839L10.4833 17.7979L10.4833 25.4922Z");
canvas.FillPath(vector63path);
canvas.RestoreState();


// View:     frameView182
// NodeName: Signal
// NodeType: GROUP
// NodeId:   8:264
canvas.SaveState();
canvas.RestoreState();


// View:     textView209
// NodeName: Уровень сигнала
// NodeType: TEXT
// NodeId:   8:265
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"Уровень сигнала", -61f, -389f, 115f, 16f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView106
// NodeName: Rectangle 3
// NodeType: VECTOR
// NodeId:   8:266
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(216, 40, 29, 255)) ,new PaintGradientStop(0, new Color(240, 231, 11, 255)) ,new PaintGradientStop(1, new Color(101, 210, 16, 255)) ,}}, new RectF(-87f, -368f, 167f, 6f));
canvas.Translate(-87f, -368f);
var vector64Builder = new PathBuilder();
var vector64path = vector64Builder.BuildPath("M0 3C0 1.34315 1.34315 0 3 0L164 0C165.657 0 167 1.34315 167 3C167 4.65685 165.657 6 164 6L3 6C1.34314 6 0 4.65685 0 3Z");
canvas.FillPath(vector64path);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(216, 40, 29, 255)) ,new PaintGradientStop(0, new Color(240, 231, 11, 255)) ,new PaintGradientStop(1, new Color(101, 210, 16, 255)) ,}}, new RectF(-87f, -368f, 167f, 6f));
canvas.StrokeSize  = 1;
canvas.Translate(-87f, -368f);
var vector65Builder = new PathBuilder();
var vector65path = vector65Builder.BuildPath("M0 3C0 1.34315 1.34315 0 3 0L164 0C165.657 0 167 1.34315 167 3C167 4.65685 165.657 6 164 6L3 6C1.34314 6 0 4.65685 0 3Z");
canvas.DrawPath(vector65path);
canvas.RestoreState();


// View:     elipseView8
// NodeName: Ellipse 3
// NodeType: ELLIPSE
// NodeId:   8:267
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(137, 132, 120, 255)) ,new PaintGradientStop(1, new Color(255, 250, 237, 255)) ,}}, new RectF(25f, -373f, 16f, 16f));
canvas.FillEllipse(25f, -373f, 16f, 16f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(255, 249, 234, 255)) ,new PaintGradientStop(1, new Color(126, 122, 110, 255)) ,}}, new RectF(25f, -373f, 16f, 16f));
canvas.StrokeSize = 2;
canvas.DrawEllipse(25f, -373f, 16f, 16f);
canvas.RestoreState();


// View:     frameView183
// NodeName: Group 25
// NodeType: GROUP
// NodeId:   26:3
canvas.SaveState();
canvas.RestoreState();


// View:     imageView107
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   17:899
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(96f, -186f);
var vector66Builder = new PathBuilder();
var vector66path = vector66Builder.BuildPath("M10.5 2.625L10.5 0L21 0L21 2.625L10.5 2.625ZM14.4375 22.1812L17.0625 22.1812L17.0625 12.1187L14.4375 12.1187L14.4375 22.1812ZM15.75 36.7062C13.5917 36.7062 11.5573 36.2906 9.64687 35.4594C7.73646 34.6281 6.06667 33.4979 4.6375 32.0687C3.20833 30.6396 2.07813 28.9698 1.24688 27.0594C0.415625 25.149 0 23.1146 0 20.9562C0 18.7979 0.415625 16.7635 1.24688 14.8531C2.07813 12.9427 3.20833 11.2729 4.6375 9.84375C6.06667 8.41458 7.73646 7.28437 9.64687 6.45312C11.5573 5.62187 13.5917 5.20625 15.75 5.20625C17.7042 5.20625 19.5417 5.53437 21.2625 6.19062C22.9833 6.84687 24.5146 7.75833 25.8562 8.925L28.0875 6.69375L29.925 8.53125L27.6938 10.7625C28.7438 11.9292 29.6406 13.3437 30.3844 15.0062C31.1281 16.6687 31.5 18.6521 31.5 20.9562C31.5 23.1146 31.0844 25.149 30.2531 27.0594C29.4219 28.9698 28.2917 30.6396 26.8625 32.0687C25.4333 33.4979 23.7635 34.6281 21.8531 35.4594C19.9427 36.2906 17.9083 36.7062 15.75 36.7062ZM15.75 34.0812C19.3958 34.0812 22.4948 32.8052 25.0469 30.2531C27.599 27.701 28.875 24.6021 28.875 20.9562C28.875 17.3104 27.599 14.2115 25.0469 11.6594C22.4948 9.10729 19.3958 7.83125 15.75 7.83125C12.1042 7.83125 9.00521 9.10729 6.45312 11.6594C3.90104 14.2115 2.625 17.3104 2.625 20.9562C2.625 24.6021 3.90104 27.701 6.45312 30.2531C9.00521 32.8052 12.1042 34.0812 15.75 34.0812Z");
canvas.FillPath(vector66path);
canvas.RestoreState();


// View:     textView210
// NodeName: 27.4 c
// NodeType: TEXT
// NodeId:   17:900
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 24f;
canvas.DrawString(@"27.4 c", 76f, -142f, 72f, 26f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView184
// NodeName: Group 24
// NodeType: GROUP
// NodeId:   26:2
canvas.SaveState();
canvas.RestoreState();


// View:     imageView108
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   17:902
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(-174f, -174f);
var vector67Builder = new PathBuilder();
var vector67path = vector67Builder.BuildPath("M3 24C2.2 24 1.5 23.7 0.9 23.1C0.3 22.5 0 21.8 0 21L0 3C0 2.23333 0.3 1.54167 0.9 0.925C1.5 0.308334 2.2 0 3 0L37 0C37.8 0 38.5 0.308334 39.1 0.925C39.7 1.54167 40 2.23333 40 3L40 21C40 21.8 39.7 22.5 39.1 23.1C38.5 23.7 37.8 24 37 24L3 24ZM3 21L37 21L37 3L30.5 3L30.5 12L27.5 12L27.5 3L21.5 3L21.5 12L18.5 12L18.5 3L12.5 3L12.5 12L9.5 12L9.5 3L3 3L3 21ZM9.5 12L12.5 12L9.5 12ZM18.5 12L21.5 12L18.5 12ZM27.5 12L30.5 12L27.5 12Z");
canvas.FillPath(vector67path);
canvas.RestoreState();


// View:     textView211
// NodeName: 407 м
// NodeType: TEXT
// NodeId:   17:903
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 24f;
canvas.DrawString(@"407 м", -193f, -142f, 78f, 26f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView185
// NodeName: Slope
// NodeType: GROUP
// NodeId:   5:57
canvas.SaveState();
canvas.RestoreState();


// View:     textView212
// NodeName: 2.5%
// NodeType: TEXT
// NodeId:   5:28
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 24f;
canvas.DrawString(@"2.5%", -188f, -302f, 68f, 26f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView109
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   5:26
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(-174f, -331f);
var vector68Builder = new PathBuilder();
var vector68path = vector68Builder.BuildPath("M2.1 24L0 21.9L10.95 10.95C12.0167 9.88333 13.3167 9.35 14.85 9.35C16.3833 9.35 17.6833 9.88333 18.75 10.95L21.05 13.25C21.55 13.75 22.1417 14 22.825 14C23.5083 14 24.1 13.75 24.6 13.25L34.85 3L29 3L29 0L40 0L40 11L37 11L37 5.15L26.7 15.4C25.6333 16.4667 24.3333 17 22.8 17C21.2667 17 19.9667 16.4667 18.9 15.4L16.55 13.05C16.0833 12.5833 15.5 12.35 14.8 12.35C14.1 12.35 13.5167 12.5833 13.05 13.05L2.1 24Z");
canvas.FillPath(vector68path);
canvas.RestoreState();


// View:     frameView186
// NodeName: Altitude
// NodeType: GROUP
// NodeId:   5:56
canvas.SaveState();
canvas.RestoreState();


// View:     textView213
// NodeName: 1955 м
// NodeType: TEXT
// NodeId:   5:31
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 24f;
canvas.DrawString(@"1955 м", 67f, -302f, 90f, 26f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView110
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   5:30
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(90f, -332f);
var vector69Builder = new PathBuilder();
var vector69path = vector69Builder.BuildPath("M0 24L12 8L21.75 21L38 21L26 5.05L19.75 13.35L17.85 10.85L26 0L44 24L0 24ZM6 21L18 21L12 13L6 21ZM6 21L18 21L6 21Z");
canvas.FillPath(vector69path);
canvas.RestoreState();


// View:     frameView187
// NodeName: Measure Init Screen
// NodeType: FRAME
// NodeId:   51:170
canvas.SaveState();
canvas.RestoreState();


// View:     frameView188
// NodeName: iPhone 11 Pro
// NodeType: COMPONENT
// NodeId:   51:171
canvas.SaveState();
canvas.RestoreState();


// View:     frameView189
// NodeName: iPhone 11 Pro
// NodeType: INSTANCE
// NodeId:   51:173
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(52, 60, 69, 255)) ,new PaintGradientStop(1, new Color(17, 22, 29, 255)) ,}}, new RectF(-210f, -1366f, 375f, 812f));
canvas.FillRoundedRectangle(-210f, -1366f, 375f, 812f, 55f);
canvas.RestoreState();


// View:     frameView190
// NodeName: Speedometer
// NodeType: GROUP
// NodeId:   51:174
canvas.SaveState();
canvas.RestoreState();


// View:     elipseView9
// NodeName: Ellipse 1
// NodeType: ELLIPSE
// NodeId:   51:175
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(35, 35, 35, 255)) ,new PaintGradientStop(1, new Color(0, 0, 0, 255)) ,}}, new RectF(-123f, -1224f, 200f, 200f));
canvas.FillEllipse(-123f, -1224f, 200f, 200f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(21, 25, 30, 255)) ,new PaintGradientStop(1, new Color(66, 70, 75, 255)) ,}}, new RectF(-123f, -1224f, 200f, 200f));
canvas.StrokeSize = 2;
canvas.DrawEllipse(-123f, -1224f, 200f, 200f);
canvas.RestoreState();


// View:     textView214
// NodeName: -
// NodeType: TEXT
// NodeId:   51:176
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 70f;
canvas.DrawString(@"-", -39f, -1173f, 32f, 98f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     textView215
// NodeName: км/ч
// NodeType: TEXT
// NodeId:   51:177
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 24f;
canvas.DrawString(@"км/ч", -59f, -1087f, 73f, 26f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     elipseView10
// NodeName: Ellipse 2
// NodeType: ELLIPSE
// NodeId:   51:178
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(217, 217, 217);
canvas.Alpha  = 1;
canvas.FillEllipse(-115f, -1216f, 185f, 185f);
canvas.StrokeSize = 8;
canvas.DrawEllipse(-115f, -1216f, 185f, 185f);
canvas.RestoreState();


// View:     textView216
// NodeName: Ролл-аут
// NodeType: TEXT
// NodeId:   51:179
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 14f;
canvas.DrawString(@"Ролл-аут", -68f, -1188f, 90f, 26f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView191
// NodeName: Results
// NodeType: GROUP
// NodeId:   51:180
canvas.SaveState();
canvas.RestoreState();


// View:     rectangleView39
// NodeName: Results screen
// NodeType: RECTANGLE
// NodeId:   51:181
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(35, 35, 35, 255)) ,new PaintGradientStop(1, new Color(0, 0, 0, 255)) ,}}, new RectF(-185f, -909f, 324f, 253f));
canvas.FillRoundedRectangle(-185f, -909f, 324f, 253f, 25f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(21, 25, 30, 255)) ,new PaintGradientStop(1, new Color(66, 70, 75, 255)) ,}}, new RectF(-185f, -909f, 324f, 253f));
canvas.StrokeSize  = 2;
canvas.DrawRoundedRectangle(-185f, -909f, 324f, 253f, 25f);
canvas.RestoreState();


// View:     frameView192
// NodeName: Group 4
// NodeType: GROUP
// NodeId:   51:206
canvas.SaveState();
canvas.RestoreState();


// View:     textView217
// NodeName: Идёт поиск устройства Racebox. Убедитесь, что прибор включен.
// NodeType: TEXT
// NodeId:   51:207
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 24f;
canvas.DrawString(@"Идёт поиск устройства Racebox. Убедитесь, что прибор включен.", -148f, -853f, 258f, 141f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView193
// NodeName: Menu
// NodeType: FRAME
// NodeId:   51:210
canvas.SaveState();
canvas.RestoreState();


// View:     frameView194
// NodeName: Measure
// NodeType: GROUP
// NodeId:   51:211
canvas.SaveState();
canvas.RestoreState();


// View:     textView218
// NodeName: Разгон
// NodeType: TEXT
// NodeId:   51:212
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Разгон", -185f, -606f, 53f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView111
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:213
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(-176f, -634f);
var vector70Builder = new PathBuilder();
var vector70path = vector70Builder.BuildPath("M21.35 25.375C24.4417 25.375 27.0521 24.2448 29.1812 21.9844C31.3104 19.724 32.375 17.0333 32.375 13.9125C32.375 10.7042 31.2302 8.02083 28.9406 5.8625C26.651 3.70417 23.9021 2.625 20.6938 2.625C19.0604 2.625 17.5292 2.82188 16.1 3.21563C14.6708 3.60938 13.1687 4.22917 11.5938 5.075L16.8875 7.04375C18.375 7.59792 19.3885 8.39271 19.9281 9.42812C20.4677 10.4635 20.7375 11.6375 20.7375 12.95C20.7375 14.7 20.1542 16.1656 18.9875 17.3469C17.8208 18.5281 16.3917 19.1187 14.7 19.1187L2.8875 19.1187C2.74167 19.7896 2.66146 20.6865 2.64688 21.8094C2.63229 22.9323 2.625 24.1208 2.625 25.375L21.35 25.375ZM3.19375 16.4937L14.4375 16.4937C15.5167 16.4937 16.399 16.151 17.0844 15.4656C17.7698 14.7802 18.1125 13.9417 18.1125 12.95C18.1125 12.1042 17.9156 11.3896 17.5219 10.8063C17.1281 10.2229 16.5521 9.8 15.7937 9.5375L9.05625 6.7375C7.59792 8.02083 6.3875 9.46458 5.425 11.0687C4.4625 12.6729 3.71875 14.4813 3.19375 16.4937L3.19375 16.4937ZM21.35 28L2.625 28C1.925 28 1.3125 27.7375 0.7875 27.2125C0.2625 26.6875 0 26.075 0 25.375L0 22.0938C0 18.9438 0.525 16.0198 1.575 13.3219C2.625 10.624 4.07604 8.29063 5.92812 6.32188C7.78021 4.35313 9.96771 2.80729 12.4906 1.68438C15.0135 0.561459 17.7479 0 20.6938 0C22.6479 0 24.4927 0.357292 26.2281 1.07188C27.9635 1.78646 29.4802 2.77083 30.7781 4.025C32.076 5.27917 33.1042 6.75208 33.8625 8.44375C34.6208 10.1354 35 11.9583 35 13.9125C35 15.8375 34.6427 17.6604 33.9281 19.3813C33.2135 21.1021 32.2365 22.5969 30.9969 23.8656C29.7573 25.1344 28.3135 26.1406 26.6656 26.8844C25.0177 27.6281 23.2458 28 21.35 28Z");
canvas.FillPath(vector70path);
canvas.RestoreState();


// View:     frameView195
// NodeName: Gauges
// NodeType: GROUP
// NodeId:   51:214
canvas.SaveState();
canvas.RestoreState();


// View:     textView219
// NodeName: Приборы
// NodeType: TEXT
// NodeId:   51:215
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Приборы", -105f, -606f, 61f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView112
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:216
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(-92f, -634f);
var vector71Builder = new PathBuilder();
var vector71path = vector71Builder.BuildPath("M14.3917 20.77C15.0665 21.4418 16.0421 21.7559 17.3185 21.7121C18.5948 21.6682 19.5264 21.1935 20.1132 20.288L29.6196 5.43349L14.8758 15.0297C13.9956 15.614 13.5261 16.5488 13.4675 17.8341C13.4088 19.1195 13.7169 20.0981 14.3917 20.77L14.3917 20.77ZM17.5165 0C19.1889 0 20.9347 0.270214 22.7539 0.810642C24.573 1.35107 26.3188 2.27856 27.9912 3.59311L25.7026 5.2144C24.3823 4.33803 22.9666 3.68805 21.4555 3.26448C19.9445 2.8409 18.6315 2.62911 17.5165 2.62911C13.4088 2.62911 9.90255 4.08972 6.9978 7.01095C4.09305 9.93218 2.64068 13.4815 2.64068 17.6588C2.64068 18.9734 2.82406 20.3026 3.19082 21.6463C3.55758 22.9901 4.07838 24.2316 4.75322 25.3709L30.2358 25.3709C30.8813 24.3192 31.3947 23.0923 31.7762 21.6901C32.1576 20.2879 32.3483 18.915 32.3483 17.5712C32.3483 16.3443 32.1649 15.0224 31.7982 13.6056C31.4314 12.1888 30.7786 10.8816 29.8397 9.68388L31.5561 7.40532C32.6711 9.04121 33.5073 10.6844 34.0648 12.3349C34.6222 13.9854 34.9303 15.6432 34.989 17.3083C35.0477 19.061 34.8716 20.7115 34.4609 22.2598C34.0501 23.808 33.4486 25.2394 32.6564 26.554C32.3043 27.2259 31.9302 27.6348 31.5341 27.7809C31.138 27.927 30.6465 28 30.0597 28L4.92927 28C4.43047 28 3.93901 27.8758 3.45489 27.6275C2.97076 27.3792 2.61134 27.0214 2.37661 26.554C1.61375 25.1518 1.02693 23.7277 0.616158 22.2817C0.205386 20.8357 0 19.2947 0 17.6588C0 15.2342 0.462119 12.9484 1.38636 10.8013C2.31059 8.65415 3.56492 6.78456 5.14932 5.19249C6.73373 3.60042 8.58954 2.33698 10.7168 1.40219C12.844 0.467396 15.1106 -1.55674e-15 17.5165 0L17.5165 0Z");
canvas.FillPath(vector71path);
canvas.RestoreState();


// View:     frameView196
// NodeName: History
// NodeType: GROUP
// NodeId:   51:217
canvas.SaveState();
canvas.RestoreState();


// View:     textView220
// NodeName: История
// NodeType: TEXT
// NodeId:   51:218
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"История", -17f, -606f, 57f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView113
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:219
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(-5f, -634f);
var vector72Builder = new PathBuilder();
var vector72path = vector72Builder.BuildPath("M2.40833 28C1.74722 28 1.18056 27.7744 0.708333 27.3233C0.236111 26.8722 8.38835e-16 26.3356 0 25.7133C0 25.06 0.236111 24.5 0.708333 24.0333C1.18056 23.5667 1.74722 23.3333 2.40833 23.3333C3.03796 23.3333 3.58102 23.5667 4.0375 24.0333C4.49398 24.5 4.72222 25.06 4.72222 25.7133C4.72222 26.3356 4.49398 26.8722 4.0375 27.3233C3.58102 27.7744 3.03796 28 2.40833 28ZM9.44444 27.0667L9.44444 24.2667L34 24.2667L34 27.0667L9.44444 27.0667ZM2.40833 16.3333C1.74722 16.3333 1.18056 16.1078 0.708333 15.6567C0.236111 15.2056 8.38835e-16 14.6533 0 14C0 13.3467 0.236111 12.7944 0.708333 12.3433C1.18056 11.8922 1.74722 11.6667 2.40833 11.6667C3.03796 11.6667 3.58102 11.9 4.0375 12.3667C4.49398 12.8333 4.72222 13.3778 4.72222 14C4.72222 14.6222 4.49398 15.1667 4.0375 15.6333C3.58102 16.1 3.03796 16.3333 2.40833 16.3333ZM9.44444 15.4L9.44444 12.6L34 12.6L34 15.4L9.44444 15.4ZM2.36111 4.66667C1.7 4.66667 1.1412 4.44111 0.684722 3.99C0.228241 3.53889 0 2.98667 0 2.33333C0 1.68 0.228241 1.12778 0.684722 0.676667C1.1412 0.225556 1.7 0 2.36111 0C3.02222 0 3.58102 0.225556 4.0375 0.676667C4.49398 1.12778 4.72222 1.68 4.72222 2.33333C4.72222 2.98667 4.49398 3.53889 4.0375 3.99C3.58102 4.44111 3.02222 4.66667 2.36111 4.66667ZM9.44444 3.73333L9.44444 0.933333L34 0.933333L34 3.73333L9.44444 3.73333Z");
canvas.FillPath(vector72path);
canvas.RestoreState();


// View:     frameView197
// NodeName: Settings
// NodeType: GROUP
// NodeId:   51:220
canvas.SaveState();
canvas.RestoreState();


// View:     textView221
// NodeName: Настройки
// NodeType: TEXT
// NodeId:   51:221
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 10f;
canvas.DrawString(@"Настройки", 67f, -606f, 70f, 21f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView114
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:222
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(88f, -634f);
var vector73Builder = new PathBuilder();
var vector73path = vector73Builder.BuildPath("M10.78 28L10.08 23.59C9.63667 23.4267 9.17 23.205 8.68 22.925C8.19 22.645 7.75833 22.3533 7.385 22.05L3.255 23.94L0 18.2L3.78 15.435C3.73333 15.225 3.70417 14.9858 3.6925 14.7175C3.68083 14.4492 3.675 14.21 3.675 14C3.675 13.79 3.68083 13.5508 3.6925 13.2825C3.70417 13.0142 3.73333 12.775 3.78 12.565L0 9.8L3.255 4.06L7.385 5.95C7.75833 5.64667 8.19 5.355 8.68 5.075C9.17 4.795 9.63667 4.585 10.08 4.445L10.78 0L17.22 0L17.92 4.41C18.3633 4.57333 18.8358 4.78917 19.3375 5.0575C19.8392 5.32583 20.265 5.62333 20.615 5.95L24.745 4.06L28 9.8L24.22 12.495C24.2667 12.7283 24.2958 12.9792 24.3075 13.2475C24.3192 13.5158 24.325 13.7667 24.325 14C24.325 14.2333 24.3192 14.4783 24.3075 14.735C24.2958 14.9917 24.2667 15.2367 24.22 15.47L28 18.2L24.745 23.94L20.615 22.05C20.2417 22.3533 19.8158 22.6508 19.3375 22.9425C18.8592 23.2342 18.3867 23.45 17.92 23.59L17.22 28L10.78 28ZM14 18.55C15.26 18.55 16.3333 18.1067 17.22 17.22C18.1067 16.3333 18.55 15.26 18.55 14C18.55 12.74 18.1067 11.6667 17.22 10.78C16.3333 9.89333 15.26 9.45 14 9.45C12.74 9.45 11.6667 9.89333 10.78 10.78C9.89333 11.6667 9.45 12.74 9.45 14C9.45 15.26 9.89333 16.3333 10.78 17.22C11.6667 18.1067 12.74 18.55 14 18.55ZM14 16.45C13.3233 16.45 12.7458 16.2108 12.2675 15.7325C11.7892 15.2542 11.55 14.6767 11.55 14C11.55 13.3233 11.7892 12.7458 12.2675 12.2675C12.7458 11.7892 13.3233 11.55 14 11.55C14.6767 11.55 15.2542 11.7892 15.7325 12.2675C16.2108 12.7458 16.45 13.3233 16.45 14C16.45 14.6767 16.2108 15.2542 15.7325 15.7325C15.2542 16.2108 14.6767 16.45 14 16.45ZM12.46 25.9L15.54 25.9L16.03 21.98C16.8 21.7933 17.5292 21.5017 18.2175 21.105C18.9058 20.7083 19.53 20.23 20.09 19.67L23.8 21.28L25.2 18.76L21.91 16.345C22.0033 15.9483 22.0792 15.5575 22.1375 15.1725C22.1958 14.7875 22.225 14.3967 22.225 14C22.225 13.6033 22.2017 13.2125 22.155 12.8275C22.1083 12.4425 22.0267 12.0517 21.91 11.655L25.2 9.24L23.8 6.72L20.09 8.33C19.5533 7.72333 18.9467 7.21583 18.27 6.8075C17.5933 6.39917 16.8467 6.13667 16.03 6.02L15.54 2.1L12.46 2.1L11.97 6.02C11.1767 6.18333 10.4358 6.46333 9.7475 6.86C9.05917 7.25667 8.44667 7.74667 7.91 8.33L4.2 6.72L2.8 9.24L6.09 11.655C5.99667 12.0517 5.92083 12.4425 5.8625 12.8275C5.80417 13.2125 5.775 13.6033 5.775 14C5.775 14.3967 5.80417 14.7875 5.8625 15.1725C5.92083 15.5575 5.99667 15.9483 6.09 16.345L2.8 18.76L4.2 21.28L7.91 19.67C8.47 20.23 9.09417 20.7083 9.7825 21.105C10.4708 21.5017 11.2 21.7933 11.97 21.98L12.46 25.9Z");
canvas.FillPath(vector73path);
canvas.RestoreState();


// View:     frameView198
// NodeName: Button
// NodeType: GROUP
// NodeId:   51:223
canvas.SaveState();
canvas.RestoreState();


// View:     rectangleView40
// NodeName: Rectangle 1
// NodeType: RECTANGLE
// NodeId:   51:224
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(146, 67, 33, 255)) ,new PaintGradientStop(1, new Color(228, 127, 83, 255)) ,}}, new RectF(-98f, -991f, 150f, 49f));
canvas.FillRoundedRectangle(-98f, -991f, 150f, 49f, 31f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(229, 120, 73, 255)) ,new PaintGradientStop(1, new Color(184, 79, 33, 255)) ,}}, new RectF(-98f, -991f, 150f, 49f));
canvas.StrokeSize  = 2;
canvas.DrawRoundedRectangle(-98f, -991f, 150f, 49f, 31f);
canvas.RestoreState();


// View:     textView222
// NodeName: Старт
// NodeType: TEXT
// NodeId:   51:225
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 24f;
canvas.DrawString(@"Старт", -65.59259f, -979f, 85.18518f, 24f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView199
// NodeName: Status block
// NodeType: GROUP
// NodeId:   51:232
canvas.SaveState();
canvas.RestoreState();


// View:     rectangleView41
// NodeName: Results screen
// NodeType: RECTANGLE
// NodeId:   51:233
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(35, 35, 35, 255)) ,new PaintGradientStop(1, new Color(0, 0, 0, 255)) ,}}, new RectF(-184f, -1306f, 324f, 50f));
canvas.FillRoundedRectangle(-184f, -1306f, 324f, 50f, 25f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(21, 25, 30, 255)) ,new PaintGradientStop(1, new Color(66, 70, 75, 255)) ,}}, new RectF(-184f, -1306f, 324f, 50f));
canvas.StrokeSize  = 2;
canvas.DrawRoundedRectangle(-184f, -1306f, 324f, 50f, 25f);
canvas.RestoreState();


// View:     frameView200
// NodeName: Sats
// NodeType: GROUP
// NodeId:   51:234
canvas.SaveState();
canvas.RestoreState();


// View:     textView223
// NodeName: 13
// NodeType: TEXT
// NodeId:   51:235
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 20f;
canvas.DrawString(@"0", -146f, -1298f, 49f, 34f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView115
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:236
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(-171f, -1297f);
var vector74Builder = new PathBuilder();
var vector74path = vector74Builder.BuildPath("M18.3621 30L18.3621 28.0583C21.056 28.0583 23.3459 27.1143 25.2317 25.2265C27.1175 23.3387 28.0603 21.0464 28.0603 18.3495L30 18.3495C30 19.9676 29.6929 21.4833 29.0787 22.8964C28.4644 24.3096 27.6347 25.5394 26.5894 26.5858C25.5442 27.6321 24.3157 28.4628 22.9041 29.0777C21.4925 29.6926 19.9784 30 18.3621 30ZM18.3621 26.1165L18.3621 24.1748C19.8491 24.1748 21.1907 23.5761 22.3869 22.3786C23.583 21.1812 24.181 19.8382 24.181 18.3495L26.1207 18.3495C26.1207 20.3991 25.3394 22.206 23.7769 23.7702C22.2144 25.3344 20.4095 26.1165 18.3621 26.1165ZM7.43534 29.4498C7.17672 29.4498 6.92349 29.4013 6.67565 29.3042C6.4278 29.2071 6.21767 29.0723 6.04526 28.8997L0.549569 23.3981C0.377155 23.2255 0.242457 23.0151 0.145474 22.767C0.0484914 22.5189 0 22.2654 0 22.0065C0 21.7476 0.0484914 21.4995 0.145474 21.2621C0.242457 21.0248 0.377155 20.8198 0.549569 20.6472L5.43103 15.7605C5.79741 15.3937 6.25539 15.2104 6.80496 15.2104C7.35453 15.2104 7.8125 15.3937 8.17888 15.7605L10.2478 17.8317L11.444 16.6343L9.375 14.5631C9.00862 14.1963 8.82543 13.7433 8.82543 13.2039C8.82543 12.6645 9.00862 12.2114 9.375 11.8447L11.7996 9.41748C12.1659 9.0507 12.6239 8.86731 13.1735 8.86731C13.7231 8.86731 14.181 9.0507 14.5474 9.41748L16.6164 11.4887L17.8125 10.2913L15.7435 8.22006C15.3772 7.85329 15.194 7.39482 15.194 6.84466C15.194 6.2945 15.3772 5.83603 15.7435 5.46926L20.625 0.582524C20.819 0.388349 21.0345 0.242718 21.2716 0.145631C21.5086 0.0485437 21.7565 0 22.0151 0C22.2737 0 22.5216 0.0431499 22.7586 0.12945C22.9957 0.21575 23.2112 0.355987 23.4052 0.550162L28.9009 6.05178C29.0948 6.24595 29.2349 6.4617 29.3211 6.69903C29.4073 6.93635 29.4504 7.18447 29.4504 7.44337C29.4504 7.70227 29.3966 7.95577 29.2888 8.20388C29.181 8.452 29.0409 8.66235 28.8685 8.83495L23.9871 13.7217C23.4914 14.2179 23.0334 14.466 22.6131 14.466C22.1929 14.466 21.7349 14.2179 21.2392 13.7217L19.1703 11.6505L17.9741 12.8479L20.0431 14.9191C20.4095 15.2859 20.5873 15.7443 20.5765 16.2945C20.5657 16.8447 20.3772 17.3031 20.0108 17.6699L17.6185 20.0647C17.2522 20.4315 16.7942 20.6149 16.2446 20.6149C15.695 20.6149 15.2371 20.4315 14.8707 20.0647L12.8017 17.9935L11.6056 19.1909L13.6746 21.2621C14.0409 21.6289 14.2241 22.0874 14.2241 22.6375C14.2241 23.1877 14.0409 23.6462 13.6746 24.0129L8.7931 28.8997C8.62069 29.0723 8.41595 29.2071 8.17888 29.3042C7.94181 29.4013 7.69397 29.4498 7.43534 29.4498L7.43534 29.4498ZM7.43534 27.5405L9.18103 25.7929L3.68534 20.2913L1.93966 22.0388L7.43534 27.5405ZM10.5711 24.4013L12.3168 22.6537L6.82112 17.1521L5.07543 18.8997L10.5711 24.4013ZM16.2608 18.7055L18.653 16.3107L13.1573 10.8091L10.7651 13.2039L16.2608 18.7055ZM22.6293 12.3301L24.375 10.5825L18.8793 5.08091L17.1336 6.82848L22.6293 12.3301ZM25.7651 9.19094L27.5108 7.44337L22.0151 1.94175L20.2694 3.68932L25.7651 9.19094Z");
canvas.FillPath(vector74path);
canvas.RestoreState();


// View:     lineView38
// NodeName: Line 11
// NodeType: LINE
// NodeId:   51:237
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(-100, -1298), new Point(-100, -1264));
canvas.RestoreState();


// View:     lineView39
// NodeName: Line 12
// NodeType: LINE
// NodeId:   51:238
canvas.SaveState();
canvas.StrokeSize = 1;
canvas.DrawLine(new Point(92, -1298), new Point(92, -1264));
canvas.RestoreState();


// View:     imageView116
// NodeName: BLE
// NodeType: VECTOR
// NodeId:   51:239
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(203, 99, 54);
canvas.Alpha  = 1;
canvas.Translate(104f, -1296f);
var vector75Builder = new PathBuilder();
var vector75path = vector75Builder.BuildPath("M8.35833 30L8.35833 17.7979L1.4875 25.3368L0 23.7047L7.93333 15L0 6.29534L1.4875 4.66321L8.35833 12.2021L8.35833 0L9.42083 0L17 8.31606L10.9083 15L17 21.6839L9.42083 30L8.35833 30ZM10.4833 12.2021L14.025 8.31606L10.4833 4.50777L10.4833 12.2021ZM10.4833 25.4922L14.025 21.6839L10.4833 17.7979L10.4833 25.4922Z");
canvas.FillPath(vector75path);
canvas.RestoreState();


// View:     frameView201
// NodeName: Signal
// NodeType: GROUP
// NodeId:   51:240
canvas.SaveState();
canvas.RestoreState();


// View:     textView224
// NodeName: Уровень сигнала
// NodeType: TEXT
// NodeId:   51:241
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 12f;
canvas.DrawString(@"Уровень сигнала", -61f, -1297f, 115f, 16f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView117
// NodeName: Rectangle 3
// NodeType: VECTOR
// NodeId:   51:242
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(216, 40, 29, 255)) ,new PaintGradientStop(0, new Color(240, 231, 11, 255)) ,new PaintGradientStop(1, new Color(101, 210, 16, 255)) ,}}, new RectF(-87f, -1276f, 167f, 6f));
canvas.Translate(-87f, -1276f);
var vector76Builder = new PathBuilder();
var vector76path = vector76Builder.BuildPath("M0 3C0 1.34315 1.34315 0 3 0L164 0C165.657 0 167 1.34315 167 3L167 3C167 4.65685 165.657 6 164 6L3 6C1.34314 6 0 4.65685 0 3L0 3Z");
canvas.FillPath(vector76path);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(216, 40, 29, 255)) ,new PaintGradientStop(0, new Color(240, 231, 11, 255)) ,new PaintGradientStop(1, new Color(101, 210, 16, 255)) ,}}, new RectF(-87f, -1276f, 167f, 6f));
canvas.StrokeSize  = 1;
canvas.Translate(-87f, -1276f);
var vector77Builder = new PathBuilder();
var vector77path = vector77Builder.BuildPath("M0 3C0 1.34315 1.34315 0 3 0L164 0C165.657 0 167 1.34315 167 3L167 3C167 4.65685 165.657 6 164 6L3 6C1.34314 6 0 4.65685 0 3L0 3Z");
canvas.DrawPath(vector77path);
canvas.RestoreState();


// View:     elipseView11
// NodeName: Ellipse 3
// NodeType: ELLIPSE
// NodeId:   51:243
canvas.SaveState();
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(137, 132, 120, 255)) ,new PaintGradientStop(1, new Color(255, 250, 237, 255)) ,}}, new RectF(-87f, -1281f, 16f, 16f));
canvas.FillEllipse(-87f, -1281f, 16f, 16f);
canvas.SetFillPaint(new LinearGradientPaint{GradientStops = new PaintGradientStop[]{new PaintGradientStop(0, new Color(255, 249, 234, 255)) ,new PaintGradientStop(1, new Color(126, 122, 110, 255)) ,}}, new RectF(-87f, -1281f, 16f, 16f));
canvas.StrokeSize = 2;
canvas.DrawEllipse(-87f, -1281f, 16f, 16f);
canvas.RestoreState();


// View:     frameView202
// NodeName: Group 25
// NodeType: GROUP
// NodeId:   51:244
canvas.SaveState();
canvas.RestoreState();


// View:     imageView118
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:245
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(96f, -1094f);
var vector78Builder = new PathBuilder();
var vector78path = vector78Builder.BuildPath("M10.5 2.625L10.5 0L21 0L21 2.625L10.5 2.625ZM14.4375 22.1812L17.0625 22.1812L17.0625 12.1187L14.4375 12.1187L14.4375 22.1812ZM15.75 36.7062C13.5917 36.7062 11.5573 36.2906 9.64687 35.4594C7.73646 34.6281 6.06667 33.4979 4.6375 32.0687C3.20833 30.6396 2.07813 28.9698 1.24688 27.0594C0.415625 25.149 0 23.1146 0 20.9562C0 18.7979 0.415625 16.7635 1.24688 14.8531C2.07813 12.9427 3.20833 11.2729 4.6375 9.84375C6.06667 8.41458 7.73646 7.28437 9.64687 6.45312C11.5573 5.62187 13.5917 5.20625 15.75 5.20625C17.7042 5.20625 19.5417 5.53437 21.2625 6.19062C22.9833 6.84687 24.5146 7.75833 25.8562 8.925L28.0875 6.69375L29.925 8.53125L27.6938 10.7625C28.7438 11.9292 29.6406 13.3437 30.3844 15.0062C31.1281 16.6687 31.5 18.6521 31.5 20.9562C31.5 23.1146 31.0844 25.149 30.2531 27.0594C29.4219 28.9698 28.2917 30.6396 26.8625 32.0687C25.4333 33.4979 23.7635 34.6281 21.8531 35.4594C19.9427 36.2906 17.9083 36.7062 15.75 36.7062ZM15.75 34.0812C19.3958 34.0812 22.4948 32.8052 25.0469 30.2531C27.599 27.701 28.875 24.6021 28.875 20.9562C28.875 17.3104 27.599 14.2115 25.0469 11.6594C22.4948 9.10729 19.3958 7.83125 15.75 7.83125C12.1042 7.83125 9.00521 9.10729 6.45312 11.6594C3.90104 14.2115 2.625 17.3104 2.625 20.9562C2.625 24.6021 3.90104 27.701 6.45312 30.2531C9.00521 32.8052 12.1042 34.0812 15.75 34.0812Z");
canvas.FillPath(vector78path);
canvas.RestoreState();


// View:     textView225
// NodeName: - c
// NodeType: TEXT
// NodeId:   51:246
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 24f;
canvas.DrawString(@"- c", 76f, -1050f, 72f, 26f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView203
// NodeName: Group 24
// NodeType: GROUP
// NodeId:   51:247
canvas.SaveState();
canvas.RestoreState();


// View:     imageView119
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:248
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(-174f, -1082f);
var vector79Builder = new PathBuilder();
var vector79path = vector79Builder.BuildPath("M3 24C2.2 24 1.5 23.7 0.9 23.1C0.3 22.5 0 21.8 0 21L0 3C0 2.23333 0.3 1.54167 0.9 0.925C1.5 0.308334 2.2 0 3 0L37 0C37.8 0 38.5 0.308334 39.1 0.925C39.7 1.54167 40 2.23333 40 3L40 21C40 21.8 39.7 22.5 39.1 23.1C38.5 23.7 37.8 24 37 24L3 24ZM3 21L37 21L37 3L30.5 3L30.5 12L27.5 12L27.5 3L21.5 3L21.5 12L18.5 12L18.5 3L12.5 3L12.5 12L9.5 12L9.5 3L3 3L3 21ZM9.5 12L12.5 12L9.5 12ZM18.5 12L21.5 12L18.5 12ZM27.5 12L30.5 12L27.5 12Z");
canvas.FillPath(vector79path);
canvas.RestoreState();


// View:     textView226
// NodeName: - м
// NodeType: TEXT
// NodeId:   51:249
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 24f;
canvas.DrawString(@"- м", -193f, -1050f, 78f, 26f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     frameView204
// NodeName: Slope
// NodeType: GROUP
// NodeId:   51:250
canvas.SaveState();
canvas.RestoreState();


// View:     textView227
// NodeName: -
// NodeType: TEXT
// NodeId:   51:251
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 24f;
canvas.DrawString(@"-", -188f, -1210f, 68f, 26f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView120
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:252
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(-174f, -1239f);
var vector80Builder = new PathBuilder();
var vector80path = vector80Builder.BuildPath("M2.1 24L0 21.9L10.95 10.95C12.0167 9.88333 13.3167 9.35 14.85 9.35C16.3833 9.35 17.6833 9.88333 18.75 10.95L21.05 13.25C21.55 13.75 22.1417 14 22.825 14C23.5083 14 24.1 13.75 24.6 13.25L34.85 3L29 3L29 0L40 0L40 11L37 11L37 5.15L26.7 15.4C25.6333 16.4667 24.3333 17 22.8 17C21.2667 17 19.9667 16.4667 18.9 15.4L16.55 13.05C16.0833 12.5833 15.5 12.35 14.8 12.35C14.1 12.35 13.5167 12.5833 13.05 13.05L2.1 24Z");
canvas.FillPath(vector80path);
canvas.RestoreState();


// View:     frameView205
// NodeName: Altitude
// NodeType: GROUP
// NodeId:   51:253
canvas.SaveState();
canvas.RestoreState();


// View:     textView228
// NodeName: - м
// NodeType: TEXT
// NodeId:   51:254
canvas.SaveState();
canvas.FontColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Font = new Microsoft.Maui.Graphics.Font("FordAntenna-Regular", 400, FontStyleType.Normal);
canvas.FontSize = 24f;
canvas.DrawString(@"- м", 67f, -1210f, 90f, 26f, HorizontalAlignment.Center, VerticalAlignment.Center);
canvas.RestoreState();


// View:     imageView121
// NodeName: Vector
// NodeType: VECTOR
// NodeId:   51:255
canvas.SaveState();
canvas.FillColor  = Color.FromRgb(232, 227, 215);
canvas.Alpha  = 1;
canvas.Translate(90f, -1240f);
var vector81Builder = new PathBuilder();
var vector81path = vector81Builder.BuildPath("M0 24L12 8L21.75 21L38 21L26 5.05L19.75 13.35L17.85 10.85L26 0L44 24L0 24ZM6 21L18 21L12 13L6 21ZM6 21L18 21L6 21Z");
canvas.FillPath(vector81path);
canvas.RestoreState();

