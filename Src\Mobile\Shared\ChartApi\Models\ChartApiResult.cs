using Racebox.ApiClient.Interfaces;

namespace Racebox.Shared.ChartApi.Models;

public class ChartApiResult : IResult<ChartResponse>
{
    public List<string> Errors { get; private set; } = new();
    public bool Success { get; private set; }
    public int Code { get; private set; }
    public ChartResponse Data { get; private set; } = new();
    public Exception Exception { get; private set; }
    public TimeSpan ResponseTime { get; private set; }

    // Convenience properties for easier usage
    public bool IsSuccess => Success;
    public string ErrorMessage => string.Join("; ", Errors);

    private ChartApiResult() { }

    public static ChartApiResult CreateSuccess(ChartResponse data, TimeSpan responseTime)
    {
        return new ChartApiResult
        {
            Success = true,
            Code = 200,
            Data = data,
            ResponseTime = responseTime,
            Errors = new List<string>()
        };
    }

    public static ChartApiResult CreateError(string errorMessage, TimeSpan responseTime = default, Exception exception = null, int code = 400)
    {
        return new ChartApiResult
        {
            Success = false,
            Code = code,
            Errors = new List<string> { errorMessage },
            ResponseTime = responseTime,
            Exception = exception,
            Data = new ChartResponse()
        };
    }
}

public class ChartResponse
{
    public string FilePath { get; set; } = string.Empty;
    public string Filename { get; set; } = string.Empty;
    public long FileSize { get; set; }
}