﻿using System.Runtime.CompilerServices;

namespace Racebox.Views.Navigation
{

    public partial class NavBarPadding : BoxView
    {
        public NavBarPadding()
        {
            InitializeComponent();

            OnLoaded();
        }


        public void OnLoaded()

        {
            Initialize();
        }


        // NavBarVisible

        private const string nameNavBarVisible = "NavBarVisible";
        public static readonly BindableProperty NavBarVisibleProperty = BindableProperty.Create(nameNavBarVisible, typeof(bool), typeof(NavBarPadding), true); //, BindingMode.TwoWay
        public bool NavBarVisible
        {
            get { return (bool)GetValue(NavBarVisibleProperty); }
            set
            {
                SetValue(NavBarVisibleProperty, value);
            }
        }



        /// <summary>
        /// Call it once at startup
        /// </summary>
        private void Initialize()
        {
            var add = Super.NavBarHeight;
            if (!NavBarVisible) add = 0;

            HeightRequest = add + Super.StatusBarHeight;
        }


        protected override void OnPropertyChanged([CallerMemberName] string propertyName = "")

        {
            base.OnPropertyChanged(propertyName);

            switch (propertyName)
            {


                //property changed
                case nameNavBarVisible:
                    Initialize();
                    break;

            }

        }

    }

}