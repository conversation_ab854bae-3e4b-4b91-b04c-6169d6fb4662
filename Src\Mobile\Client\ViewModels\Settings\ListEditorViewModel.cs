﻿

using Racebox.Interfaces;
using Racebox.Shared.Strings;
using System.Diagnostics;
using System.Windows.Input;

namespace Racebox.ViewModels
{
    public class ListEditorViewModel : ProjectViewModel, ISupportsListEditor, IUpdateUIState
    {
        private IEnumerable<ISelectableOption> _items;
        private readonly Func<IEnumerable<ISelectableOption>> _updateItems;

        public ListEditorViewModel(
            string title,
            ICommand commandAddItem,
            ICommand commandSelectItem,
            ICommand commandDeleteItem,
            ICommand commandEditItem,
            Func<IEnumerable<ISelectableOption>> updateItems)
        {
            Title = title;
            CommandAddItem = commandAddItem;
            CommandDeleteItem = commandDeleteItem;
            CommandEditItem = commandEditItem;
            CommandSelectItem = commandSelectItem;

            _updateItems = updateItems;
        }


        public ObservableRangeCollection<ISelectableOption> Items { get; } = new();
        public ICommand CommandAddItem { get; set; }
        public ICommand CommandDeleteItem { get; set; }
        public ICommand CommandEditItem { get; set; }
        public ICommand CommandSelectItem { get; }

        public ICommand CommandManageItem => new Command(async (context) =>
        //new Command(async (context) => MainThread.BeginInvokeOnMainThread(
        //         async () =>
            {
                if (context is ISelectableOption option)
                {
                    if (!option.IsReadOnly)
                    {
                        List<SelectableAction> options = new();

                        options.Add(new SelectableAction
                        {
                            Title = ResStrings.Edit,
                            Action = () => { CommandEditItem.Execute(context); }
                        });

                        options.Add(new SelectableAction
                        {
                            Title = ResStrings.Delete,
                            Action = () => { CommandDeleteItem.Execute(context); }
                        });

                        Debug.WriteLine("[DEBUG] CommandManageItem");

                        MainThread.BeginInvokeOnMainThread(async () =>
                        {

                            Debug.WriteLine("[DEBUG] CommandManageItem ++");

                            var selected = await App.Instance.UI.PresentSelection(options, ResStrings.Options) as SelectableAction;
                            selected?.Action?.Invoke();

                        });

                    }
                }




            });

        public ICommand CommandRefreshData
        {
            get
            {
                return new Command(async () =>
                {
                    //await Task.Delay(2500);
                    UpdateState(true);
                });
            }
        }

        public void UpdateState(bool forceReload = false)
        {
            if (!IsBusy)
            {
                IsBusy = true;

                try
                {
                    _items = _updateItems?.Invoke();

                    Task.Run(() => MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        IsLoading = false;
                        await Task.Delay(10);
                        Items.Clear();
                        Items.AddRange(_items.ToList());
                        HasData = true;
                        IsBusy = false;

                    }));


                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                    IsBusy = false;
                    IsLoading = false;
                }

            }
                ;


        }


        private bool _HasData;
        public bool HasData
        {
            get
            {
                return _HasData;
            }
            set
            {
                if (_HasData != value)
                {
                    _HasData = value;
                    OnPropertyChanged();
                }
            }
        }

        private bool _IsLoading;
        public bool IsLoading
        {
            get
            {
                return _IsLoading;
            }
            set
            {
                if (_IsLoading != value)
                {
                    _IsLoading = value;
                    OnPropertyChanged();
                }
            }
        }


    }

}
