﻿using AppoMobi.Specials;
using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Racebox.SDK
{
    public class RaceBoxState : INotifyPropertyChanged
    {
        public static RaceBoxState FromData(byte[] bytes)
        {
            var data = new RaceBoxState();
            bytes.DecodeTo(data);
            return data;
        }

        public static RaceBoxState FromHardwareLog(HardwareLogLine logLine)
        {
            var item = new RaceBoxState()
            {
                TimeStampUtc = logLine.Time,
                Altitude = logLine.Altitude,
                Heading = logLine.Heading,
                Latitude = logLine.Lattitude,
                Longitude = logLine.Longitude,
                Speed = logLine.Speed,
                Day = logLine.Time.Day,
                Month = logLine.Time.Month,
                Year = logLine.Time.Year,
                Hours = logLine.Time.Hour,
                Minutes = logLine.Time.Minute,
                Seconds = logLine.Time.Second,
                Milliseconds = logLine.Time.Millisecond,
                HDOP = logLine.HDOP,
                SatellitesCount = logLine.SatellitesCount,
            };
            return item;
        }

        public void Reset()
        {
            var wipe = new RaceBoxState();
            Reflection.MapProperties(wipe, this);
        }

        private int? _Year;
        public int? Year
        {
            get { return _Year; }
            set
            {
                if (_Year != value)
                {
                    _Year = value;
                    OnPropertyChanged();
                }
            }
        }

        private int? _Month;
        public int? Month
        {
            get { return _Month; }
            set
            {
                if (_Month != value)
                {
                    _Month = value;
                    OnPropertyChanged();
                }
            }
        }

        private int? _Day;
        public int? Day
        {
            get { return _Day; }
            set
            {
                if (_Day != value)
                {
                    _Day = value;
                    OnPropertyChanged();
                }
            }
        }

        private int? _Hours;
        public int? Hours
        {
            get { return _Hours; }
            set
            {
                if (_Hours != value)
                {
                    _Hours = value;
                    OnPropertyChanged();
                }
            }
        }

        private int? _Minutes;
        public int? Minutes
        {
            get { return _Minutes; }
            set
            {
                if (_Minutes != value)
                {
                    _Minutes = value;
                    OnPropertyChanged();
                }
            }
        }

        private int? _Seconds;
        public int? Seconds
        {
            get { return _Seconds; }
            set
            {
                if (_Seconds != value)
                {
                    _Seconds = value;
                    OnPropertyChanged();
                }
            }
        }

        private int? _Milliseconds;
        public int? Milliseconds
        {
            get { return _Milliseconds; }
            set
            {
                if (_Milliseconds != value)
                {
                    _Milliseconds = value;
                    OnPropertyChanged();
                }
            }
        }

        private double? _HDOP;
        /// <summary>
        /// Horizontal Dilution of Precision.
        /// Note: All values higher 9.9 clipped to 9.9 on device level and should be considered as valid fix with poor accuracy.
        /// </summary>
        public double? HDOP
        {
            get { return _HDOP; }
            set
            {
                if (_HDOP != value)
                {
                    _HDOP = value;
                    OnPropertyChanged();
                }
            }
        }

        private int _SatellitesCount;
        /// <summary>
        /// Note: device can capture >20 sats, but in this protocol the value is limited to 15 sats max
        /// </summary>
        public int SatellitesCount
        {
            get { return _SatellitesCount; }
            set
            {
                if (_SatellitesCount != value)
                {
                    _SatellitesCount = value;
                    OnPropertyChanged();
                }
            }
        }

        private double? _Heading;
        /// <summary>
        /// Курс движения: angular distance relative to north, usually 000° at north, clockwise through 359°
        /// </summary>
        public double? Heading
        {
            get { return _Heading; }
            set
            {
                if (_Heading != value)
                {
                    _Heading = value;
                    OnPropertyChanged();
                }
            }
        }

        private double? _Latitude;
        public double? Latitude
        {
            get { return _Latitude; }
            set
            {
                if (_Latitude != value)
                {
                    _Latitude = value;
                    OnPropertyChanged();
                }
            }
        }

        private double? _Longitude;
        public double? Longitude
        {
            get { return _Longitude; }
            set
            {
                if (_Longitude != value)
                {
                    _Longitude = value;
                    OnPropertyChanged();
                }
            }
        }

        private double? _Speed;
        public double? Speed
        {
            get { return _Speed; }
            set
            {
                if (_Speed != value)
                {
                    _Speed = value;
                    OnPropertyChanged();
                }
            }
        }

        public double SpeedMph
        {
            get
            {
                var speed = Speed.GetValueOrDefault();
                return speed * 0.62137119224;
            }
        }

        private double? _Altitude;
        /// <summary>
        /// Высота в метрах
        /// </summary>
        public double? Altitude
        {
            get { return _Altitude; }
            set
            {
                if (_Altitude != value)
                {
                    _Altitude = value;
                    OnPropertyChanged();
                }
            }
        }

        private DateTime _TimeStamp;
        public DateTime TimeStampUtc
        {
            get { return _TimeStamp; }
            set
            {
                if (_TimeStamp != value)
                {
                    _TimeStamp = value;
                    OnPropertyChanged();
                }
            }
        }

        private double _frequencyUpdated;
        public double FrequencyUpdatedHz
        {
            get { return _frequencyUpdated; }
            set
            {
                if (_frequencyUpdated != value)
                {
                    _frequencyUpdated = value;
                    OnPropertyChanged();
                }
            }
        }

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

    }
}
