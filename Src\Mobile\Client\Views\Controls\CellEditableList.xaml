<?xml version="1.0" encoding="utf-8" ?>
<draw:Canvas
    x:Class="Racebox.Views.Partials.CellEditableList"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:touch="clr-namespace:AppoMobi.Maui.Gestures;assembly=AppoMobi.Maui.Gestures"
    xmlns:racebox="clr-namespace:Racebox"
    xmlns:models1="clr-namespace:AppoMobi.Framework.Maui.Models;assembly=AppoMobi.Framework.Maui"
    x:Name="ThisCell"
    Padding="0,6,0,0"
    touch:TouchEffect.CommandLongPressingParameter="{Binding .}"
    touch:TouchEffect.CommandTapped="{Binding Source={x:Reference ThisCell}, Path=Parent.BindingContext.CommandSelectItem}"
    touch:TouchEffect.CommandTappedParameter="{Binding .}"
    touch:TouchEffect.HandlerDown="{x:Static racebox:MauiProgram.TouchAnimateRipple}"
    touch:TouchEffect.HandlerLongPressing="{Binding Source={x:Reference ThisCell}, Path=LongPressingHandler, Mode=OneTime}"
    touch:TouchEffect.CommandLongPressing="{Binding Source={x:Reference ThisCell}, Path=Parent.BindingContext.CommandManageItem}"
    x:DataType="models1:OptionItem"
    HeightRequest="44"
    HorizontalOptions="Fill"
    VerticalOptions="Start">
    <draw:SkiaLayout
        HorizontalOptions="Fill"
        VerticalOptions="Fill">

        <draw:SkiaLabel
            Margin="16,0"
            FontSize="14"
            MaxLines="1"
            Style="{StaticResource SkiaLabelDefaultStyle}"
            Text="{Binding Title}"
            VerticalOptions="Center" />

        <draw:SkiaSvg
            x:Name="IconCheck"
            Margin="16,0"
            HeightRequest="16"
            HorizontalOptions="End"
            IsVisible="{Binding Selected}"
            SvgString="{StaticResource SvgCheck}"
            TintColor="#CB6336"
            VerticalOptions="Center"
            WidthRequest="16" />

    </draw:SkiaLayout>
</draw:Canvas>
