﻿namespace Racebox.Shared.Models;

/// <summary>
/// From hardware device logs
/// </summary>
public class LogLine
{
    public int Index { get; set; }
    public DateTime Time { get; set; }
    public double TotalSeconds { get; set; }
    public double Lattitude { get; set; }
    public double Longitude { get; set; }
    public double Distance { get; set; }
    public double AccelerationLat { get; set; }
    public double AccelerationLon { get; set; }
    public double Altitude { get; set; }
    public double Speed { get; set; }
    public double Heading { get; set; }
    public double Incline { get; set; }
    public double HDOP { get; set; }
    public int SatellitesCount { get; set; }
}