﻿using Racebox.Shared.Enums;

namespace Racebox.Shared.Models;

//[DebuggerDisplay("{Distance:0.00} time {Time:HH':'mm':'ss':'ff} speed {Speed:0.00}")]
public class MeasureResult : BaseEntity
{
    #region FK
    public int AppUserId { get; set; }

    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]

    public AppUser AppUser { get; set; }

    #endregion

    public DateTime CreatedTimeUtc { get; set; }

    /// <summary>
    /// This can be 0 if car was deleted
    /// </summary>
    public int CarId { get; set; }

    public double Lattitude { get; set; }
    public double Longitude { get; set; }
    public DateTime StartTime { get; set; }
    public TimeSpan Duration { get; set; }
    public bool RollOut { get; set; }
    public double MaxSpeed { get; set; }
    public double MaxAcceleration { get; set; }
    public double MaxIncline { get; set; }
    public bool IsValid { get; set; }
    public bool IsInstant { get; set; }
    public string Crash { get; set; }

    public List<MeasuredRange> Ranges { get; set; } = new();
    public List<MeasuredDistance> Distances { get; set; } = new();
    public List<MeasuredLogLine> Logs { get; set; } = new();

    public OptionsUnits Units { get; set; }

    public Weather CurrentWeather { get; set; }
}

public class Weather : BaseEntity
{
    #region FK
    public int MeasureResultId { get; set; }

    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]

    public MeasureResult MeasureResult { get; set; }

    #endregion

    /// <summary>
    /// Font Awesome Pro corresponding Icon
    /// </summary>
    public string Icon { get; set; }

    /// <summary>
    /// Weather condition https://www.weatherapi.com/docs/weather_conditions.json
    /// </summary>
    public int Condition { get; set; }

    /// <summary>
    /// Temperature in Celsius
    /// </summary>
    public decimal TempC { get; set; }

    /// <summary>
    /// Temperature in Fahrenheit
    /// </summary>
    public decimal TempF { get; set; }

    public DateTime LastUpdatedUtc { get; set; }

    public DateTime LocalDateTime { get; set; }

}
