﻿using System.Runtime.CompilerServices;
using System.Windows.Input;

namespace Racebox.Views.Navigation;

public class BaseNavBar : Grid
{
    //-------------------------------------------------------------
    // ControlsColor
    //-------------------------------------------------------------
    private const string nameControlsColor = "ControlsColor";
    public static readonly BindableProperty ControlsColorProperty = BindableProperty.Create(nameControlsColor, typeof(Color), typeof(BaseNavBar), Colors.Black);
    public Color ControlsColor
    {
        get { return (Color)GetValue(ControlsColorProperty); }
        set { SetValue(ControlsColorProperty, value); }
    }


    protected override void OnPropertyChanged([CallerMemberName] string propertyName = "")
    {
        base.OnPropertyChanged(propertyName);

        /*
            if (propertyName == "BindingContext")
            {
                var model = BindingContext as NavBarViewModel;
                if (model != null)
                {
                    model.GoBackCheckDenied = GoBackCheckDenied;
                    if (CommandGoBack!=null)
                        model.CommandGoBack = CommandGoBack;
                    else
                        model.CommandGoBack = model.CommandNavbarGoBack;
                }
            }
            else
            if (propertyName == "GoBackCheckDenied")
            {
                var model = BindingContext as NavBarViewModel;
                if (model != null)
                {
                    model.GoBackCheckDenied = GoBackCheckDenied;
                }
            }
            else
            if (propertyName == "CommandGoBack")
            {
                var model = BindingContext as NavBarViewModel;
                if (model != null)
                {
                if (CommandGoBack!=null)
                    model.CommandGoBack = CommandGoBack;
                else
                    model.CommandGoBack = model.CommandNavbarGoBack;
                }
            }
            */

    }


    //-------------------------------------------------------------
    // DimStatus
    //-------------------------------------------------------------
    private const string nameDimStatus = "DimStatus";
    public static readonly BindableProperty DimStatusProperty = BindableProperty.Create(nameDimStatus, typeof(bool), typeof(BaseNavBar), true); //, BindingMode.TwoWay
    public bool DimStatus
    {
        get { return (bool)GetValue(DimStatusProperty); }
        set { SetValue(DimStatusProperty, value); }
    }


    //-------------------------------------------------------------
    // StatusColor
    //-------------------------------------------------------------
    private const string nameStatusColor = "StatusColor";
    public static readonly BindableProperty StatusColorProperty = BindableProperty.Create(nameStatusColor, typeof(Color), typeof(BaseNavBar), Colors.Transparent); //, BindingMode.TwoWay
    public Color StatusColor
    {
        get { return (Color)GetValue(StatusColorProperty); }
        set { SetValue(StatusColorProperty, value); }
    }


    ////-------------------------------------------------------------
    //// HasSearch
    ////-------------------------------------------------------------
    //private const string nameHasSearch = "HasSearch";
    //public static readonly BindableProperty HasSearchProperty = BindableProperty.Create(nameHasSearch, typeof(bool), typeof(BaseNavBar), false); //, BindingMode.TwoWay
    //public bool HasSearch
    //{
    //    get { return (bool)GetValue(HasSearchProperty); }
    //    set { SetValue(HasSearchProperty, value); }
    //}

    //-------------------------------------------------------------
    // ShowLogo
    //-------------------------------------------------------------
    private const string nameShowLogo = "ShowLogo";
    public static readonly BindableProperty ShowLogoProperty = BindableProperty.Create(nameShowLogo, typeof(bool), typeof(BaseNavBar), false); //, BindingMode.TwoWay
    public bool ShowLogo
    {
        get { return (bool)GetValue(ShowLogoProperty); }
        set { SetValue(ShowLogoProperty, value); }
    }

    //-------------------------------------------------------------
    // HasSearch
    //-------------------------------------------------------------
    private const string nameHasSearch = "HasSearch";
    public static readonly BindableProperty HasSearchProperty = BindableProperty.Create(nameHasSearch, typeof(bool), typeof(BaseNavBar), false); //, BindingMode.TwoWay
    public bool HasSearch
    {
        get { return (bool)GetValue(HasSearchProperty); }
        set { SetValue(HasSearchProperty, value); }
    }

    //-------------------------------------------------------------
    // PageTitle
    //-------------------------------------------------------------
    private const string namePageTitle = "PageTitle";
    public static readonly BindableProperty PageTitleProperty = BindableProperty.Create(namePageTitle, typeof(string), typeof(BaseNavBar), "BaseNavBar Title"); //, BindingMode.TwoWay
    public string PageTitle
    {
        get { return (string)GetValue(PageTitleProperty); }
        set
        {
            SetValue(PageTitleProperty, value);
        }
    }

    public ICommand CommandAppGoBack
    {
        get
        {
            return new Command(async (object context) =>
            {
                if (CommandGoBack == null)
                {
                    App.Instance.Presentation.GoBack();
                    return;
                }

                CommandGoBack.Execute(CommandGoBackParameter);
            });
        }
    }

    //-------------------------------------------------------------
    // GoBackCheckDenied
    //-------------------------------------------------------------
    private const string nameGoBackCheckDenied = "GoBackCheckDenied";
    public static readonly BindableProperty GoBackCheckDeniedProperty = BindableProperty.Create(nameGoBackCheckDenied, typeof(bool), typeof(BaseNavBar), false); //, BindingMode.TwoWay
    public bool GoBackCheckDenied
    {
        get { return (bool)GetValue(GoBackCheckDeniedProperty); }
        set { SetValue(GoBackCheckDeniedProperty, value); }
    }



    //-------------------------------------------------------------
    // CommandGoBack
    //-------------------------------------------------------------
    private const string nameCommandGoBack = "CommandGoBack";
    public static readonly BindableProperty CommandGoBackProperty = BindableProperty.Create(nameCommandGoBack, typeof(ICommand), typeof(BaseNavBar), null); //, BindingMode.TwoWay
    public ICommand CommandGoBack
    {
        get { return (ICommand)GetValue(CommandGoBackProperty); }
        set { SetValue(CommandGoBackProperty, value); }
    }




    //-------------------------------------------------------------
    // CommandGoBackParameter
    //-------------------------------------------------------------
    private const string nameCommandGoBackParameter = "CommandGoBackParameter";
    public static readonly BindableProperty CommandGoBackParameterProperty = BindableProperty.Create(nameCommandGoBackParameter, typeof(object), typeof(BaseNavBar), null); //, BindingMode.TwoWay
    public object CommandGoBackParameter
    {
        get { return GetValue(CommandGoBackParameterProperty); }
        set { SetValue(CommandGoBackParameterProperty, value); }
    }

    public ICommand CommandGoBackExecute
    {
        get
        {
            return new Command(async (object context) =>
            {
                CommandGoBack?.Execute(CommandGoBackParameter);
            });
        }
    }

    private const string nameShowGoBack = "ShowGoBack";
    public static readonly BindableProperty ShowGoBackProperty = BindableProperty.Create(nameShowGoBack, typeof(bool), typeof(BaseNavBar), true); //, BindingMode.TwoWay
    public bool ShowGoBack
    {
        get { return (bool)GetValue(ShowGoBackProperty); }
        set { SetValue(ShowGoBackProperty, value); }
    }


}