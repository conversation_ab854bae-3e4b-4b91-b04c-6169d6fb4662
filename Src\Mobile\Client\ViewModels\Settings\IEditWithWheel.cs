﻿using Racebox.ViewModels.Navigation;
using System.Windows.Input;

namespace Racebox.ViewModels;

public interface IEditWithWheel : IEditorForm
{
    int SelectedIndex { get; set; }

    public List<string> ItemsList { get; }

}

public interface IEditWithWheel2 : IEditWithWheel
{
    int SelectedIndex2 { get; set; }

    public List<string> ItemsList2 { get; }

}

public interface IEditorForm
{
    ICommand CommandSubmitForm { get; }

    bool CanSubmit { get; }

    string Title { get; }

    NavigationViewModel NavbarModel { get; }

    public void Bind();

    public SkiaControl CreateForm();

    bool IsDebug { get; }
}

public interface IEditorFormDouble : IEditorForm
{
    double Value { get; set; }

    string Hint { get; }

    public string ValueString { get; set; }
}


