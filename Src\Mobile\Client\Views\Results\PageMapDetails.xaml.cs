using AppoMobi.Framework.Maui.Controls.Navigation.Tabs;
using AppoMobi.Maui.Navigation;

namespace Racebox.Views;

public partial class PageMapDetails : BasePage, ILazyTab
{
    private readonly HistoryResultViewModel _viewModel;

    public PageMapDetails(HistoryResultViewModel vm)
    {
        BindingContext = _viewModel = vm;

        InitializeComponent();

    }

    public void Dispose()
    {
        //todo
    }

    public void UpdateControls(DeviceRotation orientation)
    {

    }


    void UpdateMap()
    {
        MainThread.BeginInvokeOnMainThread(async () =>
        {
            //MainMap.DrawPath();
        });
    }

    public void OnViewAppearing()
    {
        UpdateMap();

        //MainMap.SizeChanged += OnMapSizeChanged;
    }

    private void OnMapSizeChanged(object sender, EventArgs e)
    {
        UpdateMap();
    }

    public void OnViewDisappearing()
    {
        //   MainMap.SizeChanged -= OnMapSizeChanged;
    }

}