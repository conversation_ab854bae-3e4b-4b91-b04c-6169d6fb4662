﻿<?xml version="1.0" encoding="utf-8" ?>
<views:BasePage
    x:Class="Racebox.Views.PageFirmwareHelp"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:partials="clr-namespace:Racebox.Views.Partials"
    xmlns:racebox="clr-namespace:Racebox"
    xmlns:strings="clr-namespace:Racebox.Shared.Strings;assembly=Racebox.Shared"
    xmlns:viewModels="clr-namespace:Racebox.ViewModels"
    xmlns:views="clr-namespace:Racebox.Views"
    xmlns:drawn="clr-namespace:Racebox.Views.Drawn"
    x:Name="ThisPage"
    Title="PageAbout"
    x:DataType="viewModels:ProjectViewModel">


    <Grid
        HorizontalOptions="FillAndExpand"
        VerticalOptions="FillAndExpand">

        <!--  page background gradient  -->
        <partials:GradientToBorderView
            HorizontalOptions="Fill"
            MissingHeight="{Binding NavbarModel.BottomTabsHeightRequest}"
            VerticalOptions="Fill" />

        <draw:Canvas
            x:Name="MainCanvas"
            Gestures="Lock"
            RenderingMode="Accelerated"
            HorizontalOptions="Fill"
            VerticalOptions="FillAndExpand">

            <draw:SkiaLayout
                HorizontalOptions="Fill"
                VerticalOptions="Fill">

                <drawn:EmbossedFrameDrawn
                    BackgroundColor="Transparent"
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill">

                    <draw:SkiaLayout
                        HorizontalOptions="Fill"
                        Spacing="0"
                        Type="Column"
                        VerticalOptions="Fill">

                        <!--  NAVBAR  -->
                        <partials:SkiaNavBar
                            x:Name="NavBar"
                            Margin="0,0,1,0"
                            HeightRequest="65"
                            HorizontalOptions="Fill">

                            <draw:SkiaSvg
                                Margin="16,0,16,0"
                                HeightRequest="16"
                                HorizontalOptions="Start"
                                SvgString="{StaticResource SvgGoBack}"
                                TintColor="#CB6336"
                                VerticalOptions="Center"
                                WidthRequest="16" />

                            <draw:SkiaHotspot
                                CommandTapped="{Binding NavbarModel.CommandGoBack, Mode=OneTime}"
                                HorizontalOptions="Start"
                                TransformView="{x:Reference NavBar}"
                                WidthRequest="44" />

                            <draw:SkiaLabel
                                Margin="48,0,16,0"
                                FontSize="14"
                                HorizontalOptions="Start"
                                LineBreakMode="TailTruncation"
                                MaxLines="1"
                                Style="{StaticResource SkiaLabelDefaultStyle}"
                                Text="{x:Static strings:ResStrings.FirmwareHowToTitle}"
                                TextColor="#E8E3D7"
                                TranslationY="1"
                                VerticalOptions="Center" />


                            <!--  LINE HORIZONTAL  -->
                            <draw:SkiaShape
                                Margin="-16,0"
                                BackgroundColor="Black"
                                CornerRadius="0"
                                HeightRequest="1"
                                HorizontalOptions="Fill"
                                StrokeWidth="0"
                                VerticalOptions="End">
                                <draw:SkiaShape.FillGradient>

                                    <draw:SkiaGradient
                                        EndXRatio="1"
                                        EndYRatio="0"
                                        StartXRatio="0"
                                        StartYRatio="0"
                                        Type="Linear">
                                        <draw:SkiaGradient.Colors>
                                            <Color>#00E8E3D7</Color>
                                            <Color>#99E8E3D7</Color>
                                            <Color>#00E8E3D7</Color>
                                        </draw:SkiaGradient.Colors>
                                    </draw:SkiaGradient>

                                </draw:SkiaShape.FillGradient>
                            </draw:SkiaShape>

                        </partials:SkiaNavBar>

                        <!--  CONTENT SCROLL  -->
                        <draw:SkiaScroll
                            x:Name="MainScroll"
                            AutoScrollingSpeedMs="600"
                            ChangeVelocityScrolled="1.35"
                            HorizontalOptions="Fill"
                            LockChildrenGestures="PassTap"
                            RefreshDistanceLimit="4"
                            RefreshEnabled="False"
                            VerticalOptions="Fill">

                            <draw:SkiaLayout
                                x:Name="MainContainer"
                                Padding="18,16"
                                HorizontalOptions="Fill"
                                Spacing="18"
                                Type="Column"
                                UseCache="Image"
                                VerticalOptions="Start">

                                <draw:SkiaMarkdownLabel
                                    FontFamily="FontTextLight"
                                    FontSize="14"
                                    ParagraphSpacing="0.5"
                                    HorizontalOptions="Center"
                                    LinkColor="CornflowerBlue"
                                    LinkTapped="SkiaMarkdownLabel_OnLinkTapped"
                                    Tag="Me"
                                    Text="{x:Static strings:ResStrings.FirmwareWarningHelp}"
                                    TextColor="#E8E3D7" />

                            </draw:SkiaLayout>

                        </draw:SkiaScroll>


                    </draw:SkiaLayout>

                    <draw:SkiaLabelFps
                        Margin="32"
                        ForceRefresh="False"
                        HorizontalOptions="End"
                        IsVisible="{x:Static racebox:MauiProgram.ShowDebugInfo}"
                        Rotation="-45"
                        VerticalOptions="End"
                        ZIndex="100" />

                </drawn:EmbossedFrameDrawn>

            </draw:SkiaLayout>
        </draw:Canvas>



    </Grid>

</views:BasePage>