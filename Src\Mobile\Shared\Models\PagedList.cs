﻿using Microsoft.EntityFrameworkCore;
using Racebox.Shared.Interfaces;

namespace Racebox.Shared.Models;

public class PagedList<T> : PaginatedListDto<T>, IPagedList
{

    public PagedList(List<T> items, int count, int pageNumber, int pageSize)
    {
        PageNumber = pageNumber;
        PageSize = pageSize;
        TotalPageCount = (int)Math.Ceiling(count / (double)pageSize);
        TotalItemCount = count;
        Items = items;
    }



    public static async Task<PagedList<T>> CreateAsync(IQueryable<T> source, int pageIndex, int pageSize, CancellationToken cancellationToken)
    {
        var count = await source.CountAsync(cancellationToken);
        var items = await source.Skip((pageIndex - 1) * pageSize).Take(pageSize).ToListAsync(cancellationToken);

        return new PagedList<T>(items, count, pageIndex, pageSize);
    }

    #region IPagedList

    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPageCount;
    public bool IsFirstPage => PageNumber == 1;
    public bool IsLastPage => PageNumber == TotalPageCount;
    public int FirstItemOnPage => (PageNumber - 1) * PageSize + 1;
    public int LastItemOnPage
    {
        get
        {
            if (IsLastPage)
            {
                return (PageNumber - 1) * PageSize + Items.Count;
            }
            return (PageNumber - 1) * PageSize + PageSize;
        }
    }

    #endregion


}