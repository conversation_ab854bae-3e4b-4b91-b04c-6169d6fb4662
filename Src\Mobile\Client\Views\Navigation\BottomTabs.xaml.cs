﻿using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Windows.Input;
using AppoMobi.Framework.Maui.Controls;
using AppoMobi.Framework.Maui.Controls.Navigation.Tabs;


namespace Racebox.Views.Navigation.FastShell
{
	[ContentProperty("BottomTabsContent")]
	public partial class BottomTabs : ContentView, IDisposable
	{

		public static readonly BindableProperty UseTappedHandlerProperty = BindableProperty.Create(nameof(UseTappedHandler),
		typeof(TabTappedHandlerType),
		typeof(BottomTabs),
		TabTappedHandlerType.Custom,
		propertyChanged: OnTabsChanged);

		/// <summary>
		/// Gets or sets the type of the tapped handler.
		/// If you want to disable built-in and implement it yourself  
		///  invoke TabItem.EventHandler<int> Tapped manually passing index of tapped tab
		/// </summary>
		public TabTappedHandlerType UseTappedHandler
		{
			get { return (TabTappedHandlerType)GetValue(UseTappedHandlerProperty); }
			set { SetValue(UseTappedHandlerProperty, value); }
		}

		private void OnTabItemPropertyChanged(object sender, PropertyChangedEventArgs e)
		{
			var tabItem = (TabItem)sender;
			if (e.PropertyName == nameof(TabItem.IsVisible))
			{
				UpdateTabVisibility(tabItem);
			}
		}

		public static readonly BindableProperty SelectedIndexProperty = BindableProperty.Create(
			nameof(SelectedIndex),
			typeof(int),
			typeof(BottomTabs),
			defaultValue: -1,
			propertyChanged: SelectedIndexPropertyChanged);

		/*
            //-------------------------------------------------------------
            // StartupIndex
            //-------------------------------------------------------------
            private const string nameStartupIndex = "StartupIndex";
            public static readonly BindableProperty StartupIndexProperty = BindableProperty.Create(nameStartupIndex, typeof(int), typeof(BottomTabs), 0); //, BindingMode.TwoWay
            public int StartupIndex
            {
                get { return (int)GetValue(StartupIndexProperty); }
                set { SetValue(StartupIndexProperty, value); }
            }
            */

		public static readonly BindableProperty ShadowTypeProperty = BindableProperty.Create(
			nameof(ShadowType),
			typeof(ShadowType),
			typeof(BottomTabs),
			defaultBindingMode: BindingMode.OneWayToSource);

		private const int ShadowHeight = 6;




		private List<TabItem> _selectableTabs = new List<TabItem>();


		//private BoxView _contentBackgroundView;
		//private ShadowBoxView _shadow;

		//private ColumnDefinition _lastFillingColumn;

		int lastTapIndex;
		private object lockTapIndex = new();

		public ICommand TabItemTappedCommand
		{
			get
			{
				return new Command<object>((object parameter) =>
				{

					if (parameter is not int)
					{
						throw new Exception("TouchEffect parameter not integer");
					}

					//do not change selected index directly to avoid spam

					_trackingReselection = true;

					if (SelectedIndex == (int)parameter)
					{
						OnTabReselected();
					}
					else
						SelectedIndex = (int)parameter;

				});

			}
		}

		private bool _trackingReselection;


		public BottomTabs()
		{
			InitializeComponent();

			//TabItemTappedCommand = new TapCommand(OnTabItemTapped);

			//UpdateTabsSource();

			UpdateTabs();

		}




		public event EventHandler<SelectedPositionChangedEventArgs> SelectedTabIndexChanged;

		//-------------------------------------------------------------
		// Tabs
		//-------------------------------------------------------------
		private const string nameTabs = "Tabs";
		public static readonly BindableProperty TabsProperty = BindableProperty.Create(nameTabs,
			typeof(ObservableCollection<View>), typeof(BottomTabs), null,
			propertyChanging: OnTabsChanging,
			propertyChanged: OnTabsChanged);
		public ObservableCollection<View> Tabs
		{
			get { return (ObservableCollection<View>)GetValue(TabsProperty); }
			set { SetValue(TabsProperty, value); }
		}


		//public static readonly BindableProperty TabsProperty = BindableProperty.Create(
		//    nameof(Tabs),
		//    typeof(ObservableCollection<TabItem>),
		//    typeof(BottomTabs),
		//    defaultValueCreator: _ => new ObservableCollection<TabItem>());



		public TabType TabType
		{
			get => (TabType)GetValue(TabTypeProperty);
			set => SetValue(TabTypeProperty, value);
		}
		public static readonly BindableProperty TabTypeProperty = BindableProperty.Create(
			nameof(TabType),
			typeof(TabType),
			typeof(BottomTabs),
			defaultValue: TabType.Fixed,
			propertyChanging: OnTabsChanging,
			propertyChanged: OnTabsChanged,
		defaultBindingMode: BindingMode.OneWayToSource);

		private static void OnTabsChanged(BindableObject bindable, object oldvalue, object newvalue)
		{
			var control = bindable as BottomTabs;
			control.UpdateTabs();
		}

		private static void OnTabsChanging(BindableObject bindable, object oldvalue, object newvalue)
		{
			var control = bindable as BottomTabs;
			var old = oldvalue as ObservableCollection<View>;
			if (old != null)
			{
				control.Destroy(old);
			}
		}


		public int SelectedIndex
		{
			get => (int)GetValue(SelectedIndexProperty);
			set
			{
				SetValue(SelectedIndexProperty, value);
			}
		}

		public ShadowType ShadowType
		{
			get => (ShadowType)GetValue(ShadowTypeProperty);
			set => SetValue(ShadowTypeProperty, value);
		}

		public new View Content
		{
			get => base.Content;
			set =>
				throw new NotSupportedException(
					"You can only add TabItem to the BottomTabs through the Tabs property");
		}



		public View BottomTabsContent
		{
			set =>
				throw new NotSupportedException(
					"You can only add TabItem to the BottomTabs through the Tabs property");
		}

		public bool ShowScrollbar { get; set; }

		//private ICommand TabItemTappedCommand { get; }




		public void UpdateTabs()
		{
			Destroy(Tabs);

			UpdateTabType();

			if (Tabs != null)
			{
				foreach (var tab in Tabs)
				{
					OnChildAdded((TabItem)tab);
				}
				Tabs.CollectionChanged += OnTabsCollectionChanged;

				SelectedIndex = 0;


			}


		}

		protected void DisconnectTab(View tab)
		{
			if (tab is TabItem tabItem)
			{
				tabItem.Tapped -= OnTabItemTapped;
				tabItem.PropertyChanged -= OnTabItemPropertyChanged;
			}

			if (tab is IDisposable)
				((IDisposable)tab).Dispose();
		}

		void Destroy(ObservableCollection<View> tabs)
		{
			if (tabs == null)
				return;

			tabs.CollectionChanged -= OnTabsCollectionChanged;
			foreach (var tab in Tabs)
			{
				DisconnectTab(tab);
			}

			_selectableTabs.Clear();
			_grid.ColumnDefinitions.Clear();
			_grid.Children.Clear();
		}

		private void OnTabItemTapped(object sender, int index)
		{
			TabItemTappedCommand?.Execute(index);
		}

		protected bool disposed;
		public void Dispose()
		{
			if (disposed)
				return;
			disposed = true;

			if (Tabs != null)
			{
				Destroy(Tabs);
				Tabs = null;
			}

			_selectableTabs.Clear();
			_grid.ColumnDefinitions.Clear();
			_grid.Children.Clear();
		}






		private static void SelectedIndexPropertyChanged(BindableObject bindable, object oldvalue, object newvalue)
		{
			var tabHostView = (BottomTabs)bindable;


			int selectedIndex = (int)newvalue;
			if (selectedIndex < 0)
			{
				return;
			}

			tabHostView.ApplySelectedIndex();
		}

		private object lockIndex = new();
		private void ApplySelectedIndex()
		{
			var selectedIndex = SelectedIndex;

			//clamp
			if (_selectableTabs.Count == 0)
			{
				selectedIndex = 0;
			}
			if (selectedIndex > _selectableTabs.Count)
			{
				selectedIndex = _selectableTabs.Count - 1;
			}

			//set tabs views visual selection state
			for (int index = 0; index < _selectableTabs.Count; index++)
			{
				_selectableTabs[index].IsSelected = selectedIndex == index;
			}

			//selected and reselected events
			if (lastTapIndex != selectedIndex || selectedIndex == -1)
			{
				lastTapIndex = selectedIndex;
				CommandTabSelected?.Execute(selectedIndex);
			}
			else
			{
				if (_trackingReselection)
				{
					OnTabReselected();
				}
			}
		}

		void OnTabReselected()
		{
			CommandTabReselected?.Execute(SelectedIndex);
			RaiseSelectedTabIndexChanged(new SelectedPositionChangedEventArgs(SelectedIndex));
		}

		protected int lastIndex = -1;


		private void UpdateTabType()
		{
			foreach (var definition in _grid.ColumnDefinitions)
			{
				definition.Width = GridLength.Star;
			}
		}

		private void OnTabsCollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
		{
			var action = NotifyCollectionChangedAction.Reset;
			try
			{
				action = e.Action;
			}
			catch (Exception exception)
			{
				Console.WriteLine(exception);
				return;
			}

			switch (action)
			{
				case NotifyCollectionChangedAction.Add:
					foreach (var tab in e.NewItems)
					{
						OnChildAdded((TabItem)tab);
					}

					break;

				case NotifyCollectionChangedAction.Remove:
					foreach (var tab in e.OldItems)
					{
						OnChildRemoved((TabItem)tab);
					}

					break;

				case NotifyCollectionChangedAction.Move:
				case NotifyCollectionChangedAction.Replace:
				case NotifyCollectionChangedAction.Reset:
				default:
					throw new NotSupportedException();
			}

			//UpdateTabs();
		}

		/// <summary>
		/// Will set up TabIndex aswell
		/// </summary>
		/// <param name="tabItem"></param>
		/// <param name="selectedIndex"></param>
		private void AddTapCommand(TabItem tabItem, int selectedIndex)
		{
			tabItem.TabIndex = selectedIndex;

			switch (this.UseTappedHandler)
			{
				case TabTappedHandlerType.Effect:
					if (tabItem is TabItem tab1)
					{
						RemoveDefaultHandler(tab1);
					}
					TouchEffect.SetCommandTapped(tabItem, TabItemTappedCommand);
					TouchEffect.SetCommandTappedParameter(tabItem, selectedIndex);
					//TouchEffect.SetAnimationTapped(tabItem, SkiaTouchAnimation.Ripple);
					break;
				case TabTappedHandlerType.GestureRecognizer:
					if (tabItem is TabItem tab2)
					{
						RemoveDefaultHandler(tab2);
					}
					var gesture = new TapGestureRecognizer()
					{
						Command = TabItemTappedCommand,
						CommandParameter = selectedIndex
					};
					tabItem.GestureRecognizers.Add(gesture);
					break;
				default:
					if (tabItem is TabItem tab)
					{
						tab.Tapped -= OnTabItemTapped;
						tab.Tapped += OnTabItemTapped;
					}
					break;
			}
		}

		void RemoveDefaultHandler(TabItem tab)
		{
			tab.Tapped -= OnTabItemTapped;
		}

		protected int GetTabIndex(TabItem tabItem)
		{
			return Tabs.IndexOf(tabItem);
		}

		private void OnChildAdded(TabItem tabItem)
		{
			//if (!tabItem.IsEnabled)
			//    return;

			if (tabItem.TabWidth.IsAbsolute)
			{
				tabItem.WidthRequest = tabItem.TabWidth.Value;
				tabItem.HorizontalOptions = LayoutOptions.Start;
			}

			var _gridContainer = new FastGrid
			{
				HorizontalOptions = LayoutOptions.FillAndExpand,
				//BackgroundColor = Colors.Blue
			};

			if (tabItem.TabAlign.Alignment == LayoutAlignment.End && tabItem.TabAlign.Expands)
			{
				_grid.ColumnDefinitions.Add(new ColumnDefinition
				{
					Width = GridLength.Star
				});

				if (tabItem.TabAlign.Expands)
					_gridContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Star });
				_gridContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = tabItem.TabWidth });
				_gridContainer.Children.Add(tabItem);
				Grid.SetColumn(tabItem, 1);

				_grid.Children.Add(_gridContainer);
				Grid.SetColumn(_gridContainer, GetTabIndex(tabItem));

				if (tabItem.IsAction)
				{
					Grid.SetRow(_gridContainer, 0);
					Grid.SetRowSpan(_gridContainer, 2);
				}
				else
					Grid.SetRow(_gridContainer, 1);

				if (tabItem.IsSelectable)
				{
					_selectableTabs.Add(tabItem);
					var index = _selectableTabs.IndexOf((TabItem)tabItem);

					AddTapCommand(tabItem, index);
				}

				if (TabType == TabType.Fixed)
				{
					tabItem.PropertyChanged += OnTabItemPropertyChanged;
					//UpdateTabVisibility(tabItem);
				}
			}
			else
			if (tabItem.TabAlign.Alignment == LayoutAlignment.Start || tabItem.TabAlign.Alignment == LayoutAlignment.End)
			{
				if (tabItem.TabAlign.Expands)
					_grid.ColumnDefinitions.Add(new ColumnDefinition
					{
						Width = GridLength.Star
					});
				else
					_grid.ColumnDefinitions.Add(new ColumnDefinition
					{
						Width = tabItem.TabWidth
					});

				//    tabItem.BackgroundColor = Color.Fuchsia;

				_gridContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = tabItem.TabWidth });
				if (tabItem.TabAlign.Expands)
				{
					_gridContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Star });
				}
				_gridContainer.Children.Add(tabItem);
				Grid.SetColumn(tabItem, 0);


				Grid.SetColumn(_gridContainer, GetTabIndex(tabItem));

				if (tabItem.IsAction)
				{
					Grid.SetRow(_gridContainer, 0);
					Grid.SetRowSpan(_gridContainer, 2);
				}
				else
					Grid.SetRow(_gridContainer, 1);

				_grid.Children.Add(_gridContainer);

				if (tabItem.IsSelectable)
				{
					_selectableTabs.Add(tabItem);
					var index = _selectableTabs.IndexOf((TabItem)tabItem);

					AddTapCommand(tabItem, index);
				}

				if (TabType == TabType.Fixed)
				{
					tabItem.PropertyChanged += OnTabItemPropertyChanged;
					//  UpdateTabVisibility(tabItem);
				}
			}
			else
			{
				//Fill or Center = same..

				if (tabItem.TabWidth.IsAbsolute)
				{
					_grid.ColumnDefinitions.Add(new ColumnDefinition
					{
						Width = tabItem.TabWidth
					});
					//  _gridContainer.BackgroundColor= Color.Aquamarine;
				}
				else
				{
					_grid.ColumnDefinitions.Add(new ColumnDefinition
					{
						Width = GridLength.Star
					});
				}

				//tabItem.HorizontalOptions = LayoutOptions.CenterAndExpand;
				//        tabItem.BackgroundColor = Color.Salmon;


				_gridContainer.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Star });
				_gridContainer.Children.Add(tabItem);
				Grid.SetColumn(tabItem, 0);


				Grid.SetColumn(_gridContainer, GetTabIndex(tabItem));

				//if (TabType == TabType.Scrollable)
				//{
				//    if (Tabs.Count == 1)
				//    {
				//        // Add a last empty slot to fill remaining space
				//        _lastFillingColumn = new ColumnDefinition { Width = GridLength.Star };
				//        _grid.ColumnDefinitions.Add(_lastFillingColumn);
				//    }
				//    else
				//    {
				//        _grid.ColumnDefinitions.Remove(_lastFillingColumn);
				//        _grid.ColumnDefinitions.Add(_lastFillingColumn);
				//    }
				//}

				if (tabItem.IsAction)
				{
					Grid.SetRow(_gridContainer, 0);
					Grid.SetRowSpan(_gridContainer, 2);
				}
				else
					Grid.SetRow(_gridContainer, 1);

				_grid.Children.Add(_gridContainer);

				if (tabItem.IsSelectable)
				{
					_selectableTabs.Add(tabItem);
					var index = _selectableTabs.IndexOf((TabItem)tabItem);

					AddTapCommand(tabItem, index);
				}

				if (TabType == TabType.Fixed)
				{
					tabItem.PropertyChanged += OnTabItemPropertyChanged;
					//  UpdateTabVisibility(tabItem);
				}

				//tabItem.Update();
			}

			ApplySelectedIndex();
		}

		private void OnChildRemoved(TabItem tabItem)
		{
			if (_grid.ColumnDefinitions.Count == 0)
			{
				return;
			}

			if (tabItem.IsSelectable)
			{
				_selectableTabs.Remove(tabItem);
			}

			IView container = null;

			foreach (var child in _grid.Children.ToArray())
			{
				if (child is Grid gridContainer)
				{
					container = gridContainer.Children.FirstOrDefault(x => x == tabItem);
					if (container != null)
					{
						_grid.Children.Remove(child);
						_grid.ColumnDefinitions.RemoveAt(_grid.ColumnDefinitions.Count - 1);
						break;
					}
				}
			}

			DisconnectTab(tabItem);

			ApplySelectedIndex();
		}



		private void UpdateTabVisibility(TabItem tabItem)
		{
			int columnIndex = Grid.GetColumn(tabItem);
			var columnDefinition = _grid.ColumnDefinitions[columnIndex];
			columnDefinition.Width = tabItem.IsVisible ? tabItem.TabWidth : 0;
		}

		private void RaiseSelectedTabIndexChanged(SelectedPositionChangedEventArgs e)
		{

			SelectedTabIndexChanged?.Invoke(this, e);
		}


		//-------------------------------------------------------------
		// CommandTabReselected
		//-------------------------------------------------------------
		private const string nameCommandTabReselected = "CommandTabReselected";
		public static readonly BindableProperty CommandTabReselectedProperty = BindableProperty.Create(nameCommandTabReselected, typeof(ICommand), typeof(BottomTabs), null); //, BindingMode.TwoWay
		public ICommand CommandTabReselected
		{
			get { return (ICommand)GetValue(CommandTabReselectedProperty); }
			set { SetValue(CommandTabReselectedProperty, value); }
		}

		//-------------------------------------------------------------
		// CommandTabSelected
		//-------------------------------------------------------------
		private const string nameCommandTabSelected = "CommandTabSelected";
		public static readonly BindableProperty CommandTabSelectedProperty = BindableProperty.Create(nameCommandTabSelected, typeof(ICommand), typeof(BottomTabs), null); //, BindingMode.TwoWay

		//  private ObservableCollection<View> _oldTabs;

		public ICommand CommandTabSelected
		{
			get { return (ICommand)GetValue(CommandTabSelectedProperty); }
			set { SetValue(CommandTabSelectedProperty, value); }
		}

	}

}