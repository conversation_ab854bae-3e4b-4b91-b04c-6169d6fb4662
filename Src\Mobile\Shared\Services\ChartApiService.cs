using DrawnUi.Draw;
using Racebox.Shared.ChartApi.Dto;
using Racebox.Shared.ChartApi.Models;
using Racebox.Shared.Models;
using Newtonsoft.Json;
using System.Globalization;
using System.Text;
using System.Net.Http.Headers;
using System.Diagnostics;

namespace Racebox.Shared.Services;

public class ChartApiService
{
    private readonly HttpClient _http;
    private readonly CultureInfo _culture;

    public ChartApiService(IHttpClientFactory factory)
    {
        _http = factory.CreateClient("chartApi");
        _culture = CultureInfo.InvariantCulture;
    }

    public bool IsBusy { get; protected set; }

    /// <summary>
    /// Generate chart from measurement result. Safe to use without try-catch.
    /// </summary>
    /// <param name="measureResult">The measurement result to generate chart for</param>
    /// <param name="outputDirectory">Directory to save the chart image</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Chart generation result</returns>
    public async Task<ChartApiResult> GenerateChartAsync(
        MeasureResult measureResult,
        MetaInfo meta,
        string outputDirectory,
        CancellationToken cancellationToken = default)
    {
#if DEBUG
        Debug.WriteLine($"[CHART] Starting chart generation ({measureResult?.Logs?.Count ?? 0} logs)");
#endif

        if (IsBusy)
            return ChartApiResult.CreateError("Chart service is busy");

        if (measureResult?.Logs == null || !measureResult.Logs.Any())
            return ChartApiResult.CreateError("Measurement result is null or has no log data");

        if (string.IsNullOrWhiteSpace(outputDirectory))
            return ChartApiResult.CreateError("Output directory is required");

        IsBusy = true;
        var startTime = DateTime.Now;

        try
        {
            // Generate CSV data using existing Exporter
            var csvContent = GenerateCsvFromMeasureResult(measureResult);
            var csvBytes = Encoding.UTF8.GetBytes(csvContent);

            // Generate metadata
            var metadata = GenerateMetadataFromMeasureResult(measureResult, meta, csvBytes.Length);
            // Use camelCase naming to match server expectations
            var jsonSettings = new JsonSerializerSettings
            {
                ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver(),
                NullValueHandling = NullValueHandling.Ignore
            };
            var metadataJson = JsonConvert.SerializeObject(metadata, jsonSettings);

            // Create output directory if it doesn't exist
            if (!Directory.Exists(outputDirectory))
            {
                Directory.CreateDirectory(outputDirectory);
            }

            // Create multipart form content
            using var form = new MultipartFormDataContent();
            
            // Add CSV file
            var csvContent_part = new ByteArrayContent(csvBytes);
            csvContent_part.Headers.ContentType = MediaTypeHeaderValue.Parse("text/csv");
            form.Add(csvContent_part, "file", "racebox_log.csv");
            
            // Add JSON metadata
            var jsonContent = new StringContent(metadataJson, Encoding.UTF8, "application/json");
            form.Add(jsonContent, "metadata");

#if DEBUG
            Debug.WriteLine($"[CHART] Sending request for {metadata.Meta.Vehicle} - {metadata.Name}");
#endif

            // Send request to API
            var response = await _http.PostAsync("", form, cancellationToken);
            var elapsed = DateTime.Now - startTime;

#if DEBUG
            Debug.WriteLine($"[CHART] Response: {response.StatusCode} ({elapsed.TotalSeconds:F1}s)");
#endif

            if (response.IsSuccessStatusCode)
            {
                return await HandleSuccessResponse(response, outputDirectory, elapsed);
            }
            else
            {
                return await HandleErrorResponse(response, elapsed);
            }
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            return ChartApiResult.CreateError("Request timed out", DateTime.Now - startTime, ex, 408);
        }
        catch (TaskCanceledException ex)
        {
            return ChartApiResult.CreateError("Request was cancelled", DateTime.Now - startTime, ex, 499);
        }
        catch (HttpRequestException ex)
        {
            return ChartApiResult.CreateError($"Network error: {ex.Message}", DateTime.Now - startTime, ex, 503);
        }
        catch (UnauthorizedAccessException ex)
        {
            return ChartApiResult.CreateError($"File access denied: {ex.Message}", DateTime.Now - startTime, ex, 403);
        }
        catch (Exception ex)
        {
            Debug.WriteLine(ex); // Follow existing logging pattern
            return ChartApiResult.CreateError($"Unexpected error: {ex.Message}", DateTime.Now - startTime, ex, 500);
        }
        finally
        {
            IsBusy = false;
        }
    }

    private string GenerateCsvFromMeasureResult(MeasureResult measureResult)
    {
        // Use exactly the same CSV generation as the export functionality
        using var ms = new MemoryStream();
        using var writer = new StreamWriter(ms, Encoding.UTF8);
        
        // Ensure we use invariant culture for number formatting (like the tests do)
        var originalCulture = Thread.CurrentThread.CurrentCulture;
        Thread.CurrentThread.CurrentCulture = _culture;
        try
        {
            // This is the exact same call used in ContentViewModel export
            Exporter.WriteCsv(writer, measureResult);
        }
        finally
        {
            Thread.CurrentThread.CurrentCulture = originalCulture;
        }
        
        writer.Flush();
        ms.Position = 0;
        
        using var reader = new StreamReader(ms, Encoding.UTF8);
        return reader.ReadToEnd();
    }

    private ChartMetadata GenerateMetadataFromMeasureResult(MeasureResult data, MetaInfo meta, long csvSize)
    {
        var performanceMetrics = CalculatePerformanceMetrics(data);
        
        var fileName = Exporter.GenerateLogFileName(data.CreatedTimeUtc.ToLocalTime(), "csv");

        return new ChartMetadata
        {
            Name = fileName,
            Size = csvSize,
            Type = "accel",
            DataSource = "app", 
            Meta = meta,
            Spd = performanceMetrics.SpeedMetrics,
            Dst = performanceMetrics.DistanceMetrics,
            Rng = performanceMetrics.RangeMetrics,
            Shift = new { }
        };
    }

    private PerformanceMetrics CalculatePerformanceMetrics(MeasureResult measureResult)
    {
        var speedMetrics = new Dictionary<string, string>();
        var distanceMetrics = new Dictionary<string, string[]>();
        var rangeMetrics = new Dictionary<string, string>();

        try
        {
            var logs = measureResult.Logs.OrderBy(x => x.TotalSeconds).ToList();
            
            // Calculate speed achievements from existing measured distances
            if (measureResult.Distances?.Any() == true)
            {
                foreach (var distance in measureResult.Distances)
                {
                    var key = distance.Length switch
                    {
                        { } d when Math.Abs(d - 18.29) < 0.5 => "60 ft", // 60 feet in meters
                        201 => "201 m",
                        402 => "402 m",
                        _ => null
                    };
                    
                    if (key != null)
                    {
                        distanceMetrics[key] = new[] 
                        { 
                            distance.Time?.TotalSeconds.ToString("F2", _culture) ?? "0.00",
                            distance.Speed.ToString("F0", _culture)
                        };
                    }
                }
            }

            // Calculate speed ranges from existing measured ranges
            if (measureResult.Ranges?.Any() == true)
            {
                foreach (var range in measureResult.Ranges)
                {
                    var fromSpeed = (int)Math.Round(range.Start);
                    var toSpeed = (int)Math.Round(range.End);
                    var key = $"{fromSpeed}-{toSpeed}";
                    speedMetrics[key] = range.Time?.TotalSeconds.ToString("F2", _culture) ?? "0.00";
                }
            }

            // Calculate traditional 0-X speed metrics if we have log data
            if (logs.Any())
            {
                var speedTargets = new[] { 60, 100, 150, 200 };
                foreach (var target in speedTargets)
                {
                    var achievement = logs.FirstOrDefault(l => l.Speed >= target);
                    if (achievement != null)
                    {
                        speedMetrics[$"0-{target}"] = achievement.TotalSeconds.ToString("F2", _culture);
                    }
                }

                // Calculate 100-200 range if not already present and we have high speed data
                if (!speedMetrics.ContainsKey("100-200"))
                {
                    var start100 = logs.FirstOrDefault(l => l.Speed >= 100);
                    var reach200 = logs.FirstOrDefault(l => l.Speed >= 200);
                    if (start100 != null && reach200 != null)
                    {
                        var rangeTime = reach200.TotalSeconds - start100.TotalSeconds;
                        rangeMetrics["100-200"] = rangeTime.ToString("F2", _culture);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error calculating performance metrics: {ex.Message}");
        }

        return new PerformanceMetrics
        {
            SpeedMetrics = speedMetrics,
            DistanceMetrics = distanceMetrics,
            RangeMetrics = rangeMetrics
        };
    }

    private async Task<ChartApiResult> HandleSuccessResponse(
        HttpResponseMessage response, 
        string outputDirectory, 
        TimeSpan elapsed)
    {
        // Get filename from Content-Disposition header
        string filename = "chart.png";
        if (response.Content.Headers.ContentDisposition?.FileName != null)
        {
            filename = response.Content.Headers.ContentDisposition.FileName.Trim('"');
        }

        // Read image data
        var imageBytes = await response.Content.ReadAsByteArrayAsync();
        
        // Save image with server filename
        var outputPath = Path.Combine(outputDirectory, filename);
        await File.WriteAllBytesAsync(outputPath, imageBytes);

#if DEBUG
        Super.Log($"[CHART] Generated successfully: {filename} ({imageBytes.Length:N0} bytes)");
#endif

        return ChartApiResult.CreateSuccess(
            new ChartResponse
            {
                FilePath = outputPath,
                Filename = filename,
                FileSize = imageBytes.Length
            }, 
            elapsed);
    }

    private async Task<ChartApiResult> HandleErrorResponse(HttpResponseMessage response, TimeSpan elapsed)
    {
        string errorContent;
        try
        {
            errorContent = await response.Content.ReadAsStringAsync();
        }
        catch (Exception ex)
        {
            // If we can't read the response content
            return ChartApiResult.CreateError($"API Error ({response.StatusCode}): Unable to read error response - {ex.Message}", elapsed, ex, (int)response.StatusCode);
        }
        
        try
        {
            var errorObj = JsonConvert.DeserializeObject<dynamic>(errorContent);
            if (errorObj?.error != null)
            {
                return ChartApiResult.CreateError($"API Error ({response.StatusCode}): {errorObj.error}", elapsed, null, (int)response.StatusCode);
            }
        }
        catch
        {
            // JSON parsing failed, use raw content
        }

        return ChartApiResult.CreateError($"API Error ({response.StatusCode}): {errorContent}", elapsed, null, (int)response.StatusCode);
    }

    private static string GetCurrentLanguage()
    {
        // TODO: Implement based on existing localization system in the app
        // For now, return default
        return "en";
    }

    private static string? GetVehicleName(MeasureResult measureResult)
    {
        // TODO: Implement vehicle name lookup based on existing Car entity system
        // This would typically query the database or cache for car information
        // For now, return a placeholder based on CarId
        return measureResult.CarId > 0 ? $"Vehicle {measureResult.CarId}" : "Unknown Vehicle";
    }

    private static string? GetVehicleDescription(MeasureResult measureResult)
    {
        // TODO: Implement vehicle description based on existing Car entity system
        return measureResult.CarId > 0 ? $"Car ID {measureResult.CarId}" : "Unknown vehicle";
    }
}