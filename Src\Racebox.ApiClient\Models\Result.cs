﻿using Racebox.ApiClient.Interfaces;

namespace Racebox.ApiClient.Models;

public class Result<T> : Result, IResult<T>
{
    public Result()
    {

    }

    public Result(Exception exception)
    {
        Errors.Add(exception.Message);
    }

    public T Data { get; set; }
}

public class Result : IResult
{
    public List<string> Errors { get; set; } = new();

    public bool Success { get; set; }

    public int Code { get; set; }

    public string Meta { get; set; }
}