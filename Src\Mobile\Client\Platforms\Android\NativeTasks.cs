﻿using Android.Content;
using Android.Content.Res;
using Android.Media;

namespace Racebox.Helpers;

public partial class NativeTasks
{
    public void OpenLink(string link)
    {

        var intent2 = new Intent(Intent.ActionView,
            Android.Net.Uri.Parse(link));
        intent2.AddFlags(ActivityFlags.NewTask);
        Android.App.Application.Context.StartActivity(intent2);

    }

    public void OpenTelegram(string channel)
    {

        try
        {
            var intent = new Intent(Intent.ActionView,
                Android.Net.Uri.Parse($"tg://resolve?domain={channel}"));
            intent.AddFlags(ActivityFlags.NewTask);
            Android.App.Application.Context.StartActivity(intent);

            return;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
        }

        var intent2 = new Intent(Intent.ActionView,
            Android.Net.Uri.Parse($"https://t.me/{channel}"));
        intent2.AddFlags(ActivityFlags.NewTask);
        Android.App.Application.Context.StartActivity(intent2);

    }

    public void LockOrientationPortrait(bool locked)
    {
        var activity = Platform.CurrentActivity;

        if (locked)
        {
            if (activity.RequestedOrientation != Android.Content.PM.ScreenOrientation.SensorPortrait)
                activity.RequestedOrientation = Android.Content.PM.ScreenOrientation.SensorPortrait;
        }
        else
        {
            if (activity.RequestedOrientation != Android.Content.PM.ScreenOrientation.Unspecified)
                activity.RequestedOrientation = Android.Content.PM.ScreenOrientation.Unspecified;
        }
    }

    public void ExportLogs(IEnumerable<string> fullFilenames)
    {
        ShareFiles("Логи", fullFilenames);
    }
    public void ShareFiles(string message, IEnumerable<string> fullFilenames)
    {
        MainThread.BeginInvokeOnMainThread(async () =>
        {
            try
            {
                var files = fullFilenames.Select(x => new ShareFile(x)).ToList();
                await Share.Default.RequestAsync(new ShareMultipleFilesRequest
                {
                    Title = message,
                    Files = files
                });
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

        });
    }

    public bool CheckGpsIsAvailable()
    {
        bool value = false;
        Android.Locations.LocationManager manager = (Android.Locations.LocationManager)Android.App.Application.Context.GetSystemService(Android.Content.Context.LocationService);
        if (!manager.IsProviderEnabled(Android.Locations.LocationManager.GpsProvider))
        {
            //gps disable
            value = false;
        }
        else
        {
            //Gps enable
            value = true;
        }
        return value;
    }
    public void OpenSettings()
    {
        var intent = new Intent(Android.Provider.Settings.ActionApplicationDetailsSettings,
            Android.Net.Uri.Parse("package:" + Platform.CurrentActivity.PackageName));

        //Intent intent = new Android.Content.Intent(Android.Provider.Settings.ActionLocationSourceSettings);

        intent.AddFlags(ActivityFlags.NewTask);
        Android.App.Application.Context.StartActivity(intent);
    }

    /// <summary>
    /// tries to get all resources from assets folder Resources/Raw/{subfolder}
    /// </summary>
    /// <returns></returns>
    public IEnumerable<string> ListAssets(string subfolder)
    {
        AssetManager assets = Platform.AppContext.Assets;
        string[] files = assets.List(subfolder);
        return files;
    }

    MediaPlayer MediaPlayer;
    public void PlaySoundFromAssets(string filename)
    {
        try
        {
            if (MediaPlayer != null && MediaPlayer.IsPlaying)
            {
                MediaPlayer.Stop();
            }

            if (MediaPlayer != null)
            {
                MediaPlayer.Release();
                MediaPlayer = null;
            }

            if (MediaPlayer == null)
            {
                MediaPlayer = new MediaPlayer();
            }

            AssetManager assets = Platform.AppContext.Assets;

            AssetFileDescriptor descriptor = assets.OpenFd(filename);
            MediaPlayer.SetDataSource(descriptor.FileDescriptor, descriptor.StartOffset, descriptor.Length);
            descriptor.Close();

            MediaPlayer.Prepare();
            MediaPlayer.SetVolume(1f, 1f);

            MediaPlayer.Looping = false;
            MediaPlayer.Start();

        }
        catch (Exception e)
        {
            System.Diagnostics.Debug.WriteLine(e);
        }
    }



}
