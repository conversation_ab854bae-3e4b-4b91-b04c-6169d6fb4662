﻿namespace Racebox.ApiClient.Models;

public class ApiException : Exception
{
    public ApiException(string message) : base(message)
    {

    }

    public ApiException(string message, HttpMethod method, string url, int code) : base(message)
    {
        Method = method;
        Url = url;
        StatusCode = code;
    }

    public int StatusCode { get; set; }
    public string Url { get; set; }
    public HttpMethod Method { get; set; }
}