﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>Exe</OutputType>
    <SourceRevisionId>build$([System.DateTime]::UtcNow.ToString("yyyy-MM-ddTHH:mm:ss:fffZ"))</SourceRevisionId>

    <!--<TargetFrameworks>net9.0-ios;</TargetFrameworks>-->

    <TargetFrameworks>net9.0-android;net9.0-ios;net9.0-maccatalyst</TargetFrameworks>
    <TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>

    <ProjectName>Racebox</ProjectName>
    <ApplicationTitle>Racebox</ApplicationTitle>
    <ApplicationDisplayVersion>1.6</ApplicationDisplayVersion>
    <ApplicationVersion>1.6</ApplicationVersion>
    <Version>1.6</Version>
    <ApplicationId>com.raceboxcompanion.app</ApplicationId>
    <ApplicationIdGuid>75feebbd-cbf5-4555-a7dd-2acfa621aa86</ApplicationIdGuid>

    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.2</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">23.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</SupportedOSPlatformVersion>
    <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</TargetPlatformMinVersion>

    <RootNamespace>Racebox</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<NoWarn>$(NoWarn);NU1608</NoWarn>
		<WindowsPackageType>None</WindowsPackageType>

	</PropertyGroup>

	<!--WINDOWS-->
  <PropertyGroup Condition="'$(Platform)'=='net9.0-windows10.0.19041.0'">
    <WindowsPackageType>MSIX</WindowsPackageType>
    <GenerateAppInstallerFile>False</GenerateAppInstallerFile>
    <AppxPackageSigningEnabled>True</AppxPackageSigningEnabled>
    <PackageCertificateThumbprint>9F7B832E45ADB05EAEEE69130CA539188DFFFEAC</PackageCertificateThumbprint>
    <WindowsPackageType>MSIX</WindowsPackageType>
    <AppxPackageSigningTimestampDigestAlgorithm>SHA256</AppxPackageSigningTimestampDigestAlgorithm>
    <AppxAutoIncrementPackageRevision>True</AppxAutoIncrementPackageRevision>
    <AppxSymbolPackageEnabled>False</AppxSymbolPackageEnabled>
  </PropertyGroup>
  
	<ItemGroup>
		<BundleResource Include="Platforms\iOS\ru.lproj\InfoPlist.strings" Link="ru.lproj\InfoPlist.strings" />
		<BundleResource Include="Platforms\iOS\en.lproj\InfoPlist.strings" Link="en.lproj\InfoPlist.strings" />
	</ItemGroup>

  <!--ADROID-->
  <PropertyGroup Condition="'$(Platform)'=='net9.0-android'">
    <EmbedAssembliesIntoApk>true</EmbedAssembliesIntoApk>
    <AndroidEnableSGenConcurrent>True</AndroidEnableSGenConcurrent>
  </PropertyGroup>

<!--ANDROID--> 
  
  <PropertyGroup Condition=" $(Configuration) == 'Release' And $(TargetFramework.Contains('android')) ">    
    <AndroidEnableProfiledAot>True</AndroidEnableProfiledAot>
    
    <!--fixing droid crash https://github.com/dotnet/runtime/issues/95406-->
    <!--<EnableLLVM>True</EnableLLVM>
        <AndroidEnableProfiledAot>False</AndroidEnableProfiledAot>-->
    <!--<MtouchUseLlvm>True</MtouchUseLlvm>-->

    <!--
		<PublishTrimmed>True</PublishTrimmed>
	-->
    
    <!--STORE-->
    <AndroidPackageFormat>aab</AndroidPackageFormat>
    <!--FOR MANUAL APK DISTRIBUTION-->
    <!--<AndroidPackageFormat>apk</AndroidPackageFormat>
		<AndroidCreatePackagePerAbi>True</AndroidCreatePackagePerAbi>-->    
    
  </PropertyGroup>   							

	<!--iOS-->
	<PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net9.0-ios|AnyCPU'">
		<CreatePackage>false</CreatePackage>
		<MtouchLink>None</MtouchLink>
    <ProvisioningType>manual</ProvisioningType>
    <CodesignProvision />
<!--		<CodesignKey>Apple Development: Created via API (XKN6B668FL)</CodesignKey>-->
<!--		<MtouchProfiling>True</MtouchProfiling>-->
<!--		<MtouchDebug>True</MtouchDebug>-->
		<RuntimeIdentifier>iossimulator-x64</RuntimeIdentifier> 
		<ForceSimulatorX64ArchitectureInIDE>true</ForceSimulatorX64ArchitectureInIDE>
	</PropertyGroup>

  <!--IOS RELEASE-->
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net9.0-ios|AnyCPU'">
		<CreatePackage>false</CreatePackage>
		<!--to be able to use EF core dynamic stuff-->
		<MtouchExtraArgs>--interpreter</MtouchExtraArgs>
		<UseInterpreter>True</UseInterpreter>
	  <MtouchLink>SdkOnly</MtouchLink>
	  <MtouchEnableSGenConc>true</MtouchEnableSGenConc>
	  <CodesignKey>Apple Distribution: Dmitry Borodaenko (YUWVSRRN2T)</CodesignKey>
	  <CodesignProvision>RaceboxAdHoc25</CodesignProvision>
	</PropertyGroup>
  
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net9.0-android|AnyCPU'">
    <AndroidEnableMultiDex>True</AndroidEnableMultiDex>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net9.0-android|AnyCPU'">
    <AndroidEnableMultiDex>True</AndroidEnableMultiDex>
  </PropertyGroup>

  <!--<PropertyGroup Condition="'$(TargetFramework)'=='net9.0-ios'">
    <CodesignKey>Apple Distribution: Dmitry Borodaenko (YUWVSRRN2T)</CodesignKey>
    <CodesignProvision>RaceboxAdHoc25</CodesignProvision>
  </PropertyGroup>-->
		
	<ItemGroup>

		<!-- App icon NOT for Android -->
		<MauiIcon Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) != 'android'" Include="Resources\AppIcon\appicon.svg" ForegroundScale="0.9" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#FFFFFF" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.svg">
			<Color>#010101</Color>
			<BaseSize>128,128</BaseSize>
		</MauiSplashScreen>

	<!-- Images -->
		<MauiImage Include="Resources\Images\**" />
		<MauiImage Update="Resources\Images\splash.svg" BaseSize="208,208" />
	
		<!--fonts-->
		<MauiFont Include="Resources\Fonts\**" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />

	</ItemGroup>

	<ItemGroup>
	  <Compile Remove="BleLibWrapper\**" />
	  <EmbeddedResource Remove="BleLibWrapper\**" />
	  <MauiCss Remove="BleLibWrapper\**" />
	  <MauiXaml Remove="BleLibWrapper\**" />
	  <None Remove="BleLibWrapper\**" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Remove="Resources\Fonts\WeatherMappings.cs" />
	</ItemGroup>


	<ItemGroup>
		<MauiFont Remove="Resources\Fonts\FaPro.cs" />
		<MauiFont Remove="Resources\Fonts\FaProLICENSE.txt" />
	</ItemGroup>

	<ItemGroup>
	  <MauiImage Remove="Resources\Images\splash.svg" />
	</ItemGroup>

	<ItemGroup>
		<None Remove="Resources\Fonts\fa-regular-400.ttf" />
		<None Remove="Resources\Fonts\fa-solid-900.ttf" />
		<None Remove="Resources\Fonts\FaProLICENSE.txt" />
		<None Remove="Resources\Raw\connected.mp3" />
		<None Remove="Resources\Raw\disconnected.mp3" />
		<None Remove="Resources\Raw\Lottie\car.json" />
		<None Remove="Resources\Raw\Lottie\cross.json" />
		<None Remove="Resources\Raw\Lottie\Loader.json" />
		<None Remove="Resources\Raw\Lottie\statusfail.json" />
		<None Remove="Resources\Raw\Lottie\statusok.json" />
		<None Remove="Resources\Raw\Lottie\tapsparks.json" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="Resources\Fonts\FaProLICENSE.txt" />
	</ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\..\DrawnUi.Maui\src\Maui\Addons\DrawnUi.Maui.MapsUi\DrawnUi.Maui.MapsUi.csproj" />
    <ProjectReference Include="..\..\..\..\DrawnUi.Maui\src\Maui\DrawnUi\DrawnUi.Maui.csproj" />

    <ProjectReference Include="..\SDK\Racebox.SDK.csproj" />
    <ProjectReference Include="..\Shared\Racebox.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Maui.Controls" Version="9.0.70" />
    <!--<PackageReference Include="DrawnUi.Maui.MapsUi" Version="1.6.2.27" />-->
    <PackageReference Include="Accord.Math" Version="3.8.0" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.0" />
    <PackageReference Include="Mopups" Version="1.3.0" />
    <PackageReference Include="AppoMobi.Maui.FastPopups" Version="1.0.0.3" />

    <!--required to build in rider-->
    <!--<PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="8.0.21" />
    <PackageReference Include="Microsoft.Maui.Controls" Version="8.0.21" />
    <PackageReference Include="Microsoft.Maui.Essentials" Version="8.0.21" />-->

  </ItemGroup>

  <!-- Version constraints to prevent auto-upgrade -->
  <ItemGroup>
    <PackageReference Update="Microsoft.Maui.Controls" Version="9.0.70" />
    <PackageReference Update="Microsoft.Maui.Controls.Compatibility" Version="9.0.70" />
    <PackageReference Update="Microsoft.Maui.Essentials" Version="9.0.70" />
  </ItemGroup>

	<ItemGroup>
		<Folder Include="Platforms\iOS\ru.lproj\" />
		<Folder Include="Platforms\iOS\en.lproj\" />
		<Folder Include="Resources\Raw\Logs\" />
		<Folder Include="Resources\Raw\Weather\" />
		<Folder Include="Resources\Raw\Sound\" />
		<Folder Include="ViewModels\Navigation\FastShell\" />
		<Folder Include="Views\Items\" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="Views\Results\PageChartDetails.xaml.cs">
	    <DependentUpon>PageChartDetails.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Views\Results\PageResult.xaml.cs">
	    <DependentUpon>PageResult.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Views\Results\PageMapDetails.xaml.cs">
	    <DependentUpon>PageMapDetails.xaml</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
	  <MauiXaml Update="Views\Results\PageChartDetails.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	</ItemGroup>

</Project>
