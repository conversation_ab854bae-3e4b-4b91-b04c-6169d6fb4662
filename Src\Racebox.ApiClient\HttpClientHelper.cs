﻿using AppoMobi.Specials;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Racebox.ApiClient.Extensions;
using Racebox.ApiClient.Interfaces;
using Racebox.ApiClient.Models;
using System.Diagnostics;
using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Runtime.CompilerServices;
using System.Text;

namespace Racebox.ApiClient;

public class HttpClientHelper
{
    public void SetContext(IInjectContext context)
    {
        ContextResolver = new ContextResolver(context);
    }

    //public class HttpClientHelperOptions
    //{
    //    public Func<HttpClient> CreateCustomClient;

    //    public Dictionary<string, string> AddUrlParameters;

    //    public Dictionary<string, string> AddHeaders;

    //    public string BaseUrl { get; set; }
    //}

    public HttpClientHelper()
    {

    }

    public HttpClientHelper(DIContractResolver resolver)
    {
        Resolver = resolver;
    }

    public DIContractResolver Resolver { get; set; }

    protected ContextResolver ContextResolver { get; set; }

    /// <summary>
    /// Set to true to get some logs console output
    /// </summary>
    public static bool IsDebug;

    public Func<HttpClient> CreateHttpClient { get; set; }

    /// <summary>
    /// Can override this to be more dynamic 
    /// </summary>
    public virtual string BaseUrl { get; set; }

    /// <summary>
    /// This is used for GetModel(s) family, RequestWithOptionsAndErrors.
    /// </summary>
    public Func<CancellationTokenSource> CancellationSource { get; set; }

    public ILogger Logger;


    //public void SetOptions(HttpClientHelperOptions options)
    //{
    //    CreateCustomClient = options.CreateCustomClient;

    //    if (options.AddUrlParameters != null)
    //        AddUrlParameters = options.AddUrlParameters;

    //    if (options.AddHeaders != null)
    //        AddHeaders = options.AddHeaders;

    //    BaseUrl = options.BaseUrl;
    //}

    protected virtual HttpClient CreateClient()
    {
        var client = CreateHttpClient;

        return client();
    }

    public Dictionary<string, string> AddUrlParameters = new Dictionary<string, string>();

    public Dictionary<string, string> AddHeaders = new Dictionary<string, string>();

    public static string Escape(string value)
    {
        if (!string.IsNullOrEmpty(value))
            return Uri.EscapeDataString(value);
        return value;
    }

    public static string GetThisMethodName([CallerMemberName] string name = "")
    {
        return name;
    }

    public virtual void OnException(Exception e)
    {

    }

    public async void ProcessException(Exception e)
    {
        if (IsDebug) Trace.WriteLine(e);

        Logger?.LogCritical($"API {GetThisMethodName()}", e);

        OnException(e);

        //#if DEBUG
        //            if (App.DebugOptions.ShowApiPopups)
        //            {
        //                await App.CloseWaitingPopup();
        //                await App.Alert($"API {GetMethodName()}: {e.Message} {e.StackTrace.Left(200)}..");
        //            }
        //#endif
    }

    /// <summary>
    /// Not using base url, as Request does
    /// </summary>
    /// <param name="url"></param>
    /// <param name="dto"></param>
    /// <param name="bearerToken"></param>
    /// <returns></returns>
    public async Task<ApiError> PostForOkResult(string url, object dto, string bearerToken)
    {
        try
        {
            var ret = await Request<ApiResponse>(HttpMethod.Post, url, dto, new KeyValuePair<string, string>("Authorization", bearerToken));
            if (ret.StatusCode == HttpStatusCode.OK)
                return null;

            var maybeError = DeserializeResponse<ApiError>(ret.Content);

            if (maybeError != null && maybeError.ErrorCode != null)
            {
                return maybeError;
            }
            return new ApiError
            {
                ErrorCode = (int)ret.StatusCode
            };
        }
        catch (Exception e)
        {
            if (e is ApiExceptionWithErrorCode)
            {
                return ((ApiExceptionWithErrorCode)e).ApiError;
            }

            ProcessException(e);
            throw e;
        }
    }

    public static string PrettyJson(string unPrettyJson)
    {
        try
        {
            var obj = Newtonsoft.Json.JsonConvert.DeserializeObject(unPrettyJson);
            var ret = Newtonsoft.Json.JsonConvert.SerializeObject(obj, Newtonsoft.Json.Formatting.Indented);
            return ret;
        }
        catch (Exception e)
        {
            return "UNABLE TO DESERIALIZE";
        }
    }

    /// <summary>
    /// Called before request is executed, can change AddHeaders and other global properties here..
    /// </summary>
    protected virtual void OnCreatingClient()
    {

    }

    /// <summary>
    /// Not using BaseUrl, pass full url
    /// </summary>
    public async Task<TResult> Request<TResult>(CancellationTokenSource cancel, HttpMethod method, string url, dynamic fromBody,
        params KeyValuePair<string, string>[] headers) where TResult : class
    {
        return await Request<TResult>(method, url, fromBody, headers, cancel);
    }

    /// <summary>
    /// Not using BaseUrl, pass full url
    /// </summary>
    public async Task<TResult> Request<TResult>(HttpMethod method, string url, dynamic fromBody,
        params KeyValuePair<string, string>[] headers) where TResult : class
    {
        return await Request<TResult>(method, url, fromBody, headers, null);
    }

    /// <summary>
    /// Not using BaseUrl, pass full url
    /// </summary>
    public async Task<TResult> Request<TResult>(HttpMethod method, string url, dynamic fromBody, KeyValuePair<string, string>[] headers, CancellationTokenSource cancel)
        where TResult : class
    {
        if (IsDebug)
            Trace.WriteLine($"[REQUEST] {String.Format("{0:mm:ss.ff}", DateTime.Now)} Started for {url}");

        if (IsDebug && cancel != null)
            Trace.WriteLine($"[REQUEST] Using cancellation token");

        var logHeader = "Headers:";

        TResult ret = default(TResult);

        await Task.Delay(10); //unblock UI thread

        OnCreatingClient();

        var myClient = CreateClient();

        url = AddParametersToUrl(url, AddUrlParameters);

        foreach (var header in AddHeaders)
        {
            if (string.IsNullOrEmpty(header.Value)) continue;
            myClient.DefaultRequestHeaders.Remove(header.Key); //important for other projects
            myClient.DefaultRequestHeaders.Add(header.Key, header.Value);

            if (IsDebug)
                logHeader += $"\n{header.Key} {header.Value}";
        }

        foreach (var header in headers)
        {
            if (string.IsNullOrEmpty(header.Value)) continue;

            myClient.DefaultRequestHeaders.Remove(header.Key); //important for other projects
            myClient.DefaultRequestHeaders.Add(header.Key, header.Value);

            if (IsDebug)
                logHeader += $"\n{header.Key} {header.Value}";
        }

        var lol = new ApiExceptionWithErrorCode();

        try
        {
            var uri = new Uri(url);
            var escape = Uri.EscapeDataString(uri.Query);

            HttpResponseMessage response;
            string content;

            if (method == HttpMethod.Post)
            {

                async Task<HttpResponseMessage> PostWithCancel(Uri uri_, HttpContent data_)
                {
                    if (IsDebug)
                        Trace.WriteLine($"[REQUEST] Using POST with cancellation token");

                    var sendTask = myClient.PostAsync(uri_, data_);
                    while (!sendTask.IsCompleted)
                    {
                        cancel.Token.ThrowIfCancellationRequested();
                        await Task.Delay(10).ConfigureAwait(false);
                    }
                    return await sendTask.ConfigureAwait(false);
                }

                if (IsDebug)
                {
                    if (fromBody != null)
                    {
                        var send = JsonConvert.SerializeObject(fromBody);
                        Trace.WriteLine($"[API] POST to {uri} {logHeader}\n {PrettyJson(send)}");
                    }
                    else
                    {
                        Trace.WriteLine($"[API] POST to {uri} {logHeader}");
                    }
                }

                StringContent encodedContent = null;
                if (fromBody != null)
                {

                    string json = JsonConvert.SerializeObject(
                        fromBody,
                        new JsonSerializerSettings
                        {
                            DateTimeZoneHandling = DateTimeZoneHandling.Utc,
                            DateFormatHandling = DateFormatHandling.IsoDateFormat
                        });

                    encodedContent = new StringContent(json, Encoding.UTF8, "application/json");

                    if (cancel != null)
                        response = await PostWithCancel(uri, encodedContent);
                    else
                        response = await myClient.PostAsync(uri, encodedContent);

                }
                else //fixed net core Unsupported Media Type:
                {
                    var sjonContent = JsonContent.Create(new MediaTypeHeaderValue("application/json"));// new JsonContent(string.Empty);
                    //sjonContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");
                    sjonContent.Headers.ContentType.Parameters.Add(new NameValueHeaderValue("charset", "utf-8"));
                    sjonContent.Headers.ContentType.Parameters.Add(new NameValueHeaderValue("IEEE754Compatible", "true"));

                    if (cancel != null)
                        response = await PostWithCancel(uri, sjonContent);
                    else
                        response = await myClient.PostAsync(uri, sjonContent);

                }

                content = response.Content.ReadAsStringAsync().Result;
            }
            else
            if (method == HttpMethod.Put)
            {

                async Task<HttpResponseMessage> PutWithCancel(Uri uri_, HttpContent data_)
                {
                    if (IsDebug)
                        Trace.WriteLine($"[REQUEST] Using Put with cancellation token");

                    var sendTask = myClient.PutAsync(uri_, data_);
                    while (!sendTask.IsCompleted)
                    {
                        cancel.Token.ThrowIfCancellationRequested();
                        await Task.Delay(10).ConfigureAwait(false);
                    }
                    return await sendTask.ConfigureAwait(false);
                }

                if (IsDebug)
                {
                    if (fromBody != null)
                    {
                        var send = JsonConvert.SerializeObject(fromBody);
                        Trace.WriteLine($"[API] Put to {uri} {logHeader}\n {PrettyJson(send)}");
                    }
                    else
                    {
                        Trace.WriteLine($"[API] Put to {uri} {logHeader}");
                    }
                }

                StringContent encodedContent = null;
                if (fromBody != null)
                {

                    string json = JsonConvert.SerializeObject(
                        fromBody,
                        new JsonSerializerSettings
                        {
                            DateTimeZoneHandling = DateTimeZoneHandling.Utc,
                            DateFormatHandling = DateFormatHandling.IsoDateFormat
                        });

                    encodedContent = new StringContent(json, Encoding.UTF8, "application/json");

                    if (cancel != null)
                        response = await PutWithCancel(uri, encodedContent);
                    else
                        response = await myClient.PutAsync(uri, encodedContent);

                }
                else //fixed net core Unsupported Media Type:
                {
                    var sjonContent = JsonContent.Create(new MediaTypeHeaderValue("application/json"));
                    //sjonContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");
                    sjonContent.Headers.ContentType.Parameters.Add(new NameValueHeaderValue("charset", "utf-8"));
                    sjonContent.Headers.ContentType.Parameters.Add(new NameValueHeaderValue("IEEE754Compatible", "true"));

                    if (cancel != null)
                        response = await PutWithCancel(uri, sjonContent);
                    else
                        response = await myClient.PutAsync(uri, sjonContent);

                }

                content = response.Content.ReadAsStringAsync().Result;
            }
            else
            if (method == HttpMethod.Delete)
            {
                async Task<HttpResponseMessage> DeleteWithCancel(Uri uri_, CancellationToken token_)
                {

                    if (IsDebug)
                    {
                        Trace.WriteLine($"[REQUEST] Using DELETE with cancellation token");
                    }

                    var sendTask = myClient.DeleteAsync(uri_);
                    while (!sendTask.IsCompleted)
                    {
                        token_.ThrowIfCancellationRequested();
                        await Task.Delay(10).ConfigureAwait(false);
                    }
                    return await sendTask.ConfigureAwait(false);
                }

                if (IsDebug)
                {
                    if (fromBody != null)
                    {
                        var send = JsonConvert.SerializeObject(fromBody);
                        Trace.WriteLine($"[API] DELETE to {uri} {logHeader}\n {PrettyJson(send)}");
                    }
                    else
                    {
                        Trace.WriteLine($"[API] DELETE to {uri} {logHeader}");
                    }
                }

                if (cancel != null)
                    response = await DeleteWithCancel(uri, cancel.Token);
                else
                    response = await myClient.DeleteAsync(uri);

                content = await response.Content.ReadAsStringAsync();
            }
            else
            if (method == HttpMethod.Get)
            {
                async Task<HttpResponseMessage> GetWithCancel(Uri uri_, CancellationToken token_)
                {
                    if (IsDebug)
                        Trace.WriteLine($"[REQUEST] Using GET with cancellation token");

                    var sendTask = myClient.GetAsync(uri_);
                    while (!sendTask.IsCompleted)
                    {
                        token_.ThrowIfCancellationRequested();
                        await Task.Delay(10).ConfigureAwait(false);
                    }
                    return await sendTask.ConfigureAwait(false);
                }

                if (IsDebug)
                {
                    Trace.WriteLine($"[API] GET from {uri} {logHeader}");
                }

                if (cancel != null)
                {
                    response = await GetWithCancel(uri, cancel.Token);
                }
                else
                    response = await myClient.GetAsync(uri);

                content = await response.Content.ReadAsStringAsync();
            }
            else
            {
                throw new ApiException($"XAM Request Unsupported method {method}", method, url, (int)HttpStatusCode.OK);
            }

            if (IsDebug)
                Trace.WriteLine($"[REQUEST] Response:{response.StatusCode} at {url}");

            //todo
            //OnStatusCode(response.StatusCode);

            if (typeof(TResult) == typeof(ApiResponse))
            {
                var responseRet = new ApiResponse()
                {
                    StatusCode = response.StatusCode,
                    Content = content,
                    Url = url,
                    Method = method
                };
                return (TResult)(object)responseRet;
            }

            if (response.StatusCode == HttpStatusCode.OK)
            {
                try
                {
                    ret = DeserializeResponse<TResult>(content);

                    if (IsDebug) Trace.WriteLine($"[API] received from {uri} \n {PrettyJson(content)}");

                }
                catch (Exception e)
                {
                    throw new ApiException($"Deserialization failed from {url}: {content.Left(512)}..");
                }
            }
            else
            {

                try
                {
                    var maybeError = DeserializeResponse<ApiError>(content);
                    if (maybeError != null && maybeError.ErrorCode != null)
                    {
                        lol.ApiError = maybeError;
                    }
                }
                catch
                {
                }

                if (lol.ApiError != null)
                {

                }
                else
                {
                    lol.ApiError = new ApiError((int)response.StatusCode, content);

                    throw new ApiException($"Unexpected result: {response.StatusCode} {content}", method, url, (int)response.StatusCode);
                }

                //if (typeof(TResult).GetInterfaces().Any(x=>x == typeof(IWithErrorDto)))
                //        {
                //            ret = JsonConvert.DeserializeObject<TResult>(content);
                //        }
                //        else
                //        {
                //            throw new ApiException($"Unexpected result: {response.StatusCode} {content}", method, url, response.StatusCode);
                //        }
            }

            if (IsDebug) Trace.WriteLine($"[REQUEST] {String.Format("{0:mm:ss.ff}", DateTime.Now)} success for {this.GetType().FullName}");

        }
        catch (Exception e)
        {
            ProcessException(e);
        }

        if (lol.ApiError != null)
        {
            throw lol;
        }

        return ret;
    }

    public TResult DeserializeResponse<TResult>(string content) where TResult : class
    {
        TResult ret = default(TResult);

        if (ContextResolver != null)
            ret = FrameworkExtensions.DeserializeObject<TResult>(content, ContextResolver);
        else
        if (Resolver != null)
            ret = FrameworkExtensions.DeserializeObject<TResult>(content, Resolver);
        else
            ret = JsonConvert.DeserializeObject<TResult>(content);

        return ret;
    }

    protected async Task<TResult> MultipartFormPost<TResult>(string url, HttpContent encodedContent, params KeyValuePair<string, string>[] headers)
        where TResult : class
    {

        HttpMethod method = HttpMethod.Post; ;

        if (IsDebug) Trace.WriteLine($"[REQUEST] {String.Format("{0:mm:ss.ff}", DateTime.Now)} Started for {this.GetType().FullName}");

        TResult ret = default(TResult);

        await Task.Delay(10); //unblock UI thread

        OnCreatingClient();

        var myClient = CreateClient();

        foreach (var header in AddHeaders)
        {
            if (string.IsNullOrEmpty(header.Value)) continue;
            myClient.DefaultRequestHeaders.Remove(header.Key); //important for other projects
            myClient.DefaultRequestHeaders.Add(header.Key, header.Value);
        }

        foreach (var header in headers)
        {
            if (string.IsNullOrEmpty(header.Value)) continue;
            myClient.DefaultRequestHeaders.Remove(header.Key); //important for other projects
            myClient.DefaultRequestHeaders.Add(header.Key, header.Value);
        }


        try
        {
            var uri = new Uri(url);
            var escape = Uri.EscapeDataString(uri.Query);
            var escape2 = Uri.EscapeUriString(url);
            var newUri = uri.Scheme + uri.Authority + uri.LocalPath + escape;

            HttpResponseMessage response;
            string content;


            response = await myClient.PostAsync(uri, encodedContent);
            content = response.Content.ReadAsStringAsync().Result;


            if (typeof(TResult) == typeof(ApiResponse))
            {
                var responseRet = new ApiResponse()
                {
                    StatusCode = response.StatusCode,
                    Content = content,
                    Url = url,
                    Method = method
                };
                return (TResult)(object)responseRet;
            }

            if (response.StatusCode == HttpStatusCode.OK)
            {
                ret = ret = DeserializeResponse<TResult>(content);
            }
            else
            {
                throw new ApiException($"Unexpected result: {response.StatusCode} {content}");
                //   throw new ApiException("Status code doesn't indicate success", method, url, response.StatusCode);
            }

            //return new ModelAuth.HttpResponce
            //{
            //    Code = response.StatusCode,
            //    Headers = response.ToString(),
            //    Body = contents.ToString(),
            //    RequestUrl = url,
            //};
        }
        catch (Exception e)
        {
            if (IsDebug) Trace.WriteLine($"[REQUEST] {String.Format("{0:mm:ss.ff}", DateTime.Now)} ERROR for {this.GetType().FullName} {e.Message}");

            ProcessException(e);
        }



        if (IsDebug) Trace.WriteLine($"[REQUEST] {String.Format("{0:mm:ss.ff}", DateTime.Now)} success for {this.GetType().FullName}");

        return ret;


    }

    public static string AddParametersToUrl(string url, Dictionary<string, string> parameters)
    {
        bool addSome = false;

        if (!url.Contains("?"))
            url += "?";
        else
            addSome = true;

        foreach (var pair in parameters)
        {
            //if (url.Right(1)!="&")
            //    url += "&";
            if (addSome)
                url += "&";

            url += $"{pair.Key}={pair.Value}";

            addSome = true;
        }

        return url;
    }

    /// <summary>
    /// This method uses BaseUrl to combine with the url sub you passed: $"{BaseUrl}/{sub}
    /// Uses CancellationSource !
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="method"></param>
    /// <param name="sub"></param>
    /// <param name="options"></param>
    /// <returns></returns>
    public async Task<(T Content, ApiError Error)> RequestWithOptionsAndErrors<T>(HttpMethod method, string sub, dynamic options) where T : class
    {
        var methodDesc = method.ToString();

        var url = $"{BaseUrl}/{sub}";

        T dto = default(T);

        try
        {
            CancellationTokenSource cancel = CancellationSource?.Invoke();

            dto = await Request<T>(cancel, method, url, options);

            if (dto == null)
            {
                throw new Exception($"[API] {methodDesc} Request Failed");
            }

            //check IWithErrorDto error
            if (dto is IWithErrorDto)
            {
                if (((IWithErrorDto)dto).ErrorCode > 0)
                {
                    //#if DEBUG
                    //                        if (App.DebugOptions.ShowApiPopups)
                    //                            await App.Alert($"API {sub}: " + ((IWithErrorDto)dto).ErrorMessage);
                    //#endif
                    throw new Exception(((IWithErrorDto)dto).ErrorMessage);
                }
            }
        }
        catch (Exception e)
        {
            if (e is ApiExceptionWithErrorCode)
            {
                return (default(T), ((ApiExceptionWithErrorCode)e).ApiError);
            }

            var body = JsonConvert.SerializeObject(options);
            var ret = "";
            if (dto != null)
                ret = JsonConvert.SerializeObject(dto);

            var wrapper = new Exception($"{methodDesc} {url} {body} => {ret}", e);

            Logger?.LogError($"API {sub}", wrapper);

            //#if DEBUG
            //                if (App.DebugOptions.ShowApiPopups)
            //                    await App.Alert($"API {sub}: {e.Message} {e.StackTrace.Left(100)}..");
            //#endif
        }

        return (dto, null);
    }

    public async Task<(T, ApiError)> GetWithErrors<T>(string sub) where T : class
    {
        return await RequestWithOptionsAndErrors<T>(HttpMethod.Get, sub, null);
    }

    /// <summary>
    /// GET method
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="sub"></param>
    /// <returns></returns>
    public async Task<T> GetModel<T>(string sub) where T : class
    {
        T result = default(T);
        try
        {
            var withError = await GetWithErrors<T>(sub);
            result = withError.Item1;
            if (result is IFromDto)
            {
                ((IFromDto)result).Init();
            }
        }
        catch (Exception e)
        {
            ProcessException(e);
        }
        return await Task.FromResult(result);
    }


    /// <summary>
    /// POST method
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="sub"></param>
    /// <param name="options"></param>
    /// <returns></returns>
    public async Task<T> GetModel<T>(string sub, object options) where T : class
    {
        (T Content, ApiError Error) withError = (default(T), null);

        try
        {
            withError = await RequestWithOptionsAndErrors<T>(HttpMethod.Post, sub, options);

            if (withError.Item1 is IFromDto)
            {
                ((IFromDto)withError.Item1).Init();
            }
        }
        catch (Exception e)
        {
            ProcessException(e);
        }

        if (withError.Error != null)
        {
            throw new ApiException(withError.Error.ErrorMessage, HttpMethod.Post, sub, withError.Error.ErrorCode.GetValueOrDefault());
        }

        return await Task.FromResult(withError.Item1);
    }

    /// <summary>
    /// POST
    /// </summary>
    /// <param name="sub"></param>
    /// <param name="options"></param>
    /// <returns></returns>
    public async Task<bool> SendModel(string sub, object options)
    {
        var url = $"{BaseUrl}/{sub}";

        try
        {
            var withError = await PostForOkResult(url, options);

            if (withError == null)
                return true;

            if (withError.ErrorCode == 0 || withError.ErrorCode == (int)HttpStatusCode.OK)
                return true;

        }
        catch (Exception e)
        {
            ProcessException(e);
        }
        return false;
    }

    /*
    /// <summary>
    /// POST method
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="sub"></param>
    /// <param name="options"></param>
    /// <returns></returns>
    public async Task<ApiResultListPaged<T>> GetModels<T>(string sub, object options)
        where T : class, IFromDto
    {
        var dto = await RequestWithOptionsAndErrors<ApiResultListPaged<T>>(HttpMethod.Post, sub, options);

        if (dto.Item1 != null)
        {
            foreach (var item in dto.Item1.Items)
            {
                item.Init();
            }
            return dto.Item1;
        }

        if (dto.Error != null)
        {
            throw new ApiException(dto.Error.ErrorMessage, HttpMethod.Post, sub, dto.Error.ErrorCode.GetValueOrDefault());
        }

        return null;
    }

    */

    public async Task<ApiError> PostForOkResult(string url, object dto)
    {
        try
        {
            var ret = await Request<ApiResponse>(HttpMethod.Post, url, dto);
            if (ret.StatusCode == HttpStatusCode.OK)
                return null;

            var maybeError = DeserializeResponse<ApiError>(ret.Content);
            if (maybeError != null && maybeError.ErrorCode != null)
            {
                return maybeError;
            }

            return new ApiError
            {
                ErrorCode = (int)ret.StatusCode
            };
        }
        catch (Exception e)
        {
            if (e is ApiExceptionWithErrorCode)
            {
                return ((ApiExceptionWithErrorCode)e).ApiError;
            }

            ProcessException(e);
            throw e;
        }
    }

    public async Task<ApiError> DeleteForOkResult(string url)
    {
        try
        {
            var ret = await Request<ApiResponse>(HttpMethod.Delete, url, null);
            if (ret.StatusCode == HttpStatusCode.OK)
                return null;

            var maybeError = DeserializeResponse<ApiError>(ret.Content);
            if (maybeError != null && maybeError.ErrorCode != null)
            {
                return maybeError;
            }
            return new ApiError
            {
                ErrorCode = (int)ret.StatusCode
            };
        }
        catch (Exception e)
        {
            if (e is ApiExceptionWithErrorCode)
            {
                return ((ApiExceptionWithErrorCode)e).ApiError;
            }

            ProcessException(e);
            throw e;
        }
    }

}