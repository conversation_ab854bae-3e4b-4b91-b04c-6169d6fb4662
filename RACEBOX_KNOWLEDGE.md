# Racebox Mobile App - Knowledge Analysis

## Overview
Racebox is a companion mobile application for a GPS-based automotive performance measurement device. The app connects to a Racebox hardware device via Bluetooth Low Energy (BLE) to collect, display, and analyze vehicle performance data in real-time.

## Core Functionality

### Primary Purpose
- **Vehicle Performance Measurement**: Tracks acceleration, speed, GPS coordinates, and other performance metrics
- **Real-time Data Display**: Shows live measurements during driving sessions
- **Historical Analysis**: Stores and displays past measurement sessions with detailed analytics
- **Device Management**: Configures and manages connected Racebox hardware devices

### Key Features

#### 1. **Measurement & Tracking**
- Real-time speed monitoring with speedometer display
- Acceleration measurements (longitudinal and lateral)
- GPS-based position tracking and altitude monitoring
- Distance measurements for various ranges (0-60mph, 0-100mph, quarter mile, etc.)
- Incline/slope measurements
- Weather condition tracking and integration

#### 2. **Data Collection & Storage**
- Bluetooth LE communication with Racebox hardware device
- Local SQLite database for storing measurement results
- Export functionality for measurement data
- Comprehensive logging system for debugging and analysis

#### 3. **User Interface**
- **TabMeasure**: Real-time measurement display with speedometer and gauges
- **TabHistory**: Historical results browsing and filtering
- **TabSettings**: Device configuration, user profiles, and app preferences
- Custom drawn UI components using SkiaSharp for performance
- Responsive design supporting Android, iOS, Windows, and macOS

#### 4. **Device Integration**
- BLE protocol implementation for Racebox device communication
- Device firmware update support
- Battery level monitoring
- Signal strength indicators
- Device settings synchronization

## Technical Architecture

### Platform & Framework
- **.NET MAUI**: Cross-platform mobile application framework
- **Target Platforms**: Android, iOS, Windows, macOS
- **UI Framework**: Custom SkiaSharp-based drawn controls with XAML layouts
- **Database**: SQLite with Entity Framework Core migrations

### Key Components

#### Core Services
- `RaceBoxStateProcessor`: Processes incoming device data and manages measurement states
- `RaceBoxConnector`: Handles BLE communication with hardware device
- `UserManager`: Manages user profiles and preferences
- `WeatherService`: Integrates weather data into measurements
- `AppPreferences`: Handles application settings and user preferences

#### Data Models
- `MeasureResult`: Complete measurement session with speed, acceleration, GPS data
- `MeasuredDistance`: Distance-based measurements (0-60, quarter mile, etc.)
- `MeasuredRange`: Speed range measurements
- `MeasuredLogLine`: Individual data points collected during measurement
- `RaceBoxState`: Real-time device state and sensor data

#### ViewModels (MVVM Pattern)
- `RaceBoxDeviceViewModel`: Main measurement interface and device interaction
- `HistoryViewModel`: Historical data browsing and filtering
- `SettingsViewModel`: Application and device configuration
- `DeviceSettingsViewModel`: Racebox device-specific settings

### Protocol & Communication
- **BLE Protocol**: Custom 20-byte data frames for real-time communication
- **Device Settings**: Configurable parameters like log rate, units, language
- **Firmware Management**: Over-the-air firmware update capabilities

## Development Notes

### Build & Deployment
- **iOS Publishing**: Uses Apple Distribution certificates and App Store provisioning
- **Windows Publishing**: Targets Windows 10.0.19041.0 with x64 architecture
- **Database Migrations**: Entity Framework Core with custom migration workflow

### Known Issues
- Windows BLE connector has reconnection issues after disconnection
- Various platform-specific handlers for UI consistency

### Testing & Development
- Mock connector for development without hardware device
- Comprehensive test logs and GPS simulation data
- Protocol testing suite for BLE communication

## User Workflow

1. **Device Connection**: User connects mobile app to Racebox device via Bluetooth
2. **Configuration**: Sets up user profile, car details, and measurement preferences
3. **Measurement Session**: Performs driving measurements with real-time feedback
4. **Data Review**: Analyzes results in history section with detailed metrics
5. **Export/Share**: Exports measurement data for further analysis

## Business Value
The app serves automotive enthusiasts, professional drivers, and racing teams who need precise performance measurements. It transforms the Racebox hardware into a comprehensive performance analysis tool with professional-grade data collection and analysis capabilities.