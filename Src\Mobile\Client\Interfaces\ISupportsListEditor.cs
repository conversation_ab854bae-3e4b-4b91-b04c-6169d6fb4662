﻿using Racebox.ViewModels.Navigation;
using System.Windows.Input;
using AppoMobi.Framework;

namespace Racebox.Interfaces;

public interface ISupportsListEditor
{
    public string Title { get; }

    public bool HasData { get; }

    public bool IsLoading { get; }

    public NavigationViewModel NavbarModel { get; }

    public ObservableRangeCollection<ISelectableOption> Items { get; }

    public ICommand CommandAddItem { get; }
    public ICommand CommandDeleteItem { get; }
    public ICommand CommandEditItem { get; }
    public ICommand CommandSelectItem { get; }

    public ICommand CommandManageItem { get; }

    public ICommand CommandRefreshData { get; }
}
