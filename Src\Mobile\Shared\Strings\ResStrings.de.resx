<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BtnSave" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="BtnCancel" xml:space="preserve">
    <value>Stornieren</value>
  </data>
  <data name="BtnClose" xml:space="preserve">
    <value>Schließen</value>
  </data>
  <data name="BtnApply" xml:space="preserve">
    <value>Anwenden</value>
  </data>
  <data name="Inputs" xml:space="preserve">
    <value>Eingaben</value>
  </data>
  <data name="Device" xml:space="preserve">
    <value>Gerät</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Ja</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Nein</value>
  </data>
  <data name="Options" xml:space="preserve">
    <value>Optionen</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Fehler</value>
  </data>
  <data name="FeetShort" xml:space="preserve">
    <value>ft</value>
  </data>
  <data name="MetersShort" xml:space="preserve">
    <value>M</value>
  </data>
  <data name="Mph" xml:space="preserve">
    <value>Meilen pro Stunde</value>
  </data>
  <data name="Kmh" xml:space="preserve">
    <value>km/h</value>
  </data>
  <data name="MinutesShort" xml:space="preserve">
    <value>Mindest</value>
  </data>
  <data name="SecondsShort" xml:space="preserve">
    <value>s</value>
  </data>
  <data name="BtnStart" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="BtnStop" xml:space="preserve">
    <value>Stoppen</value>
  </data>
  <data name="BtnConnect" xml:space="preserve">
    <value>Verbinden</value>
  </data>
  <data name="BtnDisconnect" xml:space="preserve">
    <value>Trennen</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>An</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>Aus</value>
  </data>
  <data name="StatusConnecting" xml:space="preserve">
    <value>Verbinden...</value>
  </data>
  <data name="StatusBluetoothOff" xml:space="preserve">
    <value>Bluetooth ist ausgeschaltet</value>
  </data>
  <data name="Measure" xml:space="preserve">
    <value>Messung</value>
  </data>
  <data name="MeasureInstant" xml:space="preserve">
    <value>Sofortiger Lauf</value>
  </data>
  <data name="VendorTitle" xml:space="preserve">
    <value>Rennbox</value>
  </data>
  <data name="SignalStrength" xml:space="preserve">
    <value>Signalstärke</value>
  </data>
  <data name="HintConnectNow" xml:space="preserve">
    <value>Klicken Sie oben auf dem Bildschirm auf das Bluetooth-Symbol, um eine Verbindung zum Gerät herzustellen</value>
  </data>
  <data name="HintStartMeasuring" xml:space="preserve">
    <value>Drücken Sie die Start-Taste, um mit der Messung zu beginnen</value>
  </data>
  <data name="HintMeasureFailed" xml:space="preserve">
    <value>Die Messung ist fehlgeschlagen</value>
  </data>
  <data name="HintMonitoring" xml:space="preserve">
    <value>Warten auf den Start der Messung...</value>
  </data>
  <data name="HintGo" xml:space="preserve">
    <value>GEHEN!!!</value>
  </data>
  <data name="Racing" xml:space="preserve">
    <value>Laufen</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>Armaturenbrett</value>
  </data>
  <data name="History" xml:space="preserve">
    <value>Geschichte</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Einstellungen</value>
  </data>
  <data name="Altitude" xml:space="preserve">
    <value>Höhe</value>
  </data>
  <data name="Heading" xml:space="preserve">
    <value>Überschrift</value>
  </data>
  <data name="Latitude" xml:space="preserve">
    <value>Breite</value>
  </data>
  <data name="Longitude" xml:space="preserve">
    <value>Längengrad</value>
  </data>
  <data name="Frequency" xml:space="preserve">
    <value>Frequenz</value>
  </data>
  <data name="MaxSpeed" xml:space="preserve">
    <value>Höchstgeschwindigkeit</value>
  </data>
  <data name="Incline" xml:space="preserve">
    <value>Neigung</value>
  </data>
  <data name="AllCars" xml:space="preserve">
    <value>Alle Autos</value>
  </data>
  <data name="Filtered" xml:space="preserve">
    <value>Gefiltert</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Alle</value>
  </data>
  <data name="Cars" xml:space="preserve">
    <value>Fahrzeuge</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Wählen</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Befehl</value>
  </data>
  <data name="MeasuringSystem" xml:space="preserve">
    <value>Messsystem</value>
  </data>
  <data name="CustomMetrics" xml:space="preserve">
    <value>Messen von Metriken</value>
  </data>
  <data name="Sound" xml:space="preserve">
    <value>Klang</value>
  </data>
  <data name="RollOut" xml:space="preserve">
    <value>Ausrollen (1 Fuß)</value>
  </data>
  <data name="Car" xml:space="preserve">
    <value>Fahrzeug</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Benutzer</value>
  </data>
  <data name="EditList" xml:space="preserve">
    <value>Liste bearbeiten</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Benutzer</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>Marke</value>
  </data>
  <data name="Model" xml:space="preserve">
    <value>Modell</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Deleting" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="AreYouSure" xml:space="preserve">
    <value>Bist du sicher?</value>
  </data>
  <data name="CannotDeleteSingle" xml:space="preserve">
    <value>Der letzte Datensatz kann nicht gelöscht werden</value>
  </data>
  <data name="CannotDeleteUsed" xml:space="preserve">
    <value>Dies wird tatsächlich genutzt</value>
  </data>
  <data name="USA" xml:space="preserve">
    <value>USA</value>
  </data>
  <data name="Europe" xml:space="preserve">
    <value>Europa</value>
  </data>
  <data name="DefaultBrand" xml:space="preserve">
    <value>Dein</value>
  </data>
  <data name="DefaultModel" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="OrderOldFirst" xml:space="preserve">
    <value>Älter zuerst</value>
  </data>
  <data name="OrderNewFirst" xml:space="preserve">
    <value>Neuere zuerst</value>
  </data>
  <data name="SpeedRange" xml:space="preserve">
    <value>Geschwindigkeitsbereich</value>
  </data>
  <data name="Distance" xml:space="preserve">
    <value>Distanz</value>
  </data>
  <data name="Length" xml:space="preserve">
    <value>Distanz</value>
  </data>
  <data name="Start" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="End" xml:space="preserve">
    <value>Ende</value>
  </data>
  <data name="ValidationStartValue" xml:space="preserve">
    <value>Wert &gt;=0 und &lt;=5000 ist erforderlich</value>
  </data>
  <data name="ValidationEndValue" xml:space="preserve">
    <value>Wert &gt;0 und &lt;=5000 ist erforderlich</value>
  </data>
  <data name="ValidationDifferentValues" xml:space="preserve">
    <value>Der Endwert muss höher sein als der Startwert</value>
  </data>
  <data name="MeasuringUnits" xml:space="preserve">
    <value>Einheiten</value>
  </data>
  <data name="Feet" xml:space="preserve">
    <value>Füße</value>
  </data>
  <data name="Meters" xml:space="preserve">
    <value>Meter</value>
  </data>
  <data name="AlertTurnOnBluetooth" xml:space="preserve">
    <value>Bitte schalten Sie Bluetooth ein.</value>
  </data>
  <data name="AlertNeedGpsPermissionsForBluetooth" xml:space="preserve">
    <value>Bitte erteilen Sie Standortberechtigungen (Android-Systemvoraussetzung für die Verwendung von Bluetooth).</value>
  </data>
  <data name="AlertNeedGpsOnForBluetooth" xml:space="preserve">
    <value>Bitte aktivieren Sie GPS (Android-Systemvoraussetzung für die Verwendung von Bluetooth).</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Standort</value>
  </data>
  <data name="AlertBluetoothUnsupported" xml:space="preserve">
    <value>Ihr Gerät unterstützt kein Bluetooth.</value>
  </data>
  <data name="HintGpsLow" xml:space="preserve">
    <value>Versuchen Sie, den Standort Ihres Geräts zu ändern, um den GPS-Empfang zu verbessern</value>
  </data>
  <data name="StatusGpsLow" xml:space="preserve">
    <value>Schwaches GPS-Signal</value>
  </data>
  <data name="AlertBluetoothPermissionsOff" xml:space="preserve">
    <value>Keine Berechtigung zur Nutzung von Bluetooth.</value>
  </data>
  <data name="Hz" xml:space="preserve">
    <value>Hz</value>
  </data>
  <data name="KmhAdd" xml:space="preserve">
    <value>km/h</value>
  </data>
  <data name="MphAdd" xml:space="preserve">
    <value>Meilen pro Stunde</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Sprache</value>
  </data>
  <data name="MaxAcceleration" xml:space="preserve">
    <value>Max. Beschleunigung.</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Bearbeiten</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="HoursShort" xml:space="preserve">
    <value>h</value>
  </data>
  <data name="BtnReset" xml:space="preserve">
    <value>Zurücksetzen</value>
  </data>
  <data name="BtnCreate" xml:space="preserve">
    <value>Erstellen</value>
  </data>
  <data name="Acceleration" xml:space="preserve">
    <value>Beschleunigung</value>
  </data>
  <data name="AccelerationSide" xml:space="preserve">
    <value>Querbeschleunigung.</value>
  </data>
  <data name="InclineMax" xml:space="preserve">
    <value>Maximale Steigung</value>
  </data>
  <data name="IsValid" xml:space="preserve">
    <value>Ist gültig</value>
  </data>
  <data name="IsValidYes" xml:space="preserve">
    <value>Ja</value>
  </data>
  <data name="IsValidNo" xml:space="preserve">
    <value>NEIN</value>
  </data>
  <data name="AlertBluetoothWillNotBeAvailable" xml:space="preserve">
    <value>Bluetooth wird nicht verfügbar sein.</value>
  </data>
  <data name="SystemSettings" xml:space="preserve">
    <value>Systemeinstellungen</value>
  </data>
  <data name="Time24" xml:space="preserve">
    <value>Zeitformat 24h</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>Um</value>
  </data>
  <data name="BtnQuestion" xml:space="preserve">
    <value>Stelle eine Frage</value>
  </data>
  <data name="AboutAppText" xml:space="preserve">
    <value>Racebox ist eine Begleit-App für die Racebox Pro- und Pro+-Geräte, die für Fahrzeugleistungsmessungen entwickelt wurde.
Die App ist ausschließlich für den Einsatz auf Rennstrecken vorgesehen. Der Autor übernimmt keine Haftung für Verletzungen oder Sachschäden, die bei der Verwendung dieser Software auftreten können.</value>
  </data>
  <data name="Copyright" xml:space="preserve">
    <value>© 2025 Racebox Team</value>
  </data>
  <data name="AlertNeedLocationForBluetooth" xml:space="preserve">
    <value>Wir benötigen Ihre Erlaubnis zur Nutzung Ihres GPS-Standorts (Android-Systemvoraussetzung für die Nutzung von Bluetooth).</value>
  </data>
  <data name="PressBACKOnceAgain" xml:space="preserve">
    <value>Drücken Sie erneut ZURÜCK, um die App zu beenden</value>
  </data>
  <data name="ExportFormat" xml:space="preserve">
    <value>Protokollformat</value>
  </data>
  <data name="ErrorFailedToSaveRecord" xml:space="preserve">
    <value>Datensatz konnte nicht gespeichert werden</value>
  </data>
  <data name="ErrorMetricsAlreadyExist" xml:space="preserve">
    <value>Solche Kennzahlen gibt es bereits</value>
  </data>
  <data name="CompatibleDevicesNotFound" xml:space="preserve">
    <value>Keine Racebox-Geräte in der Nähe gefunden.</value>
  </data>
  <data name="DemoMode" xml:space="preserve">
    <value>Demo-Modus</value>
  </data>
  <data name="DemoWarning" xml:space="preserve">
    <value>In diesem Modus unterbricht die Anwendung die Verbindung zu einem echten Bluetooth-Modul und spielt stattdessen vorab aufgezeichnete Bewegungstitel ab. Sind Sie sicher, dass Sie den Demomodus aktivieren möchten?</value>
  </data>
  <data name="DemoStartHint" xml:space="preserve">
    <value>Klicken Sie auf Geschwindigkeitsanzeige, um die Simulation zu starten</value>
  </data>
  <data name="BadMeasureNotValid" xml:space="preserve">
    <value>Ungültige Messung (große Steigung)</value>
  </data>
  <data name="BadMeasureTooLong" xml:space="preserve">
    <value>Die Messzeit liegt über ACCTIMEOUT</value>
  </data>
  <data name="BadMeasureLowAccel" xml:space="preserve">
    <value>Die Beschleunigung ist gesunken</value>
  </data>
  <data name="BadMeasureInstant" xml:space="preserve">
    <value>Instant Run und die Geschwindigkeit liegt unter der Anfangsgeschwindigkeit des ersten Paares und die Messzeit beträgt mehr als 1 Sekunde</value>
  </data>
  <data name="BadMeasureGps" xml:space="preserve">
    <value>Schlechte GPS-Empfangsqualität</value>
  </data>
  <data name="BadMeasureNoRanges" xml:space="preserve">
    <value>Keine Geschwindigkeitsbereiche gemessen</value>
  </data>
  <data name="BadMeasureNoDistances" xml:space="preserve">
    <value>Keine Entfernungen gemessen</value>
  </data>
  <data name="BadMeasureLowSpeed" xml:space="preserve">
    <value>Geschwindigkeit unter READYSPEEDTHRESHOLDUNIT</value>
  </data>
  <data name="BadMeasureIncline" xml:space="preserve">
    <value>Ergebnis ist ungültig: Steigung war zu wichtig!</value>
  </data>
  <data name="Report" xml:space="preserve">
    <value>Bericht</value>
  </data>
  <data name="MeasuringStateError" xml:space="preserve">
    <value>Die Messung ist fehlgeschlagen</value>
  </data>
  <data name="MeasuringStateFinished" xml:space="preserve">
    <value>Messung abgeschlossen</value>
  </data>
  <data name="MeasuringStateReady" xml:space="preserve">
    <value>Start!</value>
  </data>
  <data name="MeasuringStateActive" xml:space="preserve">
    <value>Rennen!</value>
  </data>
  <data name="MeasuringStateMonitoring" xml:space="preserve">
    <value>Gerät ist eingeschaltet</value>
  </data>
  <data name="MeasuringStateDisabled" xml:space="preserve">
    <value>Gerät ist ausgeschaltet</value>
  </data>
  <data name="Weather" xml:space="preserve">
    <value>Wetter</value>
  </data>
  <data name="X_Power" xml:space="preserve">
    <value>Batterie</value>
  </data>
  <data name="Satellites" xml:space="preserve">
    <value>Satelliten</value>
  </data>
  <data name="DeviceLedBrightness" xml:space="preserve">
    <value>Bildschirmhelligkeit</value>
  </data>
  <data name="DeviceTransmissionType" xml:space="preserve">
    <value>Übertragungsart</value>
  </data>
  <data name="DeviceMaps" xml:space="preserve">
    <value>Karten</value>
  </data>
  <data name="DeviceTone" xml:space="preserve">
    <value>Tonsignale</value>
  </data>
  <data name="DeviceUtc" xml:space="preserve">
    <value>Zeitzone</value>
  </data>
  <data name="DeviceLogFormat" xml:space="preserve">
    <value>Protokollformat</value>
  </data>
  <data name="DeviceLogRate" xml:space="preserve">
    <value>Protokollhäufigkeit</value>
  </data>
  <data name="DevicePrediction" xml:space="preserve">
    <value>Vorhersagemodus</value>
  </data>
  <data name="DeviceRandomStart" xml:space="preserve">
    <value>Beliebige Startzeit</value>
  </data>
  <data name="DeviceMinDistance" xml:space="preserve">
    <value>Mindest. Entfernung anzeigen</value>
  </data>
  <data name="DeviceCarWeight" xml:space="preserve">
    <value>Fahrzeuggewicht</value>
  </data>
  <data name="DeviceDragRatio" xml:space="preserve">
    <value>Coef. ziehen</value>
  </data>
  <data name="DeviceFrontal" xml:space="preserve">
    <value>Querschnittsfläche</value>
  </data>
  <data name="DeviceObdLog" xml:space="preserve">
    <value>OBD-Protokollaufzeichnung</value>
  </data>
  <data name="DeviceShiftTime" xml:space="preserve">
    <value>Schaltzeitmessung</value>
  </data>
  <data name="DeviceObdPidAuto" xml:space="preserve">
    <value>Automatische PID-OBD-Erkennung</value>
  </data>
  <data name="DeviceObdPids" xml:space="preserve">
    <value>Auswählen der zu lesenden OBD-PIDs</value>
  </data>
  <data name="DeviceObdProtocol" xml:space="preserve">
    <value>OBD-Protokoll</value>
  </data>
  <data name="DeviceGnssSecondarySource" xml:space="preserve">
    <value>2. GNSS-Quelle</value>
  </data>
  <data name="DeviceGnssUartOut" xml:space="preserve">
    <value>GNSS-Datenausgabe über USB</value>
  </data>
  <data name="DeviceGnssColdStart" xml:space="preserve">
    <value>GNSS-Kaltstart</value>
  </data>
  <data name="DeviceGnssReconfig" xml:space="preserve">
    <value>GNSS-Neukonfiguration</value>
  </data>
  <data name="DeviceResetConfiguration" xml:space="preserve">
    <value>Konfiguration zurücksetzen</value>
  </data>
  <data name="ResetDevice" xml:space="preserve">
    <value>Neustart des Geräts</value>
  </data>
  <data name="WarningCannotUndo" xml:space="preserve">
    <value>Dieser Vorgang kann nicht rückgängig gemacht werden.</value>
  </data>
  <data name="DeviceSettings" xml:space="preserve">
    <value>Geräteeinstellungen</value>
  </data>
  <data name="FirmwareWarningHelp" xml:space="preserve">
    <value>Um die Firmware auf Ihrem __Racebox__-Gerät zu aktualisieren, gehen Sie folgendermaßen vor:
 1. Laden Sie die neue Version von der Website [racebox.ru](https://racebox.ru/obnovlenie-po) herunter.
 2. Kopieren Sie die Datei im `HEX`-Format, die Ihrem Gerätemodell entspricht, in das Stammverzeichnis der Speicherkarte.
 3. Legen Sie die Speicherkarte in das ausgeschaltete Gerät ein.
 4. Während Sie die `Mode`-Taste gedrückt halten, schließen Sie das Gerät über das USB-Kabel an die Stromversorgung an.
 5. Warten Sie, bis die Speicherkarte initialisiert ist und die Meldung `HOLD TO UPDATE X.XX` erscheint (wobei X.XX die heruntergeladene Softwareversion ist).
 6. Halten Sie die `Mode`-Taste gedrückt, um den Aktualisierungsvorgang zu starten.
 7. Warten Sie, bis die Aktualisierung abgeschlossen ist, und stellen Sie sicher, dass die neue Firmware-Version angezeigt wird, wenn Sie das Gerät einschalten.
 8. Starten Sie die _Racebox__-Anwendung neu.</value>
  </data>
  <data name="FirmwareHowToTitle" xml:space="preserve">
    <value>So aktualisieren Sie die Firmware</value>
  </data>
  <data name="FirmwareWarningTitle" xml:space="preserve">
    <value>Veraltete Firmware</value>
  </data>
  <data name="SettingsCanSpeak" xml:space="preserve">
    <value>Sagen Sie die Geschwindigkeit</value>
  </data>
</root>