﻿using FluentAssertions;
using MapsterMapper;
using Racebox.ApiClient;
using Racebox.ApiClient.Dto;
using Racebox.Shared;
using Racebox.Shared.Models;
using Racebox.Shared.WeatherApi;
using Racebox.Tests.Models;
using System;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace Racebox.Tests;

public class ApiTests : TestsBase
{


    protected HttpClient GetHttpClient()
    {
        var clientFactory = TestHost.Services.GetService<IHttpClientFactory>();
        return clientFactory.CreateClient("weatherApi");
    }

    [Test]
    public async Task GetWeather()
    {

        // Use the client factory to get an HttpClient with the name "Tests"
        using var client = GetHttpClient();

        var result = await client.GetCurrentWeather(SharedSecrets.WeatherApiKey, "48.856079 2.298828");

        foreach (var error in result.Errors)
        {
            TestContext.WriteLine($"{error}");
        }

        result.Success.Should().BeTrue();
        TestContext.WriteLine($"Check: " +
                              $"Now: {DateTime.UtcNow}, LastUpdated: {result.Data.Current.LastUpdatedUtc}\n" +
                              $"{Racebox.Shared.Fonts.WeatherMappings.GetIcon(result.Data.Current.Condition.Code)}\n {Json(result.Data)}");
    }

    [Test]
    public async Task GetWeatherWithServiceAndMap()
    {
        var service = TestHost.Services.GetService<WeatherService>();

        var result = await service.GetCurrentWeather(48.856079, 2.298828);

        result.Should().NotBeNull();

        var mapper = TestHost.Services.GetService<IMapper>();

        var entity = mapper.Map<Weather>(result);

        entity.TempC.Should().Be(result.TempC);
    }


}