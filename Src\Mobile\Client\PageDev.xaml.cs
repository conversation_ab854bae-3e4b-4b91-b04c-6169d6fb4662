﻿//using ColorPicker.Maui;

using System.Diagnostics;

namespace Racebox.UI.Dev
{
#pragma warning disable CS8670


    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class PageDev
    {
        public PageDev()
        {
            try
            {
                BindingContext = _vm = new DevPageViewModel();

                InitializeComponent();


            }
            catch (Exception e)
            {
                Designer.DisplayException(this, e);
            }
        }

        //public MPoint GetMapCenter()
        //{
        //    var centerPosition = new Point(mapView.Width / 2.0, mapView.Height / 2.0);
        //    var worldPosition = mapView.Viewport.ScreenToWorld(centerPosition.X, centerPosition.Y);

        //    return worldPosition;
        //}


        private bool initialized;

        private readonly DevPageViewModel _vm;

        protected override void OnAppearing()
        {
            base.OnAppearing();



            if (BindingContext is DevPageViewModel viewModel && !initialized)
            {
                initialized = true;

                Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(500), async () =>
                {
                    viewModel.OnViewAppearing();

                    //await Form.FadeTo(1.0, 150, Easing.CubicIn);

                    //MainThread.BeginInvokeOnMainThread(async () =>
                    //{

                    //    MainMap.SetPath(new (double, double)[]
                    //    {
                    //        (59.946466, 30.356859),
                    //        (59.946000, 30.355000),
                    //    });
                    //    // 59.946466, 30.356859

                    //    /*
                    //    var location = await Geolocation.GetLastKnownLocationAsync();

                    //    if (location != null)
                    //    {
                    //        var smc = SphericalMercator.FromLonLat(location.Longitude, location.Latitude);
                    //        map.Home = n => n.NavigateTo(new MPoint(smc.x, smc.y), map.Resolutions[16]);  //0 zoomed out-19 zoomed in

                    //        //Console.WriteLine($"Latitude: {location.Latitude}, Longitude: {location.Longitude}, Altitude: {location.Altitude}");
                    //        //mapView.MyLocationLayer.UpdateMyLocation(new Position(location.Latitude, location.Longitude), true);
                    //        //mapView.Navigator.ZoomTo(mapView.Viewport.Resolution, got, 200, easing);
                    //        //var gotTo = new MPoint(mapView.Width / 2.0, mapView.Height / 2.0); //GetMapCenter()
                    //        //mapView.Navigator.NavigateTo(gotTo, mapView.Viewport.Resolution, 250);

                    //    }
                    //    */



                    //});

                    return false;
                });
            }

        }


        private void Hotspot_Tapped(object sender, TouchActionEventArgs e)
        {

            //MainThread.BeginInvokeOnMainThread(() =>
            //{
            //    TestGrid.Children.Clear();
            //});

        }

        private void OpenPicker(object sender, EventArgs e)
        {

        }

        private void Button_Clicked(object sender, EventArgs e)
        {

        }

        //private void ColorPicker_PickedColorChanged(object sender, PickedColorChangedEventArgs e)
        //{
        //    if (initialized)
        //    {
        //        MainThread.BeginInvokeOnMainThread(() =>
        //        {
        //            CustomBitmapRenderer.TintColor = e.NewPickedColorValue.ToMapsui();
        //            MainMap.LayerMap.Data HasChanged();
        //            MainMap.Update();
        //        });
        //    }
        //}
        private void TappedButton(object sender, EventArgs e)
        {

            MainThread.BeginInvokeOnMainThread(() =>
            {
                _vm.HasBattery = !_vm.HasBattery;
                Debug.WriteLine($"[D] {_vm.HasBattery}");
            });

        }
    }



}