//
//  BLEController.swift
//  Racebox
//
//  Created by Д<PERSON><PERSON><PERSON><PERSON><PERSON> on 17/10/2019.
//  Copyright © 2019 <PERSON>. All rights reserved.
//
import CoreBluetooth

class BLEController: NSObject, CBCentralManagerDelegate, CBPeripheralDelegate {
    
    var peripherals: [CBPeripheral] = []
    var manager: CBCentralManager? = nil
    var mainPeripheral: CBPeripheral? = nil
    var mainCharacteristic: CBCharacteristic? = nil
    static var disconnectCount: Int = 0
    
    let BLEService = "FFE0"
    let BLECharacteristic = "FFE1"
    
    let bleNotifName = Notification.Name(rawValue: BLE_NOTIFICATION)
    
    override init() {
        super.init()
        self.manager = CBCentralManager(delegate: self, queue: nil);
        self.manager?.delegate = self
  //      if mainPeripheral == nil {
  //          scanBLEDevices()
  //      }
    }
    
    func scanBLEDevices() {
        print("Scanning for BLE devices")
        manager?.scanForPeripherals(withServices: [CBUUID.init(string: BLEService)], options: nil)
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            self.stopScanForBLEDevices()
//            for peripheral in self.peripherals {
//                if let name = peripheral.name {
//                    if name.starts(with: "Racebox") && self.mainPeripheral == nil {
//                        //self.stopScanForBLEDevices()
//                        self.manager?.connect(peripheral, options: nil)
//                    }
//                }
//            }
        }
    }
    
    func stopScanForBLEDevices() {
        manager?.stopScan()
        print("Stop scanning for BLE devices")
    }
    
    // MARK: CBCentralManagerDelegate Methods
    
    func centralManager(_ central: CBCentralManager, didDiscover peripheral: CBPeripheral, advertisementData: [String : Any], rssi RSSI: NSNumber) {
        if(!peripherals.contains(peripheral)) {
            peripherals.append(peripheral)
        }
        if let name = peripheral.name {
            if name.starts(with: "Racebox") {
                self.stopScanForBLEDevices()
                self.manager?.connect(peripheral, options: nil)
            }
        }
    }
    
    func centralManager(_ central: CBCentralManager, didConnect peripheral: CBPeripheral) {
        mainPeripheral = peripheral
        peripheral.delegate = self
        peripheral.discoverServices(nil)
        print("Connected to " +  peripheral.name!)
    }
    
    func centralManager(_ central: CBCentralManager, didFailToConnect peripheral: CBPeripheral, error: Error?) {
        print(error!)
    }
    
    func centralManager(_ central: CBCentralManager, didDisconnectPeripheral peripheral: CBPeripheral, error: Error?) {
        //mainPeripheral = nil
        BLEController.disconnectCount += 1
        print("Disconnected" + peripheral.name!)
        if central.state == CBManagerState.poweredOn {
            self.manager?.connect(peripheral, options: nil)
        }
    }
    
    func centralManagerDidUpdateState(_ central: CBCentralManager) {
        switch (central.state) {
            
        case CBManagerState.poweredOn:
            print("CoreBluetooth BLE hardware is powered on and ready")
            scanBLEDevices()
            
        case CBManagerState.poweredOff:
            print("CoreBluetooth BLE hardware is powered off")
        case CBManagerState.unauthorized:
            print("CoreBluetooth BLE state is unauthorized")
        case CBManagerState.unknown:
            print("CoreBluetooth BLE state is unknown")
        case CBManagerState.resetting:
            print("CoreBluetooth BLE hardware is resetting")
        case CBManagerState.unsupported:
            print("CoreBluetooth BLE hardware is unsupported on this platform")
        @unknown default:
            print("CoreBluetooth BLE undefined error")
        }
    }
    
    // MARK: CBPeripheralDelegate Methods
    
    func peripheral(_ peripheral: CBPeripheral, didDiscoverServices error: Error?) {
        
        for service in peripheral.services! {
            print("Service found with UUID: " + service.uuid.uuidString)
            //device information service
            if (service.uuid.uuidString == "180A") {
                peripheral.discoverCharacteristics(nil, for: service)
            }
            //GAP (Generic Access Profile) for Device Name
            // This replaces the deprecated CBUUIDGenericAccessProfileString
            if (service.uuid.uuidString == "1800") {
                peripheral.discoverCharacteristics(nil, for: service)
            }
            //Bluno Service
            if (service.uuid.uuidString == BLEService) {
                peripheral.discoverCharacteristics(nil, for: service)
            }
        }
        
    }
    
    func peripheral(_ peripheral: CBPeripheral, didDiscoverCharacteristicsFor service: CBService, error: Error?) {
        //get device name
        if (service.uuid.uuidString == "1800") {
            for characteristic in service.characteristics! {
                if (characteristic.uuid.uuidString == "2A00") {
                    peripheral.readValue(for: characteristic)
                    print("Found Device Name Characteristic")
                }
            }
        }
        if (service.uuid.uuidString == "180A") {
            for characteristic in service.characteristics! {
                if (characteristic.uuid.uuidString == "2A29") {
                    peripheral.readValue(for: characteristic)
                    print("Found a Device Manufacturer Name Characteristic")
                } else if (characteristic.uuid.uuidString == "2A23") {
                    peripheral.readValue(for: characteristic)
                    print("Found System ID")
                }
            }
        }
        if (service.uuid.uuidString == BLEService) {
            for characteristic in service.characteristics! {
                if (characteristic.uuid.uuidString == BLECharacteristic) {
                    mainCharacteristic = characteristic
                    peripheral.setNotifyValue(true, for: characteristic)
                    print("Found Racebox Data Characteristic")
                }
            }
        }
        
    }
    
    func peripheral(_ peripheral: CBPeripheral, didUpdateValueFor characteristic: CBCharacteristic, error: Error?) {
        
        if (characteristic.uuid.uuidString == "2A00") {
            //value for device name recieved
            let deviceName = characteristic.value
            print(deviceName ?? "No Device Name")
        } else if (characteristic.uuid.uuidString == "2A29") {
            //value for manufacturer name recieved
            let manufacturerName = characteristic.value
            print(manufacturerName ?? "No Manufacturer Name")
        } else if (characteristic.uuid.uuidString == "2A23") {
            //value for system ID recieved
            let systemID = characteristic.value
            print(systemID ?? "No System ID")
        } else if (characteristic.uuid.uuidString == BLECharacteristic) {
            //data recieved
            if(characteristic.value != nil) {
               // let stringValue = String(data: characteristic.value!, encoding: String.Encoding.ascii)!
                //print(characteristic.value!.hexString)
                fix.parseFrameBLE(rawData: characteristic.value!)
                NotificationCenter.default.post(name: bleNotifName, object: nil)
            }
        }
    }
    
}
