﻿using Microsoft.Extensions.DependencyInjection.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Racebox.ApiClient.Interfaces;
using ErrorEventArgs = Newtonsoft.Json.Serialization.ErrorEventArgs;

namespace Racebox.ApiClient.Extensions;

public static class FrameworkExtensions
{
    public static bool UseDIDeserialization { get; set; }

    public static void AddDeserializationWithDI(this IServiceCollection services)
    {
        services.TryAddSingleton<IDIMeta>(s =>
        {
            return new DIMetaDefault(services);
        });
        UseDIDeserialization = true;
    }

    public static void HandleDeserializationError(object sender, ErrorEventArgs errorArgs)
    {
        _ = errorArgs.ErrorContext.Error.Message;
        errorArgs.ErrorContext.Handled = true;
    }

    public static void Populate(object source, object target, DefaultContractResolver resolver)
    {
        JsonSerializerSettings settings = new JsonSerializerSettings
        {
            ContractResolver = resolver,
            Error = new EventHandler<ErrorEventArgs>(HandleDeserializationError),
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            ObjectCreationHandling = ObjectCreationHandling.Replace
        };
        JsonConvert.PopulateObject(JsonConvert.SerializeObject(source, settings), target, settings);
    }

    public static T Clone<T>(T source, DefaultContractResolver resolver)
    {
        if (source == null)
        {
            return default(T);
        }

        return DeserializeObject<T>(JsonConvert.SerializeObject(source, new JsonSerializerSettings
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                Error = new EventHandler<ErrorEventArgs>(HandleDeserializationError)
            }),
            resolver,
            new JsonSerializerSettings
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                Error = new EventHandler<ErrorEventArgs>(HandleDeserializationError)
            });
    }
    public static T DeserializeObject<T>(string json, DefaultContractResolver resolver, JsonSerializerSettings options = null)
    {
        var ret = JsonConvert.DeserializeObject<T>(json, new JsonSerializerSettings
        {
            ContractResolver = resolver
        });

        return ret;
    }

    public static object? DeserializeObject(string json, Type type, DefaultContractResolver resolver, JsonSerializerSettings options = null)
    {
        var ret = JsonConvert.DeserializeObject(json, type, new JsonSerializerSettings
        {
            ContractResolver = resolver
        });

        return ret;
    }

    public static T ConvertTo<T>(object source, DefaultContractResolver resolver)
    {
        return DeserializeObject<T>(JsonConvert.SerializeObject(source, new JsonSerializerSettings
        {
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            Error = new EventHandler<ErrorEventArgs>(HandleDeserializationError)
        }), resolver, new JsonSerializerSettings
        {
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            Error = new EventHandler<ErrorEventArgs>(HandleDeserializationError)
        });
    }

}