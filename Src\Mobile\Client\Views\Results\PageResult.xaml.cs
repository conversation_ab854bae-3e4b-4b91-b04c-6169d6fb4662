using AppoMobi.Framework.Maui.Controls.Navigation.Tabs;
using AppoMobi.Maui.Navigation;

namespace Racebox.Views;

public partial class PageResult : BasePage, ILazyTab
{
    private readonly HistoryResultViewModel _viewModel;

    public PageResult()
    {
        try
        {
            InitializeComponent();

            BindingContext = _viewModel = App.Instance.Services.GetService<HistoryResultViewModel>();

            _viewModel.Map = this.MainMap;
        }
        catch (Exception e)
        {
            Designer.DisplayException(this, e);
        }
    }

    public void Dispose()
    {
        //todo
    }

    public void UpdateControls(DeviceRotation orientation)
    {

    }


    public void OnViewAppearing()
    {
        MainMap.LayoutIsReady += OnMapSizeChanged;

        if (!_viewModel.Initialized)
        {

            Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(150), async () =>
            {
                await Task.Run(async () =>
                {
                    _viewModel.Init();

                }).ConfigureAwait(false);
                return false;
            });

            //Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(2500), async () =>
            //{
            //    MainThread.BeginInvokeOnMainThread(async () =>
            //    {
            //        MainMap.SetPath(new (double, double)[]
            //        {
            //            (59.946466, 30.356859),
            //            (59.946000, 30.355000),
            //        });
            //    });

            //    return false;
            //});
        }
    }

    //Maps.RaceBoxMap MainMap;

    private void OnMapSizeChanged(object sender, EventArgs e)
    {
        if (_viewModel.Initialized)
        {
            MainMap.DrawPath();
        }
    }

    public void OnViewDisappearing()
    {
        MainMap.LayoutIsReady -= OnMapSizeChanged;
    }

    public void KeyboardResized(double size)
    {

    }

    private void OnScrollViewAppeared(object sender, bool b)
    {
        if (b)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                await MainScroll.FadeToAsync(1.0, 250, Easing.CubicIn);
            });
        }
    }
}