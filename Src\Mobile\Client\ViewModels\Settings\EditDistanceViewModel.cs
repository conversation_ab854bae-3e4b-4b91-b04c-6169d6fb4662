﻿using Racebox.Helpers.Validation;
using Racebox.Shared.Enums;
using Racebox.Shared.Strings;
using Racebox.Views.Partials;
using System.Windows.Input;
using AppoMobi.Framework;
using AppoMobi.Framework.Maui.Models;


namespace Racebox.ViewModels
{
    public class EditDistanceViewModel : ProjectViewModel, IEditWithWheel
    {
        public SkiaControl CreateForm()
        {
            return new FormEditDistanceDrawn();
        }

        public static List<int> _distances;

        static EditDistanceViewModel()
        {
            _distances = Presets.Distances;
        }

        private int _SelectedIndex = -1;
        public int SelectedIndex
        {
            get
            {
                return _SelectedIndex;
            }
            set
            {
                if (_SelectedIndex != value)
                {
                    _SelectedIndex = value;
                    OnPropertyChanged();

                    if (value >= 0)
                        End = _distances[value];
                    else
                    {
                        End = _distances[0];
                    }
                }
            }
        }


        private List<string> _distancesList;
        public List<string> ItemsList
        {
            get
            {
                return _distancesList;
            }
            set
            {
                if (_distancesList != value)
                {
                    _distancesList = value;
                    OnPropertyChanged();
                }
            }
        }

        private bool _IsReady;
        public bool IsReady
        {
            get
            {
                return _IsReady;
            }
            set
            {
                if (_IsReady != value)
                {
                    _IsReady = value;
                    OnPropertyChanged();
                }
            }
        }


        public EditDistanceViewModel(LocalDistance item, Func<OptionItem, Task<int>> callback)
        {
            _callback = callback;
            _item = item;

            ValidationInitialize();

            Start = (int)item.Start;
            End = (int)item.End;
            Units = item.Units;
        }

        public void Bind()
        {
            ItemsList = _distances.Select(x => x.ToString()).ToList();

            Start = (int)_item.Start;
            End = (int)_item.End;
            var hasIndex = _distances.IndexOf(End);
            if (hasIndex >= 0)
            {
                SelectedIndex = hasIndex;
            }
            else
            {
                SelectedIndex = 0;
            }

            Validate();

            IsReady = true;
        }

        public ICommand CommandSubmitForm => new Command(async (object context) =>
        {
            UpdateValidator();
            if (!CanSubmit)
            {
                return;
            }

            try
            {
                IsBusy = true;
                await Task.Delay(10); //update UI

                var outItem = new LocalDistance(Start, End, Units, null);
                outItem.Id = _item.Id;

                var errorCode = await _callback(outItem);

                if (errorCode > 0)
                {
                    if (errorCode == 1)
                    {
                        throw new ApplicationException(ResStrings.ErrorMetricsAlreadyExist);
                    }
                    throw new ApplicationException(ResStrings.ErrorFailedToSaveRecord);
                }

                this.NavbarModel.CommandGoBack.Execute(null);

            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                App.Instance.UI.ShowToast($"{ResStrings.Error}: {e.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        });

        public Command CommandSelectMetricsUnit => new Command(async () =>
        {

            MainThread.BeginInvokeOnMainThread(async () =>
            {

                List<ISelectableOption> options = new()
                {
                    new SelectableAction
                    {
                        Id = OptionsUnits.EU.ToString(),
                        Title = ResStrings.Meters,
                        Action = () =>
                        {
                            Units = OptionsUnits.EU;
                            OnPropertyChanged(nameof(DisplayUnits));
                        }
                    },
                    new SelectableAction
                    {
                        Id = OptionsUnits.US.ToString(),
                        Title = ResStrings.Feet,
                        Action = () =>
                        {
                            Units = OptionsUnits.US;
                            OnPropertyChanged(nameof(DisplayUnits));
                        }
                    },
                };

                var selected = await App.Instance.UI.PresentSelection(options, ResStrings.Select) as SelectableAction;
                selected?.Action?.Invoke();
            });


        });

        public string DisplayUnits
        {
            get
            {
                if (Units == OptionsUnits.US)
                    return ResStrings.Feet;
                return ResStrings.Meters;
            }
        }

        private OptionsUnits _units;
        public OptionsUnits Units
        {
            get
            {
                return _units;
            }
            set
            {
                if (_units != value)
                {
                    _units = value;
                    OnPropertyChanged();
                }
            }
        }


        private int _Start;
        public int Start
        {
            get
            {
                return _Start;
            }
            set
            {
                if (_Start != value)
                {
                    _Start = value;
                    OnPropertyChanged();
                    UpdateValidator();
                }
            }
        }

        private int _End;
        public int End
        {
            get
            {
                return _End;
            }
            set
            {
                if (_End != value)
                {
                    _End = value;
                    OnPropertyChanged();
                    UpdateValidator();
                }
            }
        }



        #region VALIDATION

        private void ValidationInitialize()
        {

            FieldValidators["end"] = new RequiredDoubleValidator((validator, value) =>
            {
                if (value <= 0 || value > 5000)
                {
                    return ResStrings.ValidationEndValue;
                }
                //if (value <= Start)
                //{
                //    return ResStrings.ValidationDifferentValues;
                //}
                return null;
            });
        }

        void UpdateValidator()
        {
            Validate();
            OnPropertyChanged("CanSubmit");
        }

        private bool _IsBusy;
        private readonly Func<OptionItem, Task<int>> _callback;

        private readonly LocalDistance _item;

        public new bool IsBusy
        {
            get { return _IsBusy; }
            set
            {
                if (_IsBusy != value)
                {
                    _IsBusy = value;
                    OnPropertyChanged();
                    OnPropertyChanged("CanSubmit");
                }
            }
        }


        public bool CanSubmit
        {
            get
            {
                var validated = Validate();
                return validated && !IsBusy;
            }
        }

        bool Validate()
        {
            int valid = 1;
            //  valid *= Convert.ToInt32(FieldValidators["password"].Validate(Password));
            //valid *= Convert.ToInt32(FieldValidators["start"].Validate(Start));
            valid *= Convert.ToInt32(FieldValidators["end"].Validate(End));
            OnPropertyChanged("FieldValidators");
            return valid > 0;
        }

        public Dictionary<string, FieldValidator> FieldValidators { get; } = new();


        #endregion


    }
}
