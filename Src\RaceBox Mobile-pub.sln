﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.33516.290
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Racebox.Common", "Common\Racebox.Common.csproj", "{720DF65B-3531-4658-A1AC-A3D05703FD9C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Racebox.Shared", "Mobile\Shared\Racebox.Shared.csproj", "{2B095983-8E62-47BF-99AC-02DC412CC51B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Racebox.SDK", "Mobile\SDK\Racebox.SDK.csproj", "{F195E59E-85D5-484F-9AAB-44E44C161B97}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Racebox.Migrator", "Mobile\Migrator\Racebox.Migrator.csproj", "{506A9D40-9DBC-45AA-997D-6FF2C89BAFC5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Racebox", "Mobile\Client\Racebox.csproj", "{EF2F2E60-9EC1-4368-A1DE-B327D1722369}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Racebox.Tests", "Mobile\Tests\Racebox.Tests.csproj", "{F41D0AD9-6E51-456F-AA01-85C6C52A8EA7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{E032046D-81E8-4654-9AE1-92934938DF98}"
	ProjectSection(SolutionItems) = preProject
		..\README.md = ..\README.md
		..\Docs\ApiGfx\Summary.md = ..\Docs\ApiGfx\Summary.md
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Racebox.ApiClient", "Racebox.ApiClient\Racebox.ApiClient.csproj", "{762F38A2-7AF0-40EB-B994-16EB459F2642}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Dev", "Dev", "{E4C4FA8D-24F9-48E6-8740-07A201AE0CDC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TestApi", "Mobile\TestApi\TestApi.csproj", "{94591BB2-8908-0531-75B4-2540E78C372F}"
EndProject
Project("{D954291E-2A0B-460D-934E-DC6B0785DB48}") = "DrawnUi.Shared", "..\..\DrawnUi.Maui\src\Shared\DrawnUi.Shared.shproj", "{*************-48DD-BDB3-98EDECBB1107}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DrawnUi.Maui", "..\..\DrawnUi.Maui\src\Maui\DrawnUi\DrawnUi.Maui.csproj", "{B24B88E7-C166-86A5-5D2F-AC7F9038437D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DrawnUi.Maui.MapsUi", "..\..\DrawnUi.Maui\src\Maui\Addons\DrawnUi.Maui.MapsUi\DrawnUi.Maui.MapsUi.csproj", "{7B453B81-A2E2-13C8-B547-C1AA49DAC3D3}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{720DF65B-3531-4658-A1AC-A3D05703FD9C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{720DF65B-3531-4658-A1AC-A3D05703FD9C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{720DF65B-3531-4658-A1AC-A3D05703FD9C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{720DF65B-3531-4658-A1AC-A3D05703FD9C}.Release|Any CPU.Build.0 = Release|Any CPU
		{2B095983-8E62-47BF-99AC-02DC412CC51B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2B095983-8E62-47BF-99AC-02DC412CC51B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2B095983-8E62-47BF-99AC-02DC412CC51B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2B095983-8E62-47BF-99AC-02DC412CC51B}.Release|Any CPU.Build.0 = Release|Any CPU
		{F195E59E-85D5-484F-9AAB-44E44C161B97}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F195E59E-85D5-484F-9AAB-44E44C161B97}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F195E59E-85D5-484F-9AAB-44E44C161B97}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F195E59E-85D5-484F-9AAB-44E44C161B97}.Release|Any CPU.Build.0 = Release|Any CPU
		{506A9D40-9DBC-45AA-997D-6FF2C89BAFC5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{506A9D40-9DBC-45AA-997D-6FF2C89BAFC5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{506A9D40-9DBC-45AA-997D-6FF2C89BAFC5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{506A9D40-9DBC-45AA-997D-6FF2C89BAFC5}.Release|Any CPU.Build.0 = Release|Any CPU
		{EF2F2E60-9EC1-4368-A1DE-B327D1722369}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EF2F2E60-9EC1-4368-A1DE-B327D1722369}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EF2F2E60-9EC1-4368-A1DE-B327D1722369}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{EF2F2E60-9EC1-4368-A1DE-B327D1722369}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EF2F2E60-9EC1-4368-A1DE-B327D1722369}.Release|Any CPU.Build.0 = Release|Any CPU
		{EF2F2E60-9EC1-4368-A1DE-B327D1722369}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{F41D0AD9-6E51-456F-AA01-85C6C52A8EA7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F41D0AD9-6E51-456F-AA01-85C6C52A8EA7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F41D0AD9-6E51-456F-AA01-85C6C52A8EA7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F41D0AD9-6E51-456F-AA01-85C6C52A8EA7}.Release|Any CPU.Build.0 = Release|Any CPU
		{762F38A2-7AF0-40EB-B994-16EB459F2642}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{762F38A2-7AF0-40EB-B994-16EB459F2642}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{762F38A2-7AF0-40EB-B994-16EB459F2642}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{762F38A2-7AF0-40EB-B994-16EB459F2642}.Release|Any CPU.Build.0 = Release|Any CPU
		{94591BB2-8908-0531-75B4-2540E78C372F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{94591BB2-8908-0531-75B4-2540E78C372F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{94591BB2-8908-0531-75B4-2540E78C372F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{94591BB2-8908-0531-75B4-2540E78C372F}.Release|Any CPU.Build.0 = Release|Any CPU
		{B24B88E7-C166-86A5-5D2F-AC7F9038437D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B24B88E7-C166-86A5-5D2F-AC7F9038437D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B24B88E7-C166-86A5-5D2F-AC7F9038437D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B24B88E7-C166-86A5-5D2F-AC7F9038437D}.Release|Any CPU.Build.0 = Release|Any CPU
		{7B453B81-A2E2-13C8-B547-C1AA49DAC3D3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7B453B81-A2E2-13C8-B547-C1AA49DAC3D3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7B453B81-A2E2-13C8-B547-C1AA49DAC3D3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7B453B81-A2E2-13C8-B547-C1AA49DAC3D3}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{506A9D40-9DBC-45AA-997D-6FF2C89BAFC5} = {E4C4FA8D-24F9-48E6-8740-07A201AE0CDC}
		{F41D0AD9-6E51-456F-AA01-85C6C52A8EA7} = {E4C4FA8D-24F9-48E6-8740-07A201AE0CDC}
		{94591BB2-8908-0531-75B4-2540E78C372F} = {E4C4FA8D-24F9-48E6-8740-07A201AE0CDC}
		{*************-48DD-BDB3-98EDECBB1107} = {E4C4FA8D-24F9-48E6-8740-07A201AE0CDC}
		{B24B88E7-C166-86A5-5D2F-AC7F9038437D} = {E4C4FA8D-24F9-48E6-8740-07A201AE0CDC}
		{7B453B81-A2E2-13C8-B547-C1AA49DAC3D3} = {E4C4FA8D-24F9-48E6-8740-07A201AE0CDC}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {CFF0B5FF-29D3-4944-B34E-0DFC3265196E}
	EndGlobalSection
	GlobalSection(SharedMSBuildProjectFiles) = preSolution
		..\..\DrawnUi.Maui\src\Shared\Shared.projitems*{*************-48dd-bdb3-98edecbb1107}*SharedItemsImports = 13
		..\..\DrawnUi.Maui\src\Shared\Shared.projitems*{b24b88e7-c166-86a5-5d2f-ac7f9038437d}*SharedItemsImports = 5
	EndGlobalSection
EndGlobal
