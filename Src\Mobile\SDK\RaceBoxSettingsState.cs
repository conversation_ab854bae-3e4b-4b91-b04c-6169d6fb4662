﻿using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Racebox.SDK;

public class RaceBoxSettingsState : INotifyPropertyChanged
{
    public static RaceBoxSettingsState FromData(byte[] bytes)
    {
        var data = new RaceBoxSettingsState();
        bytes.DecodeTo(data);
        return data;
    }

    private int _led;
    private bool _tone;
    private DeviceSettingsTransmission _transmission;
    private int _utc;
    private int _speed1;
    private int _speed2;
    private DeviceSettingsLogFormat _logFormat;
    private DeviceLograteType _logRate;
    private DeviceSettingsLanguage _language;
    private DeviceSettingsMapsType _maps;
    private bool _rollout;
    private bool _prediction;
    private bool _randomStart;
    private DeviceSettingsMetricsType _units;
    private DeviceSettingsMinShowDistance _minDistance;
    private int _weight;
    private double _drag;
    private double _frontal;
    private bool _obdLog;
    private bool _shiftTime;
    private bool _obdPidAuto;
    private int _obdPids;
    private DeviceSettingsObdProtocolType _obdProtocol;
    private int _gnssSecondarySource;
    private bool _gnssUartOut;
    private bool _gnssColdStart;
    private bool _gnssReconfig;
    private bool _resetConfiguration;
    private bool _resetDevice;

    /// <summary>
    /// Яркость дисплея
    /// 1-4
    /// </summary>
    public int Led
    {
        get => _led;
        set
        {
            if (value == _led) return;
            _led = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Звуковые сигналы
    /// </summary>
    public bool Tone
    {
        get => _tone;
        set
        {
            if (value == _tone) return;
            _tone = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Тип трансмиссии
    /// </summary>
    public DeviceSettingsTransmission Transmission
    {
        get => _transmission;
        set
        {
            if (value == _transmission) return;
            _transmission = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Часовой пояс
    /// от -12 до 12
    /// </summary>
    public int Utc
    {
        get => _utc;
        set
        {
            if (value == _utc) return;
            _utc = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// 0-300
    /// </summary>
    public int Speed1
    {
        get => _speed1;
        set
        {
            if (value == _speed1) return;
            _speed1 = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// 0-300
    /// </summary>
    public int Speed2
    {
        get => _speed2;
        set
        {
            if (value == _speed2) return;
            _speed2 = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Формат логов
    /// </summary>
    public DeviceSettingsLogFormat LogFormat
    {
        get => _logFormat;
        set
        {
            if (value == _logFormat) return;
            _logFormat = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// МАКС, 1 раз в сек, 1 раз в 10 сек, 1 раз в мин
    /// </summary>
    public DeviceLograteType LogRate
    {
        get => _logRate;
        set
        {
            if (value == _logRate) return;
            _logRate = value;
            OnPropertyChanged();
        }
    }

    public DeviceSettingsLanguage Language
    {
        get => _language;
        set
        {
            if (value == _language) return;
            _language = value;
            OnPropertyChanged();
        }
    }

    public DeviceSettingsMapsType Maps
    {
        get => _maps;
        set
        {
            if (value == _maps) return;
            _maps = value;
            OnPropertyChanged();
        }
    }

    public bool Rollout
    {
        get => _rollout;
        set
        {
            if (value == _rollout) return;
            _rollout = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Предиктивный режим
    /// </summary>
    public bool Prediction
    {
        get => _prediction;
        set
        {
            if (value == _prediction) return;
            _prediction = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Произвольное время старта
    /// </summary>
    public bool RandomStart
    {
        get => _randomStart;
        set
        {
            if (value == _randomStart) return;
            _randomStart = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Единицы измерения
    /// </summary>
    public DeviceSettingsMetricsType Units
    {
        get => _units;
        set
        {
            if (value == _units) return;
            _units = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Минимальная дистанция отображения
    /// </summary>
    public DeviceSettingsMinShowDistance MinDistance
    {
        get => _minDistance;
        set
        {
            if (value == _minDistance) return;
            _minDistance = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Масса авто
    /// 100-5000 кг
    /// </summary>
    public int Weight
    {
        get => _weight;
        set
        {
            if (value == _weight) return;
            _weight = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Коэффициент лобового сопротивления
    /// 0,15 - 0,6
    /// </summary>
    public double Drag
    {
        get => _drag;
        set
        {
            if (value.Equals(_drag)) return;
            _drag = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Площадь поперечного сечения
    /// 1,0 - 5,0
    /// </summary>
    public double Frontal
    {
        get => _frontal;
        set
        {
            if (value.Equals(_frontal)) return;
            _frontal = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Запись лога OBD
    /// </summary>
    public bool ObdLog
    {
        get => _obdLog;
        set
        {
            if (value == _obdLog) return;
            _obdLog = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Замер времени переключения
    /// </summary>
    public bool ShiftTime
    {
        get => _shiftTime;
        set
        {
            if (value == _shiftTime) return;
            _shiftTime = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Автоопределение PID OBD
    /// </summary>
    public bool ObdPidAuto
    {
        get => _obdPidAuto;
        set
        {
            if (value == _obdPidAuto) return;
            _obdPidAuto = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Выбор читаемых PID OBD
    /// 1 - RPM only, 63 - All
    /// </summary>
    public int ObdPids
    {
        get => _obdPids;
        set
        {
            if (value == _obdPids) return;
            _obdPids = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Протокол OBD
    /// </summary>
    public DeviceSettingsObdProtocolType ObdProtocol
    {
        get => _obdProtocol;
        set
        {
            if (value == _obdProtocol) return;
            _obdProtocol = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// 2й источник GNSS
    /// </summary>
    public int GnssSecondarySource
    {
        get => _gnssSecondarySource;
        set
        {
            if (value == _gnssSecondarySource) return;
            _gnssSecondarySource = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Вывод данных GNSS по USB
    /// </summary>
    public bool GnssUartOut
    {
        get => _gnssUartOut;
        set
        {
            if (value == _gnssUartOut) return;
            _gnssUartOut = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Холодный перезапуск GNSS
    /// </summary>
    public bool GnssColdStart
    {
        get => _gnssColdStart;
        set
        {
            if (value == _gnssColdStart) return;
            _gnssColdStart = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Переконфигурация GNSS
    /// </summary>
    public bool GnssReconfig
    {
        get => _gnssReconfig;
        set
        {
            if (value == _gnssReconfig) return;
            _gnssReconfig = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Сброс конфигурации
    /// </summary>
    public bool ResetConfiguration
    {
        get => _resetConfiguration;
        set
        {
            if (value == _resetConfiguration) return;
            _resetConfiguration = value;
            OnPropertyChanged();
        }
    }

    /// <summary>
    /// Перезапуск прибора
    /// </summary>
    public bool ResetDevice
    {
        get => _resetDevice;
        set
        {
            if (value == _resetDevice) return;
            _resetDevice = value;
            OnPropertyChanged();
        }
    }

    #region INotifyPropertyChanged

    public event PropertyChangedEventHandler PropertyChanged;
    protected void OnPropertyChanged([CallerMemberName] string propertyName = "")
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    #endregion
}