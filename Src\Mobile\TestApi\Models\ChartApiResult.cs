namespace TestApi.Models;

public class ChartApiResult
{
    public bool IsSuccess { get; private set; }
    public string? ErrorMessage { get; private set; }
    public string? FilePath { get; private set; }
    public string? Filename { get; private set; }
    public long FileSize { get; private set; }
    public TimeSpan? ResponseTime { get; private set; }

    private ChartApiResult() { }

    public static ChartApiResult Success(string filePath, string filename, long fileSize, TimeSpan responseTime)
    {
        return new ChartApiResult
        {
            IsSuccess = true,
            FilePath = filePath,
            Filename = filename,
            FileSize = fileSize,
            ResponseTime = responseTime
        };
    }

    public static ChartApiResult Error(string errorMessage, TimeSpan? responseTime = null)
    {
        return new ChartApiResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            ResponseTime = responseTime
        };
    }
}