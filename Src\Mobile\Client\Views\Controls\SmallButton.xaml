<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaButton
    x:Class="Racebox.Views.Drawn.SmallButton"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    ApplyEffect="Ripple"
    HeightRequest="36"
    TouchEffectColor="White"
    UseCache="Image"
    HorizontalOptions="Center"
    x:Name="ThisControl"
    WidthRequest="120">
    
    <draw:SkiaControl.Triggers>
        <DataTrigger
            Binding="{Binding Source={x:Reference ThisControl}, Path=IsEnabled}"
            TargetType="draw:SkiaControl"
            Value="False">
            <Setter Property="InputTransparent" Value="True" />
            <Setter Property="Opacity" Value="0.5" />
        </DataTrigger>
        <DataTrigger
            Binding="{Binding Source={x:Reference ThisControl}, Path=IsEnabled}"
            TargetType="draw:SkiaControl"
            Value="True">
            <Setter Property="InputTransparent" Value="False" />
            <Setter Property="Opacity" Value="1.0" />
        </DataTrigger>
    </draw:SkiaControl.Triggers>

    <draw:SkiaShape
        BackgroundColor="#00000000"
        CornerRadius="24"
        HorizontalOptions="Fill"
        StrokeColor="#CB6336"
        StrokeWidth="1.85"
        Tag="CustomFrame"
        VerticalOptions="Fill" />

    <!--  we could put text inside shape btw..  -->
    <draw:SkiaLabel
        Margin="8,0"
        FontSize="16"
        FontFamily="FontText"
        HorizontalOptions="Center"
        Tag="CustomLabel"
        TextColor="#CB6336"
        VerticalOptions="Center" />

</draw:SkiaButton>
