﻿using Android.Media;
using Racebox.Shared.Services;

namespace Racebox.Helpers
{
    public partial class Files
    {
        public static void RefreshSystem(FileDescriptor file)
        {
            var context = Platform.AppContext;




            MediaScannerConnection.ScanFile(context,
            new String[] { file.FullFilename },
            null, null);

            //deprecated
            //            Platform.AppContext.SendBroadcast(new Intent(Intent.ActionMediaScannerScanFile, Android.Net.Uri.Parse(file.FullFilename)));

            //ContentResolver resolver = context.ContentResolver;
            //ContentValues contentValues = new ContentValues();
            //contentValues.Put(MediaStore.MediaColumns.DisplayName, file.Filename);
            //contentValues.Put(MediaStore.MediaColumns.MimeType, "text/plain");
            //contentValues.Put(MediaStore.MediaColumns.RelativePath, file.Path);

            //var uri = resolver.Insert(MediaStore.Images.Media.ExternalContentUri, contentValues);

        }

        //public static string GetPublicDirectory()
        //{

        //    return Android.OS.Environment
        //        .GetExternalStoragePublicDirectory(Android.OS.Environment.DirectoryDownloads)
        //        .AbsolutePath;
        //}






    }

}
