﻿using Newtonsoft.Json;
using System.Globalization;

namespace Racebox.ApiClient.Dto;

public class Location
{
    /// <summary>
    /// City name
    /// </summary>
    [JsonProperty("name")]
    public string Name { get; set; }

    /// <summary>
    /// Region name
    /// </summary>
    [JsonProperty("region")]
    public string Region { get; set; }

    /// <summary>
    /// Country name
    /// </summary>
    [JsonProperty("country")]
    public string Country { get; set; }

    /// <summary>
    /// Latitude
    /// </summary>
    [JsonProperty("lat")]
    public decimal Lat { get; set; }

    /// <summary>
    /// Longitude
    /// </summary>
    [JsonProperty("lon")]
    public decimal Lon { get; set; }

    /// <summary>
    /// Time zone ID
    /// </summary>
    [JsonProperty("tz_id")]
    public string TzId { get; set; }

    /// <summary>
    /// Local time epoch
    /// </summary>
    [JsonProperty("localtime_epoch")]
    public long LocaltimeEpoch { get; set; }

    /// <summary>
    /// Local time
    /// </summary>
    [JsonProperty("localtime")]
    public string Localtime { get; set; }

    [JsonIgnore]
    public DateTime LocalDateTime
    {
        get
        {
            return DateTimeOffset.FromUnixTimeSeconds(LocaltimeEpoch).DateTime;
        }
    }
}