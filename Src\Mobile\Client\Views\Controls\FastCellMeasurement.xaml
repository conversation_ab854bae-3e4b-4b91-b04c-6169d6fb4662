﻿<?xml version="1.0" encoding="utf-8" ?>
<partials:SkiaListCell
    x:Class="Racebox.Views.Partials.FastCellMeasurement"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:interfaces="clr-namespace:Racebox.Shared.Interfaces;assembly=Racebox.Shared"
    xmlns:partials="clr-namespace:Racebox.Views.Partials"
    x:DataType="interfaces:IHasDetailedDisplay"
    HeightRequest="41"
    HorizontalOptions="Fill"
    VerticalOptions="Start">

    <draw:SkiaLayout
        Padding="0,0,0,0"
        HorizontalOptions="Fill"
        VerticalOptions="Fill">

        <!--  Text="{Binding DisplayInfo}"  -->
        <draw:SkiaLabel
            x:Name="LabelDisplay1"
            Padding="4,0,0,1"
            FontFamily="FontText"
            FontSize="16.0"
            HorizontalOptions="Start"
            MaxLines="1"
            TextColor="#E8E3D7"
            TranslationY="0"
            VerticalOptions="Center" />

        <draw:SkiaLabel
            x:Name="LabelDisplay2"
            Padding="0,0,4,1"
            FontFamily="FontText"
            FontSize="16.0"
            HorizontalOptions="End"
            MaxLines="1"
            TextColor="#E8E3D7"
            TranslationY="0"
            VerticalOptions="Center" />

        <!--  LINE HORIZONTAL  -->
        <draw:SkiaShape
            BackgroundColor="Black"
            CornerRadius="0"
            HeightRequest="1"
            HorizontalOptions="Fill"
            StrokeWidth="0"
            VerticalOptions="End">
            <draw:SkiaShape.FillGradient>

                <draw:SkiaGradient
                    EndXRatio="1"
                    EndYRatio="0"
                    StartXRatio="0"
                    StartYRatio="0"
                    Type="Linear">
                    <draw:SkiaGradient.Colors>
                        <Color>#00E8E3D7</Color>
                        <Color>#99E8E3D7</Color>
                        <Color>#00E8E3D7</Color>
                    </draw:SkiaGradient.Colors>
                </draw:SkiaGradient>

            </draw:SkiaShape.FillGradient>
        </draw:SkiaShape>

    </draw:SkiaLayout>

</partials:SkiaListCell>
