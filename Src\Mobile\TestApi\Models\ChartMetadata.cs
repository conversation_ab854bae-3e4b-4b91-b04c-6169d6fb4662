namespace TestApi.Models;

public class ChartMetadata
{
    public string Name { get; set; } = string.Empty;
    public long Size { get; set; }
    public string Type { get; set; } = "accel";
    public string DataSource { get; set; } = "app";
    public MetaInfo Meta { get; set; } = new();
    public Dictionary<string, string> Spd { get; set; } = new();
    public Dictionary<string, string[]> Dst { get; set; } = new();
    public Dictionary<string, string> Rng { get; set; } = new();
    public object Shift { get; set; } = new { };
}

public class MetaInfo
{
    public string Lang { get; set; } = "en";
    public string User { get; set; } = "Mobile App User";
    public string Vehicle { get; set; } = "Test Vehicle";
    public string Descr { get; set; } = "Generated from mobile app";
    public int Share { get; set; } = 0;
    public string Date { get; set; } = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
}

public class PerformanceMetrics
{
    public Dictionary<string, string> SpeedMetrics { get; set; } = new();
    public Dictionary<string, string[]> DistanceMetrics { get; set; } = new();
    public Dictionary<string, string> RangeMetrics { get; set; } = new();
}