﻿using Mapster;
using Microsoft.EntityFrameworkCore;
using Racebox.Shared.Enums;
using Racebox.Shared.Extensions;
using Racebox.Shared.Models;
using Racebox.Shared.Strings;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Racebox.Tests.Services
{
	public class UserManager : RaceBoxSettings
	{
		public UserManager(IServiceProvider services)
		{
			_services = services;

			User = DefaultUser;
			Options = DefaultOptions;
		}

		private readonly IServiceProvider _services;

		public LocalDatabase GetDatabase()
		{
			return _services.GetService<LocalDatabase>();
		}

		public async Task InsureUserIsInDatabase()
		{
			var db = GetDatabase();
			while (db.Database.GetPendingMigrations().Any())
			{
				await Task.Delay(200);
			}

			if (User.Id == 0)
			{
				await db.AddUser(User);
				await db.SaveChangesAsync();
			}

		}



		public async Task<bool> DeleteUser(int id)
		{
			var db = GetDatabase();
			var user = await db.GetUser(id);
			if (user != null)
			{
				db.Users.Remove(user);
				await db.SaveChangesAsync();
				return true;
			}
			return false;
		}

		public async Task UpdateUser(LocalDatabase db)
		{
			db.Users.Update(User);
			await db.SaveChangesAsync();
		}

		public async Task AddUser(AppUser user)
		{
			var db = GetDatabase();
			if (user.Cars.Count == 0)
			{
				user.Cars.Add(DefaultCar);
			}
			db.Users.Add(user);
			await db.SaveChangesAsync();
		}



		public Car DefaultCar
		{
			get
			{
				return new Car()
				{
					Brand = ResStrings.DefaultBrand,
					Model = ResStrings.DefaultModel
				};
			}
		}

		public UserOptionsMeta DefaultOptions
		{
			get
			{
				return new()
				{
					Rollout = false,
					Units = OptionsUnits.EU,
					Sound = true,
					Time24 = true,
					SpeedRanges = new()
					{
						new(100, 200, OptionsUnits.EU),
					},
					Distances = new()
					{
						new(0, 100, OptionsUnits.EU),
					},
				};
			}
		}

		public AppUser DefaultUser
		{
			get
			{
				var user = new AppUser()
				{
					Name = ResStrings.User,
					Cars = new()
					{
						DefaultCar
					}
				};
				return user;
			}
		}


		public UserOptionsMeta Options { get; set; }

		public void SetUser(AppUser user)
		{


			User = user;


		}

		public AppUser User { get; set; }






	}
}
