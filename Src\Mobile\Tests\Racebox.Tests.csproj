﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<IsPackable>false</IsPackable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
		<PackageReference Include="FluentAssertions" Version="6.12.0" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.9.0" />
		<PackageReference Include="Moq" Version="4.20.70" />
		<PackageReference Include="NUnit" Version="4.1.0" />
		<PackageReference Include="NUnit3TestAdapter" Version="4.5.0" />
		<PackageReference Include="Respawn" Version="6.2.1" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\SDK\Racebox.SDK.csproj" />
	  <ProjectReference Include="..\Shared\Racebox.Shared.csproj" />
	</ItemGroup>

</Project>