﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Racebox.Shared.Strings {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ResStrings {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResStrings() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Racebox.Shared.Strings.ResStrings", typeof(ResStrings).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to About.
        /// </summary>
        public static string AboutApp {
            get {
                return ResourceManager.GetString("AboutApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Racebox is a companion app for the Racebox Pro and Pro+ devices created for vehicle performance measurements. 
        ///The app is intended for use on race tracks only. The author is not responsible for any injuries or property damage that may occur during use of this software..
        /// </summary>
        public static string AboutAppText {
            get {
                return ResourceManager.GetString("AboutAppText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Acceleration.
        /// </summary>
        public static string Acceleration {
            get {
                return ResourceManager.GetString("Acceleration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lateral Accel..
        /// </summary>
        public static string AccelerationSide {
            get {
                return ResourceManager.GetString("AccelerationSide", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No permissions to use Bluetooth..
        /// </summary>
        public static string AlertBluetoothPermissionsOff {
            get {
                return ResourceManager.GetString("AlertBluetoothPermissionsOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your device does not support Bluetooth..
        /// </summary>
        public static string AlertBluetoothUnsupported {
            get {
                return ResourceManager.GetString("AlertBluetoothUnsupported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bluetooth will not be available..
        /// </summary>
        public static string AlertBluetoothWillNotBeAvailable {
            get {
                return ResourceManager.GetString("AlertBluetoothWillNotBeAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enable GPS (Android system requirement to use Bluetooth)..
        /// </summary>
        public static string AlertNeedGpsOnForBluetooth {
            get {
                return ResourceManager.GetString("AlertNeedGpsOnForBluetooth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please grant location permissions (Android system requirement to use Bluetooth)..
        /// </summary>
        public static string AlertNeedGpsPermissionsForBluetooth {
            get {
                return ResourceManager.GetString("AlertNeedGpsPermissionsForBluetooth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to We would need you to provide permissions to use your GPS location (Android system requirement to use Bluetooth)..
        /// </summary>
        public static string AlertNeedLocationForBluetooth {
            get {
                return ResourceManager.GetString("AlertNeedLocationForBluetooth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please turn on Bluetooth..
        /// </summary>
        public static string AlertTurnOnBluetooth {
            get {
                return ResourceManager.GetString("AlertTurnOnBluetooth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All.
        /// </summary>
        public static string All {
            get {
                return ResourceManager.GetString("All", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Cars.
        /// </summary>
        public static string AllCars {
            get {
                return ResourceManager.GetString("AllCars", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Altitude.
        /// </summary>
        public static string Altitude {
            get {
                return ResourceManager.GetString("Altitude", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are You sure?.
        /// </summary>
        public static string AreYouSure {
            get {
                return ResourceManager.GetString("AreYouSure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Low quality GPS reception.
        /// </summary>
        public static string BadMeasureGps {
            get {
                return ResourceManager.GetString("BadMeasureGps", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Result is not valid: incline was too important!.
        /// </summary>
        public static string BadMeasureIncline {
            get {
                return ResourceManager.GetString("BadMeasureIncline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Instant Run and the speed is below the initial speed of the first pair and the measurement time is more than 1 second.
        /// </summary>
        public static string BadMeasureInstant {
            get {
                return ResourceManager.GetString("BadMeasureInstant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Acceleration is down.
        /// </summary>
        public static string BadMeasureLowAccel {
            get {
                return ResourceManager.GetString("BadMeasureLowAccel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Speed below READY_SPEED_THRESHOLD_UNIT.
        /// </summary>
        public static string BadMeasureLowSpeed {
            get {
                return ResourceManager.GetString("BadMeasureLowSpeed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No distances measured.
        /// </summary>
        public static string BadMeasureNoDistances {
            get {
                return ResourceManager.GetString("BadMeasureNoDistances", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No speed ranges measured.
        /// </summary>
        public static string BadMeasureNoRanges {
            get {
                return ResourceManager.GetString("BadMeasureNoRanges", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid measurement (big incline).
        /// </summary>
        public static string BadMeasureNotValid {
            get {
                return ResourceManager.GetString("BadMeasureNotValid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Measurement time is above ACC_TIMEOUT.
        /// </summary>
        public static string BadMeasureTooLong {
            get {
                return ResourceManager.GetString("BadMeasureTooLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Brand.
        /// </summary>
        public static string Brand {
            get {
                return ResourceManager.GetString("Brand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply.
        /// </summary>
        public static string BtnApply {
            get {
                return ResourceManager.GetString("BtnApply", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        public static string BtnCancel {
            get {
                return ResourceManager.GetString("BtnCancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        public static string BtnClose {
            get {
                return ResourceManager.GetString("BtnClose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connect.
        /// </summary>
        public static string BtnConnect {
            get {
                return ResourceManager.GetString("BtnConnect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create.
        /// </summary>
        public static string BtnCreate {
            get {
                return ResourceManager.GetString("BtnCreate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disconnect.
        /// </summary>
        public static string BtnDisconnect {
            get {
                return ResourceManager.GetString("BtnDisconnect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ask A Question.
        /// </summary>
        public static string BtnQuestion {
            get {
                return ResourceManager.GetString("BtnQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset.
        /// </summary>
        public static string BtnReset {
            get {
                return ResourceManager.GetString("BtnReset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string BtnSave {
            get {
                return ResourceManager.GetString("BtnSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search.
        /// </summary>
        public static string BtnSearch {
            get {
                return ResourceManager.GetString("BtnSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start.
        /// </summary>
        public static string BtnStart {
            get {
                return ResourceManager.GetString("BtnStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stop.
        /// </summary>
        public static string BtnStop {
            get {
                return ResourceManager.GetString("BtnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot delete last record.
        /// </summary>
        public static string CannotDeleteSingle {
            get {
                return ResourceManager.GetString("CannotDeleteSingle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This is being actually used.
        /// </summary>
        public static string CannotDeleteUsed {
            get {
                return ResourceManager.GetString("CannotDeleteUsed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vehicle.
        /// </summary>
        public static string Car {
            get {
                return ResourceManager.GetString("Car", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vehicles.
        /// </summary>
        public static string Cars {
            get {
                return ResourceManager.GetString("Cars", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Racebox devices found nearby..
        /// </summary>
        public static string CompatibleDevicesNotFound {
            get {
                return ResourceManager.GetString("CompatibleDevicesNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to © 2025 Racebox Team.
        /// </summary>
        public static string Copyright {
            get {
                return ResourceManager.GetString("Copyright", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Measuring Metrics.
        /// </summary>
        public static string CustomMetrics {
            get {
                return ResourceManager.GetString("CustomMetrics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dashboard.
        /// </summary>
        public static string Dashboard {
            get {
                return ResourceManager.GetString("Dashboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your.
        /// </summary>
        public static string DefaultBrand {
            get {
                return ResourceManager.GetString("DefaultBrand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Car.
        /// </summary>
        public static string DefaultModel {
            get {
                return ResourceManager.GetString("DefaultModel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string Deleting {
            get {
                return ResourceManager.GetString("Deleting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Demo Mode.
        /// </summary>
        public static string DemoMode {
            get {
                return ResourceManager.GetString("DemoMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Click on Speed Gauge to start simulation.
        /// </summary>
        public static string DemoStartHint {
            get {
                return ResourceManager.GetString("DemoStartHint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 
        ///In this mode, the application will stop connecting to a real Bluetooth module and will play pre-recorded motion tracks instead. Are you sure you want to enable the demo mode?.
        /// </summary>
        public static string DemoWarning {
            get {
                return ResourceManager.GetString("DemoWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description.
        /// </summary>
        public static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Device.
        /// </summary>
        public static string Device {
            get {
                return ResourceManager.GetString("Device", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vehicle weight.
        /// </summary>
        public static string DeviceCarWeight {
            get {
                return ResourceManager.GetString("DeviceCarWeight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Coef. drag.
        /// </summary>
        public static string DeviceDragRatio {
            get {
                return ResourceManager.GetString("DeviceDragRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cross-sectional area.
        /// </summary>
        public static string DeviceFrontal {
            get {
                return ResourceManager.GetString("DeviceFrontal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GNSS cold restart.
        /// </summary>
        public static string DeviceGnssColdStart {
            get {
                return ResourceManager.GetString("DeviceGnssColdStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GNSS reconfiguration.
        /// </summary>
        public static string DeviceGnssReconfig {
            get {
                return ResourceManager.GetString("DeviceGnssReconfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2nd GNSS source.
        /// </summary>
        public static string DeviceGnssSecondarySource {
            get {
                return ResourceManager.GetString("DeviceGnssSecondarySource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GNSS data output via USB.
        /// </summary>
        public static string DeviceGnssUartOut {
            get {
                return ResourceManager.GetString("DeviceGnssUartOut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Display brightness.
        /// </summary>
        public static string DeviceLedBrightness {
            get {
                return ResourceManager.GetString("DeviceLedBrightness", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Logs Format.
        /// </summary>
        public static string DeviceLogFormat {
            get {
                return ResourceManager.GetString("DeviceLogFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log frequency.
        /// </summary>
        public static string DeviceLogRate {
            get {
                return ResourceManager.GetString("DeviceLogRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maps.
        /// </summary>
        public static string DeviceMaps {
            get {
                return ResourceManager.GetString("DeviceMaps", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Min. display distance.
        /// </summary>
        public static string DeviceMinDistance {
            get {
                return ResourceManager.GetString("DeviceMinDistance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OBD Log Recording.
        /// </summary>
        public static string DeviceObdLog {
            get {
                return ResourceManager.GetString("DeviceObdLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto PID OBD detection.
        /// </summary>
        public static string DeviceObdPidAuto {
            get {
                return ResourceManager.GetString("DeviceObdPidAuto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selecting OBD PIDs to Read.
        /// </summary>
        public static string DeviceObdPids {
            get {
                return ResourceManager.GetString("DeviceObdPids", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OBD protocol.
        /// </summary>
        public static string DeviceObdProtocol {
            get {
                return ResourceManager.GetString("DeviceObdProtocol", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Predictive mode.
        /// </summary>
        public static string DevicePrediction {
            get {
                return ResourceManager.GetString("DevicePrediction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arbitrary start time.
        /// </summary>
        public static string DeviceRandomStart {
            get {
                return ResourceManager.GetString("DeviceRandomStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset configuration.
        /// </summary>
        public static string DeviceResetConfiguration {
            get {
                return ResourceManager.GetString("DeviceResetConfiguration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Device Settings.
        /// </summary>
        public static string DeviceSettings {
            get {
                return ResourceManager.GetString("DeviceSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switching time measurement.
        /// </summary>
        public static string DeviceShiftTime {
            get {
                return ResourceManager.GetString("DeviceShiftTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sound signals.
        /// </summary>
        public static string DeviceTone {
            get {
                return ResourceManager.GetString("DeviceTone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transmission type.
        /// </summary>
        public static string DeviceTransmissionType {
            get {
                return ResourceManager.GetString("DeviceTransmissionType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Timezone.
        /// </summary>
        public static string DeviceUtc {
            get {
                return ResourceManager.GetString("DeviceUtc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Distance.
        /// </summary>
        public static string Distance {
            get {
                return ResourceManager.GetString("Distance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit.
        /// </summary>
        public static string Edit {
            get {
                return ResourceManager.GetString("Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit List.
        /// </summary>
        public static string EditList {
            get {
                return ResourceManager.GetString("EditList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End.
        /// </summary>
        public static string End {
            get {
                return ResourceManager.GetString("End", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        public static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to save record.
        /// </summary>
        public static string ErrorFailedToSaveRecord {
            get {
                return ResourceManager.GetString("ErrorFailedToSaveRecord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Such metrics already exist.
        /// </summary>
        public static string ErrorMetricsAlreadyExist {
            get {
                return ResourceManager.GetString("ErrorMetricsAlreadyExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Europe.
        /// </summary>
        public static string Europe {
            get {
                return ResourceManager.GetString("Europe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Logs Format.
        /// </summary>
        public static string ExportFormat {
            get {
                return ResourceManager.GetString("ExportFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Feet.
        /// </summary>
        public static string Feet {
            get {
                return ResourceManager.GetString("Feet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  ft.
        /// </summary>
        public static string FeetShort {
            get {
                return ResourceManager.GetString("FeetShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filter.
        /// </summary>
        public static string Filter {
            get {
                return ResourceManager.GetString("Filter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filtered.
        /// </summary>
        public static string Filtered {
            get {
                return ResourceManager.GetString("Filtered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update Firmware.
        /// </summary>
        public static string FirmwareHowToTitle {
            get {
                return ResourceManager.GetString("FirmwareHowToTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To update the device software, follow these steps:
        /// 1. Download the new version of the software from the website [racebox.ru](https://racebox.ru/obnovlenie-po).
        /// 2. Copy the firmware file in `HEX` format corresponding to your device model to the root directory of the memory card.
        /// 3. Insert the memory card into the device when it is turned off.
        /// 4. While holding the `Mode` button, connect the device to power via the USB cable.
        /// 5. Wait until the memory card is initialized and the message `HOLD TO UPDAT [rest of string was truncated]&quot;;.
        /// </summary>
        public static string FirmwareWarningHelp {
            get {
                return ResourceManager.GetString("FirmwareWarningHelp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unsupported Firmware.
        /// </summary>
        public static string FirmwareWarningTitle {
            get {
                return ResourceManager.GetString("FirmwareWarningTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Frequency.
        /// </summary>
        public static string Frequency {
            get {
                return ResourceManager.GetString("Frequency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Heading.
        /// </summary>
        public static string Heading {
            get {
                return ResourceManager.GetString("Heading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Click the Bluetooth icon at the top of the screen to connect to the device.
        /// </summary>
        public static string HintConnectNow {
            get {
                return ResourceManager.GetString("HintConnectNow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GO!!!.
        /// </summary>
        public static string HintGo {
            get {
                return ResourceManager.GetString("HintGo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Try to change location of your device for better GPS reception.
        /// </summary>
        public static string HintGpsLow {
            get {
                return ResourceManager.GetString("HintGpsLow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Measurement failed.
        /// </summary>
        public static string HintMeasureFailed {
            get {
                return ResourceManager.GetString("HintMeasureFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Waiting for measurement to start....
        /// </summary>
        public static string HintMonitoring {
            get {
                return ResourceManager.GetString("HintMonitoring", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Press the Start button to start measuring.
        /// </summary>
        public static string HintStartMeasuring {
            get {
                return ResourceManager.GetString("HintStartMeasuring", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to History.
        /// </summary>
        public static string History {
            get {
                return ResourceManager.GetString("History", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  h.
        /// </summary>
        public static string HoursShort {
            get {
                return ResourceManager.GetString("HoursShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hz.
        /// </summary>
        public static string Hz {
            get {
                return ResourceManager.GetString("Hz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incline.
        /// </summary>
        public static string Incline {
            get {
                return ResourceManager.GetString("Incline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max Incline.
        /// </summary>
        public static string InclineMax {
            get {
                return ResourceManager.GetString("InclineMax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inputs.
        /// </summary>
        public static string Inputs {
            get {
                return ResourceManager.GetString("Inputs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Valid.
        /// </summary>
        public static string IsValid {
            get {
                return ResourceManager.GetString("IsValid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        public static string IsValidNo {
            get {
                return ResourceManager.GetString("IsValidNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        public static string IsValidYes {
            get {
                return ResourceManager.GetString("IsValidYes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to km/h.
        /// </summary>
        public static string Kmh {
            get {
                return ResourceManager.GetString("Kmh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  km/h.
        /// </summary>
        public static string KmhAdd {
            get {
                return ResourceManager.GetString("KmhAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Language.
        /// </summary>
        public static string Language {
            get {
                return ResourceManager.GetString("Language", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Latitude.
        /// </summary>
        public static string Latitude {
            get {
                return ResourceManager.GetString("Latitude", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Distance.
        /// </summary>
        public static string Length {
            get {
                return ResourceManager.GetString("Length", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Location.
        /// </summary>
        public static string Location {
            get {
                return ResourceManager.GetString("Location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Longitude.
        /// </summary>
        public static string Longitude {
            get {
                return ResourceManager.GetString("Longitude", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max Accel..
        /// </summary>
        public static string MaxAcceleration {
            get {
                return ResourceManager.GetString("MaxAcceleration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max Speed.
        /// </summary>
        public static string MaxSpeed {
            get {
                return ResourceManager.GetString("MaxSpeed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Measurement.
        /// </summary>
        public static string Measure {
            get {
                return ResourceManager.GetString("Measure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Instant Run.
        /// </summary>
        public static string MeasureInstant {
            get {
                return ResourceManager.GetString("MeasureInstant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Racing!.
        /// </summary>
        public static string MeasuringStateActive {
            get {
                return ResourceManager.GetString("MeasuringStateActive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Device is off.
        /// </summary>
        public static string MeasuringStateDisabled {
            get {
                return ResourceManager.GetString("MeasuringStateDisabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Measurement failed.
        /// </summary>
        public static string MeasuringStateError {
            get {
                return ResourceManager.GetString("MeasuringStateError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Measurement completed.
        /// </summary>
        public static string MeasuringStateFinished {
            get {
                return ResourceManager.GetString("MeasuringStateFinished", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Device is on.
        /// </summary>
        public static string MeasuringStateMonitoring {
            get {
                return ResourceManager.GetString("MeasuringStateMonitoring", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start!.
        /// </summary>
        public static string MeasuringStateReady {
            get {
                return ResourceManager.GetString("MeasuringStateReady", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Measuring System.
        /// </summary>
        public static string MeasuringSystem {
            get {
                return ResourceManager.GetString("MeasuringSystem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Units.
        /// </summary>
        public static string MeasuringUnits {
            get {
                return ResourceManager.GetString("MeasuringUnits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Meters.
        /// </summary>
        public static string Meters {
            get {
                return ResourceManager.GetString("Meters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  m.
        /// </summary>
        public static string MetersShort {
            get {
                return ResourceManager.GetString("MetersShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  min.
        /// </summary>
        public static string MinutesShort {
            get {
                return ResourceManager.GetString("MinutesShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Model.
        /// </summary>
        public static string Model {
            get {
                return ResourceManager.GetString("Model", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to mph.
        /// </summary>
        public static string Mph {
            get {
                return ResourceManager.GetString("Mph", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  mph.
        /// </summary>
        public static string MphAdd {
            get {
                return ResourceManager.GetString("MphAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string Name {
            get {
                return ResourceManager.GetString("Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        public static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Off.
        /// </summary>
        public static string Off {
            get {
                return ResourceManager.GetString("Off", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On.
        /// </summary>
        public static string On {
            get {
                return ResourceManager.GetString("On", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Options.
        /// </summary>
        public static string Options {
            get {
                return ResourceManager.GetString("Options", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Order.
        /// </summary>
        public static string Order {
            get {
                return ResourceManager.GetString("Order", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Newer first.
        /// </summary>
        public static string OrderNewFirst {
            get {
                return ResourceManager.GetString("OrderNewFirst", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Older first.
        /// </summary>
        public static string OrderOldFirst {
            get {
                return ResourceManager.GetString("OrderOldFirst", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Press BACK once again to quit the app.
        /// </summary>
        public static string PressBACKOnceAgain {
            get {
                return ResourceManager.GetString("PressBACKOnceAgain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Run.
        /// </summary>
        public static string Racing {
            get {
                return ResourceManager.GetString("Racing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Report.
        /// </summary>
        public static string Report {
            get {
                return ResourceManager.GetString("Report", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Restarting the device.
        /// </summary>
        public static string ResetDevice {
            get {
                return ResourceManager.GetString("ResetDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Roll Out (1 ft).
        /// </summary>
        public static string RollOut {
            get {
                return ResourceManager.GetString("RollOut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Satellites.
        /// </summary>
        public static string Satellites {
            get {
                return ResourceManager.GetString("Satellites", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  s.
        /// </summary>
        public static string SecondsShort {
            get {
                return ResourceManager.GetString("SecondsShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select.
        /// </summary>
        public static string Select {
            get {
                return ResourceManager.GetString("Select", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Settings.
        /// </summary>
        public static string Settings {
            get {
                return ResourceManager.GetString("Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Say Speed.
        /// </summary>
        public static string SettingsCanSpeak {
            get {
                return ResourceManager.GetString("SettingsCanSpeak", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Signal Strength.
        /// </summary>
        public static string SignalStrength {
            get {
                return ResourceManager.GetString("SignalStrength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sound.
        /// </summary>
        public static string Sound {
            get {
                return ResourceManager.GetString("Sound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Speed Range.
        /// </summary>
        public static string SpeedRange {
            get {
                return ResourceManager.GetString("SpeedRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start.
        /// </summary>
        public static string Start {
            get {
                return ResourceManager.GetString("Start", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bluetooth is off.
        /// </summary>
        public static string StatusBluetoothOff {
            get {
                return ResourceManager.GetString("StatusBluetoothOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connecting....
        /// </summary>
        public static string StatusConnecting {
            get {
                return ResourceManager.GetString("StatusConnecting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Low GPS signal.
        /// </summary>
        public static string StatusGpsLow {
            get {
                return ResourceManager.GetString("StatusGpsLow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string StatusPleaseWait {
            get {
                return ResourceManager.GetString("StatusPleaseWait", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to System Settings.
        /// </summary>
        public static string SystemSettings {
            get {
                return ResourceManager.GetString("SystemSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time Format 24h.
        /// </summary>
        public static string Time24 {
            get {
                return ResourceManager.GetString("Time24", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to USA.
        /// </summary>
        public static string USA {
            get {
                return ResourceManager.GetString("USA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User.
        /// </summary>
        public static string User {
            get {
                return ResourceManager.GetString("User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Users.
        /// </summary>
        public static string Users {
            get {
                return ResourceManager.GetString("Users", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End value must be higher than start.
        /// </summary>
        public static string ValidationDifferentValues {
            get {
                return ResourceManager.GetString("ValidationDifferentValues", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Value &gt;0 and &lt;=5000 is required.
        /// </summary>
        public static string ValidationEndValue {
            get {
                return ResourceManager.GetString("ValidationEndValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Value &gt;=0 and &lt;=5000 is required.
        /// </summary>
        public static string ValidationStartValue {
            get {
                return ResourceManager.GetString("ValidationStartValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Racebox.
        /// </summary>
        public static string VendorTitle {
            get {
                return ResourceManager.GetString("VendorTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This operation cannot be undone..
        /// </summary>
        public static string WarningCannotUndo {
            get {
                return ResourceManager.GetString("WarningCannotUndo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Weather.
        /// </summary>
        public static string Weather {
            get {
                return ResourceManager.GetString("Weather", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Power.
        /// </summary>
        public static string X_Power {
            get {
                return ResourceManager.GetString("X_Power", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        public static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
    }
}
