﻿using Racebox.Shared.Models;

namespace Racebox.Shared.Services;


public class RaceBoxSettings
{
    //read from saved locally (json)
    public static List<ValuesRange> DefaultSpeedStopsKMH { get; set; }
    public static List<ValuesRange> DefaultSpeedStopsMPH { get; set; }

    public static List<double> DefaultDistancesInMeters { get; set; }
    public static List<double> DefaultDistancesInFeet { get; set; }

    static RaceBoxSettings()
    {

        DefaultSpeedStopsKMH = new List<ValuesRange>()
        {
            new(0,60),
            new(0,100),
            new(0,150),
            new(0,200),
            new(0,300),
        };

        DefaultSpeedStopsMPH = new List<ValuesRange>()
        {
            new(0,30),
            new(0,60),
            new(0,100),
            new(0,150),
            new(0,200),
        };

        DefaultDistancesInMeters = new double[] { 18, 201, 402, 1000, 1609 }.ToList();

        DefaultDistancesInFeet = new double[] { 60, 1320, 5280 }.ToList();

    }

    //public const double KMH_MPH = 0.62137119224;

    //public const double M_TO_FT = 3.281;
    //public List<ValuesRange> SpeedStopsCustom { get; set; }

    //public List<Car> Cars { get; set; }

    //public Car SelectedCar { get; set; }

    //public OptionsUnits Units { get; set; }

    //public bool UseIncline { get; set; }

    //public bool UseRollout { get; set; }

    //public bool SoundEnabled { get; set; }

    public RaceBoxSettings()
    {
        //Units = OptionsUnits.EU;

        //SoundEnabled = true;

        //UseIncline = true;

        //UseRollout = false;

        //DEFAULTS

        //USER EDITABLE

        //todo read from local device

        //SpeedStopsCustom = new()
        //{
        //    new (50,100),
        //    new (100,125),
        //    new (100,150),
        //    //new (200,300),
        //};

        //Cars = new List<Car>
        //{
        //    new()
        //    {
        //        Title = "Lada Granta"
        //    }
        //};

        //SelectedCar = Cars.FirstOrDefault();
    }


}