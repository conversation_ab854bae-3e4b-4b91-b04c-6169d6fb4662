﻿using AVFoundation;
using CoreLocation;
using Foundation;
using Microsoft.Maui.Handlers;
using UIKit;

namespace Racebox.Helpers
{
	public partial class NativeTasks
	{


        public IEnumerable<string> ListAssets(string subfolder)
        {
            NSBundle mainBundle = NSBundle.MainBundle;
            string resourcesPath = mainBundle.ResourcePath;
            string subfolderPath = Path.Combine(resourcesPath, subfolder);

            if (Directory.Exists(subfolderPath))
            {
                string[] files = Directory.GetFiles(subfolderPath);
                return files.Select(Path.GetFileName).ToList();
            }
            else
            {
                return new List<string>();
            }
        }

        public void OpenTelegram(string channel)
		{
			var tg = new NSUrl($"tg://resolve?domain={channel}");

			if (UIApplication.SharedApplication.CanOpenUrl(tg))
			{
				UIApplication.SharedApplication.OpenUrl(tg);
				return;
			}

			var url = new NSUrl($"https://t.me/{channel}");
			var res = UIApplication.SharedApplication.OpenUrl(url);
		}

		public void OpenLink(string link)
		{
			var url = new NSUrl(link);
			var res = UIApplication.SharedApplication.OpenUrl(url);
		}

		public void LockOrientationPortrait(Page page, bool locked)
		{
			return;

			UIDevice.CurrentDevice.SetValueForKey(new NSNumber((int)UIInterfaceOrientation.Portrait), new NSString("orientation"));
			UINavigationController.AttemptRotationToDeviceOrientation();

			var vc = (page.Handler as ContentViewHandler).ViewController; //  ;//.PlatformView.ViewController?.ParentViewController

			if (locked)
			{
				if (UIDevice.CurrentDevice.CheckSystemVersion(16, 0))
				{
					var windowScene = (UIApplication.SharedApplication.ConnectedScenes.ToArray()[0] as UIWindowScene);
					if (windowScene != null)
					{
						var nav = UIApplication.SharedApplication.KeyWindow?.RootViewController;

						if (nav == null)
							nav = vc;

						if (nav != null)
						{
							// Tell the os that we changed orientations so it knows to call GetSupportedInterfaceOrientations again
							nav.SetNeedsUpdateOfSupportedInterfaceOrientations();

							windowScene.RequestGeometryUpdate(
								new UIWindowSceneGeometryPreferencesIOS(UIInterfaceOrientationMask.Portrait),
								error => { }
							);
						}
					}
				}

			}
			else
			{


			}
		}

		public void ExportLogs(IEnumerable<string> fullFilenames)
		{
			ShareFiles("Логи", fullFilenames);

		}
		public void ShareFiles(string message, IEnumerable<string> fullFilenames)
		{
			MainThread.BeginInvokeOnMainThread(async () =>
			{
				try
				{
					var files = fullFilenames.Select(x => new ShareFile(x)).ToList();
					await Share.Default.RequestAsync(new ShareMultipleFilesRequest
					{
						Title = message,
						Files = files
					});
				}
				catch (Exception e)
				{
					Console.WriteLine(e);
				}

			});
		}


		public bool CheckGpsIsAvailable()
		{
			bool status = CLLocationManager.LocationServicesEnabled;

			return status;
		}
		public void OpenSettings()
		{
			var url = new NSUrl("app-settings:");
			var res = UIApplication.SharedApplication.OpenUrl(url);
		}

		AVAudioPlayer MediaPlayer;
		public void PlaySoundFromAssets(string filename)
		{
			try
			{
				if (MediaPlayer != null && MediaPlayer.Playing)
				{
					MediaPlayer.Stop();
				}

				if (MediaPlayer != null)
				{
					MediaPlayer.Dispose();
					MediaPlayer = null;
				}

				if (MediaPlayer == null)
				{
					NSError err;
					var ext = Path.GetExtension(filename).Replace(".", string.Empty);
					var file = filename.Replace("." + ext, string.Empty);
					var url = NSBundle.MainBundle.GetUrlForResource(file, ext);

					MediaPlayer = new AVAudioPlayer(url, ext, out err);
				}

				MediaPlayer.PrepareToPlay();
				MediaPlayer.SetVolume(1f, 1f);
				MediaPlayer.NumberOfLoops = 0;
				MediaPlayer.Play();

			}
			catch (Exception e)
			{
				System.Diagnostics.Debug.WriteLine(e);
			}
		}

	}
}
