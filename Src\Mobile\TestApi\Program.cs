using System.Text;
using Newtonsoft.Json;
using TestApi.Services;
using TestApi.Models;

namespace TestApi;

/// <summary>
/// Legacy console application for manual testing of the Chart API services
/// For automated testing, use the unit tests in the Tests folder
/// </summary>
class Program
{
    private static readonly string TestDataPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "TestData");
    private static readonly string OutputPath = Path.Combine(Directory.GetCurrentDirectory(), "GeneratedCharts");
    
    static async Task Main(string[] args)
    {
        Console.WriteLine("🏁 Racebox Chart API Test Client (Legacy)");
        Console.WriteLine("=========================================");
        Console.WriteLine("Note: This is the legacy console test. Use 'dotnet test' for proper unit tests.");
        Console.WriteLine();
        
        try
        {
            await TestChartGenerationWithServices();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Unexpected Error: {ex.Message}");
            Console.WriteLine($"Details: {ex}");
        }
        
        Console.WriteLine("\nLegacy test completed. Run 'dotnet test' for comprehensive unit tests.");
    }
    
    static async Task TestChartGenerationWithServices()
    {
        Console.WriteLine("📡 Testing Chart API with new service architecture...");
        
        // Load CSV data
        var csvPath = Path.Combine(TestDataPath, "racebox_log.csv");
        
        if (!File.Exists(csvPath))
        {
            Console.WriteLine($"❌ CSV file not found: {csvPath}");
            return;
        }
        
        Console.WriteLine($"📄 Loading CSV: {csvPath}");
        
        // Read CSV file
        var csvBytes = await File.ReadAllBytesAsync(csvPath);
        var csvText = await File.ReadAllTextAsync(csvPath, Encoding.UTF8);
        
        Console.WriteLine($"📊 CSV Size: {csvBytes.Length:N0} bytes");
        
        // Generate metadata using the new service
        var metadataGenerator = new CsvMetadataGenerator();
        var metadata = metadataGenerator.GenerateFromCsv(csvText, csvBytes.Length);
        var metadataJson = JsonConvert.SerializeObject(metadata, Formatting.Indented);
        
        Console.WriteLine($"📊 Generated Metadata: {metadataJson.Length:N0} characters");
        Console.WriteLine($"🔧 Metadata includes data_source: {metadata.DataSource}");
        Console.WriteLine($"📋 Generated JSON:\n{metadataJson}");
        Console.WriteLine();
        
        // Test the chart service
        using var chartService = new ChartApiService();
        using var cancellationTokenSource = new CancellationTokenSource();
        
        Console.WriteLine("🚀 Calling Chart API Service...");
        
        var result = await chartService.GenerateChartAsync(
            csvData: csvBytes,
            metadataJson: metadataJson,
            outputDirectory: OutputPath,
            cancellationToken: cancellationTokenSource.Token
        );
        
        if (result.IsSuccess)
        {
            Console.WriteLine("✅ Chart generated successfully!");
            Console.WriteLine($"📁 Filename: {result.Filename}");
            Console.WriteLine($"🖼️  File Size: {result.FileSize:N0} bytes");
            Console.WriteLine($"⏱️  Response Time: {result.ResponseTime?.TotalSeconds:F2} seconds");
            Console.WriteLine($"💾 Chart saved to: {result.FilePath}");
            Console.WriteLine($"📂 Relative path: GeneratedCharts\\{result.Filename}");
            Console.WriteLine("🎉 Legacy test completed successfully!");
        }
        else
        {
            Console.WriteLine("❌ Chart generation failed!");
            Console.WriteLine($"🔍 Error: {result.ErrorMessage}");
            if (result.ResponseTime.HasValue)
            {
                Console.WriteLine($"⏱️  Response Time: {result.ResponseTime?.TotalSeconds:F2} seconds");
            }
        }
    }
}