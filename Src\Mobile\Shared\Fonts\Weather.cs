﻿namespace Racebox.Shared.Fonts
{
    public static class WeatherMappings
    {
        static readonly Dictionary<int, string> _weatherCodeToIconMap;

        static WeatherMappings()
        {
            _weatherCodeToIconMap = new Dictionary<int, string>
        {
            { 1000, FaPro.Sun }, // Sunny
            { 1003, FaPro.CloudSun }, // Partly cloudy
            { 1006, FaPro.Cloud }, // Cloudy
            { 1009, FaPro.Cloud }, // Overcast
            { 1030, FaPro.Smog }, // Mist
            { 1063, FaPro.CloudRain }, // Patchy rain possible
            { 1066, FaPro.Snowflake }, // Patchy snow possible
            { 1069, FaPro.CloudMeatball }, // Patchy sleet possible
            { 1072, FaPro.CloudRain }, // Patchy freezing drizzle possible
            { 1087, FaPro.PooStorm }, // Thundery outbreaks possible
            { 1114, FaPro.Snowflake }, // Blowing snow
            { 1117, FaPro.Snowflake }, // Blizzard
            { 1135, FaPro.CloudFog }, // Fog
            { 1147, FaPro.CloudFog }, // Freezing fog
            { 1150, FaPro.CloudRain }, // Patchy light drizzle
            { 1153, FaPro.CloudRain }, // Light drizzle
            { 1168, FaPro.CloudRain }, // Freezing drizzle
            { 1171, FaPro.CloudShowersHeavy }, // Heavy freezing drizzle
            { 1180, FaPro.CloudRain }, // Patchy light rain
            { 1183, FaPro.CloudRain }, // Light rain
            { 1186, FaPro.CloudRain }, // Moderate rain at times
            { 1189, FaPro.CloudShowersHeavy }, // Moderate rain
            { 1192, FaPro.CloudShowersHeavy }, // Heavy rain at times
            { 1195, FaPro.CloudShowersHeavy }, // Heavy rain
            { 1198, FaPro.CloudRain }, // Light freezing rain
            { 1201, FaPro.CloudShowersHeavy }, // Moderate or heavy freezing rain
            { 1204, FaPro.CloudSleet }, // Light sleet
            { 1207, FaPro.CloudSleet }, // Moderate or heavy sleet
            { 1210, FaPro.Snowflake }, // Patchy light snow
            { 1213, FaPro.Snowflake }, // Light snow
            { 1216, FaPro.Snowflake }, // Patchy moderate snow
            { 1219, FaPro.Snowflake }, // Moderate snow
            { 1222, FaPro.Snowflake }, // Patchy heavy snow
            { 1225, FaPro.Snowflake }, // Heavy snow
            { 1237, FaPro.Icicles }, // Ice pellets
            { 1240, FaPro.CloudRain }, // Light rain shower
            { 1243, FaPro.CloudShowersHeavy }, // Moderate or heavy rain shower
            { 1246, FaPro.CloudShowersHeavy }, // Torrential rain shower
            { 1249, FaPro.CloudSleet }, // Light sleet showers
            { 1252, FaPro.CloudSleet }, // Moderate or heavy sleet showers
            { 1255, FaPro.Snowflake }, // Light snow showers
            { 1258, FaPro.Snowflake }, // Moderate or heavy snow showers
            { 1261, FaPro.Icicles }, // Light showers of ice pellets
            { 1264, FaPro.Icicles }, // Moderate or heavy showers of ice pellets
            { 1273, FaPro.CloudRain }, // Patchy light rain with thunder
            { 1276, FaPro.CloudShowersHeavy }, // Moderate or heavy rain with thunder
            { 1279, FaPro.Snowflake }, // Patchy light snow with thunder
            { 1282, FaPro.Snowflake }, // Moderate or heavy snow with thunder
        };
        }

        public static string GetIcon(int code)
        {
            if (_weatherCodeToIconMap.TryGetValue(code, out var icon))
            {
                return icon;
            }
            return string.Empty;
        }

    }
}
