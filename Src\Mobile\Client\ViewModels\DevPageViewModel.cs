﻿
using Racebox.Shared.Enums;
using System.Diagnostics;
using System.Windows.Input;

namespace Racebox.ViewModels
{
    public class DevPageViewModel : BaseViewModel
    {

        public DevPageViewModel()
        {
            EditDistance = new EditDistanceViewModel(new()
            {
                Units = OptionsUnits.EU,
                End = 100
            }, (e) =>
            {

                return null;
            });

            EditDistance.Bind();

            Trace.WriteLine($"[EditDistance] items: {EditDistance.ItemsList.Count}, index: {EditDistance.SelectedIndex}");
        }

        public EditDistanceViewModel EditDistance { get; }

        private string _Value;
        public string Value
        {
            get
            {
                return _Value;
            }
            set
            {
                if (_Value != value)
                {
                    _Value = value;
                    OnPropertyChanged();
                }
            }
        }

        private double _GeneratedRandom;
        public double GeneratedRandom
        {
            get
            {
                return _GeneratedRandom;
            }
            set
            {
                if (_GeneratedRandom != value)
                {
                    _GeneratedRandom = value;
                    OnPropertyChanged();
                }
            }
        }



        private bool once;
        public async void OnViewAppearing()
        {
            if (!once)
            {
                once = true;


                StartMock();


            }
        }

        #region MOCK

        public void TickRandom()
        {
            this.GeneratedRandom = GetCloseRandom(GeneratedRandom, 0.25, 80, 200);

            Value = $"{GeneratedRandom:0.0}kmh";
        }

        public void StartMock()
        {
            if (!MockEnabled)
            {
                MockEnabled = true;

                Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(1000), async () =>
                {
                    TickRandom();

                    return MockEnabled; //repeat
                });
            }
        }

        private bool _MockEnabled;

        public bool MockEnabled
        {
            get
            {
                return _MockEnabled;
            }
            set
            {
                if (_MockEnabled != value)
                {
                    _MockEnabled = value;
                    OnPropertyChanged();
                }
            }
        }

        public string DisplaySatellites { get; }

        public ICommand CommandSwitchConnect { get; }

        public bool HasBattery
        {
            get => _hasBattery;
            set
            {
                if (value != _hasBattery)
                {
                    _hasBattery = value;
                    OnPropertyChanged();
                }
            }
        }
        private bool _hasBattery;


        public static double GetCloseRandom(double value, double spread, double min, double max)
        {
            double precision = 100.0;
            spread *= precision;
            var integer = value * precision;
            var mymax = (int)(max * precision);
            var mymin = (int)(min * precision);

            if (integer < mymin)
            {
                integer = mymin;
            }

            if (integer > mymax)
            {
                integer = mymax;
            }

            var minNext = (int)(integer - spread);

            var maxNext = (int)(integer + spread);

            if (maxNext > mymax)
                maxNext = mymax;
            if (minNext < mymin)
                minNext = mymin;

            try
            {
                var random = RndExtensions.CreateRandom(minNext, maxNext);
                return random / precision;

            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            return value;
        }

        #endregion


    }
}
