

namespace Racebox.Views;

public partial class PageEditCar : BasePage, ILazyPage
{
	private readonly EditCarViewModel _vm;

	public PageEditCar(LocalCar item, Func<LocalCar, Task<int>> callback)
	{
		BindingContext = _vm = new EditCarViewModel(item, callback);

		InitializeComponent();
	}

	protected override void OnAppearing()
	{
		base.OnAppearing();

		//Settings.UpdateStatusBarUponTheme();

		var canUpdate = BindingContext as IUpdateUIState;
		canUpdate?.UpdateState(true);
	}

	public void OnViewAppearing()
	{
		OnAppearing();
	}

	public void OnViewDisappearing()
	{
		OnDisappearing();
	}

	public void UpdateControls(DeviceRotation orientation)
	{

	}


	//public TouchActionEventHandler ButtonTappedHandler
	//{
	//    get
	//    {
	//        return OnTapped_Button;
	//    }
	//}

	//public void OnTapped_Button(object sender, TouchActionEventArgs args)
	//{
	//    if (sender is DrawnView control)
	//    {
	//        BtnBackground.PlayRippleAnimation(control, Colors.White, args.Location.X, args.Location.Y);
	//    }
	//}

}