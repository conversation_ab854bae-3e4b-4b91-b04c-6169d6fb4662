<?xml version="1.0" encoding="utf-8" ?>
<partials:ScaleBarBase
    x:Class="Racebox.Views.Partials.ScaleBarVerticalV2"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:Racebox.Views.Controls"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:partials="clr-namespace:Racebox.Views.Partials"
    x:Name="ThisControlBar"
    HorizontalOptions="Center"
    WidthRequest="60">

    <draw:SkiaLayout
        UseCache="ImageDoubleBuffered"
        HorizontalOptions="Fill"
        VerticalOptions="Fill">

        <controls:GfxScale
            Margin="0,0"
            BackgroundColor="Transparent"
            HorizontalOptions="End"
            Invert="{Binding Source={x:Reference ThisControlBar}, Path=Invert}"
            Limit="{Binding Source={x:Reference ThisControlBar}, Path=Limit}"
            Orientation="Vertical"
            ScaleColor="#50A9A8A2"
            ShowSign="True"
            SideOffset="3"
            TextColor="#82E8E3D7"
            TranslationX="-14"
            VerticalOptions="Fill"
            WidthRequest="42" />

        <!--  MIN  -->
        <draw:SkiaShape
            x:Name="IndicatorMin"
            Margin="5,8"
            BackgroundColor="#CCFFFFFF"
            HeightRequest="2"
            HorizontalOptions="End"
            TranslationX="0"
            VerticalOptions="Center"
            WidthRequest="5"
            ZIndex="2" />

        <!--  MAX  -->
        <draw:SkiaShape
            x:Name="IndicatorMax"
            Margin="5,8"
            BackgroundColor="#CCFFFFFF"
            HeightRequest="2"
            HorizontalOptions="End"
            IsVisible="True"
            VerticalOptions="Center"
            WidthRequest="5"
            ZIndex="2" />

        <!--  MINUS  -->
        <draw:SkiaShape
            x:Name="IndicatorRight"
            Margin="5,8"
            BackgroundColor="Orange"
            HorizontalOptions="End"
            VerticalOptions="Center"
            WidthRequest="5"
            ZIndex="1">
            <draw:SkiaShape.FillGradient>
                <draw:SkiaGradient
                    EndXRatio="0"
                    EndYRatio="1"
                    Opacity="0.60"
                    StartXRatio="0"
                    StartYRatio="0"
                    Type="Linear">
                    <draw:SkiaGradient.Colors>
                        <Color>#FF7C44</Color>
                        <Color>#FF7C44</Color>
                        <Color>#99CB6235</Color>
                    </draw:SkiaGradient.Colors>
                    <draw:SkiaGradient.ColorPositions>
                        <x:Double>0.0</x:Double>
                        <x:Double>0.75</x:Double>
                        <x:Double>1.0</x:Double>
                    </draw:SkiaGradient.ColorPositions>
                </draw:SkiaGradient>
            </draw:SkiaShape.FillGradient>
        </draw:SkiaShape>

        <!--  PLUS  -->
        <draw:SkiaShape
            x:Name="IndicatorLeft"
            Margin="5,8"
            BackgroundColor="Orange"
            HorizontalOptions="End"
            VerticalOptions="Center"
            WidthRequest="5"
            ZIndex="1">
            <draw:SkiaShape.FillGradient>
                <draw:SkiaGradient
                    EndXRatio="0"
                    EndYRatio="1"
                    Opacity="0.60"
                    StartXRatio="0"
                    StartYRatio="0"
                    Type="Linear">
                    <draw:SkiaGradient.Colors>
                        <Color>#99CB6235</Color>
                        <Color>#FF7C44</Color>
                        <Color>#FF7C44</Color>
                    </draw:SkiaGradient.Colors>
                    <draw:SkiaGradient.ColorPositions>
                        <x:Double>0.0</x:Double>
                        <x:Double>0.25</x:Double>
                        <x:Double>1.0</x:Double>
                    </draw:SkiaGradient.ColorPositions>

                </draw:SkiaGradient>
            </draw:SkiaShape.FillGradient>
        </draw:SkiaShape>

        <!--  TRACK BAR  -->
        <draw:SkiaShape
            x:Name="Bar"
            Margin="0,4,4,4"
            BackgroundColor="#44000000"
            CornerRadius="3"
            HorizontalOptions="End"
            StrokeColor="Black"
            StrokeWidth="1.0"
            VerticalOptions="Fill"
            WidthRequest="7">
            <draw:SkiaShape.Shadows>

                <draw:SkiaShadow
                    Blur="1.5"
                    Opacity="0.15"
                    X="2.5"
                    Y="2.5"
                    Color="#FFFFFF" />

                <draw:SkiaShadow
                    Blur="1.5"
                    Opacity="0.49"
                    X="-2.5"
                    Y="-2.5"
                    Color="#000000" />

            </draw:SkiaShape.Shadows>
            <draw:SkiaShape.StrokeGradient>

                <draw:SkiaGradient
                    EndXRatio="0.8"
                    EndYRatio="0.2"
                    StartXRatio="0.2"
                    StartYRatio="0.2"
                    Type="Linear">
                    <draw:SkiaGradient.Colors>
                        <Color>#15191E</Color>
                        <Color>#42464B</Color>
                    </draw:SkiaGradient.Colors>
                </draw:SkiaGradient>

            </draw:SkiaShape.StrokeGradient>
        </draw:SkiaShape>

    </draw:SkiaLayout>

</partials:ScaleBarBase>
