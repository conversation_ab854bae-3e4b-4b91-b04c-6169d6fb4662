﻿<?xml version="1.0" encoding="UTF-8" ?>
<?xaml-comp compile="true" ?>
<ResourceDictionary
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:converters1="clr-namespace:Racebox.Converters"
    xmlns:converters2="clr-namespace:AppoMobi.Framework.Maui.Converters;assembly=AppoMobi.Framework.Maui">

    <!--#region CONVERTERS-->

    <converters2:InverseBoolConverter x:Key="InverseBoolConverter" />
    <converters2:StringNotEmptyConverter x:Key="StringNotEmptyConverter" />
    <converters2:NotConverter x:Key="NotConverter" />
    <converters2:UpperCaseConverter x:Key="UpperCaseConverter" />
    <converters2:CompareIntegersConverter x:Key="CompareIntegersConverter" />
    <converters2:EnumConverter x:Key="EnumConverter" />
    <converters2:AllTrueMultiConverter x:Key="AllTrueConverter" />
    <converters2:AddValueConverter x:Key="AddValueConverter" />
    <converters2:MultiplyValueConverter x:Key="MultiplyValueConverter" />
    <converters2:DivideValueConverter x:Key="DivideValueConverter" />

    <converters1:StatesConverter x:Key="StatesConverter" />
    <converters1:IListMemberConverter x:Key="IListMemberConverter" />
    <converters1:ValidatorErrorMessageConverter x:Key="ValidatorErrorMessageConverter" />
    <converters1:ValidatorMessageNotEmptyConverter x:Key="ValidatorMessageNotEmptyConverter" />
    <converters1:ValidatorIsValidConverter x:Key="ValidatorIsValidConverter" />
    <converters1:ValidatorIsNotValidConverter x:Key="ValidatorIsNotValidConverter" />


    <!--#endregion-->


</ResourceDictionary>
