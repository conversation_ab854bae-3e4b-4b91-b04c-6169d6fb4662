<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BtnSave" xml:space="preserve">
    <value>Ahorrar</value>
  </data>
  <data name="BtnCancel" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="BtnClose" xml:space="preserve">
    <value>Cerca</value>
  </data>
  <data name="BtnApply" xml:space="preserve">
    <value>Aplicar</value>
  </data>
  <data name="Inputs" xml:space="preserve">
    <value>Entradas</value>
  </data>
  <data name="Device" xml:space="preserve">
    <value>Dispositivo</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Sí</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="Options" xml:space="preserve">
    <value>Opciones</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="FeetShort" xml:space="preserve">
    <value>pie</value>
  </data>
  <data name="MetersShort" xml:space="preserve">
    <value>metro</value>
  </data>
  <data name="Mph" xml:space="preserve">
    <value>mph</value>
  </data>
  <data name="Kmh" xml:space="preserve">
    <value>kilómetros por hora</value>
  </data>
  <data name="MinutesShort" xml:space="preserve">
    <value>mín.</value>
  </data>
  <data name="SecondsShort" xml:space="preserve">
    <value>s</value>
  </data>
  <data name="BtnStart" xml:space="preserve">
    <value>Comenzar</value>
  </data>
  <data name="BtnStop" xml:space="preserve">
    <value>Detener</value>
  </data>
  <data name="BtnConnect" xml:space="preserve">
    <value>Conectar</value>
  </data>
  <data name="BtnDisconnect" xml:space="preserve">
    <value>Desconectar</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>En</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>Apagado</value>
  </data>
  <data name="StatusConnecting" xml:space="preserve">
    <value>Conectando...</value>
  </data>
  <data name="StatusBluetoothOff" xml:space="preserve">
    <value>Bluetooth está apagado</value>
  </data>
  <data name="Measure" xml:space="preserve">
    <value>Medición</value>
  </data>
  <data name="MeasureInstant" xml:space="preserve">
    <value>Ejecución instantánea</value>
  </data>
  <data name="VendorTitle" xml:space="preserve">
    <value>caja de carreras</value>
  </data>
  <data name="SignalStrength" xml:space="preserve">
    <value>Intensidad de señal</value>
  </data>
  <data name="HintConnectNow" xml:space="preserve">
    <value>Haga clic en el ícono de Bluetooth en la parte superior de la pantalla para conectarse al dispositivo</value>
  </data>
  <data name="HintStartMeasuring" xml:space="preserve">
    <value>Presione el botón Iniciar para comenzar a medir.</value>
  </data>
  <data name="HintMeasureFailed" xml:space="preserve">
    <value>Medición fallida</value>
  </data>
  <data name="HintMonitoring" xml:space="preserve">
    <value>Esperando que comience la medición...</value>
  </data>
  <data name="HintGo" xml:space="preserve">
    <value>¡¡¡IR!!!</value>
  </data>
  <data name="Racing" xml:space="preserve">
    <value>Correr</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>Panel</value>
  </data>
  <data name="History" xml:space="preserve">
    <value>Historia</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Ajustes</value>
  </data>
  <data name="Altitude" xml:space="preserve">
    <value>Altitud</value>
  </data>
  <data name="Heading" xml:space="preserve">
    <value>Título</value>
  </data>
  <data name="Latitude" xml:space="preserve">
    <value>Latitud</value>
  </data>
  <data name="Longitude" xml:space="preserve">
    <value>Longitud</value>
  </data>
  <data name="Frequency" xml:space="preserve">
    <value>Frecuencia</value>
  </data>
  <data name="MaxSpeed" xml:space="preserve">
    <value>Máxima velocidad</value>
  </data>
  <data name="Incline" xml:space="preserve">
    <value>Inclinación</value>
  </data>
  <data name="AllCars" xml:space="preserve">
    <value>Todos los carros</value>
  </data>
  <data name="Filtered" xml:space="preserve">
    <value>Filtrado</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filtrar</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Todo</value>
  </data>
  <data name="Cars" xml:space="preserve">
    <value>Vehículos</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Descripción</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Seleccionar</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Orden</value>
  </data>
  <data name="MeasuringSystem" xml:space="preserve">
    <value>Sistema de medición</value>
  </data>
  <data name="CustomMetrics" xml:space="preserve">
    <value>Métricas de medición</value>
  </data>
  <data name="Sound" xml:space="preserve">
    <value>Sonido</value>
  </data>
  <data name="RollOut" xml:space="preserve">
    <value>Despliegue (1 pie)</value>
  </data>
  <data name="Car" xml:space="preserve">
    <value>Vehículo</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Usuario</value>
  </data>
  <data name="EditList" xml:space="preserve">
    <value>Lista de edición</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Usuarios</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>Marca</value>
  </data>
  <data name="Model" xml:space="preserve">
    <value>Modelo</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Nombre</value>
  </data>
  <data name="Deleting" xml:space="preserve">
    <value>Borrar</value>
  </data>
  <data name="AreYouSure" xml:space="preserve">
    <value>¿Está seguro?</value>
  </data>
  <data name="CannotDeleteSingle" xml:space="preserve">
    <value>No se puede eliminar el último registro</value>
  </data>
  <data name="CannotDeleteUsed" xml:space="preserve">
    <value>Esto realmente se está usando.</value>
  </data>
  <data name="USA" xml:space="preserve">
    <value>EE.UU</value>
  </data>
  <data name="Europe" xml:space="preserve">
    <value>Europa</value>
  </data>
  <data name="DefaultBrand" xml:space="preserve">
    <value>Su</value>
  </data>
  <data name="DefaultModel" xml:space="preserve">
    <value>Auto</value>
  </data>
  <data name="OrderOldFirst" xml:space="preserve">
    <value>mayores primero</value>
  </data>
  <data name="OrderNewFirst" xml:space="preserve">
    <value>Más nuevo primero</value>
  </data>
  <data name="SpeedRange" xml:space="preserve">
    <value>Rango de velocidad</value>
  </data>
  <data name="Distance" xml:space="preserve">
    <value>Distancia</value>
  </data>
  <data name="Length" xml:space="preserve">
    <value>Distancia</value>
  </data>
  <data name="Start" xml:space="preserve">
    <value>Comenzar</value>
  </data>
  <data name="End" xml:space="preserve">
    <value>Fin</value>
  </data>
  <data name="ValidationStartValue" xml:space="preserve">
    <value>Se requiere valor &gt;=0 y &lt;=5000</value>
  </data>
  <data name="ValidationEndValue" xml:space="preserve">
    <value>Se requiere valor &gt;0 y &lt;=5000</value>
  </data>
  <data name="ValidationDifferentValues" xml:space="preserve">
    <value>El valor final debe ser mayor que el inicial.</value>
  </data>
  <data name="MeasuringUnits" xml:space="preserve">
    <value>Unidades</value>
  </data>
  <data name="Feet" xml:space="preserve">
    <value>Pies</value>
  </data>
  <data name="Meters" xml:space="preserve">
    <value>Metros</value>
  </data>
  <data name="AlertTurnOnBluetooth" xml:space="preserve">
    <value>Por favor enciende Bluetooth.</value>
  </data>
  <data name="AlertNeedGpsPermissionsForBluetooth" xml:space="preserve">
    <value>Otorgue permisos de ubicación (requisito del sistema Android para usar Bluetooth).</value>
  </data>
  <data name="AlertNeedGpsOnForBluetooth" xml:space="preserve">
    <value>Habilite el GPS (requisito del sistema Android para usar Bluetooth).</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Ubicación</value>
  </data>
  <data name="AlertBluetoothUnsupported" xml:space="preserve">
    <value>Su dispositivo no es compatible con Bluetooth.</value>
  </data>
  <data name="HintGpsLow" xml:space="preserve">
    <value>Intente cambiar la ubicación de su dispositivo para una mejor recepción GPS</value>
  </data>
  <data name="StatusGpsLow" xml:space="preserve">
    <value>Señal GPS baja</value>
  </data>
  <data name="AlertBluetoothPermissionsOff" xml:space="preserve">
    <value>Sin permisos para usar Bluetooth.</value>
  </data>
  <data name="Hz" xml:space="preserve">
    <value>Hz</value>
  </data>
  <data name="KmhAdd" xml:space="preserve">
    <value>kilómetros por hora</value>
  </data>
  <data name="MphAdd" xml:space="preserve">
    <value>mph</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Idioma</value>
  </data>
  <data name="MaxAcceleration" xml:space="preserve">
    <value>Aceleración máxima.</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Editar</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Borrar</value>
  </data>
  <data name="HoursShort" xml:space="preserve">
    <value>h</value>
  </data>
  <data name="BtnReset" xml:space="preserve">
    <value>Reiniciar</value>
  </data>
  <data name="BtnCreate" xml:space="preserve">
    <value>Crear</value>
  </data>
  <data name="Acceleration" xml:space="preserve">
    <value>Aceleración</value>
  </data>
  <data name="AccelerationSide" xml:space="preserve">
    <value>Aceleración lateral.</value>
  </data>
  <data name="InclineMax" xml:space="preserve">
    <value>Inclinación máxima</value>
  </data>
  <data name="IsValid" xml:space="preserve">
    <value>Es válida</value>
  </data>
  <data name="IsValidYes" xml:space="preserve">
    <value>Sí</value>
  </data>
  <data name="IsValidNo" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="AlertBluetoothWillNotBeAvailable" xml:space="preserve">
    <value>Bluetooth no estará disponible.</value>
  </data>
  <data name="SystemSettings" xml:space="preserve">
    <value>Ajustes del sistema</value>
  </data>
  <data name="Time24" xml:space="preserve">
    <value>Formato de hora 24h</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>Acerca de</value>
  </data>
  <data name="BtnQuestion" xml:space="preserve">
    <value>Hacer una pregunta</value>
  </data>
  <data name="AboutAppText" xml:space="preserve">
    <value>Racebox es una aplicación complementaria para los dispositivos Racebox Pro y Pro+ creada para medir el rendimiento del vehículo.
La aplicación está pensada para su uso únicamente en pistas de carreras. El autor no es responsable de ninguna lesión o daño a la propiedad que pueda ocurrir durante el uso de este software.</value>
  </data>
  <data name="Copyright" xml:space="preserve">
    <value>© 2025 Equipo Racebox</value>
  </data>
  <data name="AlertNeedLocationForBluetooth" xml:space="preserve">
    <value>Necesitaríamos que proporcione permisos para usar su ubicación GPS (requisito del sistema Android para usar Bluetooth).</value>
  </data>
  <data name="PressBACKOnceAgain" xml:space="preserve">
    <value>Presione ATRÁS una vez más para salir de la aplicación.</value>
  </data>
  <data name="ExportFormat" xml:space="preserve">
    <value>Formato de registros</value>
  </data>
  <data name="ErrorFailedToSaveRecord" xml:space="preserve">
    <value>No se pudo guardar el registro</value>
  </data>
  <data name="ErrorMetricsAlreadyExist" xml:space="preserve">
    <value>Estas métricas ya existen</value>
  </data>
  <data name="CompatibleDevicesNotFound" xml:space="preserve">
    <value>No se encontraron dispositivos Racebox cerca.</value>
  </data>
  <data name="DemoMode" xml:space="preserve">
    <value>Modo de demostración</value>
  </data>
  <data name="DemoWarning" xml:space="preserve">
    <value>En este modo, la aplicación dejará de conectarse a un módulo Bluetooth real y, en su lugar, reproducirá pistas de movimiento pregrabadas. ¿Está seguro de que desea habilitar el modo de demostración?</value>
  </data>
  <data name="DemoStartHint" xml:space="preserve">
    <value>Haga clic en Speed Gauge para iniciar la simulación.</value>
  </data>
  <data name="BadMeasureNotValid" xml:space="preserve">
    <value>Medición no válida (gran inclinación)</value>
  </data>
  <data name="BadMeasureTooLong" xml:space="preserve">
    <value>El tiempo de medición está por encima de ACCTIMEOUT</value>
  </data>
  <data name="BadMeasureLowAccel" xml:space="preserve">
    <value>La aceleración está baja</value>
  </data>
  <data name="BadMeasureInstant" xml:space="preserve">
    <value>Ejecución instantánea y la velocidad está por debajo de la velocidad inicial del primer par y el tiempo de medición es más de 1 segundo</value>
  </data>
  <data name="BadMeasureGps" xml:space="preserve">
    <value>Recepción GPS de baja calidad</value>
  </data>
  <data name="BadMeasureNoRanges" xml:space="preserve">
    <value>No se han medido rangos de velocidad</value>
  </data>
  <data name="BadMeasureNoDistances" xml:space="preserve">
    <value>No se miden distancias</value>
  </data>
  <data name="BadMeasureLowSpeed" xml:space="preserve">
    <value>Velocidad por debajo de READYSPEEDTHRESHOLDUNIT</value>
  </data>
  <data name="BadMeasureIncline" xml:space="preserve">
    <value>El resultado no es válido: ¡la inclinación era demasiado importante!</value>
  </data>
  <data name="Report" xml:space="preserve">
    <value>Informe</value>
  </data>
  <data name="MeasuringStateError" xml:space="preserve">
    <value>Medición fallida</value>
  </data>
  <data name="MeasuringStateFinished" xml:space="preserve">
    <value>Medición completada</value>
  </data>
  <data name="MeasuringStateReady" xml:space="preserve">
    <value>¡Comenzar!</value>
  </data>
  <data name="MeasuringStateActive" xml:space="preserve">
    <value>¡Carreras!</value>
  </data>
  <data name="MeasuringStateMonitoring" xml:space="preserve">
    <value>El dispositivo está encendido</value>
  </data>
  <data name="MeasuringStateDisabled" xml:space="preserve">
    <value>El dispositivo está apagado</value>
  </data>
  <data name="Weather" xml:space="preserve">
    <value>Clima</value>
  </data>
  <data name="X_Power" xml:space="preserve">
    <value>Batería</value>
  </data>
  <data name="Satellites" xml:space="preserve">
    <value>Satélites</value>
  </data>
  <data name="DeviceLedBrightness" xml:space="preserve">
    <value>Brillo de la pantalla</value>
  </data>
  <data name="DeviceTransmissionType" xml:space="preserve">
    <value>Tipo de transmisión</value>
  </data>
  <data name="DeviceMaps" xml:space="preserve">
    <value>Mapas</value>
  </data>
  <data name="DeviceTone" xml:space="preserve">
    <value>Señales de sonido</value>
  </data>
  <data name="DeviceUtc" xml:space="preserve">
    <value>Zona horaria</value>
  </data>
  <data name="DeviceLogFormat" xml:space="preserve">
    <value>Formato de registros</value>
  </data>
  <data name="DeviceLogRate" xml:space="preserve">
    <value>Frecuencia de registro</value>
  </data>
  <data name="DevicePrediction" xml:space="preserve">
    <value>Modo predictivo</value>
  </data>
  <data name="DeviceRandomStart" xml:space="preserve">
    <value>Hora de inicio arbitraria</value>
  </data>
  <data name="DeviceMinDistance" xml:space="preserve">
    <value>Mín. distancia de visualización</value>
  </data>
  <data name="DeviceCarWeight" xml:space="preserve">
    <value>Peso del vehículo</value>
  </data>
  <data name="DeviceDragRatio" xml:space="preserve">
    <value>Coef. arrastrar</value>
  </data>
  <data name="DeviceFrontal" xml:space="preserve">
    <value>Área transversal</value>
  </data>
  <data name="DeviceObdLog" xml:space="preserve">
    <value>Grabación de registros OBD</value>
  </data>
  <data name="DeviceShiftTime" xml:space="preserve">
    <value>Medición del tiempo de conmutación</value>
  </data>
  <data name="DeviceObdPidAuto" xml:space="preserve">
    <value>Detección automática PID OBD</value>
  </data>
  <data name="DeviceObdPids" xml:space="preserve">
    <value>Selección de PID de OBD para leer</value>
  </data>
  <data name="DeviceObdProtocol" xml:space="preserve">
    <value>protocolo obd</value>
  </data>
  <data name="DeviceGnssSecondarySource" xml:space="preserve">
    <value>Segunda fuente GNSS</value>
  </data>
  <data name="DeviceGnssUartOut" xml:space="preserve">
    <value>Salida de datos GNSS vía USB</value>
  </data>
  <data name="DeviceGnssColdStart" xml:space="preserve">
    <value>Reinicio en frío GNSS</value>
  </data>
  <data name="DeviceGnssReconfig" xml:space="preserve">
    <value>Reconfiguración GNSS</value>
  </data>
  <data name="DeviceResetConfiguration" xml:space="preserve">
    <value>Restablecer configuración</value>
  </data>
  <data name="ResetDevice" xml:space="preserve">
    <value>Reiniciar el dispositivo</value>
  </data>
  <data name="WarningCannotUndo" xml:space="preserve">
    <value>Esta operación no se puede deshacer.</value>
  </data>
  <data name="DeviceSettings" xml:space="preserve">
    <value>Configuración de dispositivo</value>
  </data>
  <data name="FirmwareWarningHelp" xml:space="preserve">
    <value>Para actualizar el firmware en su dispositivo __Racebox__, siga estos pasos:
 1. Descargue la nueva versión del sitio web [racebox.ru](https://racebox.ru/obnovlenie-po).
 2. Copie el archivo en formato `HEX` correspondiente al modelo de su dispositivo al directorio raíz de la tarjeta de memoria.
 3. Inserte la tarjeta de memoria en el dispositivo cuando esté apagado.
 4. Mientras mantiene presionado el botón `Modo`, conecte el dispositivo a la alimentación mediante el cable USB.
 5. Espere hasta que se inicialice la tarjeta de memoria y aparezca el mensaje `HOLD TO UPDATE X.XX` (donde X.XX es la versión del software descargado)
 6. Mantenga presionado el botón `Modo` para comenzar el proceso de actualización.
 7. Espere a que se complete la actualización y asegúrese de que se muestre la nueva versión del firmware cuando encienda el dispositivo.
 8. Reinicie la aplicación __Racebox__.</value>
  </data>
  <data name="FirmwareHowToTitle" xml:space="preserve">
    <value>Cómo actualizar el firmware</value>
  </data>
  <data name="FirmwareWarningTitle" xml:space="preserve">
    <value>Firmware desactualizado</value>
  </data>
  <data name="SettingsCanSpeak" xml:space="preserve">
    <value>Dile la velocidad</value>
  </data>
</root>