﻿using DrawnUi.MapsUi;
using Mapsui;
using Mapsui.Extensions;
using Mapsui.Layers;
using Mapsui.Nts;
using Mapsui.Nts.Extensions;
using Mapsui.Projections;
using Mapsui.Styles;
using Mapsui.UI.Maui;
using Mapsui.Utilities;
using NetTopologySuite.Geometries;
using SkiaSharp;
using SkiaSharp.Views.Maui.Controls;

namespace Racebox.Views.Maps;

public class RaceBoxMap : SkiaMapsUi
{
    public class CustomMap : Mapsui.Map
    {

    }

    public ILayer LayerMap { get; protected set; }
    public ILayer LayerPath { get; protected set; }
    public GeometryFeature Path { get; protected set; }

    public RaceBoxMap()
    {
        //IsMyLocationButtonVisible = false;
        //IsNorthingButtonVisible = false;
        //IsZoomButtonVisible = false;

        BackgroundColor = Colors.Black;
        VerticalOptions = LayoutOptions.Fill;
        HorizontalOptions = LayoutOptions.Fill;

        Initialize();
    }

    public void Initialize()
    {
        //this.Renderer.StyleRenderers[typeof(RasterStyle)] = _renderer;

        var map = new CustomMap()
        {
            CRS = "EPSG:3857",
            BackColor = Mapsui.Styles.Color.Black
            //Transformation = new MinimalTransformation()
        };

        LayerMap = OpenCustomStreetMap.CreateTileLayer("raceboxcompanionapp-mobile");

        map.Layers.Add(LayerMap);
        map.Widgets.Clear();
        map.Widgets.Add(new Mapsui.Widgets.ScaleBar.ScaleBarWidget(map)
        {
            TextAlignment = Mapsui.Widgets.Alignment.Center,
            HorizontalAlignment = Mapsui.Widgets.HorizontalAlignment.Left,
            VerticalAlignment = Mapsui.Widgets.VerticalAlignment.Bottom
        });

        //map.Widgets.Add(new ZoomInOutWidget { MarginX = 10, MarginY = 20 });  //adds the +/- zoom widget

        // 48.856663, 2.351556 Paris
        // 59.946466, 30.356859

        if (!NeedPath)
        {
            //var smc = SphericalMercator.FromLonLat(30.356859, 59.946466);
            //map.Home = n => n.NavigateTo(new MPoint(smc.x, smc.y), map.Resolutions[12]);  //0 zoomed out-19 zoomed in
        }

        LayerPath = CreateLiveTrackerLineStringLayer(CreateLiveTrackingLineStringStyle());
        map.Layers.Add(LayerPath);

        this.Map = map;

        //DrawPath();
    }

    bool _boolDrawnPath;

    protected override void Paint(DrawingContext ctx)
    {
        base.Paint(ctx);

        if (LayoutReady && WasDrawn && !_boolDrawnPath)
        {
            _boolDrawnPath = true;

            DrawPath();
        }
    }

    public static IStyle CreateLiveTrackingLineStringStyle()
    {
        return new VectorStyle
        {
            Fill = null,
            Outline = null,
            Line =
            {
                Color = Mapsui.Styles.Color.FromString("#FF0000"),
                Width = 3
            }
        };
    }

    private ILayer CreateLiveTrackerLineStringLayer(IStyle style = null)
    {
        LineString lineString = LineString.Empty;
        Path = new GeometryFeature(lineString);

        var layer = new MemoryLayer()
        {
            Features = new[] { Path },
            Name = "RouteLayer",
            Style = style
        };

        return layer;
    }


    public void DrawPath()
    {
        MainThread.BeginInvokeOnMainThread(async () =>
        {
            await SetPath(PathCoords);
        });
    }

    SemaphoreSlim semaphoreSetPath = new(1, 1);

    /// <summary>
    /// Cann on UI thread only!
    /// </summary>
    /// <param name="coords"></param>
    /// <param name="zoomIn"></param>
    /// <returns></returns>
    public async Task SetPath(IEnumerable<(double Latitude, double Longitude)> coords, bool zoomIn = true)
    {
        await semaphoreSetPath.WaitAsync();

        try
        {
            LayerPath.DataHasChanged();

            if (Path == null || LayerPath == null)
                return;

            if (coords == null)
            {
                //todo
                //Path.Geometry.Coordinates.Clear();
                LayerPath.DataHasChanged();
                return;
            }

            var total = coords.Count();

            if (total > 1)
            {
                var lineString = new LineString(coords
                    .Select(s => SphericalMercator.FromLonLat(s.Longitude, s.Latitude).ToCoordinate()).ToArray());

                Path.Geometry = lineString;
                // Clear cache for change to show
                //todo
                //Path.RenderedGeometry.Clear();
                // Trigger DataChanged notification
                LayerPath.DataHasChanged();


                if (zoomIn)
                {
                    ZoomToPath();
                    await Task.Delay(350); //wait for zoom to complete
                }

            }
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
        }
        finally
        {
            semaphoreSetPath.Release();
        }


    }

    /// <summary>
    /// Fit + Fill
    /// </summary>
    /// <param name="sideOffsetDp"></param>
    public void ZoomToPath(double sideOffsetDp = 24.0)
    {
        if (Width > 0 && Height > 0)
        {
            var viewportWidth = Width - sideOffsetDp * 2;
            var viewportHeight = Height - sideOffsetDp * 2;

            var resolution = ZoomHelper.CalculateResolutionForWorldSize(
                Path.Extent.Width, Path.Extent.Height,
                viewportWidth, viewportHeight, MBoxFit.Fit);

            this.Map.Navigator.CenterOnAndZoomTo(Path.Extent.Centroid, resolution, 350, Mapsui.Animations.Easing.CubicInOut);
        }
        else
        {
            Console.WriteLine("[Map] cannot zoom to invisible path");
        }
    }

    void LockToPath()
    {

    }



    public class ViewportPosition
    {
        public double Zoom { get; set; }
        public double X { get; set; }
        public double Y { get; set; }
    }

    private static void Swap(ref double xMin, ref double xMax)
    {
        (xMin, xMax) = (xMax, xMin);
    }

    ViewportPosition GetZoom(
        double x1, double y1, double x2, double y2,
        double screenWidth, double screenHeight,
        MBoxFit scaleMethod = MBoxFit.Fit)
    {

        var ret = new ViewportPosition()
        {
            Zoom = 8.0
        };

        if (x1 > x2) Swap(ref x1, ref x2);
        if (y1 > y2) Swap(ref y1, ref y2);

        ret.X = (x2 + x1) / 2;
        ret.Y = (y2 + y1) / 2;

        if (scaleMethod == MBoxFit.Fit)
            ret.Zoom = Math.Max((x2 - x1) / screenWidth, (y2 - y1) / screenHeight);
        else if (scaleMethod == MBoxFit.Fill)
            ret.Zoom = Math.Min((x2 - x1) / screenWidth, (y2 - y1) / screenHeight);
        else if (scaleMethod == MBoxFit.FitWidth)
            ret.Zoom = (x2 - x1) / screenWidth;
        else if (scaleMethod == MBoxFit.FitHeight)
            ret.Zoom = (y2 - y1) / screenHeight;
        else
            throw new Exception("FillMethod not found");

        return ret;
    }

    public static readonly BindableProperty PathCoordsProperty = BindableProperty.Create(nameof(PathCoords),
    typeof(IEnumerable<(double, double)>),
    typeof(RaceBoxMap),
    null, propertyChanged: (bo, vo, nv) =>
    {
        if (bo is RaceBoxMap control)
        {
            control.DrawPath();
        }
    });

    public IEnumerable<(double, double)> PathCoords
    {
        get { return (IEnumerable<(double, double)>)GetValue(PathCoordsProperty); }
        set { SetValue(PathCoordsProperty, value); }
    }

    public static readonly BindableProperty NeedPathProperty
       = BindableProperty.Create(nameof(NeedPath),
       typeof(bool), typeof(RaceBoxMap),
    false);
    /// <summary>
    /// If set to true will display the map when no position was set manually,
    /// otherwise will display blank until you ZoomToPath
    /// </summary>
    public bool NeedPath
    {
        get
        {
            return (bool)GetValue(NeedPathProperty);
        }
        set
        {
            SetValue(NeedPathProperty, value);
        }
    }



}