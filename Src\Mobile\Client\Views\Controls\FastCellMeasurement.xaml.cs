using DrawnUi;

namespace Racebox.Views.Partials;



public partial class FastCellMeasurement : SkiaListCell
{
	public override void SetContentFull()
	{
		if (BindingContext is IHasDetailedDisplay model)
		{
 

			LabelDisplay1.Text = model.Display1;
			LabelDisplay2.Text = model.Display2;
 
		}

		base.SetContentFull();
	}

	public FastCellMeasurement()
	{
		InitializeComponent();

	}


	public override void OnDisposing()
	{
		//DetachGestures();

		base.OnDisposing();
	}





	private bool _IsSelected;
	public bool IsSelected
	{
		get { return _IsSelected; }
		set
		{
			if (_IsSelected != value)
			{
				_IsSelected = value;
				OnPropertyChanged();
				Update();
			}
		}
	}


	public static readonly BindableProperty MainFrameProperty = BindableProperty.Create(nameof(MainFrame),
		typeof(SkiaControl), typeof(SkiaListCell),
		null);
	public SkiaControl MainFrame
	{
		get { return (SkiaControl)GetValue(MainFrameProperty); }
		set { SetValue(MainFrameProperty, value); }
	}



}