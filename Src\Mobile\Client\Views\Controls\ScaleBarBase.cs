﻿using DrawnUi;

using System.Runtime.CompilerServices;

namespace Racebox.Views.Partials
{
	public class ScaleBarBase : Canvas
	{
		public virtual void UpdateControl()
		{

		}


		public static FloatingPosition[] GradientPoints =
		{
            //1
            new ()
			{
				IsFixed = true,
				Base = 0.0
			},
            //2
            new ()
			{
				Base = 0.48
			},  
            //3
            new ()
			{
				Base = 0.48
			},  
            //4
            new ()
			{
				Base = 0.5,
			},  
            //5
            new ()
			{
				Base = 0.52
			},  
            //6
            new ()
			{
				Base = 0.52
			},
            //7
            new ()
			{
				IsFixed = true,
				Base = 1.0
			},
		};

		protected void SetValueSafe(FloatingPosition point, double value)
		{
			var filtered = value;
			if (filtered < 0)
			{
				filtered = 0;
			}
			else
			if (filtered > 1)
			{
				filtered = 1;
			}
			point.Value = filtered;
		}
		public double[] Points
		{
			get
			{
				var points = GradientPoints.Select(point => point.Value).ToArray();
				return points;
			}
		}

		protected virtual void OnUpdating()
		{

		}
		protected override void OnPropertyChanged([CallerMemberName] string propertyName = null)
		{
			base.OnPropertyChanged(propertyName);

			if (propertyName == nameof(Value)
				|| propertyName == nameof(Invert)
				|| propertyName == nameof(Limit)
				|| propertyName == nameof(ValueSideOffset))
			{
				OnPropertyChanged(nameof(ValueScaled));
				UpdateControl();
				OnUpdating();
			}
		}

		public double ValueScaled
		{
			get
			{
				var value = Value;

				//if (Invert)
				//{
				//    value = -value;
				//}

				if (Value < -Limit)
				{
					value = -Limit;
				}
				else
				if (Value > Limit)
				{
					value = Limit;
				}

				var scaled = (1.0 + value / Limit) / 2.0;

				return scaled;
			}
		}



		public static readonly BindableProperty ValueProperty =
			BindableProperty.Create(nameof(Value),
				typeof(double),
				typeof(ScaleBarBase),
				0.0);
		public double Value
		{
			get { return (double)GetValue(ValueProperty); }
			set { SetValue(ValueProperty, value); }
		}
		/*
		public static readonly BindableProperty ValueBorderMaxProperty =
			BindableProperty.Create(nameof(ValueBorderMax),
				typeof(double),
				typeof(ScaleBarBase),
				0.0);
		/// <summary>
		/// Pass value for border here
		/// </summary>
		public double ValueBorderMax
		{
			get { return (double)GetValue(ValueBorderMaxProperty); }
			set { SetValue(ValueBorderMaxProperty, value); }
		}

		public static readonly BindableProperty ValueBorderMinProperty =
			BindableProperty.Create(nameof(ValueBorderMin),
				typeof(double),
				typeof(ScaleBarBase),
				0.0);
		/// <summary>
		/// Pass value for border here
		/// </summary>
		public double ValueBorderMin
		{
			get { return (double)GetValue(ValueBorderMinProperty); }
			set { SetValue(ValueBorderMinProperty, value); }
		}
		*/
		public static readonly BindableProperty ValueSideOffsetProperty =
			BindableProperty.Create(nameof(ValueSideOffset),
				typeof(double),
				typeof(ScaleBarBase),
				0.0);
		public double ValueSideOffset
		{
			get { return (double)GetValue(ValueSideOffsetProperty); }
			set { SetValue(ValueSideOffsetProperty, value); }
		}

		public static readonly BindableProperty LimitProperty =
			BindableProperty.Create(nameof(Limit),
				typeof(double),
				typeof(ScaleBarBase),
				1.2);
		public double Limit
		{
			get { return (double)GetValue(LimitProperty); }
			set { SetValue(LimitProperty, value); }
		}

		public static readonly BindableProperty InvertProperty = BindableProperty.Create(nameof(Invert),
			typeof(bool),
			typeof(ScaleBarBase),
			false);
		public bool Invert
		{
			get { return (bool)GetValue(InvertProperty); }
			set { SetValue(InvertProperty, value); }
		}


	}
}
