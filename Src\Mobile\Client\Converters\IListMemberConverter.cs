﻿using AppoMobi.Framework.Maui.Converters;
using System.Collections;
using System.Globalization;

namespace Racebox.Converters;

/// <summary>
/// Gets child by index as parameter
/// </summary>
public class IListMemberConverter : ConverterBase, IValueConverter
{
    public override object OnValueReceived(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is IList list)
        {
            var strIndex = parameter as string;
            if (!string.IsNullOrEmpty(strIndex))
            {
                var index = strIndex.ToInteger();
                return list[index];
            }
        }
        return value;
    }
}
