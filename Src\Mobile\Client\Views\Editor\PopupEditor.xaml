<?xml version="1.0" encoding="utf-8" ?>
<pages:PopupPage
    x:Class="Racebox.Views.Popups.PopupEditor"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:animations="clr-namespace:Mopups.Animations;assembly=Mopups"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:pages="clr-namespace:Mopups.Pages;assembly=Mopups"
    xmlns:strings="clr-namespace:Racebox.Shared.Strings;assembly=Racebox.Shared"
    xmlns:viewModels="clr-namespace:Racebox.ViewModels"
    xmlns:popups="clr-namespace:Racebox.Views.Popups"
    xmlns:drawn="clr-namespace:Racebox.Views.Drawn"
    x:DataType="viewModels:IEditorModel"
    BackgroundColor="#CC000000"
    CloseWhenBackgroundIsClicked="False">
    
    <pages:PopupPage.Animation>
        <animations:ScaleAnimation
            DurationIn="700"
            EasingIn="Linear"
            PositionIn="Center"
            PositionOut="Center"
            ScaleIn="1"
            ScaleOut="0.7" />
    </pages:PopupPage.Animation>

    <popups:MauiPopupContent
        BackgroundColor="Transparent"
        HorizontalOptions="Fill"
        VerticalOptions="Fill">

        <draw:Canvas
            BackgroundColor="Black"
            Gestures="Lock"
            RenderingMode="Accelerated"
            HeightRequest="-1"
            HorizontalOptions="Fill"
            VerticalOptions="Center"
            Margin="50,0">

            <draw:SkiaLayout
                Padding="32"
                Spacing="24"
                HorizontalOptions="Start"
                Tag="EditorPopup"
                Type="Column">

                <draw:SkiaLabel
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    Text="{Binding EditorTitle}"
                    UseCache="Operations" />

                <draw:ContentLayout
                    Margin="0,16"
                    Content="{Binding EditorContent}"
                    HorizontalOptions="Center" />

                <drawn:SmallButton
                    Margin="0,16"
                    WidthRequest="150"
                    CornerRadius="24"                    
                    HorizontalOptions="Center"
                    Tapped="OK_Tapped"                    
                    Text="{x:Static strings:ResStrings.BtnSave}" />
 
            </draw:SkiaLayout>

        </draw:Canvas>


    </popups:MauiPopupContent>

</pages:PopupPage>
