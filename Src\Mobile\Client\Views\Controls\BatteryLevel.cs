﻿using DrawnUi.Infrastructure;
using SkiaSharp;

namespace Racebox.Views.Controls;

public class BatteryLevel : SkiaShape
{
    public static readonly BindableProperty LevelProperty = BindableProperty.Create(nameof(Level),
        typeof(double), typeof(BatteryLevel),
        100.0,
        propertyChanged: NeedInvalidateMeasure);
    /// <summary>
    /// 0-100
    /// </summary>
    public double Level
    {
        get { return (double)GetValue(LevelProperty); }
        set { SetValue(LevelProperty, value); }
    }

    public override SKSize GetSizeRequest(float widthConstraint, float heightConstraint, bool insideLayout)
    {
        if (heightConstraint > 0)
        {
            heightConstraint = (float)(heightConstraint / 100.0 * this.Level);
        }

        return base.GetSizeRequest(widthConstraint, heightConstraint, insideLayout);
    }
}