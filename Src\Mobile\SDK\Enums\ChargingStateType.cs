﻿using AppoMobi.Specials.Localization;
using Racebox.SDK.Strings;
using System.ComponentModel;

namespace Racebox.SDK;

[TypeConverter(typeof(LocalizedEnumConverter))]
[FromResources(Type = typeof(ResStrings))]
public enum ChargingStateType
{
    /// <summary>
    /// 00
    /// </summary>
    NotCharging,

    /// <summary>
    /// 01
    /// </summary>
    Precharge,

    /// <summary>
    /// 10
    /// </summary>
    FastCharging,

    /// <summary>
    /// 11
    /// </summary>
    TrickleCharging
}