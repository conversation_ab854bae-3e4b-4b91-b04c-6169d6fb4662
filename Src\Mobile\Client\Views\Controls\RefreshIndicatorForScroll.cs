using DrawnUi;
using DrawnUi.Controls;

namespace Racebox.Views.Partials;

public class RefreshIndicatorForScroll : RefreshIndicator
{
    protected SkiaLottie Loader;
    public RefreshIndicatorForScroll()
    {
    }

    protected override void OnIsRunningChanged(bool value)
    {
        base.OnIsRunningChanged(value);

        if (FindLoader())
        {
            if (!value)
            {
                if (Loader.IsPlaying)
                    Loader.Stop();
            }
        }
    }

    bool FindLoader()
    {
        if (Loader == null)
        {
            Loader = this.FindView<SkiaLottie>("Loader");
        }
        return Loader != null;
    }
}