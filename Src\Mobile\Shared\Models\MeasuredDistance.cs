﻿using Racebox.Shared.Enums;
using Racebox.Shared.Interfaces;
using Racebox.Shared.Services;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics;

namespace Racebox.Shared.Models;

[DebuggerDisplay("{Distance:0.00} time {Time:HH':'mm':'ss':'ff} speed {Speed:0.00}")]
public class MeasuredDistance : BaseEntity, IHasDetailedDisplay
{
    [NotMapped]
    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]
    public bool WasShown { get; set; }

    public bool IsUserDefined { get; set; }
    public double Length { get; set; }
    public double Speed { get; set; }
    public double MaxIncline { get; set; }
    public TimeSpan? Time { get; set; }

    public OptionsUnits Units { get; set; }

    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]
    public string DistanceDisplay
    {
        get
        {
            return LocalizedDisplayProvider.Instance.GetDistanceDisplay(Units);
        }
    }

    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]
    public string SpeedDisplay
    {
        get
        {
            return LocalizedDisplayProvider.Instance.GetAddSpeedDisplay(Units);
        }
    }


    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]

    public string Display
    {
        get
        {
            if (Time.HasValue)
            {
                return $"{Length:0}{DistanceDisplay}: {TimeDisplay}";
            }
            return $"{Length:0}{DistanceDisplay}: ???";
        }
    }

    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]

    public string Display1
    {
        get
        {
            return $"{Length:0}{DistanceDisplay}";
        }
    }

    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]

    public string Display2
    {
        get
        {
            return $"{TimeDisplay} @ {Speed:0}{LocalizedDisplayProvider.Instance.GetAddSpeedDisplay(Units)}";
        }
    }


    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]
    public string TimeDisplay
    {
        get
        {
            return LocalizedDisplayProvider.Instance.GetNullableTimeDisplay(Time);
        }
    }

    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]

    public string Say
    {
        get
        {
            return $"{Display1} - {LocalizedDisplayProvider.Instance.GetNullableTimeDisplay(Time, false)}";
        }
    }


    #region FK
    public int MeasureResultId { get; set; }

    [Newtonsoft.Json.JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]

    public MeasureResult MeasureResult { get; set; }

    #endregion

}


