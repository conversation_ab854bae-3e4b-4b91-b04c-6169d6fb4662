
namespace Racebox.Views;

public partial class PageAbout : ILazyPage
{
	public PageAbout()
	{
		try
		{
			BindingContext = new ProjectViewModel();

			InitializeComponent();

		}
		catch (Exception e)
		{
			Designer.DisplayException(this, e);
		}


	}

	protected override void OnAppearing()
	{
		base.OnAppearing();

		//Settings.UpdateStatusBarUponTheme();

		var canUpdate = BindingContext as IUpdateUIState;
		canUpdate?.UpdateState(true);
	}

	public void OnViewAppearing()
	{
		OnAppearing();
	}

	public void OnViewDisappearing()
	{
		OnDisappearing();
	}

	public void UpdateControls(DeviceRotation orientation)
	{

	}


}