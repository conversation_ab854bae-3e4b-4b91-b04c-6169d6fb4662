﻿<?xml version="1.0" encoding="utf-8" ?>
<draw:Canvas
    x:Class="Racebox.Views.Partials.ConnectionBar"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:drawn="clr-namespace:Racebox.Views.Drawn"
    xmlns:partials="clr-namespace:Racebox.Views.Partials"
    xmlns:strings="clr-namespace:Racebox.Shared.Strings;assembly=Racebox.Shared"
    xmlns:viewModels1="clr-namespace:Racebox.ViewModels"
    x:Name="ThisControlBar"
    x:DataType="viewModels1:RaceBoxDeviceViewModel"
    Gestures="Lock"
    Tag="Bar"
    HorizontalOptions="Fill"
    RenderingMode="Default"
    HeightRequest="52"
    VerticalOptions="Start">

    <draw:SkiaLayout
        UseCache="Image"
        x:Name="Container"
        HorizontalOptions="Fill"
        Tag="ConnectionBar"
        VerticalOptions="Fill">

        <!--  embossed frame cache container  -->
        <drawn:DrawnFrame CornerRadius="22" />

        <partials:SkiaSignalContainer
            x:Name="BarContainer"
            Margin="12,8,12,2"
            Padding="4,6"
            HorizontalOptions="Fill"
            UseCache="Operations"
            VerticalOptions="Fill">

            <!--  col 0 - Satellites  -->
            <draw:SkiaLayout
                Margin="6,0,0,0"
                Tag="Bugged"
                UseCache="Operations"
                VerticalOptions="Fill"
                WidthRequest="51">

                <draw:SkiaLabel
                    FontSize="16"
                    HorizontalOptions="End"
                    Style="{StaticResource SkiaLabelDefaultStyle}"
                    Text="{Binding DisplaySatellites}"
                    VerticalOptions="Center" />


                <draw:SkiaSvg
                    HeightRequest="24"
                    HorizontalOptions="Start"
                    SvgString="{StaticResource SvgSatellitesSign}"
                    Tag="Sats"
                    TintColor="#E8E3D7"
                    TranslationY="-1"
                    VerticalOptions="Center"
                    WidthRequest="24" />

            </draw:SkiaLayout>

            <!--  LINE VERTICAL  -->
            <draw:SkiaShape
                BackgroundColor="Black"
                CornerRadius="0"
                HorizontalOptions="Start"
                StrokeWidth="0"
                TranslationX="64"
                UseCache="Operations"
                VerticalOptions="Fill"
                WidthRequest="1.1">
                <draw:SkiaShape.FillGradient>

                    <draw:SkiaGradient
                        EndXRatio="0"
                        EndYRatio="1"
                        StartXRatio="0"
                        StartYRatio="0"
                        Type="Linear">
                        <draw:SkiaGradient.Colors>
                            <Color>#00E8E3D7</Color>
                            <Color>#78E8E3D7</Color>
                            <Color>#78E8E3D7</Color>
                            <Color>#00E8E3D7</Color>
                        </draw:SkiaGradient.Colors>
                        <draw:SkiaGradient.ColorPositions>
                            <x:Double>0.0</x:Double>
                            <x:Double>0.2</x:Double>
                            <x:Double>0.8</x:Double>
                            <x:Double>1.0</x:Double>
                        </draw:SkiaGradient.ColorPositions>
                    </draw:SkiaGradient>

                </draw:SkiaShape.FillGradient>
            </draw:SkiaShape>

            <draw:SkiaLayout
                Margin="65,0,0,0"
                ColumnDefinitions="*, Auto, Auto"
                ColumnSpacing="0"
                HorizontalOptions="Fill"
                Type="Grid"
                VerticalOptions="Fill">

                <!--  col 0 - SignalStrength  -->
                <draw:SkiaLayout
                    x:Name="SkiaSignal"
                    HorizontalOptions="Fill"
                    Tag="SignalBar"
                    VerticalOptions="Fill">

                    <draw:SkiaLabel
                        FontFamily="FontText"
                        FontSize="11"
                        HorizontalOptions="Center"
                        MaxLines="1"
                        Text="{x:Static strings:ResStrings.SignalStrength}"
                        TextColor="#E8E3D7"
                        TranslationY="-6"
                        VerticalOptions="Center" />

                    <!--  SIGNAL LINE  -->

                    <!--  TRACK BAR  -->
                    <draw:SkiaShape
                        Margin="8"
                        BackgroundColor="#15191E"
                        CornerRadius="3"
                        HeightRequest="6"
                        HorizontalOptions="Fill"
                        StrokeColor="Black"
                        StrokeWidth="1.0"
                        Tag="ShapeTrackSignal"
                        TranslationY="8"
                        VerticalOptions="Center">

                        <draw:SkiaShape.FillGradient>

                            <draw:SkiaGradient
                                EndXRatio="1"
                                EndYRatio="0"
                                Opacity="0.60"
                                StartXRatio="0"
                                StartYRatio="0"
                                Type="Linear">

                                <draw:SkiaGradient.Colors>
                                    <Color>#bbD8281D</Color>
                                    <Color>#bbF0E70B</Color>
                                    <Color>#bb65D210</Color>
                                    <Color>#bb65D210</Color>
                                </draw:SkiaGradient.Colors>

                                <draw:SkiaGradient.ColorPositions>
                                    <x:Double>0.0</x:Double>
                                    <x:Double>0.25</x:Double>
                                    <x:Double>0.50</x:Double>
                                    <x:Double>1.0</x:Double>
                                </draw:SkiaGradient.ColorPositions>

                            </draw:SkiaGradient>

                        </draw:SkiaShape.FillGradient>

                        <draw:SkiaShape.Shadows>

                            <draw:SkiaShadow
                                Blur="0.75"
                                Opacity="0.20"
                                X="1.5"
                                Y="1.5"
                                Color="#FFFFFF" />

                            <draw:SkiaShadow
                                Blur="0.75"
                                Opacity="0.45"
                                X="-1.5"
                                Y="-1.5"
                                Color="#000000" />

                        </draw:SkiaShape.Shadows>

                        <draw:SkiaShape.StrokeGradient>

                            <draw:SkiaGradient
                                EndXRatio="0.2"
                                EndYRatio="0.8"
                                StartXRatio="0.2"
                                StartYRatio="0.2"
                                Type="Linear">
                                <draw:SkiaGradient.Colors>
                                    <Color>#15191E</Color>
                                    <Color>#42464B</Color>
                                </draw:SkiaGradient.Colors>
                            </draw:SkiaGradient>

                        </draw:SkiaShape.StrokeGradient>

                        <!--  INVERTED PLUS <=  -->
                        <!--
                            "{Binding Source={x:Reference ThisControlBar},
                            Path=Value}"
                            ColorPositions="{Binding Source={x:Reference ThisControlBar}, Path=Points}"
                        -->
                        <partials:SignalInverter
                            x:Name="Inverter"
                            BackgroundColor="Black"
                            HorizontalOptions="End"
                            VerticalOptions="Fill"
                            ZIndex="100"
                            Value="{Binding Source={x:Reference ThisControlBar}, Path=Value}">

                            <draw:SkiaControl.FillGradient>
                                <draw:SkiaGradient
                                    ColorPositions="{Binding Source={x:Reference Inverter}, Path=Points}"
                                    EndXRatio="1"
                                    EndYRatio="0"
                                    Opacity="1"
                                    StartXRatio="0"
                                    StartYRatio="0"
                                    Type="Linear">
                                    <draw:SkiaGradient.Colors>
                                        <Color>#0015191E</Color>
                                        <Color>#15191E</Color>
                                        <Color>#15191E</Color>
                                    </draw:SkiaGradient.Colors>

                                    <!--<draw:SkiaGradient.ColorPositions>
                                 <x:Double>0.0</x:Double>
                                 <x:Double>0.05</x:Double>
                                 <x:Double>1.0</x:Double>
                             </draw:SkiaGradient.ColorPositions>-->

                                </draw:SkiaGradient>
                            </draw:SkiaControl.FillGradient>


                        </partials:SignalInverter>

                    </draw:SkiaShape>

                </draw:SkiaLayout>

                <!--  col 1 - Power Icon  -->
                <draw:SkiaLayout
                    Grid.Column="1"
                    IsVisible="{Binding HasBattery}"
                    Tag="Power"
                    VerticalOptions="Fill"
                    WidthRequest="41">

                    <!--  LINE VERTICAL  -->
                    <draw:SkiaShape
                        BackgroundColor="Black"
                        CornerRadius="0"
                        StrokeWidth="0"
                        UseCache="Operations"
                        VerticalOptions="Fill"
                        WidthRequest="1.1">
                        <draw:SkiaShape.FillGradient>

                            <draw:SkiaGradient
                                EndXRatio="0"
                                EndYRatio="1"
                                StartXRatio="0"
                                StartYRatio="0"
                                Type="Linear">
                                <draw:SkiaGradient.Colors>
                                    <Color>#00E8E3D7</Color>
                                    <Color>#78E8E3D7</Color>
                                    <Color>#78E8E3D7</Color>
                                    <Color>#00E8E3D7</Color>
                                </draw:SkiaGradient.Colors>
                                <draw:SkiaGradient.ColorPositions>
                                    <x:Double>0.0</x:Double>
                                    <x:Double>0.2</x:Double>
                                    <x:Double>0.8</x:Double>
                                    <x:Double>1.0</x:Double>
                                </draw:SkiaGradient.ColorPositions>
                            </draw:SkiaGradient>

                        </draw:SkiaShape.FillGradient>
                    </draw:SkiaShape>


                    <!--  BATTERY LEVEL  -->
                    <draw:SkiaShape
                        BackgroundColor="#65D210"
                        HeightRequest="19"
                        HeightRequestRatio="{Binding DisplayBateryLevel}"
                        HorizontalOptions="Center"
                        AddMarginBottom="5"
                        VerticalOptions="End"
                        WidthRequest="10" />

                    <draw:SkiaSvg
                        x:Name="IconPower"
                        BackgroundColor="Transparent"
                        HeightRequest="24"
                        HorizontalOptions="Center"
                        AddMarginLeft="1"
                        SvgHorizontalOptions="Center"
                        SvgString="{StaticResource SvgBattery}"
                        Tag="IconPower"
                        TintColor="#E8E3D7"
                        UseCache="Operations"
                        VerticalOptions="Center"
                        WidthRequest="27" />

                    <draw:SkiaHotspot
                        CommandTapped="{Binding CommandSwitchConnect, Mode=OneTime}"
                        IsVisible="{Binding IsBusy, Converter={x:StaticResource NotConverter}}"
                        AddMarginLeft="1"
                        TransformView="{x:Reference BarContainer}" />

                </draw:SkiaLayout>

                <!--  col 2 - BLE Icon  -->
                <draw:SkiaLayout
                    Grid.Column="2"
                    BackgroundColor="Transparent"
                    Tag="BT"
                    VerticalOptions="Fill"
                    WidthRequest="41">

                    <!--  LINE VERTICAL  -->
                    <draw:SkiaShape
                        BackgroundColor="Black"
                        CornerRadius="0"
                        StrokeWidth="0"
                        UseCache="Operations"
                        VerticalOptions="Fill"
                        WidthRequest="1.1">
                        <draw:SkiaShape.FillGradient>

                            <draw:SkiaGradient
                                EndXRatio="0"
                                EndYRatio="1"
                                StartXRatio="0"
                                StartYRatio="0"
                                Type="Linear">
                                <draw:SkiaGradient.Colors>
                                    <Color>#00E8E3D7</Color>
                                    <Color>#78E8E3D7</Color>
                                    <Color>#78E8E3D7</Color>
                                    <Color>#00E8E3D7</Color>
                                </draw:SkiaGradient.Colors>
                                <draw:SkiaGradient.ColorPositions>
                                    <x:Double>0.0</x:Double>
                                    <x:Double>0.2</x:Double>
                                    <x:Double>0.8</x:Double>
                                    <x:Double>1.0</x:Double>
                                </draw:SkiaGradient.ColorPositions>
                            </draw:SkiaGradient>

                        </draw:SkiaShape.FillGradient>
                    </draw:SkiaShape>

                    <draw:SkiaSvg
                        x:Name="IconBT"
                        BackgroundColor="Transparent"
                        HeightRequest="26"
                        HorizontalOptions="Center"
                        AddMarginLeft="1"
                        SvgString="{StaticResource SvgBleSign}"
                        Tag="IconBT"
                        TintColor="Gray"
                        UseCache="Operations"
                        VerticalOptions="Center"
                        WidthRequest="27" />

                    <!--  IsVisible="{Binding IsBusy, Converter={x:StaticResource NotConverter}}"  -->
                    <draw:SkiaHotspot
                        CommandTapped="{Binding CommandSwitchConnect, Mode=OneTime}"
                        AddMarginLeft="1"
                        TransformView="{x:Reference BarContainer}" />

                </draw:SkiaLayout>

            </draw:SkiaLayout>

        </partials:SkiaSignalContainer>

    </draw:SkiaLayout>

</draw:Canvas>
