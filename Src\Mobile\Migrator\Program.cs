﻿// See https://aka.ms/new-console-template for more information

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Racebox.Shared.Services;

Console.WriteLine("Migrator project - used for EF migrations only");

// Do not instantiate LocalDatabase here as it calls Database.Migrate()
// which makes the model read-only and prevents migrations from being created

/// <summary>
/// Design-time factory for LocalDatabase that doesn't call Database.Migrate()
/// This allows EF migrations to work properly
/// </summary>
public class LocalDatabaseFactory : IDesignTimeDbContextFactory<LocalDatabase>
{
    public LocalDatabase CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<LocalDatabase>();
        optionsBuilder.UseSqlite("Data Source=temp.db"); // Temporary connection string for design time

        return new LocalDatabase(optionsBuilder.Options, skipInitialization: true);
    }
}
