﻿
using Racebox.Shared.Enums;
using Racebox.Shared.Strings;

namespace Racebox.Shared.Models;

public class LocalSpeedRange : OptionItem
{
    public double Start { get; set; }
    public double End { get; set; }
    public OptionsUnits Units { get; set; }

    public LocalSpeedRange()
    {
        Init();
    }

    public LocalSpeedRange(double start, double end, OptionsUnits units, string id = null, bool argIsReadOnly = false)
    {
        Start = start;
        End = end;
        Units = units;
        IsReadOnly = argIsReadOnly;
        Id = id;
        Init();
    }

    public void Init()
    {
        Id = string.IsNullOrEmpty(Id) ? Guid.NewGuid().ToString() : Id;
        Title = $"{Start:0}-{End:0} {LocalSpeedRange.GetUnitsDisplay(Units)}";
    }

    public static string GetUnitsDisplay(OptionsUnits units)
    {
        if (units == OptionsUnits.US)
            return ResStrings.Mph;
        return ResStrings.Kmh;
    }
}