Нужно разработать MVP универсального приложения на iOS и Android к прибору для замера динамики автомобиля (время разгона). Прибор работает как внешний GPS-приёмник и вещает навигационные данные по каналу BLE (простой протокол, только приём пакетов). В приложении нужно реализовать:
- Подключение к прибору
- Парсинг BLE-пакетов с навигационными данными (свой протокол)
- Расчёт параметров движения (ускорения, уклон дороги)
- Вывод данных на экран "Приборы"
- Алгоритм замера времени разгона автомобиля и отображение результатов на экране "Замер"
- Запись лога во время замера в текстовом формате
- Сохранение результатов и их отображение списка замеров на экране "История"
- Показ экрана с детальной информацией результатов замера
- Выбор и изменение настроек приложения на экране "Настройки"

Отладка приложения производится на реальном приборе с тестовыми данными.
Логика замера разгона должна копировать логику, используемую в приборе (есть исходники на C).