<?xml version="1.0" encoding="utf-8" ?>
<views2:BasePage
    x:Class="Racebox.Views.PageEditCar"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:partials1="clr-namespace:Racebox.Views.Partials"
    xmlns:strings="clr-namespace:Racebox.Shared.Strings;assembly=Racebox.Shared"
    xmlns:touch="clr-namespace:AppoMobi.Maui.Gestures;assembly=AppoMobi.Maui.Gestures"
    xmlns:viewModels1="clr-namespace:Racebox.ViewModels"
    xmlns:views2="clr-namespace:Racebox.Views"
    xmlns:controls="clr-namespace:AppoMobi.Framework.Maui.Controls;assembly=AppoMobi.Framework.Maui"
    x:Name="ThisPage"
    Title="PageEditSpeedRange"
    x:DataType="viewModels1:EditCarViewModel">

    <Grid
        HorizontalOptions="FillAndExpand"
        VerticalOptions="FillAndExpand">

        <!--<Grid.Background>
            <LinearGradientBrush EndPoint="0,1">
                <GradientStop Offset="0.0" Color="#343C45" />
                <GradientStop Offset="1.0" Color="#11161D" />
            </LinearGradientBrush>
        </Grid.Background>-->

        <partials1:GradientToBorderView 
            MissingHeight="{Binding NavbarModel.BottomTabsHeightRequest}"
            VerticalOptions="Fill" HorizontalOptions="Fill"/>


        <partials1:EmbossedFrame
            Padding="12,16,12,8"
            VerticalOptions="FillAndExpand">
            <partials1:EmbossedFrame.Content>

                <Grid
                    RowDefinitions="65,*"
                    RowSpacing="0">

                    <!--  NEW NAVBAR  -->
                    <draw:Canvas
                        Gestures="Enabled"
                        
                        HeightRequest="65"
                        HorizontalOptions="Fill"
                        VerticalOptions="Start">
                        <partials1:SkiaNavBar
                            x:Name="NavBar"
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill">

                            <draw:SkiaSvg
                                Margin="16,0,16,0"
                                HeightRequest="16"
                                HorizontalOptions="Start"
                                SvgString="{StaticResource SvgGoBack}"
                                TintColor="#CB6336"
                                VerticalOptions="Center"
                                WidthRequest="16" />

                            <draw:SkiaHotspot
                                CommandTapped="{Binding NavbarModel.CommandGoBack, Mode=OneTime}"
                                HorizontalOptions="Start"
                                TransformView="{x:Reference NavBar}"
                                WidthRequest="44" />

                            <draw:SkiaLabel
                                Margin="48,0,16,0"
                                FontSize="14"
                                HorizontalOptions="Start"
                                LineBreakMode="TailTruncation"
                                MaxLines="1"
                                Style="{StaticResource SkiaLabelDefaultStyle}"
                                Text="{x:Static strings:ResStrings.Car}"
                                TextColor="#E8E3D7"
                                TranslationY="1"
                                VerticalOptions="Center" />

                            <!--  LINE HORIZONTAL  -->
                            <draw:SkiaShape
                                Margin="-16,0"
                                BackgroundColor="Black"
                                CornerRadius="0"
                                HeightRequest="1"
                                HorizontalOptions="Fill"
                                StrokeWidth="0"
                                VerticalOptions="End">
                                <draw:SkiaShape.FillGradient>

                                    <draw:SkiaGradient
                                        EndXRatio="1"
                                        EndYRatio="0"
                                        StartXRatio="0"
                                        StartYRatio="0"
                                        Type="Linear">
                                        <draw:SkiaGradient.Colors>
                                            <Color>#00E8E3D7</Color>
                                            <Color>#99E8E3D7</Color>
                                            <Color>#00E8E3D7</Color>
                                        </draw:SkiaGradient.Colors>
                                    </draw:SkiaGradient>

                                </draw:SkiaShape.FillGradient>
                            </draw:SkiaShape>

                        </partials1:SkiaNavBar>
                    </draw:Canvas>

                    <ScrollView Grid.Row="1">
                        <StackLayout
                            Padding="16"
                            Spacing="16"
                            VerticalOptions="StartAndExpand">

                            <Label
                                Style="{StaticResource EditorFieldTitle}"
                                Text="{x:Static strings:ResStrings.Brand}" />

                            <controls:AMEntry
                                Padding="8,4"
                                Placeholder="..."
                                Text="{Binding Brand}" />

                            <Label
                                IsVisible="{Binding Source={RelativeSource Self}, Path=Text, Converter={StaticResource StringNotEmptyConverter}}"
                                Style="{StaticResource ValidationErrorLabel}"
                                Text="{Binding FieldValidators, Converter={StaticResource ValidatorErrorMessageConverter}, ConverterParameter=brand}" />

                            <Label
                                Style="{StaticResource EditorFieldTitle}"
                                Text="{x:Static strings:ResStrings.Model}" />

                            <controls:AMEntry
                                Padding="8,4"
                                Placeholder="..."
                                Text="{Binding Model}" />

                            <Label
                                IsVisible="{Binding Source={RelativeSource Self}, Path=Text, Converter={StaticResource StringNotEmptyConverter}}"
                                Style="{StaticResource ValidationErrorLabel}"
                                Text="{Binding FieldValidators, Converter={StaticResource ValidatorErrorMessageConverter}, ConverterParameter=model}" />

                            <Label
                                Style="{StaticResource EditorFieldTitle}"
                                Text="{x:Static strings:ResStrings.Description}" />

                            <controls:AMEntry
                                Padding="8,4"
                                Placeholder="..."
                                Text="{Binding Description}" />

                            <Label
                                IsVisible="{Binding Source={RelativeSource Self}, Path=Text, Converter={StaticResource StringNotEmptyConverter}}"
                                Style="{StaticResource ValidationErrorLabel}"
                                Text="{Binding FieldValidators, Converter={StaticResource ValidatorErrorMessageConverter}, ConverterParameter=description}" />

                            <!--  SAVE BTN  -->
                            <partials1:ButtonSmall
                                Margin="0,16"
                                IsDisabled="{Binding IsBusy}"
                                CommandTapped="{Binding CommandSubmitForm, Mode=OneTime}"
                                Text="{x:Static strings:ResStrings.BtnSave}"/>

                            <!--touch:TouchEffect.AnimationTapped="Ripple"
                            touch:TouchEffect.AnimationView="{x:Reference BtnBackground}"
                            touch:TouchEffect.CommandTapped="{Binding CommandSubmitForm}"-->

                            <!--<draw:SkiaPanel
                                Gestures="Enabled"
                                
                                Margin="0,16"
                                HeightRequest="36"
                                HorizontalOptions="Center"
                                VerticalOptions="Center"
                                WidthRequest="120">
                                <draw:SkiaLayout
                                    HorizontalOptions="Fill"
                                    VerticalOptions="Fill">

                                    <draw:SkiaShape
                                        x:Name="BtnBackground"
                                        BackgroundColor="#00000000"
                                        CornerRadius="24"
                                        HorizontalOptions="Fill"
                                        StrokeColor="#CB6336"
                                        StrokeWidth="1.85"
                                        VerticalOptions="Fill" />

                                    <draw:SkiaLabel
                                        FontFamily="FontText"
                                        FontSize="16"
                                        HorizontalOptions="Center"
                                        Text="{x:Static strings:ResStrings.BtnSave}"
                                        TextColor="#CB6336"
                                        TranslationY="0"
                                        VerticalOptions="Center" />

                                    <draw:SkiaHotspot
                                        CommandTapped="{Binding CommandSubmitForm, Mode=OneTime}"
                                        HorizontalOptions="Fill"
                                        IsVisible="{Binding IsBusy, Converter={x:StaticResource NotConverter}}"
                                        TransformView="{x:Reference BtnBackground}"
                                        VerticalOptions="Fill" />

                                </draw:SkiaLayout>
                            </draw:SkiaPanel>-->

                            <!--<StackLayout
                            BackgroundColor="Red"
                            HeightRequest="{Binding NavbarModel.BottomSafeInsetsOrKeyboard}"
                            MinimumHeightRequest="{Binding NavbarModel.BottomSafeInsetsOrKeyboard}"
                            VerticalOptions="Start" />-->

                        </StackLayout>

                    </ScrollView>



                </Grid>

            </partials1:EmbossedFrame.Content>
        </partials1:EmbossedFrame>

    </Grid>

</views2:BasePage>