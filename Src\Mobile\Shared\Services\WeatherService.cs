﻿using DrawnUi.Draw;
using Racebox.ApiClient;
using Racebox.ApiClient.Dto;
using Racebox.Shared.Models;
using Racebox.Shared.WeatherApi;
using System.Diagnostics;
using System.Globalization;
using System.Text.RegularExpressions;

namespace Racebox.Shared.Services;


public class WeatherService
{
    public WeatherService(IHttpClientFactory factory)
    {
        _http = factory.CreateClient("weatherApi");
        _culture = CultureInfo.CreateSpecificCulture("en");
    }

    HttpClient _http;
    private readonly CultureInfo _culture;
    public bool IsBusy { get; protected set; }
    public CurrentWeather CurrentWeather { get; protected set; }

    /// <summary>
    /// Safe to use without try-catch, will return this.CurrentWeather (last loaded) in case of error
    /// </summary>
    /// <param name="lat"></param>
    /// <param name="lon"></param>
    /// <returns></returns>
    public async Task<CurrentWeather> GetCurrentWeather(double lat, double lon)
    {
        return await GetCurrentWeather(lat.ToString(_culture), lon.ToString(_culture));
    }


    /// <summary>
    /// Safe to use without try-catch, will return this.CurrentWeather (last loaded) in case of error
    /// </summary>
    /// <param name="lat"></param>
    /// <param name="lon"></param>
    /// <param name="forceRefresh">Will not use cache</param>
    /// <returns></returns>
    public async Task<CurrentWeather> GetCurrentWeather(string lat, string lon, bool forceRefresh = false)
    {
        if (IsBusy)
            return null;

        if (forceRefresh || ((CurrentWeather == null || CurrentWeather != null &&
            DateTime.UtcNow - CurrentWeather.LastUpdatedUtc > TimeSpan.FromMinutes(10))))
        {
            IsBusy = true;

            try
            {
                var location = $"{lat} {lon}".Replace(",", ".");
                //Super.Log($"[WEATHER] getting for {location}");
                var result = await _http.GetCurrentWeather(SharedSecrets.WeatherApiKey,
                    location);

                if (result != null && result.Data != null)
                {
#if DEBUG
                    Super.Log($"[WEATHER] gotten for {result.Data.Location.Country}, {result.Data.Location.Name} Location {location}");
#endif
                    var item = new CurrentWeather
                    {
                        Condition = result.Data.Current.Condition.Code,
                        Icon = Fonts.WeatherMappings.GetIcon(result.Data.Current.Condition.Code),
                        LastUpdatedUtc = result.Data.Current.LastUpdatedUtc,
                        LocalDateTime = result.Data.Location.LocalDateTime,
                        TempC = result.Data.Current.TempC,
                        TempF = result.Data.Current.TempF,
                    };
                    CurrentWeather = item;
                }
            }
            catch (Exception e)
            {
                Debug.WriteLine(e); //todo add logger?..
            }
            finally
            {
                IsBusy = false;
            }
        }

        return CurrentWeather;
    }

}


public class CurrentWeather
{
    /// <summary>
    /// Font Awesome Pro corresponding Icon
    /// </summary>
    public string Icon { get; set; }

    /// <summary>
    /// Weather condition https://www.weatherapi.com/docs/weather_conditions.json
    /// </summary>
    public int Condition { get; set; }

    /// <summary>
    /// Temperature in Celsius
    /// </summary>
    public decimal TempC { get; set; }

    /// <summary>
    /// Temperature in Fahrenheit
    /// </summary>
    public decimal TempF { get; set; }

    public DateTime LastUpdatedUtc { get; set; }

    public DateTime LocalDateTime { get; set; }
}

