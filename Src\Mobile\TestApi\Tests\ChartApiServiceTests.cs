using Microsoft.VisualStudio.TestTools.UnitTesting;
using TestApi.Services;
using TestApi.Models;
using Newtonsoft.Json;
using System.Text;

namespace TestApi.Tests;

[TestClass]
public class ChartApiServiceTests
{
    private IChartApiService _service = null!;
    private string _testOutputDirectory = null!;

    [TestInitialize]
    public void Setup()
    {
        _service = new ChartApiService();
        _testOutputDirectory = Path.Combine(Path.GetTempPath(), "RaceboxChartTests", Guid.NewGuid().ToString());
        Directory.CreateDirectory(_testOutputDirectory);
    }

    [TestCleanup]
    public void Cleanup()
    {
        _service?.Dispose();
        
        if (Directory.Exists(_testOutputDirectory))
        {
            try
            {
                Directory.Delete(_testOutputDirectory, true);
            }
            catch
            {
                // Ignore cleanup failures
            }
        }
    }

    [TestMethod]
    public async Task GenerateChartAsync_NullCsvData_ReturnsError()
    {
        // Arrange
        var metadata = CreateTestMetadata();
        var metadataJson = JsonConvert.SerializeObject(metadata);

        // Act
        var result = await _service.GenerateChartAsync(null!, metadataJson, _testOutputDirectory);

        // Assert
        Assert.IsFalse(result.IsSuccess);
        Assert.AreEqual("CSV data is null or empty", result.ErrorMessage);
    }

    [TestMethod]
    public async Task GenerateChartAsync_EmptyCsvData_ReturnsError()
    {
        // Arrange
        var emptyData = Array.Empty<byte>();
        var metadata = CreateTestMetadata();
        var metadataJson = JsonConvert.SerializeObject(metadata);

        // Act
        var result = await _service.GenerateChartAsync(emptyData, metadataJson, _testOutputDirectory);

        // Assert
        Assert.IsFalse(result.IsSuccess);
        Assert.AreEqual("CSV data is null or empty", result.ErrorMessage);
    }

    [TestMethod]
    public async Task GenerateChartAsync_NullMetadata_ReturnsError()
    {
        // Arrange
        var csvData = Encoding.UTF8.GetBytes("test,data\n1,2");

        // Act
        var result = await _service.GenerateChartAsync(csvData, null!, _testOutputDirectory);

        // Assert
        Assert.IsFalse(result.IsSuccess);
        Assert.AreEqual("Metadata JSON is null or empty", result.ErrorMessage);
    }

    [TestMethod]
    public async Task GenerateChartAsync_EmptyMetadata_ReturnsError()
    {
        // Arrange
        var csvData = Encoding.UTF8.GetBytes("test,data\n1,2");

        // Act
        var result = await _service.GenerateChartAsync(csvData, string.Empty, _testOutputDirectory);

        // Assert
        Assert.IsFalse(result.IsSuccess);
        Assert.AreEqual("Metadata JSON is null or empty", result.ErrorMessage);
    }

    [TestMethod]
    public async Task GenerateChartAsync_InvalidJson_ReturnsError()
    {
        // Arrange
        var csvData = Encoding.UTF8.GetBytes("test,data\n1,2");
        var invalidJson = "{invalid json}";

        // Act
        var result = await _service.GenerateChartAsync(csvData, invalidJson, _testOutputDirectory);

        // Assert
        Assert.IsFalse(result.IsSuccess);
        Assert.IsTrue(result.ErrorMessage!.StartsWith("Invalid metadata JSON:"));
    }

    [TestMethod]
    public async Task GenerateChartAsync_NullOutputDirectory_ReturnsError()
    {
        // Arrange
        var csvData = Encoding.UTF8.GetBytes("test,data\n1,2");
        var metadata = CreateTestMetadata();
        var metadataJson = JsonConvert.SerializeObject(metadata);

        // Act
        var result = await _service.GenerateChartAsync(csvData, metadataJson, null!);

        // Assert
        Assert.IsFalse(result.IsSuccess);
        Assert.AreEqual("Output directory is null or empty", result.ErrorMessage);
    }

    [TestMethod]
    public async Task GenerateChartAsync_ValidInputs_CreatesOutputDirectory()
    {
        // Arrange
        var nonExistentDirectory = Path.Combine(Path.GetTempPath(), "NonExistent_" + Guid.NewGuid());
        var csvData = GetTestCsvData();
        var metadata = CreateTestMetadata();
        var metadataJson = JsonConvert.SerializeObject(metadata);

        try
        {
            // Act
            var result = await _service.GenerateChartAsync(csvData, metadataJson, nonExistentDirectory);

            // Assert (either success or network error, but directory should be created)
            Assert.IsTrue(Directory.Exists(nonExistentDirectory));
        }
        finally
        {
            // Cleanup
            if (Directory.Exists(nonExistentDirectory))
            {
                try
                {
                    Directory.Delete(nonExistentDirectory, true);
                }
                catch { }
            }
        }
    }

    [TestMethod]
    public async Task GenerateChartAsync_CancellationRequested_ReturnsCancelledError()
    {
        // Arrange
        var csvData = GetTestCsvData();
        var metadata = CreateTestMetadata();
        var metadataJson = JsonConvert.SerializeObject(metadata);
        var cancellationTokenSource = new CancellationTokenSource();
        cancellationTokenSource.Cancel(); // Cancel immediately

        // Act
        var result = await _service.GenerateChartAsync(csvData, metadataJson, _testOutputDirectory, cancellationTokenSource.Token);

        // Assert
        Assert.IsFalse(result.IsSuccess);
        Assert.AreEqual("Request was cancelled", result.ErrorMessage);
    }

    [TestMethod, Timeout(10000)] // 10 second timeout for integration test
    public async Task GenerateChartAsync_ValidRealData_AttemptsApiCall()
    {
        // This is more of an integration test that will attempt to call the real API
        // It may fail due to network issues, but we can verify the service behavior

        // Arrange
        var csvData = GetRealTestCsvData();
        var metadata = CreateRealTestMetadata();
        var metadataJson = JsonConvert.SerializeObject(metadata, Formatting.Indented);

        // Act
        var result = await _service.GenerateChartAsync(csvData, metadataJson, _testOutputDirectory);

        // Assert
        if (result.IsSuccess)
        {
            // If API call succeeded
            Assert.IsNotNull(result.FilePath);
            Assert.IsNotNull(result.Filename);
            Assert.IsTrue(result.FileSize > 0);
            Assert.IsNotNull(result.ResponseTime);
            Assert.IsTrue(File.Exists(result.FilePath));
        }
        else
        {
            // If API call failed (network issues, timeout, etc.)
            Assert.IsNotNull(result.ErrorMessage);
            Assert.IsTrue(
                result.ErrorMessage.Contains("Network error") ||
                result.ErrorMessage.Contains("Request timed out") ||
                result.ErrorMessage.Contains("API Error"));
        }
    }

    [TestMethod]
    public void Dispose_MultipleCallsToDispose_DoesNotThrow()
    {
        // Arrange & Act & Assert
        _service.Dispose();
        _service.Dispose(); // Should not throw
    }

    private static ChartMetadata CreateTestMetadata()
    {
        return new ChartMetadata
        {
            Name = "test.csv",
            Size = 1000,
            Type = "accel",
            DataSource = "app",
            Meta = new MetaInfo
            {
                Lang = "en",
                User = "Test User",
                Vehicle = "Test Vehicle",
                Descr = "Test Description",
                Share = 0,
                Date = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")
            },
            Spd = new Dictionary<string, string> { { "0-60", "5.0" } },
            Dst = new Dictionary<string, string[]> { { "60 ft", new[] { "3.0", "50" } } },
            Rng = new Dictionary<string, string>()
        };
    }

    private static ChartMetadata CreateRealTestMetadata()
    {
        return new ChartMetadata
        {
            Name = $"AccelLog_{DateTime.Now:yyyy-MM-dd_HHmmss}.csv",
            Size = 44814,
            Type = "accel",
            DataSource = "app",
            Meta = new MetaInfo
            {
                Lang = "en",
                User = "Unit Test User",
                Vehicle = "Test BMW X1",
                Descr = "Unit test generated",
                Share = 0,
                Date = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")
            },
            Spd = new Dictionary<string, string>
            {
                { "0-60", "3.34" },
                { "0-100", "7.25" },
                { "0-150", "15.93" },
                { "0-200", "38.31" }
            },
            Dst = new Dictionary<string, string[]>
            {
                { "60 ft", new[] { "2.41", "46" } },
                { "201 m", new[] { "9.78", "119" } },
                { "402 m", new[] { "15.18", "147" } }
            },
            Rng = new Dictionary<string, string>
            {
                { "100-200", "31.06" }
            }
        };
    }

    private static byte[] GetTestCsvData()
    {
        var csvContent = """
            Total Time (s);Lat;Lon;Speed (km/h);LonAccel (G);Distance (m);Alt (m);Incline (%);Course (deg);HDOP;Sats
            0.18;56.7916267;60.5735093;2.5;0.06;1.9;268.8;0.0;296;0.7;15
            3.34;56.7916267;60.5735093;60.5;0.06;18.5;268.8;0.0;296;0.7;15
            """;
        return Encoding.UTF8.GetBytes(csvContent);
    }

    private static byte[] GetRealTestCsvData()
    {
        // This would ideally load from the actual test CSV file
        // For now, we'll use inline data that matches the expected format
        var csvContent = """
            Total Time (s);Lat;Lon;Speed (km/h);LonAccel (G);Distance (m);Alt (m);Incline (%);Course (deg);HDOP;Sats
            0.18;56.7916267;60.5735093;2.5;0.06;1.9;268.8;0.0;296;0.7;15
            3.34;56.7916267;60.5735093;60.5;0.06;18.5;268.8;0.0;296;0.7;15
            7.25;56.7916267;60.5735093;100.2;0.06;50.1;268.8;0.0;296;0.7;15
            15.93;56.7916267;60.5735093;150.8;0.06;120.3;268.8;0.0;296;0.7;15
            38.31;56.7916267;60.5735093;200.5;0.06;800.3;268.8;0.0;296;0.7;15
            """;
        return Encoding.UTF8.GetBytes(csvContent);
    }
}