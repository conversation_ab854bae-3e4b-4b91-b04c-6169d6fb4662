﻿namespace Racebox.Helpers.Validation
{
    public class FieldValidator
    {
        public FieldValidator()
        {

        }

        public FieldValidator(Func<FieldValidator, bool> validate)
        {
            ValidateFunc = validate;
        }

        public virtual bool Validate(object value)
        {
            return ValidateFunc.Invoke(this);
        }
        public bool ValueChanged { get; set; }

        public Func<FieldValidator, bool> ValidateFunc { get; set; }

        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
    }



}
