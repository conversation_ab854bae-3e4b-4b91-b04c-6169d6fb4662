<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="14868" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="PPP-Tk-X5K">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment version="4864" identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14824"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--История-->
        <scene sceneID="BlM-vt-N4u">
            <objects>
                <viewController id="wuD-eV-fLb" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="zdv-fP-dKF"/>
                        <viewControllerLayoutGuide type="bottom" id="eO9-dp-eHO"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="Dqf-NP-N3V">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
                    </view>
                    <tabBarItem key="tabBarItem" title="История" image="clock" catalog="system" id="BAi-E2-OqY"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="uyy-JK-spa" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="121" y="-528"/>
        </scene>
        <!--Настройки-->
        <scene sceneID="U08-ex-bqm">
            <objects>
                <viewController id="R5y-Kp-5fX" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="lIn-Tg-1MK"/>
                        <viewControllerLayoutGuide type="bottom" id="t7V-AV-wMY"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="sv6-Od-lVK">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
                    </view>
                    <tabBarItem key="tabBarItem" title="Настройки" image="gear" catalog="system" id="9ua-My-pFx"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="I3H-lK-IiU" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="999" y="-528"/>
        </scene>
        <!--Разгон-->
        <scene sceneID="2L7-q5-7UU">
            <objects>
                <viewController id="cWD-zK-8jn" customClass="AccelViewController" customModule="Racebox" customModuleProvider="target" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="vJh-TB-aMi"/>
                        <viewControllerLayoutGuide type="bottom" id="qde-CS-mdW"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="Xj8-PZ-0Yh">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
                    </view>
                    <tabBarItem key="tabBarItem" title="Разгон" image="speedometer" catalog="system" id="FEn-3n-DLv"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="3fa-7j-fZr" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-815" y="-528"/>
        </scene>
        <!--Tab Bar Controller-->
        <scene sceneID="0gX-Mn-icj">
            <objects>
                <tabBarController id="PPP-Tk-X5K" sceneMemberID="viewController">
                    <tabBar key="tabBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="wgm-Af-EkX">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="49"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </tabBar>
                    <connections>
                        <segue destination="cWD-zK-8jn" kind="relationship" relationship="viewControllers" id="vnG-lC-xmW"/>
                        <segue destination="9Pu-vz-22V" kind="relationship" relationship="viewControllers" id="VVb-lK-cKX"/>
                        <segue destination="wuD-eV-fLb" kind="relationship" relationship="viewControllers" id="KRc-MI-UWd"/>
                        <segue destination="R5y-Kp-5fX" kind="relationship" relationship="viewControllers" id="e7l-LS-0ph"/>
                    </connections>
                </tabBarController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="yPe-Df-KT3" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="121" y="-1358"/>
        </scene>
        <!--Информация GPS-->
        <scene sceneID="3l9-QK-tAN">
            <objects>
                <viewController id="9Pu-vz-22V" customClass="InfoViewController" customModule="Racebox" customModuleProvider="target" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="dMi-f7-hVa"/>
                        <viewControllerLayoutGuide type="bottom" id="bQj-Tb-Mkf"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="BcI-Uw-D2L">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="--" textAlignment="right" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="AGg-zv-bff">
                                <rect key="frame" x="139" y="30" width="170" height="90"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="90" id="MaM-JY-ZD0"/>
                                    <constraint firstAttribute="width" constant="170" id="jeu-dE-ZHp"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="55"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Спутники" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontForContentSizeCategory="YES" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WGB-AS-U5a">
                                <rect key="frame" x="20" y="270" width="167.5" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="sUs-HB-CKn"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="25"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="HDOP" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontForContentSizeCategory="YES" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="seU-eY-Rg3">
                                <rect key="frame" x="187.5" y="270" width="167.5" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="ZUS-6t-4ws"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="25"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Высота" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontForContentSizeCategory="YES" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lMT-KF-LnN">
                                <rect key="frame" x="20" y="210" width="167.5" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="5JQ-3a-fOQ"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="25"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Широта" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontForContentSizeCategory="YES" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="s5k-GT-aEB">
                                <rect key="frame" x="20" y="130" width="142.5" height="70"/>
                                <color key="tintColor" systemColor="systemPinkColor" red="1" green="0.17647058823529413" blue="0.33333333333333331" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="70" id="MzB-cn-kyE"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="25"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Долгота" textAlignment="right" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontForContentSizeCategory="YES" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bW3-iL-30s">
                                <rect key="frame" x="212.5" y="130" width="142.5" height="70"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="70" id="jwD-XT-96p"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="25"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Курс" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontForContentSizeCategory="YES" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="OqM-0P-MxT">
                                <rect key="frame" x="187.5" y="210" width="167.5" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="Rcw-Uf-9Q4"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="25"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Дата" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="RzW-4l-oYQ" userLabel="Date String">
                                <rect key="frame" x="20" y="330" width="335" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="cDC-rB-HAP"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="25"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Время" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9mE-EV-7hC" userLabel="Time String">
                                <rect key="frame" x="20" y="390" width="335" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="N4h-51-K1g"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="25"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Частота обновления" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="NEj-7b-mOX">
                                <rect key="frame" x="20" y="450" width="335" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="m1f-Ox-B4u"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="25"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="speedometer" catalog="system" translatesAutoresizingMaskIntoConstraints="NO" id="0lu-Kb-Kzg">
                                <rect key="frame" x="69" y="10.5" width="70" height="129.5"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="70" id="ce7-qz-BiH"/>
                                </constraints>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="location" catalog="system" translatesAutoresizingMaskIntoConstraints="NO" id="GI2-ik-c2Y">
                                <rect key="frame" x="162.5" y="138.5" width="50" height="53.5"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="GI2-ik-c2Y" secondAttribute="height" multiplier="56:61" id="Crt-eV-Wrj"/>
                                    <constraint firstAttribute="width" constant="50" id="aW8-Nx-cku"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Разрывы" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5Dd-bn-rLO">
                                <rect key="frame" x="20" y="510" width="335" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="Ymr-Kj-f3D"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="25"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor" cocoaTouchSystemColor="whiteColor"/>
                        <constraints>
                            <constraint firstItem="s5k-GT-aEB" firstAttribute="leading" secondItem="BcI-Uw-D2L" secondAttribute="leading" constant="20" id="4Gq-Lu-Gue"/>
                            <constraint firstItem="seU-eY-Rg3" firstAttribute="top" secondItem="OqM-0P-MxT" secondAttribute="bottom" constant="10" id="8SK-UH-5VZ"/>
                            <constraint firstAttribute="trailing" secondItem="5Dd-bn-rLO" secondAttribute="trailing" constant="20" id="9Rk-jj-d7N"/>
                            <constraint firstItem="NEj-7b-mOX" firstAttribute="top" secondItem="9mE-EV-7hC" secondAttribute="bottom" constant="10" id="At8-B2-lBn"/>
                            <constraint firstItem="seU-eY-Rg3" firstAttribute="leading" secondItem="WGB-AS-U5a" secondAttribute="trailing" id="BSJ-nL-JQz"/>
                            <constraint firstItem="WGB-AS-U5a" firstAttribute="top" secondItem="lMT-KF-LnN" secondAttribute="bottom" constant="10" id="Ba5-i3-MI8"/>
                            <constraint firstItem="lMT-KF-LnN" firstAttribute="width" secondItem="OqM-0P-MxT" secondAttribute="width" id="BjS-Zv-cqu"/>
                            <constraint firstAttribute="trailing" secondItem="NEj-7b-mOX" secondAttribute="trailing" constant="20" id="FI5-Ly-zjJ"/>
                            <constraint firstItem="9mE-EV-7hC" firstAttribute="top" secondItem="RzW-4l-oYQ" secondAttribute="bottom" constant="10" id="G3i-iH-HFa"/>
                            <constraint firstAttribute="trailing" secondItem="RzW-4l-oYQ" secondAttribute="trailing" constant="20" id="Gx4-lz-IIv"/>
                            <constraint firstItem="9mE-EV-7hC" firstAttribute="leading" secondItem="BcI-Uw-D2L" secondAttribute="leading" constant="20" id="PDk-xS-lF9"/>
                            <constraint firstItem="RzW-4l-oYQ" firstAttribute="leading" secondItem="BcI-Uw-D2L" secondAttribute="leading" constant="20" id="RKc-dZ-zkG"/>
                            <constraint firstItem="GI2-ik-c2Y" firstAttribute="leading" secondItem="s5k-GT-aEB" secondAttribute="trailing" id="SnM-2V-NEM"/>
                            <constraint firstItem="seU-eY-Rg3" firstAttribute="leading" secondItem="WGB-AS-U5a" secondAttribute="trailing" id="TbS-Ng-G1s"/>
                            <constraint firstItem="AGg-zv-bff" firstAttribute="top" secondItem="dMi-f7-hVa" secondAttribute="bottom" constant="30" id="UCq-Vq-bjI"/>
                            <constraint firstItem="WGB-AS-U5a" firstAttribute="width" secondItem="seU-eY-Rg3" secondAttribute="width" id="Vfv-S6-YJf"/>
                            <constraint firstItem="0lu-Kb-Kzg" firstAttribute="centerY" secondItem="AGg-zv-bff" secondAttribute="centerY" id="YFD-Ax-iQz"/>
                            <constraint firstItem="lMT-KF-LnN" firstAttribute="top" secondItem="s5k-GT-aEB" secondAttribute="bottom" constant="10" id="YWs-Xm-7A3"/>
                            <constraint firstItem="lMT-KF-LnN" firstAttribute="leading" secondItem="BcI-Uw-D2L" secondAttribute="leading" constant="20" id="Zys-Du-0e0"/>
                            <constraint firstItem="0lu-Kb-Kzg" firstAttribute="top" secondItem="dMi-f7-hVa" secondAttribute="bottom" constant="10" id="awu-QR-nhX"/>
                            <constraint firstItem="s5k-GT-aEB" firstAttribute="width" secondItem="bW3-iL-30s" secondAttribute="width" id="az5-kh-SVd"/>
                            <constraint firstItem="bW3-iL-30s" firstAttribute="top" secondItem="AGg-zv-bff" secondAttribute="bottom" constant="10" id="cig-Hv-mIX"/>
                            <constraint firstItem="WGB-AS-U5a" firstAttribute="leading" secondItem="BcI-Uw-D2L" secondAttribute="leading" constant="20" id="dKs-xH-7Y9"/>
                            <constraint firstItem="NEj-7b-mOX" firstAttribute="leading" secondItem="BcI-Uw-D2L" secondAttribute="leading" constant="20" id="eSA-22-0Tk"/>
                            <constraint firstItem="5Dd-bn-rLO" firstAttribute="leading" secondItem="BcI-Uw-D2L" secondAttribute="leading" constant="20" id="eiW-bm-tNF"/>
                            <constraint firstItem="bW3-iL-30s" firstAttribute="leading" secondItem="GI2-ik-c2Y" secondAttribute="trailing" id="f2I-F3-3go"/>
                            <constraint firstItem="5Dd-bn-rLO" firstAttribute="top" secondItem="NEj-7b-mOX" secondAttribute="bottom" constant="10" id="iBl-0C-Vdx"/>
                            <constraint firstAttribute="trailingMargin" secondItem="AGg-zv-bff" secondAttribute="trailing" constant="50" id="kEc-c2-PGe"/>
                            <constraint firstAttribute="trailing" secondItem="9mE-EV-7hC" secondAttribute="trailing" constant="20" id="kaR-1c-PSL"/>
                            <constraint firstAttribute="trailing" secondItem="seU-eY-Rg3" secondAttribute="trailing" constant="20" id="l5B-UF-XKf"/>
                            <constraint firstItem="GI2-ik-c2Y" firstAttribute="centerX" secondItem="BcI-Uw-D2L" secondAttribute="centerX" id="nC0-Ly-6eE"/>
                            <constraint firstItem="OqM-0P-MxT" firstAttribute="leading" secondItem="lMT-KF-LnN" secondAttribute="trailing" id="qNm-RH-5ku"/>
                            <constraint firstItem="s5k-GT-aEB" firstAttribute="top" secondItem="AGg-zv-bff" secondAttribute="bottom" constant="10" id="qsU-wS-q7a"/>
                            <constraint firstItem="OqM-0P-MxT" firstAttribute="top" secondItem="bW3-iL-30s" secondAttribute="bottom" constant="10" id="tbW-w0-czF"/>
                            <constraint firstItem="GI2-ik-c2Y" firstAttribute="centerY" secondItem="s5k-GT-aEB" secondAttribute="centerY" id="uhW-7e-Bz2"/>
                            <constraint firstItem="AGg-zv-bff" firstAttribute="leading" secondItem="0lu-Kb-Kzg" secondAttribute="trailing" id="v1F-fB-ZaY"/>
                            <constraint firstAttribute="trailing" secondItem="bW3-iL-30s" secondAttribute="trailing" constant="20" id="v3A-Kq-tDc"/>
                            <constraint firstItem="RzW-4l-oYQ" firstAttribute="top" secondItem="WGB-AS-U5a" secondAttribute="bottom" constant="10" id="vdl-Jb-Qas"/>
                            <constraint firstAttribute="trailing" secondItem="OqM-0P-MxT" secondAttribute="trailing" constant="20" id="zEX-kX-5mP"/>
                        </constraints>
                    </view>
                    <tabBarItem key="tabBarItem" title="Инфо" image="info.circle" catalog="system" id="wcp-fG-twD"/>
                    <navigationItem key="navigationItem" title="Информация GPS" id="nko-pp-Oka">
                        <barButtonItem key="backBarButtonItem" title="Назад" id="rlu-49-p5D"/>
                    </navigationItem>
                    <connections>
                        <outlet property="altitudeString" destination="lMT-KF-LnN" id="WCm-4I-dXY"/>
                        <outlet property="dateString" destination="RzW-4l-oYQ" id="3Kx-LT-htr"/>
                        <outlet property="disconnectString" destination="5Dd-bn-rLO" id="8sA-rr-0DX"/>
                        <outlet property="hdopString" destination="seU-eY-Rg3" id="xPj-NK-8tA"/>
                        <outlet property="headingString" destination="OqM-0P-MxT" id="KuX-hh-WMt"/>
                        <outlet property="latString" destination="bW3-iL-30s" id="akX-cl-2wa"/>
                        <outlet property="lonString" destination="s5k-GT-aEB" id="Y8V-vj-Qab"/>
                        <outlet property="refrashRate" destination="NEj-7b-mOX" id="RFl-gp-uiC"/>
                        <outlet property="satsString" destination="WGB-AS-U5a" id="ETN-sD-FlH"/>
                        <outlet property="speedString" destination="AGg-zv-bff" id="ZQm-Ex-CK2"/>
                        <outlet property="timeString" destination="9mE-EV-7hC" id="QaR-AC-tYN"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="wUz-kK-x5v" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1892" y="-528.48575712143929"/>
        </scene>
    </scenes>
    <resources>
        <image name="clock" catalog="system" width="64" height="62"/>
        <image name="gear" catalog="system" width="64" height="60"/>
        <image name="info.circle" catalog="system" width="64" height="62"/>
        <image name="location" catalog="system" width="64" height="60"/>
        <image name="speedometer" catalog="system" width="64" height="62"/>
    </resources>
</document>
