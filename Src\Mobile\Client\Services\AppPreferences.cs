﻿using AppoMobi.Framework.Maui.Interfaces;
using Microsoft.Maui.Controls.Internals;
#if WINDOWS
using Windows.Storage;
#endif

namespace Racebox.Services
{
    [Preserve(AllMembers = true)]
    public class AppStorage : IAppStorage
    {
#if WINDOWS

    /// <summary>
    /// Application settings
    /// Limit to 200*4095
    /// </summary>
    private const int CHUNK_SIZE = 4095;

    static private ApplicationDataContainer _localSettings;
    
    static private bool? _isPackaged;
    
    static private bool IsPackaged
    {
        get
        {
            if (_isPackaged == null)
            {
                try
                {
                    // Try to access ApplicationData.Current to detect if we're packaged
                    var applicationData = ApplicationData.Current;
                    Console.WriteLine($"[AppStorage] ApplicationData.Current is available: {applicationData != null}");
                    _isPackaged = true;
                }
                catch (Exception ex)
                {
                    // Running unpackaged - ApplicationData.Current throws exception
                    Console.WriteLine($"[AppStorage] Running unpackaged, using MAUI Preferences fallback. Exception: {ex.Message}");
                    _isPackaged = false;
                }
            }
            return _isPackaged.Value;
        }
    }
    
    static private ApplicationDataContainer LocalSettings
    {
        get
        {
            if (!IsPackaged)
                return null; // Running unpackaged, use fallback
                
            if (_localSettings == null)
            {
                try
                {
                    _localSettings = ApplicationData.Current.LocalSettings;
                }
                catch (Exception)
                {
                    // Shouldn't happen if IsPackaged is true, but just in case
                    return null;
                }
            }
            return _localSettings;
        }
    }

    static private string LoadString(string SET_STR, string defaultValue)
    {
        var localSettings = LocalSettings;
        if (localSettings == null)
        {
            // Fallback to MAUI Preferences for unpackaged apps
            return Microsoft.Maui.Storage.Preferences.Get(SET_STR, defaultValue);
        }

        string set = "";
        bool existing = false;
        try
        {
            for (int i = 0; i < 200; i++)
            {
                string s = (string)localSettings.Values[SET_STR + i];
                if (s != null)
                {
                    existing = true;
                    set += s;
                }
                else
                {
                    break;
                }
            }

            if (!existing)
                return defaultValue;

            return set;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
        }

        return defaultValue;
    }

    static private void SaveString(string SET_STR, string value)
    {
        var localSettings = LocalSettings;
        if (localSettings == null)
        {
            // Fallback to MAUI Preferences for unpackaged apps
            Microsoft.Maui.Storage.Preferences.Set(SET_STR, value ?? "");
            return;
        }

        try
        {
            if (value == null)
            {
                value = "";
            }

            for (int i = 0; i < 200; i++)
            {
                localSettings.Values[SET_STR + i] = null;
            }

            for (int i = 0; i * CHUNK_SIZE < value.Length; i++)
            {
                if (value.Length > i * CHUNK_SIZE)
                {
                    int len = (i + 1) * CHUNK_SIZE > value.Length ? value.Length % CHUNK_SIZE : CHUNK_SIZE;
                    localSettings.Values[SET_STR + i] = value.Substring(i * CHUNK_SIZE, len);
                }
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
        }
    }


#endif

        public T Get<T>(string key, T defaultValue = default)
        {
            if (typeof(T) == typeof(bool))
            {
                return (T)(object)Microsoft.Maui.Storage.Preferences.Get(key, (bool)(object)defaultValue);
            }

            if (typeof(T) == typeof(int))
            {
                return (T)(object)Microsoft.Maui.Storage.Preferences.Get(key, (bool)(object)defaultValue);
            }

            if (typeof(T) == typeof(long))
            {
                return (T)(object)Microsoft.Maui.Storage.Preferences.Get(key, (long)(object)defaultValue);
            }

            if (typeof(T) == typeof(float))
            {
                return (T)(object)Microsoft.Maui.Storage.Preferences.Get(key, (float)(object)defaultValue);
            }

            if (typeof(T) == typeof(double))
            {
                return (T)(object)Microsoft.Maui.Storage.Preferences.Get(key, (double)(object)defaultValue);
            }

#if WINDOWS
            if (IsPackaged)
            {
                return (T)(object)LoadString(key, (string)(object)defaultValue);
            }
            else
            {
                // Unpackaged - use MAUI Preferences
                return (T)(object)Microsoft.Maui.Storage.Preferences.Get(key, (string)(object)defaultValue);
            }
#else
            return (T)(object)Microsoft.Maui.Storage.Preferences.Get(key, (string)(object)defaultValue);
#endif
        }

        public void Set<T>(string key, T value)
        {
            if (typeof(T) == typeof(bool))
            {
                Microsoft.Maui.Storage.Preferences.Set(key, (bool)(object)value);
            }
            else if (typeof(T) == typeof(int))
            {
                Microsoft.Maui.Storage.Preferences.Set(key, (int)(object)value);
            }
            else if (typeof(T) == typeof(long))
            {
                Microsoft.Maui.Storage.Preferences.Set(key, (long)(object)value);
            }
            else if (typeof(T) == typeof(float))
            {
                Microsoft.Maui.Storage.Preferences.Set(key, (float)(object)value);
            }
            else if (typeof(T) == typeof(double))
            {
                Microsoft.Maui.Storage.Preferences.Set(key, (double)(object)value);
            }
            else
#if WINDOWS
            {
                if (IsPackaged)
                {
                    SaveString(key, (string)(object)value);
                }
                else
                {
                    // Unpackaged - use MAUI Preferences
                    Microsoft.Maui.Storage.Preferences.Set(key, (string)(object)value);
                }
            }
#else
                Microsoft.Maui.Storage.Preferences.Set(key, (string)(object)value);
#endif
        }
    }
}