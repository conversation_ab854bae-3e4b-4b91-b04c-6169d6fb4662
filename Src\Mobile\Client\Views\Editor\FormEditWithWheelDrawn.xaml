<?xml version="1.0" encoding="utf-8" ?>
<draw:SkiaLayout
    x:Class="Racebox.Views.Partials.FormEditWithWheelDrawn"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:drawn="clr-namespace:Racebox.Views.Drawn"
    xmlns:strings="clr-namespace:Racebox.Shared.Strings;assembly=Racebox.Shared"
    xmlns:viewModels="clr-namespace:Racebox.ViewModels"
    Padding="16"
    x:DataType="viewModels:IEditWithWheel"
    VerticalOptions="StartAndExpand">


    <!--  absolute layout wrapper  -->
    <!--  todo without it gesture will act differently  -->
    <draw:SkiaLayout
        HorizontalOptions="Fill"
        AddMarginBottom="32"
        VerticalOptions="Start">

        <!--  vertical stack  -->
        <draw:SkiaLayout
            x:Name="Form"
            Padding="16"
            HorizontalOptions="Fill"
            Spacing="16"
            UseCache="ImageComposite"
            Split="1"
            Type="Wrap">

            <!--  EDIT METRIC  -->
            <draw:SkiaLabel
                Style="{StaticResource SkiaEditorFieldTitle}"
                Text="{Binding Title}" />

            <!--  wheel inside darkened container  -->
            <draw:SkiaLayout
                BackgroundColor="#33000000"
                HorizontalOptions="Fill">

                <drawn:WheelPicker
                    x:Name="Picker"
                    DataSource="{Binding ItemsList}"
                    HorizontalOptions="Center"
                    SelectedIndex="{Binding SelectedIndex, Mode=TwoWay}" />

            </draw:SkiaLayout>

            <!--  Save and Errors  -->
            <draw:SkiaLayout
                HeightRequest="100"
                HorizontalOptions="Fill">

                <!--  SAVE BTN  -->
                <drawn:SmallButton
                    Margin="16"
                    CommandTapped="{Binding CommandSubmitForm, Mode=OneTime}"
                    HorizontalOptions="Center"
                    IsEnabled="{Binding CanSubmit}"
                    Text="{x:Static strings:ResStrings.BtnSave}" />

            </draw:SkiaLayout>

        </draw:SkiaLayout>

        <!--  FPS  -->
        <draw:SkiaLabelFps
            Margin="0,0,4,24"
            BackgroundColor="DarkRed"
            ForceRefresh="False"
            HorizontalOptions="End"
            IsVisible="{Binding IsDebug}"
            Rotation="-45"
            TextColor="White"
            VerticalOptions="End" />

    </draw:SkiaLayout>





</draw:SkiaLayout>
