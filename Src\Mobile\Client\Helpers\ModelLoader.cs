﻿using System.Collections;
using System.Diagnostics;
using System.Windows.Input;
using AppoMobi.Framework.Maui.Interfaces;

namespace Racebox.Helpers;

public class PagedListLoader<T> : BindableObject, IDisposable, IInfiniteListLoader, IPagedListLoader where T : class, IHasIntId
{


    public IEnumerable Iterator
    {
        get
        {
            return Items;
        }
    }

    public ObservableRangeCollection<T> Items { get; set; }


    /// <summary>
    /// Call on UI thread
    /// </summary>
    public void Reset()
    {
        Items.Clear();

        Item = null;

        LoadStatus = 0;
        LoadOk = false;
        DataPage = 0;
        PagesTotal = 0;
        LastData = null;
        CanLoadMore = false;

        OnPropertyChanged("HasData");
        OnPropertyChanged("HasItem");
        OnPropertyChanged("TotalItems");

        IsBusy = false;

    }

    private bool _IsBusy;
    public bool IsBusy
    {
        get { return _IsBusy; }
        set
        {
            if (_IsBusy != value)
            {
                _IsBusy = value;
                OnPropertyChanged();
            }
        }
    }

    public PagedListLoader(Func<bool, string, string, string, int, int, Task<FilteredPagedList<T>>> funcLoadItems)
    {
        InitOnce();
        _funcLoadItems = funcLoadItems;
    }

    void InitOnce()
    {
        var collection = new ObservableRangeCollection<T>();
        //var fake = (T)Activator.CreateInstance(typeof(T));
        //collection.Add(fake);

        Items = collection;
    }

    public PagedListLoader(Func<object[], Task<T>> funcLoadItem)
    {
        InitOnce();
        _funcLoadItem = funcLoadItem;
    }

    public PagedListLoader()
    {
        InitOnce();
    }

    public void Init(Func<bool, string, string, string, int, int, Task<FilteredPagedList<T>>> funcLoadItems)
    {
        _funcLoadItems = funcLoadItems;
    }

    public void Init(Func<object[], Task<T>> funcLoadItem)
    {
        _funcLoadItem = funcLoadItem;
    }



    //many
    protected Func<bool, string, string, string, int, int, Task<FilteredPagedList<T>>> _funcLoadItems;

    //one
    protected Func<object[], Task<T>> _funcLoadItem;

    public Func<Task<T>> FuncCall;

    private bool onceLoaded;

    public bool HasData
    {
        get
        {
            return onceLoaded && (Items.Count > 0 || Item != null);
        }
    }

    public bool LoadedHasNoData
    {
        get
        {
            return onceLoaded && (Items.Count == 0 && Item == null);
        }
    }

    public bool HasItem
    {
        get
        {
            return Item != null;
        }
    }

    private bool _LoadOk;
    public bool LoadOk
    {
        get { return _LoadOk; }
        set
        {
            if (_LoadOk != value)
            {
                _LoadOk = value;
                OnPropertyChanged();
            }
        }
    }

    protected void UpdateDataStatus(bool error)
    {
        if (error)
        {
            LoadOk = false;
            LoadStatus = 3;
            return;
        }

        if (HasData)
            LoadStatus = 2;
        else
            LoadStatus = 1;

        LoadOk = true;
        OnPropertyChanged("HasData");
        OnPropertyChanged("LoadedHasNoData");
        OnPropertyChanged("HasItem");
        OnPropertyChanged("TotalItems");
    }

    public int TotalItems
    {
        get
        {
            if (Items != null)
                return Items.Count;
            return 0;
        }
    }

    private int _LoadStatus;
    public int LoadStatus
    {
        get
        {
            return _LoadStatus;
        }
        set
        {
            if (_LoadStatus != value)
            {
                _LoadStatus = value;
                OnPropertyChanged();
            }
        }
    }

    // private IRemoteDataStorage _dataStore => App.Instance.GetHostService<IRemoteDataStorage>();

    private int _DataPage;
    public int DataPage
    {
        get { return _DataPage; }
        set
        {
            if (_DataPage != value)
            {
                _DataPage = value;
                OnPropertyChanged();
            }
        }
    }

    private int _PagesTotal;
    public int PagesTotal
    {
        get { return _PagesTotal; }
        protected set
        {
            if (_PagesTotal != value)
            {
                _PagesTotal = value;
                OnPropertyChanged();
            }
        }
    }



    private int _DataPageSize = 20;
    public int DataPageSize
    {
        get { return _DataPageSize; }
        set
        {
            if (_DataPageSize != value)
            {
                _DataPageSize = value;
                OnPropertyChanged();
            }
        }
    }

    private T _Item;
    public T Item
    {
        get { return _Item; }
        set
        {
            if (_Item != value)
            {
                _Item = value;
                OnPropertyChanged();
            }
        }
    }

    private FilteredPagedList<T> _LastData;
    public FilteredPagedList<T> LastData
    {
        get { return _LastData; }
        set
        {
            if (_LastData != value)
            {
                _LastData = value;
                OnPropertyChanged();
            }
        }
    }

    public async Task Call()
    {
        bool reload = true;

        if (FuncCall == null)
            throw new Exception("ApiHelper FuncCall Not initialized!");

        if (IsBusy)
            return;

        IsBusy = true;
        bool error = true;
        try
        {
            Item = await FuncCall();
            error = false;
        }
        catch (Exception ex)
        {

            Debug.WriteLine(ex);
        }
        finally
        {
            UpdateDataStatus(error);
            IsBusy = false;
        }
    }

    public async Task LoadItem(params object[] args)
    {
        bool reload = true;

        if (IsBusy)
            return;

        IsBusy = true;
        bool error = true;
        try
        {
            var maybeItem = await _funcLoadItem(args);


            // todo Update on the UI thread???
            Item = maybeItem;

            onceLoaded = true;

            error = false;
        }
        catch (Exception ex)
        {

            Debug.WriteLine(ex);
        }
        finally
        {
            UpdateDataStatus(error);
            IsBusy = false;
        }
    }

    protected bool LockLoadData = false;


    private int _ScrollToIndex = -1;
    public int ScrollToIndex
    {
        get { return _ScrollToIndex; }
        set
        {
            //_ScrollToIndex = value;
            //OnPropertyChanged();
            if (_ScrollToIndex != value)
            {
                _ScrollToIndex = value;

                if (value == -1)
                    OnPropertyChanged();
            }
        }
    }

    public async Task<int> LoadData(bool reload = true, string filter = null, string search = null, string order = null, bool loadMore = false, int pageSize = -1)
    {
        if (IsBusy || LockLoadData || _disposing)
            return -1;

        if (pageSize > 0)
            DataPageSize = pageSize;

        IsBusy = true;

        bool error = true;
        try
        {
            if (_funcLoadItems == null)
            {
                throw new Exception("ApiHelper Not initialized!");
            }

            if (!loadMore)
            {
                DataPage = 0;
            }

            LastData = await _funcLoadItems(reload, filter, search, order, DataPage, DataPageSize);


            PagesTotal = LastData.TotalPageCount;

            LockLoadData = true;

            onceLoaded = true;

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                try
                {
                    // Update the UI
                    if (!loadMore)
                    {
                        if (reload || DetectEntitiesListChanged(Items, LastData.Items))
                        {
                            //await Task.Delay(2000);
                            Items.Clear();
                            await Task.Delay(10);
                            Items.AddRange(LastData.Items);
                        }
                    }
                    else
                    {
                        //LOAD MORE
                        Items.AddRange(LastData.Items);
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
                finally
                {
                    LockLoadData = false;
                }
            });



            while (LockLoadData)
            {
                await Task.Delay(25);
            }

            if (LastData.TotalItemCount > LastData.TotalItemCount)
                CanLoadMore = true;
            else
                CanLoadMore = false;

            error = false;

            return LastData.Items.Count();
        }
        catch (Exception ex)
        {
            Debug.WriteLine(ex);
#if DEBUG
            //  App.ShowToast("LoadData " + ex);
#endif
            return -1;
        }
        finally
        {
            UpdateDataStatus(error);
            IsBusy = false;
        }
    }

    protected bool DetectEntitiesListChanged<T>(IList<T> a, IList<T> b) where T : IHasIntId
    {
        if (a == null || b == null)
            return true;

        var added = b.Where(p => a.All(p2 => p2.Id != p.Id));
        if (added.Any())
            return true;

        var removed = a.Where(p => b.All(p2 => p2.Id != p.Id)).ToArray();
        if (removed.Any())
            return true;

        var intersection = a.Where(p => b.Any(x => x.Id == p.Id)).ToArray();
        var moved = intersection.Where(x => b[a.IndexOf(x)].Id != x.Id && !removed.Contains(x)).ToArray();
        if (moved.Any())
            return true;


        /*
          List<T> newlyInserted = new List<T>();
          foreach (var item in removed)
          {
              //Newly inserted into the list - D1
              newlyInserted.Add(b[a.IndexOf(item)]);
              //Index of D1 if required
              var indexOfNewlyAddedItem = a.IndexOf(item);
          }
        */
        return false;
    }


    /// <summary>
    /// Nothing will be stored in properties, use for one time call to loading function
    /// </summary>
    public async Task<FilteredPagedList<T>> LoadDataSimple(bool reload = true, string filter = null, string search = null, string order = null, int pageSize = -1)
    {
        if (_disposing)
            return null;

        try
        {
            if (_funcLoadItems == null)
            {
                throw new Exception("ApiHelper Not initialized!");
            }

            if (pageSize < 0)
                pageSize = DataPageSize;

            var dto = await _funcLoadItems(reload, filter, search, order, 0, pageSize);

            onceLoaded = true;

            return dto;

        }
        catch (Exception ex)
        {
            Debug.WriteLine(ex);

        }
        finally
        {
        }
        return null;
    }

    private bool _IsLoadingMore;
    public bool IsLoadingMore
    {
        get { return _IsLoadingMore; }
        set
        {
            if (_IsLoadingMore != value)
            {
                _IsLoadingMore = value;
                OnPropertyChanged();
            }
        }
    }



    /// <summary>
    /// if FALSE nocontent OR it's last page. TRUE if has more data left to load
    /// </summary>
    /// <returns></returns>
    public async Task<bool> LoadMoreData()
    {
        if (LastData == null || _disposing)
            return false;

        if (DataPage + 1 > LastData?.TotalPageCount)
        {
            CanLoadMore = false;
            return false;
        }

        if (IsBusy)
            return true;

        //IsBusy = true;

        IsLoadingMore = true;

        var backupDataPage = DataPage;
        DataPage++;
        var count = 0;

        try
        {
            count = await LoadData(true, LastData.Filter, LastData.Search, LastData.Order, true);

            if (DataPage + 1 == LastData?.TotalPageCount)
            {
                CanLoadMore = false;
                return false;
            }

            CanLoadMore = true;
        }
        catch (Exception ex)
        {
            Debug.WriteLine(ex);

            DataPage = backupDataPage;
        }
        finally
        {
            IsLoadingMore = false;
            IsBusy = false;
            CommandOnDataLoaded?.Execute(count);
        }

        return true;
    }

    /// <summary>
    /// Set this to your command to be notified that data was loaded
    /// </summary>
    public ICommand CommandOnDataLoaded { get; set; }

    private bool _CanLoadMore;
    public bool CanLoadMore
    {
        get { return _CanLoadMore; }
        set
        {
            if (_CanLoadMore != value)
            {
                _CanLoadMore = value;
                OnPropertyChanged();
            }
        }
    }


    public async void OnScroll(int lastVisibleIndex)
    {

        if (lastVisibleIndex > Items.Count - 3)
        {
            if (!IsBusy)
            {
                await LoadMoreData();
            }
        }

    }

    public ICommand CommandLoadMore
    {
        get
        {
            return new Command(async (object context) =>
            {
                if (!IsBusy)
                {
                    await LoadMoreData();
                }
            });
        }
    }

    private bool _InverseItems;
    public bool InverseItems
    {
        get { return _InverseItems; }
        set
        {
            if (_InverseItems != value)
            {
                _InverseItems = value;
                OnPropertyChanged();
            }
        }
    }

    bool _disposing;

    public bool Disposed;

    public virtual void Dispose()
    {
        if (_disposing)
            return;

        _disposing = true;
        if (Items.Any(x => x is IDisposable))
        {
            foreach (IDisposable item in Items)
            {
                item.Dispose();
            }
        }

        Disposed = true;
    }
}