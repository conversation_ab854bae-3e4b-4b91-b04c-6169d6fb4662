﻿using AppoMobi.Framework;
using Microsoft.Maui.Controls.Internals;
using Racebox.Shared.Strings;
using Racebox.Views.Popups;
using System.Collections.Concurrent;
using AppoMobi.Framework.Maui.Interfaces;
using FastPopups;


#if IOS
using UIKit;
#endif

namespace Racebox.Services
{
    [Preserve(AllMembers = true)]
    public class UIAssistant : BindableObject, IUIAssistant
    {
        //static SnackbarOptions snackOptions = new SnackbarOptions
        //{
        //    BackgroundColor = Colors.Black,
        //    TextColor = Colors.White,
        //    Font = Microsoft.Maui.Font.OfSize("FontText", 14)
        //};

        public void ShowToast(string text)
        {
            //            MainThread.BeginInvokeOnMainThread(async () =>
            //            {
            //#if IOS
            //				if (!UIDevice.CurrentDevice.CheckSystemVersion(13, 0))
            //				{
            //					await App.Current.MainPage.DisplayAlert(ResStrings.OwnerTitleShort, text, "OK");
            //					return;
            //				}
            //#endif
            //                await App.Current.MainPage.DisplaySnackbar(text, null, "", TimeSpan.FromMilliseconds(2500), snackOptions);
            //            });

            AppoMobi.Mobile.Toast.ShortMessage(text);
        }

        public void ShowPleaseWait(string text = null)
        {
            ShowWaitingPopup(text);
        }

        public void HidePleaseWait()
        {
            CloseAllPopups();
            ResetWaitMessage();
        }

        void IUIAssistant.ShowNetworkError(string error)
        {
            //todo
        }


        public void SetWaitMessage(string text)
        {
            WaitMessage = text;
        }

        public void SetDefaultWaitMessage(string text)
        {
            _defaultWaitMessage = text;
        }

        string _defaultWaitMessage = "Пожалуйста, подождите..";

        public void HideAllPopups()
        {
            CloseAllPopups();
            ResetWaitMessage();
        }

        private string _WaitMessage;

        public string WaitMessage
        {
            get { return _WaitMessage; }
            set
            {
                if (_WaitMessage != value)
                {
                    _WaitMessage = value;
                    OnPropertyChanged();
                }
            }
        }

        static bool lockPopups;

        static readonly string keyLoader = "Loader";

        public async void ShowWaitingPopup(string text = null)
        {
            if (!string.IsNullOrEmpty(text))
                SetWaitMessage(text);

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                var topPopup = PopupNavigationStack.Instance.Peek();
                if (topPopup != null)
                {
                    if (topPopup is not WaitingPopup)
                    {
                        var popup = new WaitingPopup();
                        await App.Current.MainPage.ShowPopupAsync(popup);
                    }
                }
            });
        }

        public void CloseAllPopups()
        {
            if (lockPopups)
                return;

            lockPopups = true;

            void Act()
            {
                PopupNavigationStack.Instance.Clear();

                // Clear local dictionary as backup

                lockPopups = false;
            }

            MainThread.BeginInvokeOnMainThread(Act);
        }


        public void ResetWaitMessage()
        {
            WaitMessage = _defaultWaitMessage;
        }

        void IUIAssistant.ShowError(string error)
        {
            Alert(ResStrings.Error, error).ConfigureAwait(false);
        }


        public async Task Alert(string title, string text)
        {
            await App.Current.MainPage.DisplayAlert(title, text, "OK");
        }

        public async Task<bool> Prompt(string title, string text, string yes = null, string no = null)
        {
            if (string.IsNullOrEmpty(yes))
            {
                yes = "OK";
            }

            if (string.IsNullOrEmpty(no))
            {
                no = ResStrings.BtnCancel;
            }

            var ret = await App.Current.MainPage.DisplayAlert(title, text, yes, no);
            return ret;
        }

        public async Task<ISelectableOption> PresentSelection(IEnumerable<ISelectableOption> options,
            string title = null, string cancel = null)
        {
            if (string.IsNullOrEmpty(title))
                title = ResStrings.Select;

            if (string.IsNullOrEmpty(cancel))
                cancel = ResStrings.BtnCancel;

            var result = await App.Current.MainPage.DisplayActionSheet(title, cancel,
                null, options.Select(x => x.Title).ToArray()
            );

            if (string.IsNullOrEmpty(result))
            {
                return null; //cancel
            }

            var selected = options.FirstOrDefault(x => x.Title == result);
            return selected;
        }
    }

    /*
        public class UIAssistant : BindableObject, IUIAssistant
        {
            static SnackbarOptions snackOptions = new SnackbarOptions
            {
                BackgroundColor = Colors.Black,
                TextColor = Colors.White,
                Font = Microsoft.Maui.Font.OfSize("FontText", 14)
            };

            public void ShowToast(string text)
            {

                MainThread.BeginInvokeOnMainThread(async () =>
                {
    #if IOS
                    if (!UIDevice.CurrentDevice.CheckSystemVersion(13, 0))
                    {
                        await App.Current.MainPage.DisplayAlert(ResStrings.VendorTitle, text, "OK");
                        return;
                    }
    #endif
                    await App.Current.MainPage.DisplaySnackbar(text, null, "", TimeSpan.FromMilliseconds(2500), snackOptions);
                });

            }

            public void ShowPleaseWait(string text = null)
            {
                ShowWaitingPopup(text);
            }

            public void HidePleaseWait()
            {
                CloseAllPopups();
                ResetWaitMessage();
            }

            void IUIAssistant.ShowNetworkError(string error)
            {
                //todo
            }


            public void SetWaitMessage(string text)
            {
                WaitMessage = text;
            }

            public void SetDefaultWaitMessage(string text)
            {
                _defaultWaitMessage = text;
            }

            string _defaultWaitMessage = "Пожалуйста, подождите..";

            public void HideAllPopups()
            {
                CloseAllPopups();
                ResetWaitMessage();
            }

            private string _WaitMessage;
            public string WaitMessage
            {
                get { return _WaitMessage; }
                set
                {
                    if (_WaitMessage != value)
                    {
                        _WaitMessage = value;
                        OnPropertyChanged();
                    }
                }
            }

            static bool lockPopups;

            static readonly string keyLoader = "Loader";

            public async void ShowWaitingPopup(string text = null)
            {
                if (!string.IsNullOrEmpty(text))
                    SetWaitMessage(text);

                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    if (Popups.Keys.Count > 0)
                        return;

                    if (Popups.Keys.All(a => a != keyLoader))
                    {
                        var popup = new WaitingPopup();
                        Popups[keyLoader] = popup;
                        await App.Current.MainPage.ShowPopupAsync(popup);
                    }
                });
            }


            ConcurrentDictionary<string, Popup> Popups { get; } = new();

            public void CloseAllPopups()
            {
                if (lockPopups)
                    return;

                lockPopups = true;

                void Act()
                {
                    var keys = Popups.Keys.Select(s => s).ToList();
                    foreach (var key in keys)
                    {
                        try
                        {
                            var popup = Popups[key];
                            popup.Close();
                            Popups[key] = null;
                        }
                        catch (Exception)
                        {
                            continue;
                        }
                        finally
                        {
                            lockPopups = false;
                        }
                    }
                }

                MainThread.BeginInvokeOnMainThread(Act);

            }


            public void ResetWaitMessage()
            {
                WaitMessage = _defaultWaitMessage;
            }

            void IUIAssistant.ShowError(string error)
            {
                Alert(ResStrings.Error, error).ConfigureAwait(false);
            }


            public async Task Alert(string title, string text)
            {
                await App.Current.MainPage.DisplayAlert(title, text, "OK");
            }

            public async Task<bool> Prompt(string title, string text, string yes = null, string no = null)
            {
                if (string.IsNullOrEmpty(yes))
                {
                    yes = "OK";
                }
                if (string.IsNullOrEmpty(no))
                {
                    no = ResStrings.BtnCancel;
                }
                var ret = await App.Current.MainPage.DisplayAlert(title, text, yes, no);
                return ret;
            }

            public async Task<ISelectableOption> PresentSelection(IEnumerable<ISelectableOption> options,
                string title = null, string cancel = null)
            {
                if (string.IsNullOrEmpty(title))
                    title = ResStrings.Select;

                if (string.IsNullOrEmpty(cancel))
                    cancel = ResStrings.BtnCancel;

                var result = await App.Current.MainPage.DisplayActionSheet(title, cancel,
                    null, options.Select(x => x.Title).ToArray()
                );

                if (string.IsNullOrEmpty(result))
                {
                    return null; //cancel
                }

                var selected = options.FirstOrDefault(x => x.Title == result);
                return selected;
            }

        }

        */
}