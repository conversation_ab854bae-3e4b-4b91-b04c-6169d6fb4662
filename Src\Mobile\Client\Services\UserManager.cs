using AppoMobi.Framework.Maui.Interfaces;
using Mapster;
using MapsterMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Maui.Controls.Internals;
using Newtonsoft.Json;
using Racebox.Shared.Enums;
using Racebox.Shared.Extensions;
using Racebox.Shared.Services;
using Racebox.Shared.Strings;

namespace Racebox.Services
{
    [Preserve(AllMembers = true)]
    public class UserManager : RaceBoxSettings
    {
        private IAppStorage _preferences;

        public UserManager(
            IMapper mapper,
            IAppStorage preferences)
        {
            _preferences = preferences;
            _mapper = mapper;

            User = new();
        }

        private readonly IMapper _mapper;

        public LocalDatabase GetDatabase()
        {
            return App.Instance.Services.GetService<LocalDatabase>();
        }

        public async Task InsureUserIsInDatabase()
        {
            var db = GetDatabase();
            while (db.Database.GetPendingMigrations().Any())
            {
                await Task.Delay(200);
            }

            bool updated = false;

            if (User.Id == 0)
            {
                ValidateUser(User);
                await db.AddUser(User, _mapper);
                await ReloadUser(db);
                updated = true;
            }

            if (User.Options.CarId == 0)
            {
                var car = await db.Cars.Where(x => x.AppUserId == User.Id).FirstOrDefaultAsync();
                if (car != null)
                {
                    User.Options.CarId = car.Id;
                    updated = true;
                }
            }

            if (updated)
            {
                SaveLocally();
            }
        }

        public List<LocalUser> CachedUsers { get; protected set; }

        public async Task<List<LocalUser>> GetUsers()
        {
            var db = GetDatabase();
            CachedUsers = await db.GetUsersList(_mapper);
            return CachedUsers;
        }

        public async Task<bool> DeleteUser(int id)
        {
            var db = GetDatabase();
            var user = await db.GetUser(id);
            if (user != null)
            {
                db.Users.Remove(user);
                _preferences.Set($"Options{user.Id}", string.Empty);
                await db.SaveChangesAsync();
                return true;
            }
            return false;
        }

        public async Task<bool> UpdateUser(LocalUser user)
        {
            var db = GetDatabase();
            var dbUser = await db.GetUser(user.Id);
            if (dbUser != null)
            {
                await _mapper.From(user).AdaptToAsync(dbUser);
                db.Update(dbUser);
                await db.SaveChangesAsync();
                return true;
            }

            return false;
        }

        public async Task AddUser(LocalUser user)
        {
            var db = GetDatabase();
            if (user.Cars.Count == 0)
            {
                user.Cars.Add(DefaultCar);
            }
            var dbUser = await _mapper.MapAsync<AppUser>(user);
            db.Users.Add(dbUser);
            await db.SaveChangesAsync();
        }

        public async Task<bool> ReloadUser(LocalDatabase db = null)
        {
            if (db == null)
                db = GetDatabase();

            var dbUser = await db.GetUser(User.Id);
            await _mapper.From(dbUser).AdaptToAsync(User);

            ApplyLocalOptions(User);
            ValidateUser(User);
            SaveLocally();

            return true;
        }

        public async Task SyncUser()
        {
            var db = GetDatabase();
            var dbUser = await db.GetUser(User.Id);
            await _mapper.From(User).AdaptToAsync(dbUser);
            db.Update(dbUser);
            await db.SaveChangesAsync();

            await _mapper.From(dbUser).AdaptToAsync(User);

            ValidateUser(User);

            SaveLocally();
        }

        public LocalCar DefaultCar
        {
            get
            {
                return new LocalCar()
                {
                    Brand = ResStrings.DefaultBrand,
                    Model = ResStrings.DefaultModel,
                    Description = ""
                };
            }
        }

        public UserOptionsMeta DefaultOptions
        {
            get
            {

                var create = new UserOptionsMeta()
                {
                    Version = 1,
                    Rollout = false,
                    Units = OptionsUnits.EU,
                    Sound = true,
                    CanSay = true,
                    Time24 = true,
                    SpeedRanges = new(),
                    Distances = new()
                };

                foreach (var value in RaceBoxSettings.DefaultDistancesInMeters)
                {
                    var item = new LocalDistance()
                    {
                        IsReadOnly = true,
                        End = value,
                        Units = OptionsUnits.EU,
                    };
                    item.Init();
                    create.Distances.Add(item);
                }
                foreach (var value in RaceBoxSettings.DefaultDistancesInFeet)
                {
                    var item = new LocalDistance()
                    {
                        IsReadOnly = true,
                        End = value,
                        Units = OptionsUnits.US
                    };
                    item.Init();
                    create.Distances.Add(item);
                }
                foreach (var value in RaceBoxSettings.DefaultSpeedStopsKMH)
                {
                    var item = new LocalSpeedRange()
                    {
                        IsReadOnly = true,
                        Start = value.Start,
                        End = value.End,
                        Units = OptionsUnits.EU
                    };
                    item.Init();
                    create.SpeedRanges.Add(item);
                }
                foreach (var value in RaceBoxSettings.DefaultSpeedStopsMPH)
                {
                    var item = new LocalSpeedRange()
                    {
                        IsReadOnly = true,
                        Start = value.Start,
                        End = value.End,
                        Units = OptionsUnits.US
                    };
                    item.Init();
                    create.SpeedRanges.Add(item);
                }

                return create;
            }
        }

        public LocalUser DefaultUser
        {
            get
            {
                var user = new LocalUser()
                {
                    Name = ResStrings.User,
                    Options = DefaultOptions,
                    Cars = new()
                    {
                        DefaultCar
                    }
                };
                return user;
            }
        }

        public void LoginOffline()
        {
            if (!RestoreLocally())
            {
                SetUser(DefaultUser);
            }
            else
                ApplyUserOptions();
        }

        void ApplyUserOptions()
        {
            LocalizedDisplayProvider.HoursFormat = User.Options.Time24 ? "HH" : "hh";

            App.Instance.Messager.All("User", "Changed");
        }

        public bool RestoreLocally()
        {
            try
            {
                var json = _preferences.Get("User", string.Empty);
                if (string.IsNullOrEmpty(json))
                    return false;
                var user = JsonConvert.DeserializeObject<LocalUser>(json);
                user.Init();
                SetUser(user);
                return true;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return false;
        }

        void ValidateUser(LocalUser user)
        {
            if (user.Options == null || user.Options.Version < 1)
            {
                user.Options = DefaultOptions;
            }

            if (user.Cars == null || user.Cars.Count == 0)
            {
                user.Cars = new()
                {
                    DefaultCar
                };
            };

            user.Init();
        }

        void ApplyLocalOptions(LocalUser user)
        {
            try
            {
                var jsonOptions = _preferences.Get($"Options{user.Id}", string.Empty);
                if (!string.IsNullOrEmpty(jsonOptions))
                {
                    var options = JsonConvert.DeserializeObject<UserOptionsMeta>(jsonOptions);
                    if (options != null)
                        user.Options = options;
                }
            }
            catch { }
        }
        public void SetUser(LocalUser user)
        {
            ApplyLocalOptions(user);

            ValidateUser(user);

            User = user;

            SaveLocally();
        }

        public LocalUser User { get; set; }



        //public void UpdateMeta()
        //{
        //    User.RollOut = this.UseRollout
        //    var meta = new UserOptionsMeta
        //    {
        //        RollOut = this.UseRollout,
        //        Sound = this.SoundEnabled,
        //        SpeedStops = this.SpeedStopsCustom
        //    };
        //}

        //public async Task UpdateUser()
        //{
        //    await InsureUserIsInDatabase();
        //    await Database.UpdateUser(User, _mapper);
        //}

        public void SaveLocally()
        {
            JsonSerializerSettings settings = new JsonSerializerSettings();
            settings.Error = (serializer, err) =>
            {
                err.ErrorContext.Handled = true;
            };

            _preferences.Set("User", JsonConvert.SerializeObject(User, settings));
            _preferences.Set($"Options{User.Id}", JsonConvert.SerializeObject(User.Options, settings));

            ApplyUserOptions();
        }



    }
}
