﻿using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Racebox.Models;

public class BindableWrapper : INotifyPropertyChanged, IDisposable
{
    public void Dispose()
    {
        if (Bindable != null)
        {
            Bindable.PropertyChanged -= OnBindablePropertyChanged;
        }
    }

    public BindableWrapper(INotifyPropertyChanged bindable,
        string propertyName,
        Func<object> getValue,
        Action<object> setValue)
    {
        _propertyName = propertyName;

        GetValue = getValue;

        SetValue = setValue;

        Bindable = bindable;

        Bindable.PropertyChanged += OnBindablePropertyChanged;
    }

    public Func<object> GetValue;

    public Action<object> SetValue;

    private readonly string _propertyName;

    public virtual void OnBindablePropertyChanged(object sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == _propertyName)
        {
            OnPropertyChanged("Value");
        }
    }

    public INotifyPropertyChanged Bindable { get; }

    public object Value
    {
        get
        {
            if (Bindable != null)
            {
                return GetValue();
            }
            return null;
        }
        set
        {
            if (Bindable != null)
            {
                if (value != GetValue())
                {
                    SetValue(value);
                    OnPropertyChanged();
                }
            }
        }
    }

    #region INotifyPropertyChanged
    public event PropertyChangedEventHandler PropertyChanged;
    protected void OnPropertyChanged([CallerMemberName] string propertyName = "")
    {
        var changed = PropertyChanged;
        if (changed == null)
            return;

        changed.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
    #endregion

}