﻿namespace Racebox.Views.Partials;

public class DrawnCellMeasurement : SkiaDrawnCell
{
    protected override void SetContent(object ctx)
    {
        if (ctx is IHasDetailedDisplay model)
        {
            if (LabelDisplay1 == null)
            {
                LabelDisplay1 = this.FindView<SkiaLabel>("LabelDisplay1");
            }
            if (LabelDisplay2 == null)
            {
                LabelDisplay2 = this.FindView<SkiaLabel>("LabelDisplay2");
            }

            LockUpdate(true);

            if (LabelDisplay1 != null)
            {
                LabelDisplay1.Text = model.Display1;
            }

            if (LabelDisplay2 != null)
            {
                LabelDisplay2.Text = model.Display2;
            }

            LockUpdate(false);
        }

        base.SetContent(ctx);
    }

    protected SkiaLabel LabelDisplay1;
    protected SkiaLabel LabelDisplay2;
}
