﻿using AppoMobi.Maui.BLE.Connector;
using Racebox.SDK;
using Racebox.Shared.Strings;
using System.Collections.ObjectModel;
using System.Diagnostics;
using AppoMobi.Framework.Maui.Interfaces;


namespace Racebox.ViewModels
{
	public class DevicesPageViewModel : BaseViewModel, IDisposable
	{
		private BleDeviceViewModel _selectedFoundDevice;

		public ObservableCollection<BleDeviceViewModel> Items => Service.FoundDevices;

		public Command LoadItemsCommand => new Command(async () => await ExecuteLoadItemsCommand());

		public Command<BleDeviceViewModel> ItemTapped { get; }

		public DevicesPageViewModel(IServiceProvider services, IUIAssistant ui)
		{
			_services = services;
			_ui = ui;

			Title = "Устройства";

			ItemTapped = new Command<BleDeviceViewModel>(OnItemSelected);


		}

		public void Dispose()
		{

		}

		private IServiceProvider _services;
		private IUIAssistant _ui;
		private RaceBoxConnector _service;

		private RaceBoxConnector Service
		{
			get
			{
				if (_service == null)
				{
					_service = _services.GetService<RaceBoxConnector>();
					if (!_service.Initialized)
					{
						_service.Init(Application.Current.MainPage, ResStrings.VendorTitle);
					}
				}
				return _service;
			}
		}



		private bool _IsRefreshing;
		public bool IsRefreshing
		{
			get { return _IsRefreshing; }
			set
			{
				if (_IsRefreshing != value)
				{
					_IsRefreshing = value;
					OnPropertyChanged();
				}
			}
		}


		async Task ExecuteLoadItemsCommand()
		{
			if (IsBusy || Service.IsBusy)
				return;

			try
			{

				IsBusy = true;

				// Android has an internal limit of 5 startScan(…) method calls every 30 seconds per app

				if (await Service.CheckCanConnectDisplayErrors())
				{
					Items.Clear();
					await Service.ScanAsync();
					HasData = true;
				}

			}
			catch (Exception ex)
			{
				Debug.WriteLine(ex);
			}
			finally
			{
				IsBusy = false;
				IsRefreshing = false;
			}
		}



		private bool HasData { get; set; }

		public void OnAppearing()
		{
			return;

			SelectedFoundDevice = null;

			if (!HasData)
			{
				IsBusy = true;
				//LoadItemsCommand.Execute(null);
			}

		}

		// private FoundDevice _selectedFoundDevice;
		public BleDeviceViewModel SelectedFoundDevice
		{
			get { return _selectedFoundDevice; }
			set
			{
				if (_selectedFoundDevice != value)
				{
					_selectedFoundDevice = value;
					OnPropertyChanged();
				}
			}
		}



		async void OnItemSelected(BleDeviceViewModel foundDevice)
		{
			if (foundDevice == null)
				return;

			//todo

			// This will push the ItemDetailPage onto the navigation stack
			//var state = $"{nameof(DevicePageInsideShell)}?{nameof(DeviceViewModel.ItemId)}={foundDevice.Id}";
			//await Shell.Current.GoToAsync(state);

		}
	}
}