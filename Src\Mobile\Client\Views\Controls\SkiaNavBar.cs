using SkiaSharp;

namespace Racebox.Views.Partials;

public class SkiaNavBar : SkiaLayout
{
    public SkiaNavBar()
    {
        UseCache = SkiaCacheType.Image;
    }


    public override SKPath CreateClip(object arguments, bool usePosition, SKPath path = null)
    {
        path ??= new SKPath();

        var scale = RenderingScale;
        var cornerRadius = (float)(16.0 * scale);

        var rrect = new SKRoundRect();
        rrect.SetRectRadii(DrawingRect, new[]
        {
            new SKPoint(cornerRadius,cornerRadius),
            new SKPoint(cornerRadius, cornerRadius),
            new SKPoint(0,0),
            new SKPoint(0,0),
        });

        path.AddRoundRect(rrect);
        return path;
    }

}