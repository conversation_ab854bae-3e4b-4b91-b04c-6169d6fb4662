using DrawnUi.Controls;

namespace Racebox.Views.Partials;

public class DrawnIHasDetailedDisplayCell : SkiaDrawnCell
{
    protected SkiaLabel LabelDisplay1;
    protected SkiaLabel LabelDisplay2;

    protected override void SetContent(object ctx)
    {

        if (ctx is IHasDetailedDisplay item)
        {
            LockUpdate(true);

            if (LabelDisplay1 == null)
            {
                LabelDisplay1 = this.FindView<SkiaLabel>("LabelDisplay1");
            }
            if (LabelDisplay2 == null)
            {
                LabelDisplay2 = this.FindView<SkiaLabel>("LabelDisplay2");
            }

            if (LabelDisplay1 != null)
            {
                LabelDisplay1.Text = item.Display1;
            }

            if (LabelDisplay2 != null)
            {
                LabelDisplay2.Text = item.Display2;
            }

            LockUpdate(false);
            Update();
        }
    }

    public override void OnAppearing()
    {
        base.OnAppearing();

        if (BindingContext is IHasDetailedDisplay item)
        {
            if (!item.WasShown)
            {
                //abort previous running anim if any
                _cancellationTokenSourceFinale?.Cancel();
                _cancellationTokenSourceFinale = new();

                item.WasShown = true;

                Opacity = 0;
                FadeToAsync(1.0, 200, Easing.CubicIn, _cancellationTokenSourceFinale);

            }
            else
            {
                Opacity = _savedOpacity;
            }
        }
    }

    CancellationTokenSource _cancellationTokenSourceFinale;
    double _savedOpacity = 1.0;



}