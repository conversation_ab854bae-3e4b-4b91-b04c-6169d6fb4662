﻿<?xml version="1.0" encoding="utf-8"?>

<views2:BasePage
    x:Class="Racebox.Views.PageResult"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:interfaces="clr-namespace:Racebox.Shared.Interfaces;assembly=Racebox.Shared"
    xmlns:maps1="clr-namespace:Racebox.Views.Maps"
    xmlns:partials="clr-namespace:Racebox.Views.Partials"
    xmlns:strings="clr-namespace:Racebox.Shared.Strings;assembly=Racebox.Shared"
    xmlns:viewModels="clr-namespace:Racebox.ViewModels"
    xmlns:views2="clr-namespace:Racebox.Views"
    xmlns:drawn="clr-namespace:Racebox.Views.Drawn"
    xmlns:racebox="clr-namespace:Racebox"
    xmlns:resources="clr-namespace:Racebox.Resources"
    Title="PageResult"
    x:DataType="viewModels:HistoryResultViewModel">

    <ContentPage.Resources>
        <ResourceDictionary>

            <Style
                x:Key="StyleSkiaIconIsValid"
                Class="StyleSkiaIconIsValid"
                TargetType="draw:SkiaSvg">
                <Setter Property="SvgString" Value="{StaticResource SvgStatusOk}" />
                <Setter Property="TintColor" Value="Green" />
            </Style>

            <Style
                x:Key="StyleSkiaIconNotValid"
                Class="StyleSkiaIconNotValid"
                TargetType="draw:SkiaSvg">
                <Setter Property="SvgString" Value="{StaticResource SvgStatusFail}" />
                <Setter Property="TintColor" Value="Red" />
            </Style>

        </ResourceDictionary>
    </ContentPage.Resources>

    <ContentPage.Content>

        <Grid
            HorizontalOptions="Fill"
            RowDefinitions="*"
            VerticalOptions="FillAndExpand">

            <!--  page background with gradient and frame  -->
            <draw:Canvas
                HorizontalOptions="Fill"
                Tag="BackgroundWithFrame"
                VerticalOptions="Fill">

                <partials:GradientToBorder
                    HorizontalOptions="Fill"
                    MissingHeight="{Binding NavbarModel.BottomTabsHeightRequest}"
                    VerticalOptions="Fill">

                    <drawn:EmbossedFrameDrawn
                        x:Name="DesignFrame"
                        BackgroundColor="Transparent"
                        HorizontalOptions="Fill"
                        VerticalOptions="Fill" />

                </partials:GradientToBorder>
            </draw:Canvas>

            <draw:Canvas
                Gestures="Enabled"
                RenderingMode="Accelerated"
                HorizontalOptions="Fill"
                VerticalOptions="FillAndExpand">

                <draw:SkiaLayout
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill">

                    <draw:SkiaLayout
                        Margin="12,16,12,8"
                        HorizontalOptions="Fill"
                        Spacing="0"
                        Type="Column"
                        VerticalOptions="Fill">

                        <!--  NAVBAR  -->
                        <partials:SkiaNavBar
                            x:Name="NavBar"
                            Margin="0,0,1,0"
                            HeightRequest="65"
                            HorizontalOptions="Fill">

                            <draw:SkiaSvg
                                Margin="16,0,16,0"
                                HeightRequest="16"
                                HorizontalOptions="Start"
                                SvgString="{StaticResource SvgGoBack}"
                                TintColor="#CB6336"
                                VerticalOptions="Center"
                                WidthRequest="16" />

                            <draw:SkiaHotspot
                                CommandTapped="{Binding NavbarModel.CommandGoBack, Mode=OneTime}"
                                HorizontalOptions="Start"
                                TransformView="{x:Reference NavBar}"
                                WidthRequest="44" />

                            <draw:SkiaLabel
                                Margin="48,0,16,0"
                                FontSize="14"
                                LineBreakMode="TailTruncation"
                                MaxLines="1"
                                Style="{StaticResource SkiaLabelDefaultStyle}"
                                Tag="NavTitle"
                                Text="{Binding Title}"
                                TextColor="#E8E3D7"
                                TranslationY="1"
                                VerticalOptions="Center" />

                            <draw:SkiaSvg
                                Margin="0,0,16,0"
                                HeightRequest="16"
                                HorizontalOptions="End"
                                SvgString="{StaticResource SvgBin}"
                                TintColor="#CB6336"
                                VerticalOptions="Center"
                                WidthRequest="16" />

                            <draw:SkiaHotspot
                                CommandTapped="{Binding CommandDelete, Mode=OneTime}"
                                HorizontalOptions="End"
                                TransformView="{x:Reference NavBar}"
                                WidthRequest="44" />

                            <!--  LINE HORIZONTAL  -->
                            <draw:SkiaShape
                                Margin="-16,0"
                                BackgroundColor="Black"
                                CornerRadius="0"
                                HeightRequest="1"
                                HorizontalOptions="Fill"
                                StrokeWidth="0"
                                VerticalOptions="End">
                                <draw:SkiaShape.FillGradient>

                                    <draw:SkiaGradient
                                        EndXRatio="1"
                                        EndYRatio="0"
                                        StartXRatio="0"
                                        StartYRatio="0"
                                        Type="Linear">
                                        <draw:SkiaGradient.Colors>
                                            <Color>#00E8E3D7</Color>
                                            <Color>#99E8E3D7</Color>
                                            <Color>#00E8E3D7</Color>
                                        </draw:SkiaGradient.Colors>
                                    </draw:SkiaGradient>

                                </draw:SkiaShape.FillGradient>
                            </draw:SkiaShape>

                        </partials:SkiaNavBar>

                        <!--  CONTENT SCROLL  -->
                        <draw:SkiaScroll
                            x:Name="MainScroll"
                            HorizontalOptions="Fill"
                            IsVisible="{Binding HasData}"
                            Opacity="0.01"
                            VisibilityChanged="OnScrollViewAppeared"
                            VerticalOptions="Fill">

                            <draw:SkiaLayout
                                Padding="16,0"
                                HorizontalOptions="Fill"
                                Spacing="0"
                                Tag="ResultStack"
                                Type="Column"
                                UseCache="ImageComposite">

                                <!--  HEADER  -->
                                <draw:SkiaLayout
                                    Padding="0,0,1,0"
                                    HeightRequest="90"
                                    HorizontalOptions="Fill"
                                    UseCache="Operations"
                                    VerticalOptions="Fill">

                                    <!--  EXPORT BTN  -->
                                    <draw:SkiaLayout
                                        HeightRequest="78"
                                        HorizontalOptions="Start"
                                        LockRatio="1"
                                        TranslationX="12"
                                        TranslationY="-1"
                                        UseCache="Image"
                                        VerticalOptions="Center">

                                        <draw:SkiaShape
                                            x:Name="BtnBackground"
                                            Margin="16"
                                            BackgroundColor="#42464B"
                                            CornerRadius="24"
                                            HorizontalOptions="Fill"
                                            StrokeColor="#15191E"
                                            StrokeWidth="1.85"
                                            VerticalOptions="Fill">

                                            <draw:SkiaShape.StrokeGradient>

                                                <draw:SkiaGradient
                                                    EndXRatio="0.9"
                                                    EndYRatio="0.9"
                                                    StartXRatio="0.1"
                                                    StartYRatio="0.1"
                                                    Type="Linear">
                                                    <draw:SkiaGradient.Colors>
                                                        <Color>#E47F53</Color>
                                                        <Color>#924321</Color>
                                                    </draw:SkiaGradient.Colors>
                                                </draw:SkiaGradient>

                                            </draw:SkiaShape.StrokeGradient>

                                            <draw:SkiaShape.FillGradient>

                                                <draw:SkiaGradient
                                                    EndXRatio="1"
                                                    EndYRatio="1"
                                                    StartXRatio="0"
                                                    StartYRatio="0"
                                                    Type="Linear">
                                                    <draw:SkiaGradient.Colors>
                                                        <Color>#924321</Color>
                                                        <Color>#E47F53</Color>
                                                    </draw:SkiaGradient.Colors>
                                                </draw:SkiaGradient>

                                            </draw:SkiaShape.FillGradient>

                                            <draw:SkiaShape.Shadows>

                                                <draw:SkiaShadow
                                                    Blur="6"
                                                    Opacity="0.26"
                                                    X="3"
                                                    Y="3"
                                                    Color="#090A0B" />

                                                <draw:SkiaShadow
                                                    Blur="9"
                                                    Opacity="0.32"
                                                    X="-3"
                                                    Y="-3"
                                                    Color="#979696" />

                                            </draw:SkiaShape.Shadows>
                                        </draw:SkiaShape>

                                        <draw:SkiaSvg
                                            HeightRequest="24"
                                            HorizontalOptions="Center"
                                            SvgString="{StaticResource SvgShare}"
                                            TintColor="White"
                                            TranslationY="-1"
                                            VerticalOptions="Center"
                                            WidthRequest="24" />

                                        <draw:SkiaHotspot
                                            CommandTapped="{Binding CommandExportLogs, Mode=OneTime}"
                                            TransformView="{x:Reference BtnBackground}" />

                                    </draw:SkiaLayout>

                                    <!--  Car Title  -->
                                    <draw:SkiaLabel
                                        FontFamily="FontTextBold"
                                        FontSize="20"
                                        HorizontalOptions="EndAndExpand"
                                        LineBreakMode="TailTruncation"
                                        MaxLines="1"
                                        Text="{Binding CarTitle}"
                                        TextColor="#CB6336"
                                        TranslationY="8"
                                        VerticalOptions="Start" />

                                    <!--  Time  -->
                                    <draw:SkiaLabel
                                        BackgroundColor="Transparent"
                                        FontFamily="FontTextBold"
                                        FontSize="16"
                                        HorizontalOptions="EndAndExpand"
                                        LineBreakMode="TailTruncation"
                                        MaxLines="1"
                                        Style="{StaticResource SkiaLabelStyle}"
                                        Text="{Binding ExplainDate}"
                                        TranslationY="-32"
                                        VerticalOptions="End" />

                                    <draw:SkiaLabel
                                        FontFamily="FontTextBold"
                                        FontSize="16"
                                        HorizontalOptions="EndAndExpand"
                                        LineBreakMode="TailTruncation"
                                        MaxLines="1"
                                        Style="{StaticResource SkiaLabelStyle}"
                                        Text="{Binding ExplainTime}"
                                        TranslationY="-12"
                                        VerticalOptions="End" />

                                    <!--  IsValid header 106  -->

                                    <!--<draw:SkiaLabel
                                Margin="0,0,19,0"
                                FontFamily="FontTextBold"
                                FontSize="16"
                                HorizontalOptions="EndAndExpand"
                                LineBreakMode="TailTruncation"
                                MaxLines="1"
                                Style="{StaticResource SkiaLabelStyle}"
                                Text="{x:Static strings:ResStrings.IsValid}"
                                TranslationY="-48"
                                VerticalOptions="End" />-->

                                    <!--<draw:SkiaSvg
                                HeightRequest="21"
                                HorizontalOptions="End"
                                TranslationX="3"
                                States="{Binding VisualStates}"
                                TranslationY="-50.0"
                                VerticalOptions="End"
                                WidthRequest="21">
                                <draw:SkiaControl.Styles>
                                    <draw:ConditionalStyle
                                        State="Normal"
                                        Style="{x:StaticResource StyleSkiaIconIsValid}" />
                                    <draw:ConditionalStyle
                                        State="Invalid"
                                        Style="{x:StaticResource StyleSkiaIconNotValid}" />
                                </draw:SkiaControl.Styles>
                            </draw:SkiaSvg>-->

                                    <!--  LINE HORIZONTAL  -->
                                    <draw:SkiaShape
                                        Margin="-16,0"
                                        BackgroundColor="Black"
                                        CornerRadius="0"
                                        HeightRequest="2"
                                        HorizontalOptions="Fill"
                                        StrokeWidth="0"
                                        VerticalOptions="End">
                                        <draw:SkiaShape.FillGradient>

                                            <draw:SkiaGradient
                                                EndXRatio="1"
                                                EndYRatio="0"
                                                StartXRatio="0"
                                                StartYRatio="0"
                                                Type="Linear">
                                                <draw:SkiaGradient.Colors>
                                                    <Color>#00E8E3D7</Color>
                                                    <Color>#99E8E3D7</Color>
                                                    <Color>#00E8E3D7</Color>
                                                </draw:SkiaGradient.Colors>
                                            </draw:SkiaGradient>

                                        </draw:SkiaShape.FillGradient>
                                    </draw:SkiaShape>

                                </draw:SkiaLayout>

                                <!--  RESULTS  -->
                                <draw:SkiaLayout
                                    x:Name="ReportLayout"
                                    Margin="0,0,0,10"
                                    ItemsSource="{Binding MeasureResults}"
                                    Spacing="0"
                                    Type="Column"
                                    UseCache="Image">

                                    <draw:SkiaLayout.ItemTemplate>
                                        <DataTemplate x:DataType="interfaces:IHasDetailedDisplay">

                                            <partials:DrawnCellMeasurement
                                                Margin="0,0"
                                                HeightRequest="41"
                                                HorizontalOptions="Fill"
                                                UseCache="Operations">

                                                <draw:SkiaLayout
                                                    Padding="0,0,0,0"
                                                    HorizontalOptions="Fill"
                                                    VerticalOptions="Fill">

                                                    <!--  Text="{Binding DisplayInfo}"  -->
                                                    <draw:SkiaLabel
                                                        Padding="4,0,0,1"
                                                        FontFamily="FontText"
                                                        FontSize="16.0"
                                                        HorizontalOptions="Start"
                                                        MaxLines="1"
                                                        Tag="LabelDisplay1"
                                                        TextColor="#E8E3D7"
                                                        TranslationY="0"
                                                        VerticalOptions="Center" />

                                                    <draw:SkiaLabel
                                                        Padding="0,0,4,1"
                                                        FontFamily="FontText"
                                                        FontSize="16.0"
                                                        HorizontalOptions="End"
                                                        MaxLines="1"
                                                        Tag="LabelDisplay2"
                                                        TextColor="#E8E3D7"
                                                        TranslationY="0"
                                                        VerticalOptions="Center" />

                                                    <!--  LINE HORIZONTAL  -->
                                                    <draw:SkiaShape
                                                        BackgroundColor="Black"
                                                        CornerRadius="0"
                                                        HeightRequest="1"
                                                        HorizontalOptions="Fill"
                                                        StrokeWidth="0"
                                                        VerticalOptions="End">
                                                        <draw:SkiaShape.FillGradient>

                                                            <draw:SkiaGradient
                                                                EndXRatio="1"
                                                                EndYRatio="0"
                                                                StartXRatio="0"
                                                                StartYRatio="0"
                                                                Type="Linear">
                                                                <draw:SkiaGradient.Colors>
                                                                    <Color>#00E8E3D7</Color>
                                                                    <Color>#99E8E3D7</Color>
                                                                    <Color>#00E8E3D7</Color>
                                                                </draw:SkiaGradient.Colors>
                                                            </draw:SkiaGradient>

                                                        </draw:SkiaShape.FillGradient>
                                                    </draw:SkiaShape>

                                                </draw:SkiaLayout>


                                            </partials:DrawnCellMeasurement>

                                        </DataTemplate>
                                    </draw:SkiaLayout.ItemTemplate>

                                </draw:SkiaLayout>

                                <!--  STATS  -->
                                <draw:SkiaDecoratedGrid
                                    ColumnDefinitions="*,*"
                                    ColumnSpacing="1.1"
                                    DefaultRowDefinition="68"
                                    RowSpacing="1.0"
                                    Type="Grid"
                                    UseCache="Operations"
                                    VerticalOptions="Start">

                                    <!--  row  0  -->

                                    <!--  MAX SPEED  -->
                                    <draw:SkiaLayout
                                        Grid.Row="0"
                                        Grid.Column="0"
                                        HorizontalOptions="FillAndExpand"
                                        UseCache="Operations"
                                        VerticalOptions="FillAndExpand">

                                        <draw:SkiaLabel
                                            Style="{StaticResource OutputSkiaLabelStyle}"
                                            Text="{Binding DisplayMaxSpeed}" />

                                        <draw:SkiaLabel
                                            HorizontalOptions="Center"
                                            Style="{StaticResource SkiaLabelStyle}"
                                            Text="{x:Static strings:ResStrings.MaxSpeed}"
                                            TranslationY="-12"
                                            VerticalOptions="End" />

                                    </draw:SkiaLayout>

                                    <!--  УКЛОН  -->
                                    <draw:SkiaLayout
                                        Grid.Row="0"
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        UseCache="Operations"
                                        VerticalOptions="FillAndExpand">

                                        <draw:SkiaLabel
                                            Style="{StaticResource OutputSkiaLabelStyle}"
                                            Text="{Binding DisplayIncline}">
                                            <!--<draw:SkiaControl.Styles>
                                        <draw:ConditionalStyle
                                            State="Normal"
                                            Style="{x:StaticResource SkiaEnabledLabel}" />
                                        <draw:ConditionalStyle
                                            State="Invalid"
                                            Style="{x:StaticResource SkiaErrorLabel}" />
                                    </draw:SkiaControl.Styles>-->
                                            <draw:SkiaControl.Triggers>
                                                <DataTrigger
                                                    Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Normal'}"
                                                    TargetType="draw:SkiaLabel"
                                                    Value="True">
                                                    <Setter Property="TextColor"
                                                            Value="{x:StaticResource ColorEnabled}" />
                                                </DataTrigger>
                                                <DataTrigger
                                                    Binding="{Binding VisualStates, Converter={StaticResource StatesConverter}, ConverterParameter='Invalid'}"
                                                    TargetType="draw:SkiaLabel"
                                                    Value="True">
                                                    <Setter Property="TextColor" Value="{x:StaticResource ColorFail}" />
                                                </DataTrigger>
                                            </draw:SkiaControl.Triggers>

                                        </draw:SkiaLabel>

                                        <draw:SkiaLabel
                                            Style="{StaticResource SkiaLabelStyle}"
                                            Text="{x:Static strings:ResStrings.InclineMax}"
                                            TranslationY="-12"
                                            VerticalOptions="End" />

                                    </draw:SkiaLayout>

                                    <!--  row 1  -->

                                    <!--  ACCELERATION  -->
                                    <draw:SkiaLayout
                                        Grid.Row="1"
                                        Grid.Column="0"
                                        HorizontalOptions="FillAndExpand"
                                        UseCache="Operations"
                                        VerticalOptions="FillAndExpand">

                                        <draw:SkiaLabel
                                            Style="{StaticResource OutputSkiaLabelStyle}"
                                            Text="{Binding Item.MaxAcceleration, StringFormat='{0:0.00}G'}" />

                                        <draw:SkiaLabel
                                            HorizontalOptions="Center"
                                            Style="{StaticResource SkiaLabelStyle}"
                                            Text="{x:Static strings:ResStrings.MaxAcceleration}"
                                            TranslationY="-12"
                                            VerticalOptions="End" />

                                    </draw:SkiaLayout>

                                    <!--  ROLLOUT  -->
                                    <draw:SkiaLayout
                                        Grid.Row="1"
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        UseCache="Operations"
                                        VerticalOptions="FillAndExpand">

                                        <draw:SkiaLabel
                                            Style="{StaticResource OutputSkiaLabelStyle}"
                                            Text="{Binding ExplainRollOut}" />

                                        <draw:SkiaLabel
                                            Style="{StaticResource SkiaLabelStyle}"
                                            Text="{x:Static strings:ResStrings.RollOut}"
                                            TranslationY="-12"
                                            VerticalOptions="End" />

                                    </draw:SkiaLayout>

                                    <!--  row 2  -->

                                    <!--  INSTANT  -->
                                    <draw:SkiaLayout
                                        Grid.Row="2"
                                        Grid.Column="0"
                                        HorizontalOptions="FillAndExpand"
                                        UseCache="Operations"
                                        VerticalOptions="FillAndExpand">

                                        <draw:SkiaLabel
                                            Style="{StaticResource OutputSkiaLabelStyle}"
                                            Text="{Binding ExplainIsInstant}" />

                                        <draw:SkiaLabel
                                            HorizontalOptions="Center"
                                            Style="{StaticResource SkiaLabelStyle}"
                                            Text="{x:Static strings:ResStrings.MeasureInstant}"
                                            TranslationY="-12"
                                            VerticalOptions="End" />

                                    </draw:SkiaLayout>

                                    <!--  VALID  -->
                                    <draw:SkiaLayout
                                        Grid.Row="2"
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        UseCache="Operations"
                                        VerticalOptions="FillAndExpand">

                                        <draw:SkiaLabel
                                            AutoSize="FitHorizontal"
                                            HorizontalOptions="Center"
                                            HorizontalTextAlignment="Center"
                                            Style="{StaticResource OutputSkiaLabelStyle}"
                                            Text="{Binding ExplainIsValid}" />

                                        <draw:SkiaLabel
                                            Style="{StaticResource SkiaLabelStyle}"
                                            Text="{x:Static strings:ResStrings.IsValid}"
                                            TranslationY="-12"
                                            VerticalOptions="End" />

                                    </draw:SkiaLayout>

                                    <!--  row 3  -->

                                    <!--  SATS  -->
                                    <draw:SkiaLayout
                                        Grid.Row="3"
                                        Grid.Column="0"
                                        HorizontalOptions="FillAndExpand"
                                        UseCache="Operations"
                                        VerticalOptions="FillAndExpand">

                                        <draw:SkiaLabel
                                            Style="{StaticResource OutputSkiaLabelStyle}"
                                            Text="{Binding DisplaySatellites}" />

                                        <draw:SkiaLabel
                                            HorizontalOptions="Center"
                                            Style="{StaticResource SkiaLabelStyle}"
                                            Text="{x:Static strings:ResStrings.Satellites}"
                                            TranslationY="-12"
                                            VerticalOptions="End" />

                                    </draw:SkiaLayout>

                                    <!--  WEATHER  -->
                                    <draw:SkiaLayout
                                        Grid.Row="3"
                                        Grid.Column="1"
                                        HorizontalOptions="FillAndExpand"
                                        UseCache="Operations"
                                        VerticalOptions="FillAndExpand">

                                        <draw:SkiaLayout
                                            HorizontalOptions="Center"
                                            TranslationY="15"
                                            Type="Row">

                                            <draw:SkiaLabel
                                                FontFamily="Fa"
                                                FontSize="16"
                                                Text="{Binding WeatherIcon}"
                                                TranslationY="4" />

                                            <draw:SkiaLabel
                                                Margin="0"
                                                HorizontalOptions="Start"
                                                HorizontalTextAlignment="Start"
                                                MonoForDigits="8"
                                                Style="{StaticResource OutputSkiaLabelStyle}"
                                                Text="{Binding DisplayTemp}"
                                                TranslationY="0"
                                                VerticalOptions="Start" />

                                        </draw:SkiaLayout>

                                        <draw:SkiaLabel
                                            Style="{StaticResource SkiaLabelStyle}"
                                            Text="{x:Static strings:ResStrings.Weather}"
                                            TranslationY="-12"
                                            VerticalOptions="End" />

                                    </draw:SkiaLayout>

                                </draw:SkiaDecoratedGrid>

                                <!--  MAP  -->
                                <draw:SkiaShape
                                    Margin="12,16"
                                    draw:AddGestures.CommandTapped="{Binding CommandSmallMapTapped}"
                                    BackgroundColor="Black"
                                    CornerRadius="12"
                                    HeightRequest="100"
                                    HorizontalOptions="Fill"
                                    UseCache="Image">

                                    <maps1:RaceBoxMap
                                        x:Name="MainMap"
                                        InputTransparent="True"
                                        IsVisible="{Binding MapReady}"
                                        NeedPath="True"
                                        PathCoords="{Binding Path}">
                                        <!--<draw:SkiaControl.VisualEffects>

                                                <draw:TintWithAlphaEffect
                                                    Alpha="0.15"
                                                    ColorTint="Blue" />

                                                <draw:ChainSaturationEffect
                                            Value="0.58" />

                                            </draw:SkiaControl.VisualEffects>-->

                                    </maps1:RaceBoxMap>

                                </draw:SkiaShape>

                                <!--CHART FROM API-->
                                <draw:SkiaShape
                                    Margin="12,0,12,16"
                                    draw:AddGestures.CommandTapped="{Binding CommandSmallChartTapped}"
                                    BackgroundColor="Black"
                                    CornerRadius="12"
                                    HeightRequest="100"
                                    HorizontalOptions="Fill"
                                    UseCache="Image">

                                    <draw:SkiaLayout
                                        HorizontalOptions="Fill"
                                        VerticalOptions="Fill">

                                        <!--PREVIEW-->
                                        <draw:SkiaImage
                                            IsVisible="{Binding HasChart}"
                                            Source="{Binding ChartSource}"
                                            HorizontalOptions="Fill"
                                            VerticalOptions="Fill"
                                            x:Name="ChartPreview"
                                            VerticalAlignment="End"
                                            Aspect="AspectCover"
                                            UseCache="Image"
                                            InputTransparent="True">
                                            <draw:SkiaControl.VisualEffects>

                                                <draw:TintWithAlphaEffect
                                                    Alpha="{x:Static resources:Styles.TintContentColorAlpha}"
                                                    ColorTint="{x:Static resources:Styles.TintContentColor}" />

                                                <draw:ChainAdjustContrastEffect Value="{x:Static resources:Styles.TintContentContrast}" />

                                                <draw:ChainAdjustLightnessEffect Value="{x:Static resources:Styles.TintContentLightness}" />

                                                <draw:ChainSaturationEffect Value="{x:Static resources:Styles.TintContentSaturation}" />

                                            </draw:SkiaControl.VisualEffects>
                                        </draw:SkiaImage>

                                                <draw:SkiaRichLabel 
                                            IsVisible="{Binding HasChart, Converter={x:StaticResource InverseBoolConverter}}"
                                            Text="{x:Static strings:ResStrings.BtnCreate}"
                                            HorizontalOptions="Center" VerticalOptions="Center"/>

                                        <!--CONTROLS               IsVisible="{Binding HasChart}" -->
                                        <draw:SkiaLayout
                               
                                            HorizontalOptions="Fill"
                                            BackgroundColor="#33401122"
                                            VerticalOptions="Fill">

                                            <!--<draw:SkiaSvg
                                            Margin="0,0,20,0"
                                            HeightRequest="16"
                                            HorizontalOptions="End"
                                            SvgString="{StaticResource SvgBin}"
                                            TintColor="#ffffff"
                                            VerticalOptions="Center"
                                            LockRatio="1" />

                                        <draw:SkiaHotspot
                                            CommandTapped="{Binding CommandDeleteChart, Mode=OneTime}"
                                            HorizontalOptions="End"
                                            WidthRequest="50" />

                                        <draw:SkiaSvg
                                            Margin="0,0,60,0"
                                            HeightRequest="20"
                                            HorizontalOptions="End"
                                            SvgString="{StaticResource SvgShare}"
                                            TintColor="#ffffff"
                                            VerticalOptions="Center"
                                            LockRatio="1" />

                                        <draw:SkiaHotspot
                                            Margin="0,0,50,0"
                                            CommandTapped="{Binding CommandShareChart, Mode=OneTime}"
                                            HorizontalOptions="End"
                                            WidthRequest="44" />-->

                                        </draw:SkiaLayout>

                                    </draw:SkiaLayout>
                                </draw:SkiaShape>

                            </draw:SkiaLayout>
                        </draw:SkiaScroll>
                    </draw:SkiaLayout>

                    <draw:SkiaLabelFps
                        Margin="32"
                        ForceRefresh="False"
                        HorizontalOptions="End"
                        IsVisible="{x:Static racebox:MauiProgram.ShowDebugInfo}"
                        Rotation="-45"
                        VerticalOptions="End"
                        ZIndex="100" />


                </draw:SkiaLayout>
            </draw:Canvas>


            <ActivityIndicator
                HorizontalOptions="Center"
                InputTransparent="True"
                IsRunning="{Binding IsBusy}"
                IsVisible="{Binding IsBusy}"
                VerticalOptions="Center" />

        </Grid>

    </ContentPage.Content>

</views2:BasePage>