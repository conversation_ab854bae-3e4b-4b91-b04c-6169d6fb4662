



namespace Racebox.Views.Partials;

//[ContentProperty(nameof(Content))]
public partial class EmbossedFrame : ContentView
{
	public EmbossedFrame()
	{
		InitializeComponent();
	}

	protected override SizeRequest OnMeasure(double widthConstraint, double heightConstraint)
	{
		var measured = base.OnMeasure(widthConstraint, heightConstraint);

		if (this.Content is DrawnView drawn)
		{
			drawn.Update();
		}

		return measured;
	}

	public static readonly BindableProperty CornerRadiusProperty = BindableProperty.Create(
		nameof(CornerRadius),
		typeof(double), typeof(EmbossedFrame), 16.0);
	public double CornerRadius
	{
		get { return (double)GetValue(CornerRadiusProperty); }
		set { SetValue(CornerRadiusProperty, value); }
	}

	//protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
	//{
	//    base.OnPropertyChanged(propertyName);

	//    if (propertyName == nameof(CornerRadius))
	//    {

	//    }

	//}
}