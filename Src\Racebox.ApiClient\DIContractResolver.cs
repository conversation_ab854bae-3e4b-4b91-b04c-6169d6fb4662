﻿using Newtonsoft.Json.Serialization;
using Racebox.ApiClient.Interfaces;

namespace Racebox.ApiClient;

public class DIContractResolver : CamelCasePropertyNamesContractResolver
{

    IDIMeta diMeta;
    IServiceProvider sp;

    public DIContractResolver(IDIMeta diMeta, IServiceProvider sp)
    {
        this.diMeta = diMeta;
        this.sp = sp;
    }

    protected override JsonObjectContract CreateObjectContract(Type objectType)
    {
        try
        {
            JsonObjectContract contract = DIResolveContract(objectType);

            var stop = true;

            contract.DefaultCreator = () => sp.GetRequiredService(objectType);

            return contract;
        }
        catch (Exception e)
        {
            Console.WriteLine($"[DIContractResolver] Cannot resolve type {objectType.Name}");
            //Console.WriteLine(e);
            throw;
        }


        //if (diMeta.IsRegistred(objectType))
        //{
        //    JsonObjectContract contract = DIResolveContract(objectType);
        //    contract.DefaultCreator = () => sp.GetService(objectType);

        //    return contract;
        //}

        //return base.CreateObjectContract(objectType);
    }
    private JsonObjectContract DIResolveContract(Type objectType)
    {
        var fType = diMeta.RegistredTypeFor(objectType);

        if (fType != null)
            return base.CreateObjectContract(fType);

        return CreateObjectContract(objectType);
    }



}