﻿ 
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AppoMobi.Mobile
{
    public enum ToastPosition
    {
        Top,
        Center,
        Bottom
    }

    public partial class Toast
    {
        private static Toast _instance;

        public static Toast Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new();
                }
                return _instance;
            }
        }

        public static void ShortMessage(string text)
        {
            Trace.WriteLine(text);
            MainThread.BeginInvokeOnMainThread(() =>
            {
                Instance.ShortAlert(text);
            });
        }

        public static void LongMessage(string text)
        {
            Trace.WriteLine(text);
            MainThread.BeginInvokeOnMainThread(() =>
            {
                Instance.LongAlert(text, ToastPosition.Bottom);
            });
        }
    }
}
