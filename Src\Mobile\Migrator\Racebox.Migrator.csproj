﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    
	  <TargetFrameworks>net9.0</TargetFrameworks>

    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

	  <ApplicationId>com.appomobi.racebox.migrator</ApplicationId>

  </PropertyGroup>

 
	<ItemGroup>

    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.0" />
    <PackageReference Include="SQLitePCLRaw.bundle_e_sqlite3" Version="2.1.10" />

    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>

	</ItemGroup>

 
	<ItemGroup>
	  <ProjectReference Include="..\Shared\Racebox.Shared.csproj" />
	</ItemGroup>



</Project>
