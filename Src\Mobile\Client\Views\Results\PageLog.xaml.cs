namespace Racebox.Views;

public partial class PageLog : ILazyPage
{
    public PageLog()
    {
        try
        {
            BindingContext = new ProjectViewModel();

            InitializeComponent();



            var processor = App.Instance.Services.GetService<RaceBoxStateProcessor>();
            var check = processor.InternalLog.Count();
            var list = processor.InternalLog.Flush();
            DebugEditor.Text = list.ToTags("\r\n");

        }
        catch (Exception e)
        {
            Designer.DisplayException(this, e);
        }


    }

    protected override void OnAppearing()
    {
        base.OnAppearing();

        //Settings.UpdateStatusBarUponTheme();

        var canUpdate = BindingContext as IUpdateUIState;
        canUpdate?.UpdateState(true);
    }

    public void OnViewAppearing()
    {
        OnAppearing();
    }

    public void OnViewDisappearing()
    {
        OnDisappearing();
    }

    public void UpdateControls(DeviceRotation orientation)
    {

    }


}