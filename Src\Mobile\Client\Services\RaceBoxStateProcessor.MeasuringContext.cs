﻿using Racebox.Shared.Enums;

namespace Racebox.Services;

public partial class RaceBoxStateProcessor
{
    public class MeasuringContext
    {
        public List<MeasuredRange> Ranges { get; set; } = new();
        public List<MeasuredDistance> Distances { get; set; } = new();

        // максимальное ускорение
        public double MaxAccel; // 1

        // флаг параметра ролл-аут
        public bool Rollout; // 1

        // флаг валидности замера по уклону дороги
        public bool InclineValid; // 1

        // флаг проверки уклона дороги
        public bool InclineCheck; // 1

        // максимальный уклон дороги при замере
        public double MaxIncline; // 2

        // максимальная скорость
        public double maxSpeed; // 2

        // enum единиц измерения (0 - км/ч, 1 - миль/ч)
        public OptionsUnits Units;

        public void Initialize(UserManager settings)
        {

            Units = settings.User.Options.Units;
            Rollout = settings.User.Options.Rollout;
            InclineCheck = true;

            InitializeDistances(settings);

            //стандартные скорости от нуля +
            //пользовательские скорости от X1 до X2
            InitializeSpeedRanges(settings, 0);
        }
        public MeasuringContext(UserManager settings)
        {
            Initialize(settings);
        }

        public void InitializeDistances(UserManager settings)
        {
            var custom = settings.User.Options.Distances.Where(s =>
                    s.Units == settings.User.Options.Units)
                .Select(s => new MeasuredDistance()
                {
                    Length = s.End,
                    Units = settings.User.Options.Units,
                    IsUserDefined = true
                })
                .ToList();

            IList<MeasuredDistance> standart = null;

            //if (settings.User.Options.Units == OptionsUnits.EU)
            //{
            //    standart = settings.DistancesInMeters.Select(s =>
            //            new MeasuredDistance()
            //            {
            //                Units = settings.User.Options.Units,
            //                Length = s
            //            })
            //        .ToList();
            //}
            //else
            //{
            //    standart = settings.DistancesInFeet.Select(s =>
            //            new MeasuredDistance()
            //            {
            //                Units = settings.User.Options.Units,
            //                Length = s
            //            })
            //        .ToList();
            //} было Concat(standart).

            Distances = custom.OrderBy(o => o.Length).ToList();
        }


        /// <summary>
        /// Достигнутые дистанции,требующие закрытия
        /// </summary>
        /// <param name="position"></param>
        /// <returns></returns>
        public List<MeasuredDistance> GetNeedFinalizeDistances(double position)
        {
            return Distances.Where(s =>
                    position > s.Length
                    && s.Time == null)
                .ToList();
        }

        /// <summary>
        /// Все незакрытые дистанции
        /// </summary>
        public List<MeasuredDistance> GetUnmeasuredDistances()
        {
            return Distances.Where(s =>
                    s.Time == null)
                .ToList();
        }

        /// <summary>
        /// Все незакрытые пользовательские диапазоны
        /// </summary>
        /// <param name="position"></param>
        /// <returns></returns>
        public int GetUnmeasuredCustomRangesCount()
        {
            return Ranges.Count(s =>
                s.IsUserDefined &&
                s.Time == null);
        }

        /// <summary>
        /// Все незакрытые пользовательские диапазоны
        /// </summary>
        public List<MeasuredRange> GetUnmeasuredCustomRanges()
        {
            return Ranges.Where(s =>
                    s.IsUserDefined &&
                    s.Time == null)
                .ToList();
        }

        /// <summary>
        /// Все незакрытые дистанции
        /// </summary>
        public int GetUnmeasuredDistancesCount()
        {
            return Distances.Count(s =>
                s.Time == null);
        }

        /// <summary>
        /// Берем только те диапазона которые еще не начались при указанной скорости
        /// </summary>
        /// <param name="settings"></param>
        /// <param name="startingSpeed"></param>
        public void InitializeSpeedRanges(UserManager settings, double startingSpeed)
        {
            var custom = settings.User.Options.SpeedRanges.Where(s =>
                    s.Units == settings.User.Options.Units &&
                    s.Start >= startingSpeed)
                .Select(s => new MeasuredRange(s.Start, s.End)
                {
                    IsUserDefined = !s.IsReadOnly,
                })
                .ToList();

            //IList<MeasuredRange> standart = null;

            //if (settings.User.Options.Units == OptionsUnits.EU)
            //{
            //    standart = settings.SpeedStopsKMH.Where(s =>
            //            s.Start >= startingSpeed)
            //        .Select(s => new MeasuredRange(s.Start, s.End)
            //        {
            //            Units = settings.User.Options.Units,
            //            IsUserDefined = false
            //        })
            //        .ToList();
            //}
            //else
            //{
            //    standart = settings.SpeedStopsMPH.Where(s =>
            //            s.Start >= startingSpeed)
            //        .Select(s => new MeasuredRange(s.Start, s.End)
            //        {
            //            Units = settings.User.Options.Units,
            //            IsUserDefined = false
            //        })
            //        .ToList();
            //} было Concat(standart).

            Ranges = custom.OrderBy(o => o.Start).ToList();
        }

        /// <summary>
        /// Начали замерять и теперь надо завершить замер
        /// </summary>
        /// <param name="speed"></param>
        /// <returns></returns>
        public List<MeasuredRange> GetNeedFinalizeRages(double speed)
        {
            return Ranges.Where(s =>
                s.StartedAtMs > 0
                && s.Time == null
                && s.End <= speed)
                .ToList();
        }

        /// <summary>
        /// Можно замерять
        /// </summary>
        /// <param name="speed"></param>
        /// <returns></returns>
        public List<MeasuredRange> GetRangesNeedStart(double speed)
        {
            return Ranges.Where(s =>
                    s.StartedAtMs == 0
                    && speed >= s.Start
                    && speed < s.End)
                    .ToList();
        }

        /// <summary>
        /// Можно замерять - только пользовательские
        /// </summary>
        /// <param name="speed"></param>
        /// <returns></returns>
        public List<MeasuredRange> GetRangesNeedStartForInstant(double speed, double prevSpeed)
        {
            var passed = Ranges.Where(s =>
                    s.IsUserDefined
                    && s.StartedAtMs == 0
                    && speed >= s.Start
                    && speed < s.End
                    && prevSpeed < s.Start)
                   .ToList();

            if (passed.Count > 0)
            {
                var stop = 1;
            }

            return passed;
        }
        /// <summary>
        /// Замер пошел но еще не завершен
        /// </summary>
        /// <param name="speed"></param>
        /// <returns></returns>
        public List<MeasuredRange> GetIncompleteRanges(double speed)
        {
            return Ranges.Where(s =>
                    s.StartedAtMs > 0
                    && s.Time == null)
                .ToList();
        }




    }
}