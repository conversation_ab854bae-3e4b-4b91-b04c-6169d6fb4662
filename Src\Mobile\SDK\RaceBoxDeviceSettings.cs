﻿using AppoMobi.Specials;

namespace Racebox.SDK;

/// <summary>
/// Custom logic to work with Racebox device settings.
/// </summary>
public class RaceBoxDeviceSettings
{
    public RaceBoxDeviceSettings(RaceBoxConnector connector)
    {
        _connector = connector;
    }

    private RaceBoxConnector _connector;
    private bool _busy;
    private bool _hasObtained;
    private bool _isWaiting;
    private Action _callback;

    public void Initialize(RaceBoxConnector connector)
    {
        _connector = connector;
    }

    void OnSettings(RaceBoxSettingsState raceBoxSettingsState)
    {
        _hasObtained = true;
        _callback?.Invoke();
    }

    public void ObtainSettings(Action callback)
    {
        if (_busy)
        {
            return; //already working on this
        }

        _hasObtained = false;
        _callback = callback;

        Tasks.StartDelayed(TimeSpan.FromMilliseconds(1), async () =>
        {

            while (!_hasObtained)
            {
                try
                {
                    if (!await _connector.RequestSettings(OnSettings))
                    {
                        break;
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
                await Task.Delay(2000);
            }

            _busy = false;

        });
    }
}