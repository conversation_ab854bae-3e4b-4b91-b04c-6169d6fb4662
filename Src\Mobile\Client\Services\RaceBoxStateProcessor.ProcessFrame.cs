﻿using Racebox.SDK;
using Racebox.Shared.Enums;
using Racebox.Shared.Strings;
using System.Diagnostics;

namespace Racebox.Services;

public partial class RaceBoxStateProcessor : BindableObject
{


    /// <summary>
    /// Мы никогда сюда не попадем если буфер не заполнен так тут не проверяем CycleCount итп
    /// </summary>
    /// <param name="data"></param>
    private async Task ProcessFrame(RaceBoxState data)
    {
        await semaphore.WaitAsync();

        try
        {

            _lastData = data;

            var altitudeBuffer = Buffer.Select(s => s.Altitude.GetValueOrDefault()).ToArray();
            //var speedBuffer = Buffer.Select(s => s.Speed.GetValueOrDefault()).ToArray();
            var timeBuffer = Buffer.Select(s => (s.TimeStampUtc - UtcBase).TotalMilliseconds).ToArray();

            //Частота легко-усредненная
            var hzBuffer = Buffer.GetBufferFrequency();

            // расчёт фильтрованных значений HZ
            var hzCurrent = hzBuffer.Average();
            var hzPrevious = FrequencyHz;
            if (hzPrevious == 0)
            {
                hzPrevious = data.FrequencyUpdatedHz;
            }
            FrequencyHz = alphaHz * hzPrevious + (1 - alphaHz) * hzCurrent;

            //Monitoring values
            AltitudeAverage = BufferAverage(altitudeBuffer);
            var okHDOP = statusHDOP(data);

            //SPEED приходит всегда в кмч
            // сохранение предыдущей скорости
            prevSpeed = Speed;

            //получение текущей скорости (км/ч или миль/ч)
            if (_userManager.User.Options.Units == OptionsUnits.EU)
            {
                Speed = data.Speed.HasValue ? data.Speed.GetValueOrDefault() : Speed;
            }
            else
            {
                Speed = data.Speed.HasValue ? data.SpeedMph : Speed;
            }

            //сглаживаем вывод на приборы
            SpeedOutput = (SpeedOutput == 0.0) ? Speed : 0.75 * SpeedOutput + 0.25 * Speed;

            // заполнение кольцевого буфера скоростей и времени
            speedBuffer[buffIndex] = Speed; // запись текущей скорости в буфер
            timeBuffer[buffIndex] = Ticks; // запись текущего времени в буфер
            buffIndex = (buffIndex == BUFFER_SIZE - 1) ? 0 : buffIndex + 1;
            // расчёт средней скорости в буфере
            SpeedAverage = AverageSpeed(speedBuffer);

            //TIMING
            // startTime > 0 - если качество приёма ухудшилось во время замера, замер не прерывается
            prevMs = Ticks;

            // расчёт текущего времени (время приёма пакета GPS данных)
            Ticks = HAL_GetTick(data.TimeStampUtc); // gps.satellites.age();

            //FILTERED ACCELERATION
            // расчёт текущего ускорения (в G * 100), с проверкой валидности текущей и предыдущей скоростей
            if (data.Speed.HasValue && prevSpeedValid)
            {
                rawAccelG = (short)(((Speed - prevSpeed) / (Ticks - prevMs)) * ACCEL_COEFF);
            }
            prevSpeedValid = data.Speed.HasValue;

            // расчёт фильтрованных значений ускорения (сильная и слабая фильтрация)
            AccellerationFiltered = (AccellerationFiltered == 0) ? 1 : (short)(alphaAccel * AccellerationFiltered + (1 - alphaAccel) * rawAccelG); // постоянная времени = 0.3 сек, в G*100
            fastAccelG = (fastAccelG == 0) ? rawAccelG : (short)(alphaFastAccel * fastAccelG + (1 - alphaFastAccel) * rawAccelG);

            //SIDE ACCELERATION
            if (data.Heading.HasValue)
            {
                //var alphaHeading = lastHeading - data.Heading.Value;

                prevHeading = (curHeading == 0.0) ? data.Heading.Value : curHeading;
                curHeading = data.Heading.Value;

                angSpeed = Ticks > lastMs
                    ?
                    s_angle180(prevHeading, curHeading) * DEG_TO_RAD * 1000 / (Ticks - lastMs)
                    :
                    angSpeed;

                SideAccellerationFiltered = (SideAccellerationFiltered == 0.0)
                    ?
                    (Speed / 3.6) * angSpeed / GRAV_ACCEL
                    :
                    HEADING_ALPHA_16HZ * SideAccellerationFiltered + (1 - HEADING_ALPHA_16HZ) * (Speed / 3.6) * angSpeed / GRAV_ACCEL;
            }
            else
            {
                curHeading = 0.0;
                angSpeed = 0.0;
            }


            //pressed START
            if (IsMeasuring)
            {

                void End(string explain)
                {
                    EndCause = explain;
                    measureFinished = true;
                }

                //intialize once
                if (!_lastWasMeasuring)
                {
                    StartTimeMs = Ticks;
                    InitializeMeasuring();
                    _lastWasMeasuring = true;
                }

                //расчет производных вне зависимости от замера
                if (Speed > MeasuringMaxSpeed)
                    MeasuringMaxSpeed = Speed;

                //валидные условия замера
                if (okHDOP || StartTime > 0)
                {
                    MeasuringTimeMs = Ticks;

                    // сохранение максимального значения ускорения
                    if (AccellerationFiltered > _context.MaxAccel && AccellerationFiltered <= 190)
                        _context.MaxAccel = AccellerationFiltered;

                    // сохранение максимального значения скорости
                    _context.maxSpeed = ((int)(Speed * 10) > _context.maxSpeed) ? (int)(Speed * 10) : _context.maxSpeed;

                    // расчёт качества вертикального местоположения
                    vdopOk = statusVDOP(data);

                    // выполняем перед перед замером
                    if (StartTime == 0) //замер еще не начался
                    {

                        // включение проверки уклона дороги, в зависимости качества вертикального местоположения
                        if (Ticks > 15000)//HAL_GetTick(data.TimeStampUtc)
                        {
                            // задержка после запуска прибора
                            if (InclineOK && !vdopOk)
                            {
                                InclineOK = false;
                                avgAlt = 0;
                                distAlt = 0.0;
                                _context.InclineCheck = false;
                            }
                            else if (!InclineOK && vdopOk)
                            {
                                InclineOK = true;
                                _context.InclineCheck = true;
                            }
                        }

                        // при готовности к замеру
                        // (зафиксировали что скорость упала до минимально необходимой)
                        if (ReadyToMeasure)
                        {
                            // логика ACCEL_FREE_RUN MODE

                            // проверка остановки автомобиля
                            if (Speed < START_SPEED_THRESHOLD_UNIT)
                            {

                                if (!_hadGo) //once
                                {
                                    Debug.WriteLine(" -GO-");
                                    App.Instance.PlaySoundFile("ready.mp3");
                                    _hadGo = true;
                                    MeasuringState = MeasuringState.Ready;
                                }

                                // усреднение точки старта (ACCEL_ALPHA имеет подходящее значение, ускорение ни при чём:)
                                startLoc.lat = startLoc.lat * ACCEL_ALPHA_16HZ + data.Latitude.GetValueOrDefault() * (1 - ACCEL_ALPHA_16HZ);
                                startLoc.lon = startLoc.lon * ACCEL_ALPHA_16HZ + data.Longitude.GetValueOrDefault() * (1 - ACCEL_ALPHA_16HZ);

                                // начало замера с места при превышении скорости START_SPEED_THRESHOLD_UNIT после остановки


                            }
                            else
                            {
                                // сброс флага готовности к замеру
                                ReadyToMeasure = false;

                                // расчёт поправки времени старта
                                var timeLag = (uint)(ACCEL_COEFF * Speed / fastAccelG);

                                // расчёт поправки при включенном параметре ролл-аут
                                if (_userManager.User.Options.Rollout)
                                {
                                    rolloutTime = (uint)(2493 / Math.Sqrt(fastAccelG)); // учёт ролл-аута
                                }

                                // расчёт времени старта
                                StartTime = MeasuringTimeMs + RACELOGIC_CORR - timeLag; // RACELOGIC_CORR - калибровочная поправка
                                                                                        // сброс флага замера с ходу

                                Debug.WriteLine($"startTime changed ({StartTime})");

                                IsInstant = false;
                            }


                        }
                        // при неготовности к замеру (скорость была/есть выше минимально необходимой)
                        else
                        {
                            //скорость упала?
                            if (SpeedAverage <= READY_SPEED_THRESHOLD)
                            {
                                //if (!_hadGo) //единоразово
                                //{
                                //    App.Instance.PlaySoundFile("ready.mp3");
                                //    _hadGo = true;

                                //    MeasuringChanged?.Invoke(this, MeasuringState.Ready);
                                //}

                                Debug.WriteLine("READY to measure.. ПОЕХАЛИ");

                                // условие готовности к замеру
                                // сброс времени начала замера
                                // установка точки старта
                                SetupMeasuring(data);

                                // установка флага готовности к замеру
                                ReadyToMeasure = true;
                            }

                            //ПРОВЕРКА НА СТАРТ ЗАМЕРА СХОДУ
                            if (prevSpeed > 0)
                            {
                                var userRangesForInstant = _context.GetRangesNeedStartForInstant(Speed, prevSpeed);
                                if (userRangesForInstant.Any())
                                {
                                    {
                                        // старт замера "сходу" между парами пользовательских скоростей
                                        Debug.WriteLine($"замер сходу между парами пользовательских скоростей {userRangesForInstant.Count} шт");

                                        //// установка точки старта
                                        SetupMeasuring(data);

                                        foreach (var range in userRangesForInstant)
                                        {
                                            // расчёт поправки времени старта (линейная интерполяция)
                                            var timeLag = (uint)((Speed - range.Start) * (MeasuringTimeMs - prevMs) / (Speed - prevSpeed));

                                            range.StartedAtMs = (timeLag < MeasuringTimeMs) ? MeasuringTimeMs - timeLag : MeasuringTimeMs;

                                            // расчёт времени старта - берем минимальную из доступных диапазонов
                                            if (range.StartedAtMs < StartTime || StartTime == 0)
                                            {
                                                StartTime = range.StartedAtMs;
                                                Debug.WriteLine("startTime changed");
                                            }
                                        }

                                        // установка флага замера "сходу"
                                        IsInstant = true;

                                        // джингл о начале замера - не здесь!!! - там где чек startTime > 0
                                    }
                                }

                            }

                        }
                    }

                    //==================================
                    // замер уже идет, начался (startTime > 0 )
                    //==================================
                    else
                    {

                        if (!_hadStart) //execute once
                        {
                            App.Instance.PlaySoundFile("start.mp3");
                            _hadStart = true;

                            MeasuringState = MeasuringState.Active;
                            //MeasuringChanged?.Invoke(this, MeasuringState.Active);
                        }


                        // расчёт дистанции от точки старта в метрах
                        MeasuringDistanceInMeters = DistanceMeters(startLoc.lat, startLoc.lon,
                            data.Latitude.GetValueOrDefault(), data.Longitude.GetValueOrDefault());

                        if (_measuredLat.HasValue && _measuredLon.HasValue)
                        {
                            _measuredDistance1 += DistanceMeters(_measuredLat.Value, _measuredLon.Value,
                                data.Latitude.GetValueOrDefault(), data.Longitude.GetValueOrDefault());
                        }
                        else
                        {
                            _measuredDistance1 = MeasuringDistanceInMeters;
                        }

                        _measuredLat = data.Latitude.GetValueOrDefault();
                        _measuredLon = data.Longitude.GetValueOrDefault();

                        // поправка дистанции при включенном параметре ролл-аут
                        if (_userManager.User.Options.Rollout)
                        {
                            MeasuringDistanceInMeters = (MeasuringDistanceInMeters >= 0.3)
                                ? MeasuringDistanceInMeters - 0.3
                                : 0;
                        }

                        // APP: обновление лейбла дистации

                        // расчёт уклона дороги
                        if (_context.InclineCheck)
                        {
                            var acmAltitude = (data.Altitude.GetValueOrDefault() - 500) * 100;

                            // фильтрация значения высоты (в сантиметрах)
                            avgAlt = (avgAlt == 0)
                                ?
                                acmAltitude
                                :
                                avgAlt * 0.95 + acmAltitude * 0.05;

                            // APP: обновление лейбла высоты на экране

                            // расчёт дистанции от предыдущей точки замера высоты
                            distAlt = (lastLoc.lat == 0)
                                ?
                                INCLINE_SAMPLE_DIST + 0.1
                                :
                                DistanceMeters(lastLoc.lat, lastLoc.lon,
                                data.Latitude.GetValueOrDefault(), data.Longitude.GetValueOrDefault());

                            // расчёт уклона дороги на отрезке INCLINE_SAMPLE_DIST
                            if (distAlt >= INCLINE_SAMPLE_DIST)
                            {
                                // расчёт уклона дороги (в % * 10)
                                MeasuringIncline = (lastAvgAlt == 0)
                                    ? 0
                                    : (avgAlt - lastAvgAlt) / (distAlt * 100) * 100; //в оригинале *1000 <--- !!!!

                                // сохранение максимального значения уклона
                                var newMacIncl = (Math.Abs(MeasuringIncline) > Math.Abs(MeasuringInclineMax))
                                    ?
                                    MeasuringIncline
                                    :
                                    MeasuringInclineMax;

                                if (MeasuringInclineMax != newMacIncl)
                                {
                                    Debug.WriteLine($"[INCLINE] new max {newMacIncl:0.00}");
                                }

                                MeasuringInclineMax = newMacIncl;

                                // сохранение текущего значения высоты
                                lastAvgAlt = avgAlt;

                                // начало нового отрезка замера уклона дороги
                                lastLoc.lat = data.Latitude.GetValueOrDefault();
                                lastLoc.lon = data.Longitude.GetValueOrDefault();
                            }
                        }

                        // заполнение структуры для записи лога
                        frame.speed = Speed;
                        frame.AccelG = AccellerationFiltered;
                        frame.accelG_raw = rawAccelG;

                        // averageSpeed in km/ms => 
                        var distCorrM = (int)(Speed / 360 * distCorr); // (MeasuringSpeed * 1000 / 60 / 60 / 1000 * 10 * distCorr) - in m*10 (dm)

                        if ((int)(MeasuringDistanceInMeters) >= distCorrM)
                        {
                            frame.Distance = (_userManager.User.Options.Units == OptionsUnits.EU)
                                ?
                                MeasuringDistanceInMeters - distCorrM
                                :
                                MeasuringDistanceInMeters * M_TO_FT - distCorrM * M_TO_FT;
                        }
                        else
                        {
                            frame.Distance = (_userManager.User.Options.Units == OptionsUnits.EU)
                                ?
                                MeasuringDistanceInMeters
                                :
                                MeasuringDistanceInMeters * M_TO_FT;
                        }

                        DistanceMeasured = frame.Distance;

                        frame.Incline = MeasuringIncline;

                        frame.TotalTimeMs = (MeasuringTimeMs > StartTime) ? MeasuringTimeMs - StartTime : 0;

                        MeasuredTimeMs = frame.TotalTimeMs;

                        // сохранение максимального уклона дороги
                        _context.MaxIncline = MeasuringInclineMax;

                        // запись строки с параметрами в лог-файл

                        //send log line totally async
                        void Log(MeasuredLogLine info)
                        {
                            Tasks.StartTimerAsync(TimeSpan.FromMilliseconds(10), async () =>
                            {
                                await Task.Run(() =>
                                {
                                    MeasuredFrame?.Invoke(this, info);
                                }).ConfigureAwait(false);

                                return false;
                            });
                        }

                        var log = new MeasuredLogLine
                        {
                            Index = TmpResult.Logs.Count,
                            TotalSeconds = frame.TotalTimeMs / 1000.0,
                            Lattitude = RaceBoxState.Latitude.GetValueOrDefault(),
                            Longitude = RaceBoxState.Longitude.GetValueOrDefault(),
                            Speed = frame.speed,
                            Distance = frame.Distance,
                            AccelerationLat = SideAccellerationFiltered,
                            AccelerationLon = frame.AccelG / 100.0,
                            AccelerationLonRaw = frame.accelG_raw / 100.0,
                            Altitude = RaceBoxState.Altitude.GetValueOrDefault(),
                            Incline = frame.Incline,
                            Heading = RaceBoxState.Heading.GetValueOrDefault(),
                            HDOP = RaceBoxState.HDOP.GetValueOrDefault(),
                            SatellitesCount = RaceBoxState.SatellitesCount
                        };

                        TmpResult.Logs.Add(log);

                        //Debug.WriteLine($"[LOG] {log.TotalSeconds:0.00}  " +
                        //                $"{log.Lattitude:0.0000000}, {log.Longitude:0.0000000} " +
                        //                $"speed {log.Speed:0.00} " +
                        //                $"dist {log.Distance:0.00} | {_measuredDistance1:0.00} " +
                        //                $"SIDE accel {log.AccelerationLat:0.00000} " +
                        //                $"accel {log.AccelerationLon:0.00} " +
                        //                //                                            $"accelRAW {log.AccelerationLonRaw:0.00} " +
                        //                $"alt {log.Altitude:0.00} " +
                        //                $"incline {log.Incline:0.00} " +
                        //                $"cource {log.Heading:0.00} " +
                        //                $"hdop {log.HDOP:0.00} " +
                        //                $"sats {log.SatellitesCount:0} (start {startLoc.lat:0.0000000})");

                        Log(log);


                        //дополнительные диапазоны во время замера сходу..
                        if (IsInstant && prevSpeed > 0)
                        {
                            var userRangesForInstant = _context.GetRangesNeedStartForInstant(Speed, prevSpeed);
                            if (userRangesForInstant.Any())
                            {
                                foreach (var range in userRangesForInstant)
                                {
                                    if (range.StartedAtMs == 0)
                                    {
                                        // расчёт поправки времени старта (линейная интерполяция)
                                        var timeLag = (uint)((Speed - range.Start) * (MeasuringTimeMs - prevMs) / (Speed - prevSpeed));
                                        range.StartedAtMs = (timeLag < MeasuringTimeMs) ? MeasuringTimeMs - timeLag : MeasuringTimeMs;
                                    }
                                }
                            }
                        }

                        var rangesNeedFinalize = _context.GetNeedFinalizeRages(Speed);

                        // ---------------------------------------------------------------------------
                        // Проверка на условия прекращения замера:
                        // ---------------------------------------------------------------------------

                        #region Проверка на условия прекращения замера

                        // - замерили всё что было в массивах - NEW 
                        if (_context.GetUnmeasuredDistancesCount() == 0 && rangesNeedFinalize.Count == 0)
                        {
                            End($"Замеряли все данные");
                        }
                        else
                        // - время замера выше ACC_TIMEOUT
                        if (MeasuringTimeMs > StartTime + ACC_TIMEOUT)
                        {
                            End(ResStrings.BadMeasureTooLong);
                        }
                        else
                        // - ускорение ниже порога DECELERATION_THRESHOLD
                        if (AccellerationFiltered < DECELERATION_THRESHOLD)
                        {
                            End(ResStrings.BadMeasureLowAccel);
                        }
                        else
                        // замер "сходу" и скорость ниже начальной скорости первой пары и время замера больше 1 сек
                        if (IsInstant && rangesNeedFinalize.Any()
                                      && Speed < rangesNeedFinalize.Min(m => m.Start)
                                      && MeasuringTimeMs > StartTime + 1000)
                        {
                            End(ResStrings.BadMeasureInstant);
                        }
                        else
                        // замер с места и скорость ниже READY_SPEED_THRESHOLD_UNIT
                        if (!IsInstant && Speed < READY_SPEED_THRESHOLD_UNIT)
                        {
                            End(ResStrings.BadMeasureLowSpeed);
                        }

                        #endregion

                        // ---------------------------------------------------------------------------
                        // проверка флага завершения замера
                        // ---------------------------------------------------------------------------

                        #region проверка флага завершения замера

                        var rangesFailed = _context.GetIncompleteRanges(Speed);
                        if (measureFinished && HAL_GetTick(data.TimeStampUtc) >= delayTime)
                        {
                            // проверка "неудачности" завершения замера:

                            // было:
                            // - замер "сходу" и ненулевой результат замера между парами скоростей
                            // - замер с места и ненулевой результат замера до первой граничной скорости
                            // стало:

                            // определение валидности замера в зависимости от уклона дороги
                            _context.InclineValid = Math.Abs(_context.MaxIncline) < INCLINE_THRESHOLD;

                            // нет измеренных диапазонов или..
                            if (TmpResult.Ranges.Count == 0)
                            {
                                // замер неудачный
                                Debug.WriteLine("вывод сообщения о неудачности замера");
                                App.Instance.PlaySoundFile("fail.mp3");
                                End(ResStrings.BadMeasureNoRanges);
                                MeasureFail?.Invoke(this, MeasureError.Other);
                                MeasuringState = MeasuringState.Error;
                            }
                            else
                            // нет замерянных дистанций
                            if (!IsInstant && TmpResult.Distances.Count == 0)
                            {
                                // замер неудачный
                                App.Instance.PlaySoundFile("fail.mp3");
                                End(ResStrings.BadMeasureNoDistances);
                                MeasureFail?.Invoke(this, MeasureError.Other);
                                MeasuringState = MeasuringState.Error;
                            }
                            else
                            {
                                // замер удачный
                                Debug.WriteLine("замер удачный");

                                // установка даты и времени замера

                                // проверка корректности записи лога (для приложения не применимо)

                                // APP: сохранение результата замера на экране История

                                TmpResult.IsValid = _context.InclineValid;
                                TmpResult.IsInstant = IsInstant;
                                TmpResult.MaxSpeed = MeasuringMaxSpeed;// MaxSpeed;
                                TmpResult.MaxIncline = MeasuringInclineMax;
                                TmpResult.MaxAcceleration = MaxAcceleration;
                                TmpResult.Duration = MeasuredTime;

                                App.Instance.PlaySoundFile("finish.mp3");

                                MeasureSucces?.Invoke(this, TmpResult);
                                MeasuringState = MeasuringState.Finished;

                            }
                            // выход из функции замера (перезагрузка функции)

                            IsMeasuring = false; //TADAM

                            Debug.WriteLine("выход из функции замера: IsMeasuring = false");

                            return;
                        }

                        #endregion

                        // проверка достижения начальной граничной скорости пар скоростей
                        if (!IsInstant)
                        {
                            var maybeStarted = _context.GetRangesNeedStart(Speed);
                            if (maybeStarted.Count > 0)
                            {
                                foreach (var range in maybeStarted)
                                {
                                    var timeLag = (uint)((Speed - range.Start) * TIME_LAG_UNIT_COEFF / AverageAccelerationLegacy(speedBuffer, timeBuffer));
                                    if (range.Start == 0.0)
                                    {
                                        range.StartedAtMs = StartTime;
                                    }
                                    else
                                    {
                                        // расчёт поправки времени начала замера пары скоростей (линейная интерполяция)
                                        range.StartedAtMs = MeasuringTimeMs - timeLag;
                                    }
                                    Debug.WriteLine($"--- START RANGE SPEED: {range.Start}, Timelag: {timeLag}, StartTime: {range.StartedAtMs}");
                                }
                            }
                        }

                        // проверка достижения конечной граничной скорости из пар скоростей
                        if (rangesNeedFinalize.Count > 0)
                        {
                            // проигрывание джингла
                            if (_userManager.User.Options.Sound)
                            {
                                App.Instance.PlaySoundFile("new.mp3");

                                Debug.WriteLine("PlaySuccessJingle1");

                            }

                            foreach (var range in rangesNeedFinalize)
                            {
                                if (IsInstant && !range.IsUserDefined)
                                {
                                    continue; //пропускаем стандартные при замере СХОДУ
                                }

                                // расчёт поправки времени окончания замера пары скоростей (линейная интерполяция)
                                var timeLag = (uint)((Speed - range.End) * TIME_LAG_UNIT_COEFF / AverageAccelerationLegacy(speedBuffer, timeBuffer));

                                double endMs = 0.0;
                                if (range.Start == 0.0)
                                {
                                    endMs = MeasuringTimeMs - timeLag - range.StartedAtMs;
                                }
                                else
                                {
                                    endMs = MeasuringTimeMs - timeLag - range.StartedAtMs + RANGE_CORR;
                                }
                                range.Time = TimeSpan.FromMilliseconds(endMs);

                                var addResult = new MeasuredRange(range.Start, range.End)
                                {
                                    Units = range.Units,
                                    Time = range.Time,
                                    IsUserDefined = true
                                };

                                TmpResult.Ranges.Add(addResult);

                                // ------------------------------------------------------------------------------------------
                                MeasuredRange?.Invoke(this, addResult);
                                // ------------------------------------------------------------------------------------------

                                Debug.WriteLine($"---NEW RESULT (user): {addResult.Start}-{addResult.End}: {addResult.Time:g}");
                            }

                            // установка флага завершения замера "сходу" при замере всех пар скоростей
                            if (IsInstant && _context.GetUnmeasuredCustomRangesCount() == 0)
                                End($"При замере сходу замеряли все пары пользовательских скоростей");
                        }


                        // ---------------------------------------------------------------------------
                        // _measuring.dist[distIndex]
                        // ---------------------------------------------------------------------------
                        // проверка достижения граничной дистанции из списка dynRecord.distLimit[] при замере "с места"
                        var maybeReached = _context.GetNeedFinalizeDistances(MeasuringDistanceInMeters);
                        if (!IsInstant && maybeReached.Count > 0)
                        {
                            // проигрывание джингла
                            App.Instance.PlaySoundFile("new.mp3");

                            foreach (var distance in maybeReached)
                            {
                                // расчёт поправки времени окончания замера дистанции (линейная интерполяция)
                                // var timeLag = (uint)(MeasuringDistanceInMeters - distance.Length / SpeedAverage / 360.0);
                                var timeLag = (uint)((MeasuringDistanceInMeters - distance.Length) / SpeedAverage / 3.6 * 1000);

                                // расчёт и сохранение времени замера дистанции
                                distance.Time = TimeSpan.FromMilliseconds(MeasuringTimeMs - StartTime - timeLag - rolloutTime + distCorr);

                                // сохранение скорости на замере дистанции
                                distance.Speed = Speed;

                                distance.MaxIncline = MeasuringInclineMax;

                                Debug.WriteLine($"[CHECKPOINT] " +
                                                $"dist {distance.Length} " +
                                                $"time {distance.Time} " +
                                                $"speed {distance.Speed:0.00}");

                                TmpResult.Distances.Add(distance);

                                // APP: добавление результата в список результатов на экране Разгон
                                MeasuredDistance?.Invoke(this, distance);
                            }

                        }
                    }

                }
                //запрещение замера
                else
                {
                    End(ResStrings.BadMeasureGps);
                    IsMeasuring = false;
                    MeasureFail?.Invoke(this, MeasureError.GPSLow);
                }

            }
            else
            {
                //чтобы не сломать логику измерения мы добавляем монитор уклона сюда
                #region Monitor Incline

                var acmAltitude = (data.Altitude.GetValueOrDefault() - 500) * 100;
                // фильтрация значения высоты (в сантиметрах)
                avgAlt = (avgAlt == 0)
                    ?
                    acmAltitude
                    :
                    avgAlt * 0.95 + acmAltitude * 0.05;

                if (Speed < START_SPEED_THRESHOLD_UNIT)
                {

                    // обнуление уклона при остановке
                    MeasuringIncline = 0;

                    // сохранение текущего значения высоты
                    lastAvgAlt = avgAlt;

                    // начало нового отрезка замера уклона дороги
                    lastLoc.lat = data.Latitude.GetValueOrDefault();
                    lastLoc.lon = data.Longitude.GetValueOrDefault();


                }
                else
                {

                    // расчёт дистанции от предыдущей точки замера высоты
                    distAlt = (lastLoc.lat == 0)
                        ?
                        INCLINE_SAMPLE_DIST + 0.1
                        :
                        DistanceMeters(lastLoc.lat, lastLoc.lon,
                        data.Latitude.GetValueOrDefault(), data.Longitude.GetValueOrDefault());

                    // расчёт уклона дороги на отрезке INCLINE_SAMPLE_DIST
                    if (distAlt >= INCLINE_SAMPLE_DIST)
                    {
                        // расчёт уклона дороги (в % * 10)
                        MeasuringIncline = (lastAvgAlt == 0)
                            ? 0
                            : (avgAlt - lastAvgAlt) / (distAlt * 100) * 100; //в оригинале *1000 <--- !!!!

                        // сохранение текущего значения высоты
                        lastAvgAlt = avgAlt;

                        // начало нового отрезка замера уклона дороги
                        lastLoc.lat = data.Latitude.GetValueOrDefault();
                        lastLoc.lon = data.Longitude.GetValueOrDefault();
                    }

                }
                #endregion

                _lastWasMeasuring = false;
            }

            lastMs = Ticks;
        }
        catch (Exception e)
        {
            EndCause = e.ToString();

            MeasuringState = MeasuringState.Error;

            IsMeasuring = false; //error

        }
        finally
        {
            semaphore.Release();

            DataProcessed?.Invoke(this, null);
        }
    }

}

