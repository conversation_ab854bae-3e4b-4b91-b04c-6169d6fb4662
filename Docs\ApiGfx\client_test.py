import requests, json, re, time
from loguru import logger

url = 'https://chart.racebox.cc:5000'


def main():

    with open("test\\racebox_metadata.json", "r", encoding='utf-8') as metafile:
        metadata = json.load(metafile)
        logger.info(f"Metadata: {metadata}")

    files = {
        'file': open('test\\racebox_log.csv', 'rb'), 
        'metadata': (None, json.dumps(metadata, ensure_ascii=False), 'application/json')}  # Открываем файл в бинарном режиме

    start_time = time.time()
    response = requests.post(url, files=files, verify=False)

    if response.status_code == 200:
        content_disposition = response.headers.get('Content-Disposition')
        if content_disposition:
            # Ищем имя файла в строке (регулярное выражение)
            filename = re.findall('filename="?(.+)"?', content_disposition)[0]
        else:
            # Если заголовка нет, генерируем имя самостоятельно
            filename = "chart.png"  # или взять из URL
        with open(f"charts/{filename}", "wb") as f:
            f.write(response.content)
        stop_time = time.time()
        logger.info(f"Файл {filename} сгенерирован за {(stop_time - start_time):.3f} сек")
    else:
        logger.error(f"Ошибка [{response.status_code}]: {json.loads(response.text)['error']}")


if __name__ == "__main__":
    main()